# SheMove Document Management Fixes

## Overview
This guide provides comprehensive fixes for two critical issues in the SheMove document management system:

1. **Supabase Storage Upload Failures** - "Network request failed" errors
2. **Database Trigger Error** - "control reached end of trigger procedure without RETURN"

## Issue Analysis

### Issue 1: Storage Upload Failures
**Root Causes:**
- Bucket name inconsistencies (`driver-document` vs `driver-documents`)
- Private bucket with overly restrictive RLS policies
- Multiple conflicting storage policies from different setup attempts
- Missing proper error handling in React Native upload code

### Issue 2: Database Trigger Error
**Root Causes:**
- `update_updated_at_column()` trigger function not properly handling all execution paths
- Missing RETURN statements in some code branches
- Potential NULL handling issues

## Step-by-Step Fix Implementation

### Step 1: Apply Database Fixes
Run the comprehensive fix script in your Supabase SQL Editor:

```sql
-- Execute this file in Supabase SQL Editor
-- File: fix_document_management_issues.sql
```

This script will:
- ✅ Clean up conflicting storage policies
- ✅ Create/update the `driver-documents` bucket with correct settings
- ✅ Set up simple, working storage policies
- ✅ Fix the database trigger function to always return values
- ✅ Ensure all required document status enum values exist

### Step 2: Verify Fixes
Run the test script to verify everything is working:

```sql
-- Execute this file in Supabase SQL Editor  
-- File: test_document_management_fixes.sql
```

Expected results:
- ✅ Storage bucket configured correctly
- ✅ All required storage policies exist
- ✅ Trigger function exists and has RETURN statement
- ✅ Document uploads trigger exists
- ✅ All required document status values exist

### Step 3: Update Driver App (Already Done)
The driver app upload code has been improved with:
- ✅ Better error handling and user feedback
- ✅ Retry logic for failed uploads
- ✅ Proper TypeScript error handling
- ✅ More detailed logging for debugging

### Step 4: Test the Complete Workflow

#### Test Document Upload:
1. Open the SheMove driver app
2. Navigate to document upload screen
3. Try uploading a document (image or PDF)
4. Verify the upload succeeds and shows real Supabase URL (not example.com)

#### Test Document Approval:
1. Open the SheMove admin dashboard
2. Navigate to document review section
3. Try approving or rejecting a document
4. Verify no trigger errors occur
5. Confirm document status updates successfully

## Configuration Details

### Storage Bucket Configuration
```
Bucket ID: driver-documents
Name: driver-documents
Public: true (for easier access)
File Size Limit: 10MB (10485760 bytes)
Allowed MIME Types: ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
```

### Storage Policies
- `driver_docs_authenticated_upload` - Allows authenticated users to upload
- `driver_docs_public_read` - Allows public read access
- `driver_docs_authenticated_update` - Allows authenticated users to update
- `driver_docs_authenticated_delete` - Allows authenticated users to delete

### Document Status Values
- `uploaded` - Initial status after upload
- `under_review` - When admin starts reviewing
- `approved` - When admin approves document
- `rejected` - When admin rejects document

## Troubleshooting

### If Storage Uploads Still Fail:
1. Check Supabase project URL and anon key in driver app
2. Verify user is authenticated before upload
3. Check network connectivity
4. Review browser/app console for detailed error messages

### If Trigger Errors Still Occur:
1. Verify the trigger function was updated correctly
2. Check for any custom triggers that might conflict
3. Review Supabase logs for detailed error information

### If Document Status Updates Fail:
1. Ensure admin user has proper permissions
2. Check RLS policies on document_uploads table
3. Verify document_status enum has all required values

## Expected File URLs
After fixes, uploaded documents should have URLs like:
```
https://[your-project-id].supabase.co/storage/v1/object/public/driver-documents/[user-id]/[document-type]_[timestamp].[extension]
```

Example:
```
https://pcacyfyhxvzbjcouxzub.supabase.co/storage/v1/object/public/driver-documents/f2391652-176b-4920-bb7a-11f6f15188a7/drivers_license_1752175540627.jpg
```

## Success Indicators
- ✅ Document uploads complete without "Network request failed" errors
- ✅ Uploaded files show real Supabase URLs (not example.com)
- ✅ Admin dashboard can approve/reject documents without trigger errors
- ✅ Document status updates successfully in database
- ✅ Driver verification status updates when all documents approved

## Next Steps
After implementing these fixes:
1. Test the complete driver onboarding flow
2. Test the admin document review workflow
3. Monitor logs for any remaining issues
4. Consider adding automated tests for document management
