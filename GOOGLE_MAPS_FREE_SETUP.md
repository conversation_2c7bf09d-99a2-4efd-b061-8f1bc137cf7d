# Google Maps API: Free Legal Usage Guide

## Overview

Google provides generous free tiers for their mapping APIs that can handle most small to medium-scale applications legally and sustainably. Here's how to set it up for your SheMove app.

## Google Maps API Free Tiers (2024)

### 1. Places API (Address Search)
- **Free Quota**: $200 credit monthly = ~40,000 requests
- **Cost After**: $17 per 1,000 requests
- **Best For**: Address autocomplete, place search
- **Rate Limit**: 1,000 requests per 100 seconds per user

### 2. Geocoding API (Address → Coordinates)
- **Free Quota**: $200 credit monthly = ~40,000 requests  
- **Cost After**: $5 per 1,000 requests
- **Best For**: Converting addresses to lat/lng
- **Rate Limit**: 50 requests per second

### 3. Maps JavaScript API (Map Display)
- **Free Quota**: $200 credit monthly = ~28,000 map loads
- **Cost After**: $7 per 1,000 map loads
- **Best For**: Interactive maps in web/mobile apps

### 4. Directions API (Routing)
- **Free Quota**: $200 credit monthly = ~40,000 requests
- **Cost After**: $5 per 1,000 requests
- **Best For**: Route calculation and navigation

## Step-by-Step Setup

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing one
3. Name it "SheMove-Maps" or similar

### Step 2: Enable Required APIs

```bash
# Enable APIs via gcloud CLI (optional)
gcloud services enable places-backend.googleapis.com
gcloud services enable geocoding-backend.googleapis.com
gcloud services enable maps-backend.googleapis.com
gcloud services enable directions-backend.googleapis.com
```

Or via Console:
1. Go to "APIs & Services" → "Library"
2. Search and enable:
   - Places API
   - Geocoding API
   - Maps JavaScript API
   - Directions API

### Step 3: Create API Key

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "API Key"
3. Copy the generated key
4. Click "Restrict Key" for security

### Step 4: Configure API Key Restrictions

#### Application Restrictions (Choose One):
- **HTTP referrers**: For web apps (e.g., `https://yourdomain.com/*`)
- **IP addresses**: For server-side usage
- **Android apps**: For mobile apps (requires SHA-1 fingerprint)
- **iOS apps**: For iOS apps (requires bundle ID)

#### API Restrictions:
- Select only the APIs you need
- Don't leave unrestricted (security risk)

### Step 5: Set Up Billing (Required for Free Tier)

⚠️ **Important**: You MUST add a billing account to access free tier
- Add credit card (won't be charged within free limits)
- Set up billing alerts at $50, $100, $150
- Enable billing quotas to prevent overages

## Implementation Examples

### 1. Address Autocomplete (Places API)

```javascript
// Google Places Autocomplete Service
class GooglePlacesService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://maps.googleapis.com/maps/api/place';
  }

  async searchPlaces(query, location = null, radius = 50000) {
    const params = new URLSearchParams({
      input: query,
      key: this.apiKey,
      types: 'address',
      components: 'country:za', // Restrict to South Africa
    });

    // Add location bias for better local results
    if (location) {
      params.append('location', `${location.lat},${location.lng}`);
      params.append('radius', radius.toString());
    }

    const response = await fetch(
      `${this.baseUrl}/autocomplete/json?${params}`
    );

    if (!response.ok) {
      throw new Error(`Places API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Places API error: ${data.status}`);
    }

    return data.predictions.map(prediction => ({
      placeId: prediction.place_id,
      description: prediction.description,
      mainText: prediction.structured_formatting.main_text,
      secondaryText: prediction.structured_formatting.secondary_text,
      types: prediction.types
    }));
  }

  async getPlaceDetails(placeId) {
    const params = new URLSearchParams({
      place_id: placeId,
      key: this.apiKey,
      fields: 'formatted_address,geometry,name,types'
    });

    const response = await fetch(
      `${this.baseUrl}/details/json?${params}`
    );

    const data = await response.json();
    
    if (data.status !== 'OK') {
      throw new Error(`Place Details error: ${data.status}`);
    }

    const place = data.result;
    return {
      formattedAddress: place.formatted_address,
      coordinates: {
        lat: place.geometry.location.lat,
        lng: place.geometry.location.lng
      },
      name: place.name,
      types: place.types
    };
  }
}
```

### 2. Direct Geocoding (Geocoding API)

```javascript
// Google Geocoding Service
class GoogleGeocodingService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://maps.googleapis.com/maps/api/geocode';
  }

  async geocodeAddress(address) {
    const params = new URLSearchParams({
      address: address,
      key: this.apiKey,
      components: 'country:ZA', // Restrict to South Africa
      region: 'za' // Bias results to South Africa
    });

    const response = await fetch(`${this.baseUrl}/json?${params}`);
    const data = await response.json();

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Geocoding error: ${data.status}`);
    }

    return data.results.map(result => ({
      formattedAddress: result.formatted_address,
      coordinates: {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng
      },
      types: result.types,
      addressComponents: result.address_components,
      placeId: result.place_id
    }));
  }

  async reverseGeocode(lat, lng) {
    const params = new URLSearchParams({
      latlng: `${lat},${lng}`,
      key: this.apiKey,
      result_type: 'street_address|route|neighborhood|locality'
    });

    const response = await fetch(`${this.baseUrl}/json?${params}`);
    const data = await response.json();

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      throw new Error(`Reverse geocoding error: ${data.status}`);
    }

    return data.results[0] || null;
  }
}
```

### 3. Route Calculation (Directions API)

```javascript
// Google Directions Service
class GoogleDirectionsService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://maps.googleapis.com/maps/api/directions';
  }

  async getRoute(origin, destination, mode = 'driving') {
    const params = new URLSearchParams({
      origin: `${origin.lat},${origin.lng}`,
      destination: `${destination.lat},${destination.lng}`,
      mode: mode,
      key: this.apiKey,
      region: 'za'
    });

    const response = await fetch(`${this.baseUrl}/json?${params}`);
    const data = await response.json();

    if (data.status !== 'OK') {
      throw new Error(`Directions error: ${data.status}`);
    }

    const route = data.routes[0];
    const leg = route.legs[0];

    return {
      distance: leg.distance,
      duration: leg.duration,
      startAddress: leg.start_address,
      endAddress: leg.end_address,
      steps: leg.steps,
      polyline: route.overview_polyline.points
    };
  }
}
```

## Integration with Existing SheMove Code

### Enhanced Geocoding Service

```javascript
// Add to your existing geocodingService.ts
import { GooglePlacesService, GoogleGeocodingService } from './googleMapsService';

class EnhancedGeocodingService extends GeocodingService {
  private googlePlaces: GooglePlacesService;
  private googleGeocoding: GoogleGeocodingService;

  constructor() {
    super();
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    if (apiKey) {
      this.googlePlaces = new GooglePlacesService(apiKey);
      this.googleGeocoding = new GoogleGeocodingService(apiKey);
    }
  }

  async searchWithGoogleFallback(query: string, limit: number = 5) {
    try {
      // Try free services first
      const freeResults = await this.searchWithLocationIQFallback(query, limit);
      
      // If results are poor and Google API is available, use it as fallback
      if (this.googlePlaces && freeResults.results.length < 2) {
        console.log('🔄 Using Google Places API for better results');
        
        const googleResults = await this.googlePlaces.searchPlaces(query);
        const enhancedResults = await Promise.all(
          googleResults.slice(0, limit).map(async (place) => {
            const details = await this.googlePlaces.getPlaceDetails(place.placeId);
            return {
              display_name: details.formattedAddress,
              lat: details.coordinates.lat.toString(),
              lon: details.coordinates.lng.toString(),
              importance: 0.9, // High confidence for Google results
              address: { display_name: details.formattedAddress }
            };
          })
        );

        return {
          results: enhancedResults,
          provider: 'google_places'
        };
      }

      return freeResults;
    } catch (error) {
      console.error('Enhanced search failed:', error);
      return { results: [], error: error.message };
    }
  }
}
```

## Cost Optimization Strategies

### 1. Smart Caching
```javascript
// Cache results to avoid repeat API calls
const addressCache = new Map();

async function cachedGeocode(address) {
  if (addressCache.has(address)) {
    return addressCache.get(address);
  }
  
  const result = await googleGeocoding.geocodeAddress(address);
  addressCache.set(address, result);
  return result;
}
```

### 2. Request Batching
```javascript
// Batch multiple requests when possible
async function batchGeocode(addresses) {
  // Use Places API batch requests where available
  // Or implement request queuing to stay within rate limits
}
```

### 3. Fallback Strategy
```javascript
// Use free services first, Google as premium fallback
const searchStrategies = [
  { service: nominatimService, cost: 0 },
  { service: locationIQService, cost: 0 }, // Free tier
  { service: googlePlacesService, cost: 0.017 } // Per request
];
```

### 4. Usage Monitoring
```javascript
// Track API usage to stay within free limits
class APIUsageTracker {
  constructor() {
    this.dailyUsage = new Map();
    this.monthlyUsage = new Map();
  }

  trackRequest(service, cost) {
    const today = new Date().toDateString();
    const month = new Date().toISOString().slice(0, 7);
    
    this.dailyUsage.set(today, (this.dailyUsage.get(today) || 0) + cost);
    this.monthlyUsage.set(month, (this.monthlyUsage.get(month) || 0) + cost);
    
    // Alert if approaching limits
    if (this.monthlyUsage.get(month) > 150) { // $150 of $200 free tier
      console.warn('⚠️ Approaching Google API free tier limit');
    }
  }
}
```

## Security Best Practices

### 1. Environment Variables
```bash
# .env file
GOOGLE_MAPS_API_KEY=AIzaSyC4YourActualAPIKeyHere
```

### 2. API Key Restrictions
- Restrict by HTTP referrer for web apps
- Restrict by package name for mobile apps
- Restrict to specific APIs only
- Regularly rotate API keys

### 3. Rate Limiting
```javascript
// Implement client-side rate limiting
class RateLimiter {
  constructor(requestsPerSecond = 10) {
    this.requests = [];
    this.limit = requestsPerSecond;
  }

  async throttle() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < 1000);
    
    if (this.requests.length >= this.limit) {
      const waitTime = 1000 - (now - this.requests[0]);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.requests.push(now);
  }
}
```

## Quick Start Guide

### 1. Run the Setup Script
```bash
cd my-new-app
./setup-google-maps.sh
```

This interactive script will:
- Guide you through Google Cloud Console setup
- Help you create and configure API keys
- Test your API key automatically
- Update your .env file

### 2. Test the Integration
```bash
# Test Google Maps API integration
node test-google-maps-integration.js

# Test enhanced geocoding service
node -e "
const service = require('./services/geocodingService.ts');
const geocoder = new service.default();
geocoder.searchLocationsEnhanced('3 Aries Road, Johannesburg')
  .then(results => console.log(JSON.stringify(results, null, 2)));
"
```

### 3. Monitor Usage
- Check Google Cloud Console for real-time usage
- Use `geocoder.getGoogleUsageStats()` in your app
- Set up billing alerts at $50, $100, $150

## Integration Status

Your SheMove app now uses this intelligent fallback strategy:

1. **Nominatim** (Free) → Try first
2. **LocationIQ** (Free tier) → Fallback for better house numbers
3. **Google Places API** (Premium) → Fallback for best results
4. **Google Geocoding API** (Premium) → Alternative premium option

## Cost Optimization

### Free Tier Maximization
- **$200 monthly credit** = ~40,000 requests
- **Smart fallback** = Use free services first
- **Caching** = Avoid duplicate requests
- **Rate limiting** = Stay within quotas

### Expected Costs for SheMove
- **Development/Testing**: $0-10/month
- **Small user base** (<1000 users): $0-50/month
- **Medium user base** (1000-10000 users): $50-200/month
- **Large user base** (>10000 users): $200+/month

## Next Steps

1. **✅ Set up Google Cloud Project** and enable APIs
2. **✅ Create and restrict API key** for security
3. **✅ Implement the services** in your SheMove app
4. **🔄 Test with small volume** to verify functionality
5. **📊 Monitor usage** to stay within free limits
6. **📈 Scale gradually** as your app grows

This approach gives you access to Google's high-quality address data legally and sustainably, with a clear path to scale as your SheMove app grows!

## Support

If you encounter issues:
1. Check the test reports in `google-maps-test-report.json`
2. Verify API key restrictions in Google Cloud Console
3. Monitor billing and usage quotas
4. Review the enhanced geocoding service logs

Your SheMove app now has enterprise-grade address resolution while staying within free tiers! 🚀
