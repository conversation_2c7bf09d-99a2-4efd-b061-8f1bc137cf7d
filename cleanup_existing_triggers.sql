-- =====================================================
-- CLEANUP EXISTING TRIGGERS
-- Run this first to remove any conflicting triggers
-- =====================================================

-- Step 1: Show current triggers before cleanup
SELECT 
    '=== CURRENT TRIGGERS BEFORE CLEANUP ===' as section,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 2: Remove all existing triggers on document_uploads table
DROP TRIGGER IF EXISTS handle_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;
DROP TRIGGER IF EXISTS admin_document_update_trigger ON document_uploads;

-- Step 3: Remove any other document-related triggers
DROP TRIGGER IF EXISTS update_verification_metrics_trigger ON document_reviews;

-- Step 4: Show triggers after cleanup
SELECT 
    '=== TRIGGERS AFTER CLEANUP ===' as section,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ All triggers removed successfully'
        ELSE '⚠️ Some triggers still exist'
    END as status,
    COUNT(*) as remaining_triggers
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads';

-- Step 5: List any remaining triggers
SELECT 
    '=== REMAINING TRIGGERS (if any) ===' as section,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

SELECT '✅ CLEANUP COMPLETE - Now run fix_admin_approval_targeted.sql' as result;
