-- =====================================================
-- COMPREHENSIVE APPROVAL FLOW DEBUGGING
-- This will help identify exactly where the trigger error occurs
-- =====================================================

-- Step 1: Enable detailed logging for this session
SET log_min_messages = 'notice';
SET log_statement = 'all';

-- Step 2: Check current trigger status
SELECT 
    '=== CURRENT TRIGGER STATUS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 3: Check trigger function source code
SELECT 
    '=== TRIGGER FUNCTION SOURCE ===' as section,
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE proname IN (
    'document_approval_trigger',
    'update_updated_at_column',
    'handle_document_update'
)
ORDER BY proname;

-- Step 4: Find a document to test with
SELECT 
    '=== AVAILABLE TEST DOCUMENTS ===' as section,
    id,
    driver_id,
    document_type,
    status,
    created_at,
    updated_at,
    reviewed_at
FROM document_uploads 
WHERE status IN ('under_review', 'uploaded')
ORDER BY created_at DESC
LIMIT 5;

-- Step 5: Simulate the exact admin dashboard update with detailed logging
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    original_updated_at TIMESTAMP;
    new_updated_at TIMESTAMP;
    update_successful BOOLEAN := false;
    error_occurred BOOLEAN := false;
    error_message TEXT;
    error_code TEXT;
BEGIN
    -- Find a document to test with
    SELECT id, status, updated_at 
    INTO test_doc_id, original_status, original_updated_at
    FROM document_uploads 
    WHERE status IN ('under_review', 'uploaded')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE '🧪 STARTING COMPREHENSIVE TEST';
        RAISE NOTICE '🧪 Document ID: %', test_doc_id;
        RAISE NOTICE '🧪 Original status: %', original_status;
        RAISE NOTICE '🧪 Original updated_at: %', original_updated_at;
        RAISE NOTICE '🧪 Current timestamp: %', NOW();
        
        -- Test 1: Simple update (just timestamp)
        RAISE NOTICE '🧪 TEST 1: Simple timestamp update';
        BEGIN
            UPDATE document_uploads 
            SET updated_at = NOW()
            WHERE id = test_doc_id;
            
            RAISE NOTICE '✅ TEST 1 PASSED: Simple update successful';
        EXCEPTION
            WHEN OTHERS THEN
                error_occurred := true;
                error_message := SQLERRM;
                error_code := SQLSTATE;
                RAISE NOTICE '❌ TEST 1 FAILED: % (Code: %)', error_message, error_code;
        END;
        
        -- Test 2: Status change to approved (what admin dashboard does)
        IF NOT error_occurred THEN
            RAISE NOTICE '🧪 TEST 2: Status change to approved';
            BEGIN
                UPDATE document_uploads 
                SET 
                    status = 'approved',
                    reviewed_at = NOW()
                WHERE id = test_doc_id;
                
                RAISE NOTICE '✅ TEST 2 PASSED: Approval update successful';
                update_successful := true;
            EXCEPTION
                WHEN OTHERS THEN
                    error_occurred := true;
                    error_message := SQLERRM;
                    error_code := SQLSTATE;
                    RAISE NOTICE '❌ TEST 2 FAILED: % (Code: %)', error_message, error_code;
                    
                    -- Check for specific trigger error
                    IF error_code = '2F005' THEN
                        RAISE NOTICE '🔥 TRIGGER ERROR CONFIRMED: This is the exact error admin dashboard sees';
                    END IF;
            END;
        END IF;
        
        -- Test 3: Status change to rejected
        IF NOT error_occurred THEN
            RAISE NOTICE '🧪 TEST 3: Status change to rejected';
            BEGIN
                UPDATE document_uploads 
                SET 
                    status = 'rejected',
                    reviewed_at = NOW(),
                    rejection_reason = 'Test rejection'
                WHERE id = test_doc_id;
                
                RAISE NOTICE '✅ TEST 3 PASSED: Rejection update successful';
            EXCEPTION
                WHEN OTHERS THEN
                    error_occurred := true;
                    error_message := SQLERRM;
                    error_code := SQLSTATE;
                    RAISE NOTICE '❌ TEST 3 FAILED: % (Code: %)', error_message, error_code;
            END;
        END IF;
        
        -- Restore original state
        BEGIN
            UPDATE document_uploads 
            SET 
                status = original_status::document_status,
                reviewed_at = NULL,
                rejection_reason = NULL,
                admin_notes = NULL,
                updated_at = original_updated_at
            WHERE id = test_doc_id;
            
            RAISE NOTICE '🔄 DOCUMENT RESTORED to original state';
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '⚠️ Could not restore document: % (Code: %)', SQLERRM, SQLSTATE;
        END;
        
        -- Summary
        IF error_occurred THEN
            RAISE NOTICE '📊 TEST SUMMARY: FAILED';
            RAISE NOTICE '📊 Error: %', error_message;
            RAISE NOTICE '📊 Code: %', error_code;
            RAISE NOTICE '📊 This is the exact issue causing admin dashboard failures';
        ELSE
            RAISE NOTICE '📊 TEST SUMMARY: ALL TESTS PASSED';
            RAISE NOTICE '📊 The trigger is working correctly';
            RAISE NOTICE '📊 The issue might be elsewhere in the admin dashboard';
        END IF;
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents found for testing';
        RAISE NOTICE 'ℹ️ Upload a document in the driver app first';
    END IF;
    
END $$;

-- Step 6: Check for any conflicting triggers or functions
SELECT 
    '=== POTENTIAL CONFLICTS ===' as section,
    schemaname,
    tablename,
    triggername,
    CASE 
        WHEN triggername LIKE '%document%' THEN '📄 Document related'
        WHEN triggername LIKE '%update%' THEN '🔄 Update related'
        ELSE '📋 Other'
    END as category
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE NOT t.tgisinternal
AND c.relname = 'document_uploads'
ORDER BY triggername;

-- Step 7: Check for any RLS policies that might interfere
SELECT 
    '=== ROW LEVEL SECURITY POLICIES ===' as section,
    schemaname,
    tablename,
    policyname,
    cmd as operation,
    qual as condition
FROM pg_policies 
WHERE tablename = 'document_uploads'
ORDER BY policyname;

-- Step 8: Final recommendations
SELECT 
    '=== DEBUGGING COMPLETE ===' as section,
    'Check the NOTICE messages above for detailed test results' as message;

SELECT 
    '=== NEXT STEPS ===' as section,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name = 'document_approval_trigger'
            AND event_object_table = 'document_uploads'
        ) THEN 'Trigger exists - check test results above'
        ELSE 'Run safe_fix_document_trigger_only.sql first'
    END as recommendation;
