-- =====================================================
-- DIAGNOSE ADMIN DASHBOARD ERROR
-- Specifically check what's causing the empty error object {}
-- =====================================================

-- Step 1: Check if documents exist and their current state
SELECT 
    '=== DOCUMENT UPLOADS TABLE STATUS ===' as section,
    COUNT(*) as total_documents,
    COUNT(CASE WHEN status = 'under_review' THEN 1 END) as under_review,
    COUNT(CASE WHEN status = 'uploaded' THEN 1 END) as uploaded,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
FROM document_uploads;

-- Step 2: Show sample documents that admin would try to approve
SELECT 
    '=== SAMPLE DOCUMENTS FOR APPROVAL ===' as section,
    id,
    driver_id,
    document_type,
    status,
    file_name,
    created_at,
    updated_at,
    reviewed_at
FROM document_uploads 
WHERE status IN ('under_review', 'uploaded')
ORDER BY created_at DESC
LIMIT 3;

-- Step 3: Check table structure to ensure all required columns exist
SELECT 
    '=== DOCUMENT_UPLOADS TABLE STRUCTURE ===' as section,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'document_uploads'
AND column_name IN ('id', 'status', 'reviewed_at', 'admin_notes', 'rejection_reason', 'updated_at')
ORDER BY column_name;

-- Step 4: Check if the document_status enum has required values
SELECT 
    '=== DOCUMENT STATUS ENUM CHECK ===' as section,
    enumlabel as status_value,
    CASE 
        WHEN enumlabel IN ('approved', 'rejected', 'under_review') THEN '✅ Required for admin'
        ELSE '📋 Other'
    END as importance
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- Step 5: Test the exact update that admin dashboard tries to perform
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    error_occurred BOOLEAN := false;
    error_message TEXT;
BEGIN
    -- Find a document to test with
    SELECT id, status INTO test_doc_id, original_status
    FROM document_uploads 
    WHERE status IN ('under_review', 'uploaded')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing admin dashboard update on document: %', test_doc_id;
        
        BEGIN
            -- This is exactly what the admin dashboard does
            UPDATE document_uploads 
            SET 
                status = 'approved',
                reviewed_at = NOW()
            WHERE id = test_doc_id;
            
            RAISE NOTICE '✅ SUCCESS: Admin update completed without errors';
            
            -- Check if the update actually worked
            IF FOUND THEN
                RAISE NOTICE '✅ Document was successfully updated';
            ELSE
                RAISE NOTICE '⚠️ No rows were affected by the update';
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                error_occurred := true;
                error_message := SQLERRM;
                RAISE NOTICE '❌ ERROR during update: %', error_message;
                RAISE NOTICE 'Error code: %', SQLSTATE;
        END;
        
        -- Restore original status regardless of success/failure
        BEGIN
            UPDATE document_uploads 
            SET 
                status = original_status::document_status,
                reviewed_at = NULL
            WHERE id = test_doc_id;
            RAISE NOTICE '✅ Document restored to original status: %', original_status;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE '⚠️ Could not restore original status: %', SQLERRM;
        END;
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents available for testing';
        RAISE NOTICE 'Upload a document in the driver app first';
    END IF;
    
END $$;

-- Step 6: Check for any RLS policies that might be interfering
SELECT 
    '=== ROW LEVEL SECURITY POLICIES ===' as section,
    schemaname,
    tablename,
    policyname,
    cmd as operation,
    CASE 
        WHEN cmd = 'UPDATE' THEN '🔄 Affects admin updates'
        ELSE '📋 Other operation'
    END as relevance
FROM pg_policies 
WHERE tablename = 'document_uploads'
ORDER BY policyname;

-- Step 7: Check current trigger status
SELECT 
    '=== CURRENT TRIGGERS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    CASE 
        WHEN trigger_name LIKE '%admin%' THEN '👨‍💼 Admin specific'
        WHEN trigger_name LIKE '%update%' THEN '🔄 Update related'
        ELSE '📋 Other'
    END as category
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 8: Final recommendations
SELECT 
    '=== DIAGNOSTIC COMPLETE ===' as section,
    'Check the results above for issues' as message;

SELECT 
    '=== NEXT STEPS ===' as section,
    CASE 
        WHEN EXISTS (SELECT 1 FROM document_uploads WHERE status IN ('under_review', 'uploaded')) 
        THEN '1. Run fix_admin_approval_targeted.sql'
        ELSE '1. Upload documents in driver app first'
    END as step1,
    '2. Test approval in admin dashboard' as step2,
    '3. Check browser console for detailed errors' as step3;
