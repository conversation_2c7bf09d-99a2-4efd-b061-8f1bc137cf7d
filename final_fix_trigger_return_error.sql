-- =====================================================
-- FINAL FIX FOR TRIGGER RETURN ERROR
-- Based on PostgreSQL documentation and error analysis
-- =====================================================

-- Step 1: Remove ALL existing triggers completely
DROP TRIGGER IF EXISTS handle_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;
DROP TRIGGER IF EXISTS admin_document_update_trigger ON document_uploads;

-- Step 2: Remove any trigger functions that might be problematic
DROP FUNCTION IF EXISTS handle_document_update();
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS safe_update_document_status();
DROP FUNCTION IF EXISTS admin_safe_document_update();

-- Step 3: Create a bulletproof trigger function that ALWAYS returns
CREATE OR REPLACE FUNCTION bulletproof_document_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- This function is designed to NEVER reach the end without RETURN
    
    -- Handle UPDATE operations (what admin dashboard does)
    IF TG_OP = 'UPDATE' THEN
        -- Update timestamp
        NEW.updated_at = NOW();
        
        -- Set reviewed_at for status changes
        IF NEW.status IN ('approved', 'rejected') THEN
            NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
        END IF;
        
        -- Log for debugging
        RAISE LOG 'Document % updated: % -> %', NEW.id, OLD.status, NEW.status;
        
        -- ALWAYS return NEW for UPDATE
        RETURN NEW;
    END IF;
    
    -- Handle INSERT operations
    IF TG_OP = 'INSERT' THEN
        -- Set timestamps
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        
        -- ALWAYS return NEW for INSERT
        RETURN NEW;
    END IF;
    
    -- Handle DELETE operations
    IF TG_OP = 'DELETE' THEN
        -- ALWAYS return OLD for DELETE
        RETURN OLD;
    END IF;
    
    -- This should NEVER be reached, but just in case
    -- We MUST return something to avoid the error
    IF TG_OP IN ('UPDATE', 'INSERT') THEN
        RETURN NEW;
    ELSE
        RETURN OLD;
    END IF;
    
    -- Additional safety net (should never be reached)
    RETURN NULL;
    
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create the trigger with minimal scope
CREATE TRIGGER bulletproof_document_trigger
    BEFORE INSERT OR UPDATE OR DELETE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION bulletproof_document_trigger();

-- Step 5: Test the trigger immediately
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    test_passed BOOLEAN := false;
BEGIN
    -- Find a document to test with
    SELECT id, status INTO test_doc_id, original_status
    FROM document_uploads 
    WHERE status IN ('under_review', 'uploaded')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing trigger with document: %', test_doc_id;
        
        -- Test the exact update that admin dashboard performs
        UPDATE document_uploads 
        SET 
            status = 'approved',
            reviewed_at = NOW()
        WHERE id = test_doc_id;
        
        -- If we get here, the trigger worked
        test_passed := true;
        RAISE NOTICE '✅ SUCCESS: Trigger test passed - no return error';
        
        -- Restore original status
        UPDATE document_uploads 
        SET 
            status = original_status::document_status,
            reviewed_at = NULL
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ Document restored to original status';
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents available for testing';
        RAISE NOTICE 'Upload a document first, then test admin approval';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ TRIGGER TEST FAILED: %', SQLERRM;
        RAISE NOTICE 'Error code: %', SQLSTATE;
        
        -- Try to restore document if possible
        IF test_doc_id IS NOT NULL THEN
            BEGIN
                UPDATE document_uploads 
                SET status = original_status::document_status
                WHERE id = test_doc_id;
            EXCEPTION
                WHEN OTHERS THEN
                    -- Ignore restoration errors
                    NULL;
            END;
        END IF;
END $$;

-- Step 6: Verify the setup
SELECT 
    '=== TRIGGER VERIFICATION ===' as section,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 7: Show function source to verify it has proper returns
SELECT 
    '=== FUNCTION VERIFICATION ===' as section,
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' AND prosrc LIKE '%RETURN OLD%' THEN '✅ Has proper RETURN statements'
        WHEN prosrc LIKE '%RETURN%' THEN '⚠️ Has some RETURN statements'
        ELSE '❌ Missing RETURN statements'
    END as return_check
FROM pg_proc 
WHERE proname = 'bulletproof_document_trigger';

-- Step 8: Final status check
SELECT 
    '=== FINAL STATUS ===' as section,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name = 'bulletproof_document_trigger'
            AND event_object_table = 'document_uploads'
        ) THEN '✅ Trigger is active and ready'
        ELSE '❌ Trigger not found'
    END as trigger_status;

SELECT '🎉 FINAL FIX COMPLETE - Admin dashboard approval should now work!' as result;
SELECT 'Try approving a document in the admin dashboard' as next_step;
