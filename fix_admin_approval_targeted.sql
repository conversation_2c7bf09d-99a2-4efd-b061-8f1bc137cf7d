-- =====================================================
-- TARGETED FIX FOR ADMIN DASHBOARD APPROVAL ERROR
-- Specifically addresses the empty error object {} issue
-- =====================================================

-- Step 1: First, let's see what's currently happening
SELECT 
    '=== CURRENT TRIGGER STATUS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 2: Check current trigger functions
SELECT 
    '=== CURRENT TRIGGER FUNCTIONS ===' as section,
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' THEN '✅ Returns NEW'
        WHEN prosrc LIKE '%RETURN OLD%' THEN '✅ Returns OLD'  
        WHEN prosrc LIKE '%RETURN%' THEN '⚠️ Has RETURN'
        ELSE '❌ No RETURN'
    END as return_status
FROM pg_proc 
WHERE proname IN (
    'handle_document_update',
    'update_updated_at_column',
    'safe_update_document_status'
);

-- Step 3: Remove ALL existing triggers to start fresh
DROP TRIGGER IF EXISTS handle_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;

-- Step 4: Create a minimal, bulletproof trigger function
CREATE OR REPLACE FUNCTION admin_safe_document_update()
RETURNS TRIGGER AS $$
BEGIN
    -- This function is designed specifically for admin dashboard operations
    
    -- For UPDATE operations (which is what admin dashboard does)
    IF TG_OP = 'UPDATE' THEN
        -- Ensure we have NEW record
        IF NEW IS NULL THEN
            RAISE EXCEPTION 'NEW record cannot be null in UPDATE trigger';
        END IF;
        
        -- Always update the timestamp
        NEW.updated_at = NOW();
        
        -- Set reviewed_at when status changes to approved/rejected
        IF NEW.status IN ('approved', 'rejected') THEN
            NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
        END IF;
        
        -- Log for debugging
        RAISE LOG 'Admin document update: ID=%, Status=%, ReviewedAt=%', 
                  NEW.id, NEW.status, NEW.reviewed_at;
        
        -- ALWAYS return NEW for UPDATE
        RETURN NEW;
    END IF;
    
    -- For INSERT operations
    IF TG_OP = 'INSERT' THEN
        IF NEW IS NULL THEN
            RAISE EXCEPTION 'NEW record cannot be null in INSERT trigger';
        END IF;
        
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        
        RETURN NEW;
    END IF;
    
    -- For DELETE operations
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    END IF;
    
    -- This should never be reached, but just in case
    RAISE WARNING 'Unexpected trigger operation: %', TG_OP;
    RETURN COALESCE(NEW, OLD);
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the operation
        RAISE WARNING 'Error in admin_safe_document_update: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
        
        -- Return appropriate record based on operation
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            -- Ensure basic fields are set even on error
            IF NEW IS NOT NULL THEN
                NEW.updated_at = NOW();
            END IF;
            RETURN NEW;
        END IF;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Create the trigger with minimal scope (only UPDATE for now)
CREATE TRIGGER admin_document_update_trigger
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION admin_safe_document_update();

-- Step 6: Test the trigger with a real document
DO $$
DECLARE
    test_doc_id UUID;
    test_status TEXT;
    update_count INTEGER;
BEGIN
    -- Find a document to test with
    SELECT id, status INTO test_doc_id, test_status
    FROM document_uploads 
    WHERE status = 'under_review'
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing with document ID: %', test_doc_id;
        
        -- Test the exact update that admin dashboard performs
        UPDATE document_uploads 
        SET 
            status = 'approved',
            reviewed_at = NOW(),
            admin_notes = 'Test approval'
        WHERE id = test_doc_id;
        
        GET DIAGNOSTICS update_count = ROW_COUNT;
        
        IF update_count = 1 THEN
            RAISE NOTICE '✅ SUCCESS: Document update completed without errors';
            
            -- Restore original status
            UPDATE document_uploads 
            SET 
                status = test_status::document_status,
                reviewed_at = NULL,
                admin_notes = NULL
            WHERE id = test_doc_id;
            
            RAISE NOTICE '✅ Document restored to original state';
        ELSE
            RAISE NOTICE '❌ FAILED: No rows were updated';
        END IF;
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents with under_review status found for testing';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ TEST FAILED: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END $$;

-- Step 7: Verify the setup
SELECT 
    '=== VERIFICATION ===' as section,
    'Trigger created successfully' as status;

SELECT 
    '=== ACTIVE TRIGGERS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads';

-- Step 8: Check document status enum values
SELECT 
    '=== DOCUMENT STATUS VALUES ===' as section,
    enumlabel as available_status
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- Step 9: Show current documents for testing
SELECT 
    '=== AVAILABLE TEST DOCUMENTS ===' as section,
    id,
    document_type,
    status,
    created_at
FROM document_uploads 
WHERE status IN ('under_review', 'uploaded')
ORDER BY created_at DESC
LIMIT 5;

SELECT '✅ TARGETED FIX COMPLETE - Try approving a document in admin dashboard now' as result;
