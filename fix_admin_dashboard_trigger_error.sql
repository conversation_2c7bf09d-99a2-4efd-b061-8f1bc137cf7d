-- =====================================================
-- FIX ADMIN DASHBOARD TRIGGER ERROR
-- Resolves "control reached end of trigger procedure without <PERSON><PERSON><PERSON>N" 
-- for document approval in admin dashboard
-- =====================================================

-- Step 1: Drop any problematic triggers that might be causing issues
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;
DROP TRIGGER IF EXISTS handle_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS update_verification_metrics_trigger ON document_reviews;

-- Step 2: Create a robust trigger function that ALWAYS returns a value
CREATE OR REPLACE FUNCTION handle_document_update()
R<PERSON>URNS TRIGGER AS $$
BEGIN
    -- Ensure we have a NEW record for UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        IF NEW IS NULL THEN
            RAISE EXCEPTION 'NEW record is null in document update trigger';
        END IF;
        
        -- Always update the updated_at timestamp
        NEW.updated_at = NOW();
        
        -- Set reviewed_at when status changes to approved or rejected
        IF NEW.status IN ('approved', 'rejected') AND 
           (OLD.status IS NULL OR OLD.status != NEW.status) THEN
            NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
        END IF;
        
        -- Ensure status is never null
        IF NEW.status IS NULL THEN
            NEW.status = COALESCE(OLD.status, 'uploaded');
        END IF;
        
        -- Log the update for debugging
        RAISE LOG 'Document % status updated from % to %', NEW.id, OLD.status, NEW.status;
        
        -- ALWAYS return NEW for UPDATE operations
        RETURN NEW;
        
    ELSIF TG_OP = 'INSERT' THEN
        IF NEW IS NULL THEN
            RAISE EXCEPTION 'NEW record is null in document insert trigger';
        END IF;
        
        -- Set timestamps for new records
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        
        -- Ensure status has a default value
        IF NEW.status IS NULL THEN
            NEW.status = 'uploaded';
        END IF;
        
        -- ALWAYS return NEW for INSERT operations
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        -- For DELETE operations, return OLD
        RETURN OLD;
    END IF;
    
    -- Fallback return (should never reach here, but ensures we always return)
    RAISE WARNING 'Unexpected trigger operation: %', TG_OP;
    RETURN COALESCE(NEW, OLD);
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the transaction
        RAISE WARNING 'Error in handle_document_update trigger: %', SQLERRM;
        
        -- Ensure we still return a value even on error
        IF TG_OP = 'DELETE' THEN
            RETURN OLD;
        ELSE
            -- For INSERT/UPDATE, ensure basic fields are set
            IF NEW IS NOT NULL THEN
                NEW.updated_at = NOW();
                IF NEW.status IS NULL THEN
                    NEW.status = COALESCE(OLD.status, 'uploaded');
                END IF;
            END IF;
            RETURN NEW;
        END IF;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create the trigger with proper timing
CREATE TRIGGER handle_document_update_trigger
    BEFORE INSERT OR UPDATE OR DELETE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION handle_document_update();

-- Step 4: Create a simpler backup trigger function (just in case)
CREATE OR REPLACE FUNCTION simple_update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    -- Simple function that just updates timestamp
    IF TG_OP = 'UPDATE' AND NEW IS NOT NULL THEN
        NEW.updated_at = NOW();
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' AND NEW IS NOT NULL THEN
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        RETURN OLD;
    END IF;
    
    -- Always return something
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Step 5: Verify the trigger function is properly created
SELECT 
    '=== TRIGGER FUNCTION VERIFICATION ===' as section,
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' AND prosrc LIKE '%RETURN OLD%' THEN '✅ Has both RETURN NEW and RETURN OLD'
        WHEN prosrc LIKE '%RETURN%' THEN '⚠️ Has RETURN statements'
        ELSE '❌ Missing RETURN statements'
    END as return_status,
    CASE 
        WHEN prosrc LIKE '%EXCEPTION%' THEN '✅ Has exception handling'
        ELSE '⚠️ No exception handling'
    END as exception_handling
FROM pg_proc 
WHERE proname = 'handle_document_update';

-- Step 6: Verify the trigger is active
SELECT 
    '=== TRIGGER VERIFICATION ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    event_object_table
FROM information_schema.triggers 
WHERE trigger_name = 'handle_document_update_trigger'
AND event_object_table = 'document_uploads';

-- Step 7: Test the trigger with a safe operation
DO $$
DECLARE
    test_doc_id UUID;
    test_result BOOLEAN := false;
BEGIN
    -- Find a document to test with
    SELECT id INTO test_doc_id
    FROM document_uploads 
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        -- Test update operation
        UPDATE document_uploads 
        SET updated_at = NOW()
        WHERE id = test_doc_id;
        
        test_result := true;
        RAISE NOTICE '✅ Test update successful - trigger is working';
    ELSE
        RAISE NOTICE 'ℹ️ No documents found to test with';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test update failed: %', SQLERRM;
        RAISE NOTICE 'This indicates the trigger is still problematic';
END $$;

-- Step 8: Ensure document status enum has all required values
DO $$
BEGIN
    -- Ensure all status values exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'approved' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'approved';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'rejected' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'rejected';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
    END IF;
END $$;

-- Success message
SELECT '✅ SUCCESS: Admin dashboard trigger error should now be fixed' as result;
SELECT 'Try approving a document in the admin dashboard now' as next_step;
