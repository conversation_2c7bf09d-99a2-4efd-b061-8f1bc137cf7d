-- =====================================================
-- FIX DATABASE TRIGGER ONLY
-- Fixes the "control reached end of trigger procedure without RETURN" error
-- =====================================================

-- Step 1: Fix the update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure we have a NEW record
    IF NEW IS NULL THEN
        RAISE EXCEPTION 'NEW record is null in trigger';
    END IF;
    
    -- Update timestamp
    NEW.updated_at = NOW();
    
    -- ALWAYS return NEW for BEFORE UPDATE triggers
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail transaction
        RAISE WARNING 'Error in update_updated_at_column: %', SQLERRM;
        -- Ensure timestamp is still updated
        IF NEW IS NOT NULL THEN
            NEW.updated_at = NOW();
        END IF;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Recreate the trigger safely
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
CREATE TRIGGER update_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 3: Ensure document status enum has all required values
DO $$
BEGIN
    -- Add missing status values if they don't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'approved' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'approved';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'rejected' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'rejected';
    END IF;
END $$;

-- Step 4: Test the trigger function
SELECT 
    '=== TRIGGER FUNCTION TEST ===' as section,
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' THEN '✅ Has RETURN statement'
        ELSE '❌ Missing RETURN statement'
    END as return_status
FROM pg_proc 
WHERE proname = 'update_updated_at_column';

-- Step 5: Verify trigger exists
SELECT 
    '=== TRIGGER STATUS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    event_object_table
FROM information_schema.triggers 
WHERE trigger_name = 'update_document_uploads_updated_at'
AND event_object_table = 'document_uploads';

-- Step 6: Show available document status values
SELECT 
    '=== DOCUMENT STATUS VALUES ===' as section,
    enumlabel as status_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- Success message
SELECT '✅ SUCCESS: Database trigger fixed for document approval workflow' as result;
