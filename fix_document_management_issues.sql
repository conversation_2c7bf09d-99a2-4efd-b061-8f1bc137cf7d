-- =====================================================
-- COMPREHENSIVE FIX FOR DOCUMENT MANAGEMENT ISSUES
-- Fixes both storage upload failures and trigger errors
-- Run this in your Supabase SQL Editor
-- =====================================================

-- =====================================================
-- PART 1: FIX STORAGE UPLOAD ISSUES
-- =====================================================

-- Clean up conflicting storage policies
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_upload_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "public_read_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_update_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_delete_driver_documents" ON storage.objects;

-- Remove any incorrectly named buckets
DELETE FROM storage.buckets WHERE id IN ('driver-document', 'documents');

-- Create/update the correct driver-documents bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'driver-documents',
    'driver-documents', 
    true, -- Public bucket for easier access
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
)
ON CONFLICT (id) DO UPDATE SET
    public = true,
    file_size_limit = 10485760,
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];

-- Create simple, working storage policies
CREATE POLICY "driver_docs_authenticated_upload" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "driver_docs_public_read" ON storage.objects
    FOR SELECT USING (bucket_id = 'driver-documents');

CREATE POLICY "driver_docs_authenticated_update" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "driver_docs_authenticated_delete" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- =====================================================
-- PART 2: FIX DATABASE TRIGGER ISSUES
-- =====================================================

-- Fix the update_updated_at_column function to always return a value
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure we have a NEW record
    IF NEW IS NULL THEN
        RAISE EXCEPTION 'NEW record is null in trigger';
    END IF;
    
    -- Update timestamp
    NEW.updated_at = NOW();
    
    -- ALWAYS return NEW for BEFORE UPDATE triggers
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail transaction
        RAISE WARNING 'Error in update_updated_at_column: %', SQLERRM;
        NEW.updated_at = NOW();
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger safely
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
CREATE TRIGGER update_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- PART 3: ENSURE DOCUMENT STATUS ENUM IS CORRECT
-- =====================================================

-- Make sure all required document status values exist
DO $$
BEGIN
    -- Check if under_review status exists, add if missing
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
    END IF;
    
    -- Check if approved status exists, add if missing  
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'approved' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'approved';
    END IF;
    
    -- Check if rejected status exists, add if missing
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'rejected' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'rejected';
    END IF;
END $$;

-- =====================================================
-- PART 4: VERIFICATION QUERIES
-- =====================================================

-- Check storage bucket configuration
SELECT 
    '=== STORAGE BUCKET STATUS ===' as section,
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- Check storage policies
SELECT 
    '=== STORAGE POLICIES ===' as section,
    policyname,
    cmd as operation
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver_docs%'
ORDER BY policyname;

-- Check document status enum values
SELECT 
    '=== DOCUMENT STATUS VALUES ===' as section,
    enumlabel as status_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- Check trigger functions
SELECT 
    '=== TRIGGER FUNCTIONS ===' as section,
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' OR prosrc LIKE '%RETURN OLD%' THEN 'Has RETURN ✅'
        ELSE 'Missing RETURN ❌'
    END as return_status
FROM pg_proc 
WHERE proname = 'update_updated_at_column';

-- Final success message
SELECT '🎉 SUCCESS: All document management issues have been fixed!' as result;
