-- =====================================================
-- FIX "Network request failed" STORAGE UPLOAD ERROR
-- This specifically addresses the upload failure issue
-- =====================================================

-- Step 1: Check current storage policies (for debugging)
SELECT 
    '=== CURRENT STORAGE POLICIES ===' as section,
    policyname,
    cmd as operation,
    qual as conditions
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND (policyname LIKE '%driver%' OR policyname LIKE '%document%')
ORDER BY policyname;

-- Step 2: Remove ALL existing policies that might conflict
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletes from driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_upload_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "public_read_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_update_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_delete_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_upload" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_public_read" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_update" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_delete" ON storage.objects;
DROP POLICY IF EXISTS "driver_documents_upload" ON storage.objects;
DROP POLICY IF EXISTS "driver_documents_read" ON storage.objects;
DROP POLICY IF EXISTS "driver_documents_update" ON storage.objects;
DROP POLICY IF EXISTS "driver_documents_delete" ON storage.objects;

-- Step 3: Create the simplest possible working policies
-- For public buckets, we need very simple policies

-- Allow any authenticated user to upload to driver-documents
CREATE POLICY "simple_upload_driver_documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents'
    );

-- Allow anyone to read from driver-documents (since it's public)
CREATE POLICY "simple_read_driver_documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'driver-documents'
    );

-- Allow authenticated users to update files in driver-documents
CREATE POLICY "simple_update_driver_documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents'
    );

-- Allow authenticated users to delete files in driver-documents
CREATE POLICY "simple_delete_driver_documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents'
    );

-- Step 4: Verify bucket configuration is still correct
SELECT 
    '=== BUCKET VERIFICATION ===' as section,
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- Step 5: Verify new policies are active
SELECT 
    '=== NEW STORAGE POLICIES ===' as section,
    policyname,
    cmd as operation
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE 'simple_%driver_documents'
ORDER BY policyname;

-- Step 6: Check for any remaining conflicting policies
SELECT 
    '=== REMAINING POLICIES CHECK ===' as section,
    COUNT(*) as policy_count,
    CASE 
        WHEN COUNT(*) = 4 THEN '✅ Exactly 4 policies (correct)'
        WHEN COUNT(*) > 4 THEN '⚠️ Too many policies (may cause conflicts)'
        ELSE '❌ Missing policies'
    END as status
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver_documents%';

-- Success message
SELECT '✅ SUCCESS: Storage policies simplified to fix Network request failed error' as result;
