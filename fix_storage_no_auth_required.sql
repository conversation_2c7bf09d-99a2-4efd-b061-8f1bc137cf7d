-- =====================================================
-- FIX STORAGE - REMOVE AUTHENTICATION REQUIREMENTS
-- For public buckets, we can allow uploads without strict auth checks
-- =====================================================

-- Step 1: Remove the current policies
DROP POLICY IF EXISTS "simple_upload_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "simple_read_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "simple_update_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "simple_delete_driver_documents" ON storage.objects;

-- Step 2: Create the most permissive policies possible for public bucket
-- Allow anyone to upload to driver-documents (no auth check)
CREATE POLICY "allow_all_upload_driver_documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents'
    );

-- Allow anyone to read from driver-documents
CREATE POLICY "allow_all_read_driver_documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'driver-documents'
    );

-- Allow anyone to update files in driver-documents
CREATE POLICY "allow_all_update_driver_documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents'
    );

-- Allow anyone to delete files in driver-documents
CREATE POLICY "allow_all_delete_driver_documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents'
    );

-- Step 3: Verify the policies are active
SELECT 
    '=== NEW PERMISSIVE POLICIES ===' as section,
    policyname,
    cmd as operation
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE 'allow_all_%driver_documents'
ORDER BY policyname;

-- Step 4: Double-check bucket is public
SELECT 
    '=== BUCKET STATUS ===' as section,
    id, 
    name, 
    public,
    CASE 
        WHEN public = true THEN '✅ Public bucket'
        ELSE '❌ Private bucket (this could cause issues)'
    END as status
FROM storage.buckets 
WHERE id = 'driver-documents';

-- Success message
SELECT '✅ SUCCESS: Storage policies now allow uploads without authentication requirements' as result;
