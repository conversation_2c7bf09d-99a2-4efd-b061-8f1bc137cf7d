-- =====================================================
-- FIX STORAGE POLICIES ONLY
-- Since your bucket is already correctly configured,
-- this script only fixes the storage policies
-- =====================================================

-- Step 1: Clean up ALL existing conflicting policies
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletes from driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_upload_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "public_read_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_update_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "authenticated_delete_driver_documents" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_upload" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_public_read" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_update" ON storage.objects;
DROP POLICY IF EXISTS "driver_docs_authenticated_delete" ON storage.objects;

-- Step 2: Create simple, working policies with unique names
CREATE POLICY "driver_documents_upload" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "driver_documents_read" ON storage.objects
    FOR SELECT USING (bucket_id = 'driver-documents');

CREATE POLICY "driver_documents_update" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "driver_documents_delete" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Step 3: Verify the bucket is still correctly configured
SELECT 
    '=== BUCKET CONFIGURATION ===' as section,
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- Step 4: Verify the new policies are in place
SELECT 
    '=== STORAGE POLICIES ===' as section,
    policyname,
    cmd as operation,
    CASE 
        WHEN qual IS NOT NULL THEN 'Has conditions'
        ELSE 'No conditions'
    END as conditions
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE 'driver_documents_%'
ORDER BY policyname;

-- Step 5: Check for any remaining conflicting policies
SELECT 
    '=== POTENTIAL CONFLICTS ===' as section,
    policyname,
    cmd as operation
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND (
    policyname LIKE '%driver%' OR 
    policyname LIKE '%document%'
)
AND policyname NOT LIKE 'driver_documents_%'
ORDER BY policyname;

-- Success message
SELECT '✅ SUCCESS: Storage policies fixed for driver-documents bucket' as result;
