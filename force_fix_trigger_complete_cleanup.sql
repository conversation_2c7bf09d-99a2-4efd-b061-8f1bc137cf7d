-- =====================================================
-- FORCE FIX: COMPLETE TRIGGER CLEANUP AND REBUILD
-- This will identify and fix ALL triggers causing the issue
-- =====================================================

-- Step 1: Show ALL current triggers on document_uploads (including hidden ones)
SELECT 
    '=== ALL CURRENT TRIGGERS ON DOCUMENT_UPLOADS ===' as section,
    t.tgname as trigger_name,
    p.proname as function_name,
    CASE t.tgtype::int & 66
        WHEN 2 THEN 'BEFORE'
        WHEN 64 THEN 'INSTEAD OF'
        ELSE 'AFTER'
    END as timing,
    CASE t.tgtype::int & 28
        WHEN 4 THEN 'INSERT'
        WHEN 8 THEN 'DELETE'
        WHEN 16 THEN 'UPDATE'
        WHEN 12 THEN 'INSERT OR DELETE'
        WHEN 20 THEN 'INSERT OR UPDATE'
        WHEN 24 THEN 'DELETE OR UPDATE'
        WHEN 28 THEN 'INSERT OR DELETE OR UPDATE'
    END as events
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'document_uploads'
AND NOT t.tgisinternal
ORDER BY t.tgname;

-- Step 2: Show the source code of ALL trigger functions
SELECT 
    '=== ALL TRIGGER FUNCTION SOURCE CODE ===' as section,
    p.proname as function_name,
    p.prosrc as source_code
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE c.relname = 'document_uploads'
AND NOT t.tgisinternal
ORDER BY p.proname;

-- Step 3: FORCE DROP ALL TRIGGERS (using system catalogs)
DO $$
DECLARE
    trigger_record RECORD;
BEGIN
    -- Get all triggers on document_uploads table
    FOR trigger_record IN 
        SELECT t.tgname as trigger_name
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        WHERE c.relname = 'document_uploads'
        AND NOT t.tgisinternal
    LOOP
        BEGIN
            EXECUTE format('DROP TRIGGER IF EXISTS %I ON document_uploads', trigger_record.trigger_name);
            RAISE NOTICE 'Dropped trigger: %', trigger_record.trigger_name;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not drop trigger %: %', trigger_record.trigger_name, SQLERRM;
        END;
    END LOOP;
END $$;

-- Step 4: Verify all triggers are gone
SELECT 
    '=== TRIGGERS AFTER CLEANUP ===' as section,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ All triggers successfully removed'
        ELSE '❌ Some triggers still exist'
    END as status,
    COUNT(*) as remaining_count
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname = 'document_uploads'
AND NOT t.tgisinternal;

-- Step 5: Create the most basic trigger function possible
CREATE OR REPLACE FUNCTION ultra_simple_document_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Ultra-simple trigger that just handles the basic cases
    
    IF TG_OP = 'UPDATE' THEN
        NEW.updated_at = NOW();
        IF NEW.status IN ('approved', 'rejected') THEN
            NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
        END IF;
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'INSERT' THEN
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    END IF;
    
    -- Should never reach here, but just in case
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Step 6: Create the trigger
CREATE TRIGGER ultra_simple_document_trigger
    BEFORE INSERT OR UPDATE OR DELETE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION ultra_simple_document_trigger();

-- Step 7: Test the trigger immediately with the exact admin dashboard operation
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    test_passed BOOLEAN := false;
BEGIN
    -- Find a document to test
    SELECT id, status INTO test_doc_id, original_status
    FROM document_uploads 
    WHERE status IN ('under_review', 'uploaded')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE '🧪 TESTING with document: %', test_doc_id;
        
        -- Test the EXACT operation that admin dashboard performs
        UPDATE document_uploads 
        SET 
            status = 'approved',
            reviewed_at = NOW()
        WHERE id = test_doc_id;
        
        -- If we get here, it worked
        test_passed := true;
        RAISE NOTICE '✅ SUCCESS: Admin dashboard operation test PASSED';
        
        -- Restore original state
        UPDATE document_uploads 
        SET 
            status = original_status::document_status,
            reviewed_at = NULL
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ Document restored to original state';
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents available for testing';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ TEST FAILED: %', SQLERRM;
        RAISE NOTICE '❌ Error code: %', SQLSTATE;
        
        IF SQLSTATE = '2F005' THEN
            RAISE NOTICE '🔥 STILL GETTING TRIGGER ERROR - There may be a hidden trigger';
        END IF;
END $$;

-- Step 8: Final verification
SELECT 
    '=== FINAL TRIGGER STATUS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 9: Check for any remaining issues
SELECT 
    '=== SYSTEM CHECK ===' as section,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE event_object_table = 'document_uploads'
            AND trigger_name = 'ultra_simple_document_trigger'
        ) THEN '✅ New trigger is active'
        ELSE '❌ New trigger not found'
    END as trigger_status,
    (
        SELECT COUNT(*) 
        FROM information_schema.triggers 
        WHERE event_object_table = 'document_uploads'
    ) as total_triggers;

SELECT '🚀 COMPLETE CLEANUP AND REBUILD FINISHED' as result;
SELECT 'Try the admin dashboard approval now' as next_step;
