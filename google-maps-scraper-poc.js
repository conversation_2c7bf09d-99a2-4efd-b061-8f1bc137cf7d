/**
 * Google Maps Scraper - Proof of Concept
 * 
 * ⚠️  WARNING: This is for educational/feasibility testing ONLY
 * ⚠️  Using this script may violate Google's Terms of Service
 * ⚠️  Do NOT use for production or commercial purposes
 * 
 * Purpose: Demonstrate technical feasibility of address extraction
 * Scope: Limited to 10 test addresses in Johannesburg for analysis
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs').promises;

// Use stealth plugin to avoid detection
puppeteer.use(StealthPlugin());

class GoogleMapsScraperPoC {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = [];
    this.detectionFlags = [];
    
    // Test addresses for Johannesburg (limited scope)
    this.testAddresses = [
      "1 Sandton Drive, Sandton, Johannesburg",
      "2 Sandton Drive, Sandton, Johannesburg", 
      "3 Sandton Drive, Sandton, Johannesburg",
      "1 Rivonia Road, Sandton, Johannesburg",
      "2 Rivonia Road, Sandton, Johannesburg",
      "1 West Street, Sandton, Johannesburg",
      "2 West Street, Sandton, Johannesburg",
      "1 Maude Street, Sandton, Johannesburg",
      "2 Maude Street, Sandton, Johannesburg",
      "3 Maude Street, Sandton, Johannesburg"
    ];
  }

  /**
   * Initialize browser with stealth configuration
   */
  async initialize() {
    console.log('🚀 Initializing stealth browser...');
    
    this.browser = await puppeteer.launch({
      headless: false, // Set to true for production
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--window-size=1366,768'
      ]
    });

    this.page = await this.browser.newPage();
    
    // Set realistic viewport and user agent
    await this.page.setViewport({ width: 1366, height: 768 });
    await this.page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    );

    // Block unnecessary resources to speed up loading
    await this.page.setRequestInterception(true);
    this.page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['image', 'stylesheet', 'font'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    console.log('✅ Browser initialized successfully');
  }

  /**
   * Navigate to Google Maps and perform initial setup
   */
  async navigateToGoogleMaps() {
    console.log('🗺️  Navigating to Google Maps...');
    
    try {
      await this.page.goto('https://maps.google.com', { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      });

      // Wait for search box to be available
      await this.page.waitForSelector('input[data-value="Search"]', { timeout: 10000 });
      console.log('✅ Google Maps loaded successfully');
      
      // Check for any CAPTCHA or unusual elements
      await this.checkForDetection();
      
    } catch (error) {
      console.error('❌ Failed to load Google Maps:', error.message);
      this.detectionFlags.push({
        type: 'navigation_error',
        message: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  /**
   * Check for detection mechanisms (CAPTCHA, unusual prompts, etc.)
   */
  async checkForDetection() {
    const detectionSelectors = [
      'div[data-value="captcha"]',
      '.g-recaptcha',
      'iframe[src*="recaptcha"]',
      'div:contains("unusual traffic")',
      'div:contains("automated queries")'
    ];

    for (const selector of detectionSelectors) {
      try {
        const element = await this.page.$(selector);
        if (element) {
          this.detectionFlags.push({
            type: 'detection_element',
            selector: selector,
            timestamp: new Date().toISOString()
          });
          console.log(`⚠️  Detection element found: ${selector}`);
        }
      } catch (error) {
        // Selector not found, which is good
      }
    }
  }

  /**
   * Search for a specific address and extract data
   */
  async searchAddress(address) {
    console.log(`🔍 Searching for: ${address}`);
    
    try {
      // Clear search box and enter address
      const searchBox = await this.page.$('input[data-value="Search"]');
      await searchBox.click({ clickCount: 3 }); // Select all text
      await searchBox.type(address);
      
      // Press Enter to search
      await this.page.keyboard.press('Enter');
      
      // Wait for results to load
      await this.page.waitForTimeout(3000);
      
      // Extract coordinates from URL
      const url = this.page.url();
      const coordinates = this.extractCoordinatesFromUrl(url);
      
      // Extract address details from the page
      const addressDetails = await this.extractAddressDetails();
      
      const result = {
        searchQuery: address,
        extractedAddress: addressDetails.formattedAddress,
        coordinates: coordinates,
        confidence: this.calculateConfidence(address, addressDetails),
        timestamp: new Date().toISOString(),
        url: url
      };
      
      this.results.push(result);
      console.log(`✅ Extracted: ${addressDetails.formattedAddress} (${coordinates.lat}, ${coordinates.lng})`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ Failed to search ${address}:`, error.message);
      this.detectionFlags.push({
        type: 'search_error',
        address: address,
        message: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }
  }

  /**
   * Extract coordinates from Google Maps URL
   */
  extractCoordinatesFromUrl(url) {
    // Google Maps URLs contain coordinates in various formats
    const patterns = [
      /@(-?\d+\.\d+),(-?\d+\.\d+)/,  // @lat,lng format
      /!3d(-?\d+\.\d+)!4d(-?\d+\.\d+)/, // !3dlat!4dlng format
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return {
          lat: parseFloat(match[1]),
          lng: parseFloat(match[2])
        };
      }
    }
    
    return { lat: null, lng: null };
  }

  /**
   * Extract address details from the current page
   */
  async extractAddressDetails() {
    try {
      // Wait for address elements to load
      await this.page.waitForTimeout(2000);
      
      // Try to find the formatted address
      const addressSelectors = [
        '[data-value="Directions"]',
        '.DUwDvf',
        '.rogA2c',
        'h1[data-attrid="title"]'
      ];
      
      let formattedAddress = '';
      
      for (const selector of addressSelectors) {
        try {
          const element = await this.page.$(selector);
          if (element) {
            const text = await this.page.evaluate(el => el.textContent, element);
            if (text && text.length > formattedAddress.length) {
              formattedAddress = text.trim();
            }
          }
        } catch (error) {
          // Continue to next selector
        }
      }
      
      return {
        formattedAddress: formattedAddress || 'Address not found'
      };
      
    } catch (error) {
      console.error('Error extracting address details:', error);
      return {
        formattedAddress: 'Extraction failed'
      };
    }
  }

  /**
   * Calculate confidence score based on search vs extracted address
   */
  calculateConfidence(searchQuery, extractedDetails) {
    const search = searchQuery.toLowerCase();
    const extracted = extractedDetails.formattedAddress.toLowerCase();
    
    // Simple similarity check
    const commonWords = search.split(' ').filter(word => 
      extracted.includes(word) && word.length > 2
    );
    
    return Math.min(commonWords.length / search.split(' ').length, 1.0);
  }

  /**
   * Add random delay to mimic human behavior
   */
  async humanDelay() {
    const delay = Math.random() * 3000 + 2000; // 2-5 seconds
    console.log(`⏳ Waiting ${Math.round(delay/1000)}s (human simulation)...`);
    await this.page.waitForTimeout(delay);
  }

  /**
   * Run the complete proof of concept test
   */
  async runPoC() {
    console.log('🧪 Starting Google Maps Scraping Proof of Concept');
    console.log('⚠️  WARNING: This is for educational testing only!');
    console.log(`📍 Testing ${this.testAddresses.length} addresses in Johannesburg\n`);
    
    try {
      await this.initialize();
      await this.navigateToGoogleMaps();
      
      // Process each test address
      for (let i = 0; i < this.testAddresses.length; i++) {
        const address = this.testAddresses[i];
        
        console.log(`\n--- Processing ${i + 1}/${this.testAddresses.length} ---`);
        await this.searchAddress(address);
        
        // Check for detection after each search
        await this.checkForDetection();
        
        // Human-like delay between searches
        if (i < this.testAddresses.length - 1) {
          await this.humanDelay();
        }
        
        // Stop if detection is suspected
        if (this.detectionFlags.length > 0) {
          console.log('⚠️  Detection flags raised, stopping test');
          break;
        }
      }
      
      // Generate report
      await this.generateReport();
      
    } catch (error) {
      console.error('💥 PoC failed:', error);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Generate comprehensive test report
   */
  async generateReport() {
    const report = {
      testInfo: {
        timestamp: new Date().toISOString(),
        totalAddresses: this.testAddresses.length,
        successfulExtractions: this.results.length,
        detectionFlags: this.detectionFlags.length,
        successRate: (this.results.length / this.testAddresses.length * 100).toFixed(2) + '%'
      },
      results: this.results,
      detectionFlags: this.detectionFlags,
      analysis: {
        averageConfidence: this.results.length > 0 ? 
          (this.results.reduce((sum, r) => sum + r.confidence, 0) / this.results.length).toFixed(3) : 0,
        coordinateAccuracy: this.results.filter(r => r.coordinates.lat && r.coordinates.lng).length,
        recommendations: this.generateRecommendations()
      }
    };
    
    // Save report to file
    await fs.writeFile('google-maps-poc-report.json', JSON.stringify(report, null, 2));
    
    console.log('\n📊 PROOF OF CONCEPT RESULTS:');
    console.log(`✅ Successful extractions: ${report.testInfo.successfulExtractions}/${report.testInfo.totalAddresses}`);
    console.log(`📈 Success rate: ${report.testInfo.successRate}`);
    console.log(`⚠️  Detection flags: ${report.testInfo.detectionFlags}`);
    console.log(`🎯 Average confidence: ${report.analysis.averageConfidence}`);
    console.log(`📍 Coordinates extracted: ${report.analysis.coordinateAccuracy}/${report.testInfo.totalAddresses}`);
    console.log('\n📄 Full report saved to: google-maps-poc-report.json');
  }

  /**
   * Generate recommendations based on test results
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.detectionFlags.length > 0) {
      recommendations.push('HIGH RISK: Detection mechanisms triggered - not suitable for production');
    }
    
    if (this.results.length < this.testAddresses.length * 0.8) {
      recommendations.push('LOW SUCCESS RATE: Consider alternative data sources');
    }
    
    const avgConfidence = this.results.reduce((sum, r) => sum + r.confidence, 0) / this.results.length;
    if (avgConfidence < 0.7) {
      recommendations.push('LOW CONFIDENCE: Address matching quality is poor');
    }
    
    recommendations.push('LEGAL RISK: This approach likely violates Google ToS');
    recommendations.push('RECOMMENDATION: Use legitimate APIs or open data sources instead');
    
    return recommendations;
  }

  /**
   * Clean up resources and remove test data
   */
  async cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (this.browser) {
      await this.browser.close();
    }
    
    // Clear results from memory (security measure)
    this.results = [];
    this.testAddresses = [];
    
    console.log('✅ Cleanup completed');
    console.log('\n⚠️  REMINDER: This was a proof of concept test only!');
    console.log('⚠️  Do not use this approach for production systems!');
  }
}

// Export for testing purposes
module.exports = GoogleMapsScraperPoC;

// Run PoC if called directly
if (require.main === module) {
  console.log('⚠️  LEGAL DISCLAIMER:');
  console.log('⚠️  This script is for educational/feasibility testing ONLY');
  console.log('⚠️  Using this may violate Google\'s Terms of Service');
  console.log('⚠️  Do NOT use for commercial purposes');
  console.log('⚠️  Proceed at your own risk\n');

  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Do you understand the risks and want to proceed? (yes/no): ', (answer) => {
    if (answer.toLowerCase() === 'yes') {
      const scraper = new GoogleMapsScraperPoC();
      scraper.runPoC().catch(console.error);
    } else {
      console.log('Test cancelled. Consider using legitimate APIs instead.');
    }
    rl.close();
  });
}
