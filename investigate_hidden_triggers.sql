-- =====================================================
-- INVESTIGATE HIDDEN TRIGGERS
-- Find any triggers that might be causing the issue
-- =====================================================

-- Step 1: Check ALL triggers in the entire database (not just document_uploads)
SELECT 
    '=== ALL TRIGGERS IN DATABASE ===' as section,
    n.nspname as schema_name,
    c.relname as table_name,
    t.tgname as trigger_name,
    p.proname as function_name,
    CASE 
        WHEN c.relname = 'document_uploads' THEN '🎯 TARGET TABLE'
        WHEN p.proname LIKE '%document%' THEN '📄 Document related'
        WHEN p.proname LIKE '%update%' THEN '🔄 Update related'
        ELSE '📋 Other'
    END as relevance
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE NOT t.tgisinternal
AND (
    c.relname = 'document_uploads' OR
    p.proname LIKE '%document%' OR
    p.proname LIKE '%update%' OR
    t.tgname LIKE '%document%' OR
    t.tgname LIKE '%update%'
)
ORDER BY 
    CASE WHEN c.relname = 'document_uploads' THEN 1 ELSE 2 END,
    n.nspname, c.relname, t.tgname;

-- Step 2: Check for any functions that might be called by triggers
SELECT 
    '=== FUNCTIONS THAT MIGHT BE TRIGGERS ===' as section,
    proname as function_name,
    prorettype::regtype as return_type,
    CASE 
        WHEN prorettype = 'trigger'::regtype THEN '🎯 TRIGGER FUNCTION'
        WHEN prosrc LIKE '%RETURN NEW%' OR prosrc LIKE '%RETURN OLD%' THEN '⚠️ Looks like trigger'
        ELSE '📋 Other function'
    END as function_type,
    CASE 
        WHEN prosrc LIKE '%document_uploads%' THEN '📄 References document_uploads'
        WHEN prosrc LIKE '%updated_at%' THEN '🔄 Updates timestamps'
        ELSE '📋 Other'
    END as relevance
FROM pg_proc 
WHERE (
    prorettype = 'trigger'::regtype OR
    prosrc LIKE '%document_uploads%' OR
    prosrc LIKE '%RETURN NEW%' OR
    prosrc LIKE '%RETURN OLD%' OR
    proname LIKE '%document%' OR
    proname LIKE '%update%'
)
ORDER BY 
    CASE WHEN prorettype = 'trigger'::regtype THEN 1 ELSE 2 END,
    proname;

-- Step 3: Check for any RLS policies that might interfere
SELECT 
    '=== ROW LEVEL SECURITY POLICIES ===' as section,
    schemaname,
    tablename,
    policyname,
    cmd as operation,
    CASE 
        WHEN cmd = 'UPDATE' THEN '🎯 Affects updates'
        WHEN cmd = 'ALL' THEN '⚠️ Affects all operations'
        ELSE '📋 Other'
    END as relevance
FROM pg_policies 
WHERE tablename = 'document_uploads'
ORDER BY policyname;

-- Step 4: Check table structure for any constraints that might cause issues
SELECT 
    '=== DOCUMENT_UPLOADS TABLE CONSTRAINTS ===' as section,
    conname as constraint_name,
    contype as constraint_type,
    CASE contype
        WHEN 'c' THEN 'CHECK'
        WHEN 'f' THEN 'FOREIGN KEY'
        WHEN 'p' THEN 'PRIMARY KEY'
        WHEN 'u' THEN 'UNIQUE'
        WHEN 't' THEN 'TRIGGER'
        ELSE contype::text
    END as constraint_description
FROM pg_constraint 
WHERE conrelid = 'document_uploads'::regclass
ORDER BY contype, conname;

-- Step 5: Check for any inherited triggers (from parent tables)
SELECT 
    '=== TABLE INHERITANCE CHECK ===' as section,
    c.relname as table_name,
    p.relname as parent_table,
    CASE 
        WHEN p.relname IS NOT NULL THEN '⚠️ Has parent table (may inherit triggers)'
        ELSE '✅ No inheritance'
    END as inheritance_status
FROM pg_class c
LEFT JOIN pg_inherits i ON c.oid = i.inhrelid
LEFT JOIN pg_class p ON i.inhparent = p.oid
WHERE c.relname = 'document_uploads';

-- Step 6: Check for any event triggers that might affect document_uploads
SELECT 
    '=== EVENT TRIGGERS ===' as section,
    evtname as event_trigger_name,
    evtevent as event_type,
    evtfoid::regproc as function_name,
    CASE 
        WHEN evtevent = 'ddl_command_start' THEN '⚠️ DDL start'
        WHEN evtevent = 'ddl_command_end' THEN '⚠️ DDL end'
        WHEN evtevent = 'table_rewrite' THEN '⚠️ Table rewrite'
        ELSE evtevent
    END as event_description
FROM pg_event_trigger
ORDER BY evtname;

-- Step 7: Look for any custom types or domains that might have constraints
SELECT 
    '=== CUSTOM TYPES AND DOMAINS ===' as section,
    typname as type_name,
    typtype as type_category,
    CASE typtype
        WHEN 'd' THEN 'DOMAIN'
        WHEN 'e' THEN 'ENUM'
        WHEN 'c' THEN 'COMPOSITE'
        ELSE typtype::text
    END as type_description
FROM pg_type 
WHERE typname LIKE '%document%' OR typname LIKE '%status%'
ORDER BY typname;

-- Step 8: Final diagnostic query - check what happens during an update
SELECT 
    '=== DIAGNOSTIC COMPLETE ===' as section,
    'Review all sections above for potential issues' as message;

SELECT 
    '=== RECOMMENDATIONS ===' as section,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_trigger t
            JOIN pg_class c ON t.tgrelid = c.oid
            WHERE c.relname = 'document_uploads'
            AND NOT t.tgisinternal
        ) THEN 'Found triggers on document_uploads - check section 1'
        ELSE 'No triggers found - the issue may be elsewhere'
    END as trigger_analysis,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_policies 
            WHERE tablename = 'document_uploads'
            AND cmd IN ('UPDATE', 'ALL')
        ) THEN 'Found RLS policies that might interfere'
        ELSE 'No problematic RLS policies found'
    END as rls_analysis;
