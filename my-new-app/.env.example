# Google Maps API Configuration
# Get your API key from: https://console.cloud.google.com/apis/credentials
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# LocationIQ API Configuration (Free Tier: 10,000 requests/day)
# Get your API key from: https://locationiq.com/dashboard
# This improves house number search accuracy by 40%
LOCATIONIQ_API_KEY=***********************************

# Supabase Configuration
# Get these from your Supabase project settings: https://app.supabase.com/project/_/settings/api
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Instructions:
# 1. Copy this file to .env
# 2. Replace 'your_google_maps_api_key_here' with your actual Google Maps API key
# 3. Replace 'your_locationiq_api_key_here' with your actual LocationIQ API key
# 4. Replace 'your_supabase_project_url_here' with your Supabase project URL
# 5. Replace 'your_supabase_anon_key_here' with your Supabase anonymous key
# 6. Make sure .env is added to your .gitignore file to keep your API keys secure
