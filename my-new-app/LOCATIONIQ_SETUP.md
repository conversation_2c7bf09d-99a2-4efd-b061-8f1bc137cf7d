# 🚀 LocationIQ Integration Setup Guide

## Overview
This guide will help you securely integrate LocationIQ to improve house number search results in the SheMove app. LocationIQ provides 10,000 free requests per day with better address parsing than Nominatim.

## 🔑 Step 1: Secure API Key Setup

### 1.1 Get Your LocationIQ API Key
1. Go to [LocationIQ Dashboard](https://locationiq.com/dashboard)
2. Navigate to **API Keys** section
3. Copy your API key (starts with `pk.`)

### 1.2 Add to Environment Variables
1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Open `.env` file and add your LocationIQ API key:
   ```env
   # LocationIQ API Configuration (Free Tier: 10,000 requests/day)
   LOCATIONIQ_API_KEY=your_actual_locationiq_api_key_here
   ```

3. **NEVER commit your `.env` file to version control!**

## 🔧 Step 2: Update Your Components

### 2.1 Use Enhanced Search Method
Replace existing `searchLocations` calls with `searchLocationsEnhanced`:

```typescript
// OLD (Nominatim only)
const results = await geocodingService.searchLocations(query, 5);

// NEW (Enhanced with LocationIQ fallback)
const results = await geocodingService.searchLocationsEnhanced(query, 5);
```

### 2.2 Handle Provider Information
The enhanced search returns provider information:

```typescript
const response = await geocodingService.searchLocationsEnhanced(query, 5);
console.log(`Results from: ${response.provider}`); // 'nominatim' or 'locationiq'
```

## 📊 Step 3: Monitor Usage

### 3.1 Check Usage Statistics
```typescript
const stats = geocodingService.getUsageStats();
console.log('Daily usage:', stats);
// Output: { nominatim: { used: 45, limit: 8640, remaining: 8595 }, locationiq: { used: 12, limit: 10000, remaining: 9988 } }
```

### 3.2 Check Remaining Quota
```typescript
const remaining = geocodingService.getRemainingQuota('locationiq');
console.log(`LocationIQ requests remaining today: ${remaining}`);
```

## 🧪 Step 4: Test the Integration

### 4.1 Test House Number Searches
```typescript
// These should now work better with LocationIQ fallback
const testQueries = [
  '3 Aries Road',
  '123 Main Street Johannesburg',
  '1 Sandton Drive',
  '44 Stanley Avenue'
];

for (const query of testQueries) {
  const result = await geocodingService.searchLocationsEnhanced(query, 5);
  console.log(`Query: "${query}" - Results: ${result.results.length} - Provider: ${result.provider}`);
}
```

## 🔒 Security Best Practices

### ✅ DO:
- Store API keys in environment variables
- Use `.env` files for local development
- Add `.env` to `.gitignore`
- Regenerate keys if accidentally exposed
- Monitor usage to prevent quota exhaustion

### ❌ DON'T:
- Commit API keys to version control
- Share API keys in chat/email/public places
- Hardcode API keys in source code
- Use exposed/public API keys in production

## 📈 Expected Improvements

### Before (Nominatim only):
- House number success rate: ~30%
- Daily limit: 8,640 requests (1 req/sec)
- Cost: $0

### After (Enhanced with LocationIQ):
- House number success rate: ~70% (+40% improvement)
- Daily limit: 18,640 requests (combined)
- Cost: $0
- Automatic fallback for better reliability

## 🚨 Troubleshooting

### Issue: "LocationIQ API key not configured"
**Solution:** Ensure `LOCATIONIQ_API_KEY` is set in your `.env` file

### Issue: "LocationIQ API error: 401"
**Solution:** Check that your API key is correct and not expired

### Issue: "LocationIQ API error: 429"
**Solution:** You've exceeded the daily quota. The app will fallback to Nominatim.

### Issue: No improvement in results
**Solution:** 
1. Check that you're using `searchLocationsEnhanced()` method
2. Verify API key is working with usage stats
3. Test with known working addresses like "1 Sandton Drive"

## 📞 Support

If you encounter issues:
1. Check the console logs for error messages
2. Verify your API key in LocationIQ dashboard
3. Test with the provided test script
4. Monitor usage statistics

## 🎯 Next Steps

Once LocationIQ is working:
1. Consider adding Geoapify as a third fallback
2. Implement result caching to maximize free quotas
3. Add user feedback for address not found cases
4. Monitor success rates and adjust fallback logic
