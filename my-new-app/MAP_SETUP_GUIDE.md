# 🗺️ SheMove Map Integration Setup Guide

## 📋 Overview

This guide will help you set up production-ready maps for the SheMove ride-sharing app using Google Maps with react-native-maps.

## ✅ What's Already Implemented

### 🔧 Fixed Issues
- ✅ **MaterialIcons Fixed**: Replaced invalid icon names with valid alternatives:
  - `car` → `directions-car`
  - `car-sport` → `airport-shuttle`
  - `car-convertible` → `local-taxi`
- ✅ **Fallback Icons**: Added tesla.png as backup for any icon failures
- ✅ **Interactive Map**: Replaced static background with react-native-maps
- ✅ **Error Handling**: Graceful fallback to static image if map fails

### 🎨 Features Implemented
- Interactive map with zoom, pan, and markers
- Loading states and error handling
- Sample pickup/destination markers
- Maintains SheMove pink branding
- Compatible with enhanced bottom sheet

## 🚀 Production Setup Steps

### Step 1: Get Google Maps API Key

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Required APIs**
   - Enable "Maps SDK for Android"
   - Enable "Maps SDK for iOS"

3. **Create API Key**
   - Go to Credentials → Create Credentials → API Key
   - Copy the generated API key

### Step 2: Configure API Key Restrictions

#### For Android:
1. Edit your API key
2. Under "Application restrictions" → Select "Android apps"
3. Add package name: `com.jaimon10.mynewapp`
4. Add SHA-1 certificate fingerprint (see below)

#### For iOS:
1. Edit your API key  
2. Under "Application restrictions" → Select "iOS apps"
3. Add bundle identifier: `com.jaimon10.mynewapp`

### Step 3: Get SHA-1 Certificate

#### For Development:
```bash
# Run this command in your project directory
npx expo credentials:manager
# Select your project → Android → Keystore → View SHA-1
```

#### For Production:
- Upload your app to Google Play Console
- Go to Release → Setup → App integrity → App Signing
- Copy SHA-1 certificate fingerprint

### Step 4: Add API Key to Project

1. **Create Environment File**
   ```bash
   cp .env.example .env
   ```

2. **Add Your API Key**
   ```env
   GOOGLE_MAPS_API_KEY=your_actual_api_key_here
   ```

3. **Update app.json** (Already configured)
   - Android: `android.config.googleMaps.apiKey`
   - iOS: `ios.config.googleMapsApiKey`

### Step 5: Build and Test

```bash
# For development build
npx expo run:android
npx expo run:ios

# For production build
eas build --platform android
eas build --platform ios
```

## 🆓 Free Tier Limits

### Google Maps Free Tier:
- **$200 monthly credit** (covers ~28,000 map loads)
- **Dynamic Maps**: $7 per 1,000 loads
- **Static Maps**: $2 per 1,000 loads
- **Geocoding**: $5 per 1,000 requests

### Alternative Free Options:
1. **OpenStreetMap** with react-native-maps
2. **Mapbox** (50,000 free map views/month)

## 🔧 Technical Implementation

### Map Component Features:
- Interactive zoom, pan, rotate
- Custom markers for pickup/destination
- Loading states with SheMove branding
- Error handling with static image fallback
- Optimized for performance

### Performance Optimizations:
- Lazy loading of map component
- Efficient marker rendering
- Proper memory management
- Smooth animations with bottom sheet

## 🎨 Customization Options

### Map Styling:
```javascript
// Add to MapView component
customMapStyle={[
  // Custom map styling JSON
]}
```

### Marker Customization:
```javascript
// Custom marker icons
<Marker
  coordinate={coordinate}
  image={require('./custom-marker.png')}
/>
```

## 🐛 Troubleshooting

### Common Issues:

1. **Map not loading**
   - Check API key configuration
   - Verify bundle ID/package name
   - Check SHA-1 certificate

2. **"Authorization failure" error**
   - API key restrictions too strict
   - Wrong bundle identifier
   - Missing SHA-1 fingerprint

3. **Icons not showing**
   - Falls back to tesla.png automatically
   - Check MaterialIcons import

### Debug Commands:
```bash
# Check current credentials
npx expo credentials:manager

# Clear cache and restart
npx expo start --clear

# Check bundle identifier
grep -r "bundleIdentifier" app.json
```

## 📱 Testing Checklist

- [ ] Map loads correctly in Expo Go
- [ ] Map loads in development build
- [ ] Markers appear correctly
- [ ] Zoom/pan gestures work
- [ ] Bottom sheet doesn't interfere
- [ ] Loading states work
- [ ] Error fallback works
- [ ] Icons display properly
- [ ] Performance is smooth

## 🔒 Security Notes

- Never commit API keys to version control
- Use environment variables for API keys
- Restrict API keys by platform
- Monitor usage in Google Cloud Console
- Set up billing alerts

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all setup steps
3. Test with a fresh API key
4. Check Google Cloud Console for errors
