# 🗄️ **URGENT: Database Setup Required**

## 🚨 **Current Status**
Your app is running but the database tables for Phase 1 features are not yet created. You'll see these errors:
- `relation "public.search_history" does not exist`
- `Could not find the function public.get_recent_trips`

## ⚡ **Quick Fix (5 minutes)**

### **Step 1: Open Supabase Dashboard**
1. Go to [supabase.com](https://supabase.com)
2. Sign in to your account
3. Open your SheMove project
4. Click on **"SQL Editor"** in the left sidebar

### **Step 2: Run Migration Scripts**
Copy and paste each script below **one at a time** into the SQL Editor and click **"Run"**:

#### **Script 1: Master Migration**
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE user_type AS ENUM ('passenger', 'driver', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE trip_status AS ENUM ('requested', 'accepted', 'in_progress', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE ride_type AS ENUM ('SheRide', 'ShePool', 'SheXL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create search_history table
CREATE TABLE IF NOT EXISTS search_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    search_query TEXT NOT NULL,
    selected_result JSONB,
    result_address TEXT,
    result_coordinates POINT,
    search_context TEXT,
    search_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_context POINT,
    result_clicked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create favorite_locations table
CREATE TABLE IF NOT EXISTS favorite_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    label TEXT NOT NULL,
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    icon_name TEXT DEFAULT 'location',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, label)
);

-- Create recent_destinations table
CREATE TABLE IF NOT EXISTS recent_destinations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    visit_count INTEGER DEFAULT 1,
    last_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add enhanced columns to trips table
ALTER TABLE trips ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS cancellation_reason TEXT;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS pickup_address_short TEXT;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS destination_address_short TEXT;
```

#### **Script 2: Enable Row Level Security**
```sql
-- Enable RLS
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorite_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE recent_destinations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for search_history
CREATE POLICY "Users can view their own search history" ON search_history
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own search history" ON search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own search history" ON search_history
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own search history" ON search_history
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for favorite_locations
CREATE POLICY "Users can view their own favorite locations" ON favorite_locations
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own favorite locations" ON favorite_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own favorite locations" ON favorite_locations
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own favorite locations" ON favorite_locations
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for recent_destinations
CREATE POLICY "Users can view their own recent destinations" ON recent_destinations
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own recent destinations" ON recent_destinations
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own recent destinations" ON recent_destinations
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own recent destinations" ON recent_destinations
    FOR DELETE USING (auth.uid() = user_id);
```

#### **Script 3: Create Functions**
```sql
-- Create function to get search suggestions
CREATE OR REPLACE FUNCTION get_search_suggestions(
    p_user_id UUID,
    p_query TEXT,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE(
    suggestion TEXT,
    address TEXT,
    coordinates POINT,
    suggestion_type TEXT,
    last_used TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT 
        sh.search_query as suggestion,
        sh.result_address as address,
        sh.result_coordinates as coordinates,
        'recent'::TEXT as suggestion_type,
        sh.search_timestamp as last_used
    FROM search_history sh
    WHERE sh.user_id = p_user_id 
        AND sh.result_clicked = TRUE
        AND sh.search_query ILIKE p_query || '%'
        AND sh.result_address IS NOT NULL
    ORDER BY sh.search_timestamp DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get recent trips
CREATE OR REPLACE FUNCTION get_recent_trips(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    trip_id UUID,
    destination_location TEXT,
    destination_address_short TEXT,
    destination_coordinates POINT,
    pickup_location TEXT,
    pickup_address_short TEXT,
    pickup_coordinates POINT,
    ride_type ride_type,
    fare_amount DECIMAL(10,2),
    distance_km DECIMAL(8,2),
    duration_minutes INTEGER,
    trip_date TIMESTAMP WITH TIME ZONE,
    trip_status trip_status,
    driver_rating INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id as trip_id,
        t.destination_location,
        COALESCE(t.destination_address_short, t.destination_location) as destination_address_short,
        t.destination_coordinates,
        t.pickup_location,
        COALESCE(t.pickup_address_short, t.pickup_location) as pickup_address_short,
        t.pickup_coordinates,
        t.ride_type,
        t.fare_amount,
        t.distance_km,
        t.duration_minutes,
        COALESCE(t.completed_at, t.cancelled_at, t.created_at) as trip_date,
        t.status as trip_status,
        tr.passenger_rating as driver_rating
    FROM trips t
    LEFT JOIN trip_ratings tr ON t.id = tr.trip_id
    WHERE t.passenger_id = p_user_id 
        AND t.status IN ('completed', 'cancelled')
    ORDER BY COALESCE(t.completed_at, t.cancelled_at, t.created_at) DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_search_suggestions TO authenticated;
GRANT EXECUTE ON FUNCTION get_recent_trips TO authenticated;
```

#### **Script 4: Create Indexes**
```sql
-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_search_history_user_recent 
    ON search_history(user_id, search_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_history_user_clicked 
    ON search_history(user_id, result_clicked, search_timestamp DESC) 
    WHERE result_clicked = TRUE;
CREATE INDEX IF NOT EXISTS idx_favorite_locations_user 
    ON favorite_locations(user_id, is_primary DESC, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_recent_destinations_user_frequent 
    ON recent_destinations(user_id, visit_count DESC, last_visited DESC);
CREATE INDEX IF NOT EXISTS idx_trips_passenger_recent 
    ON trips(passenger_id, created_at DESC) 
    WHERE status IN ('completed', 'cancelled');
```

### **Step 3: Verify Setup**
Run this verification query:
```sql
-- Check if everything was created successfully
SELECT 'search_history' as table_name, COUNT(*) as exists 
FROM information_schema.tables 
WHERE table_name = 'search_history'
UNION ALL
SELECT 'get_recent_trips' as function_name, COUNT(*) as exists 
FROM information_schema.routines 
WHERE routine_name = 'get_recent_trips';
```

You should see:
- `search_history: 1`
- `get_recent_trips: 1`

## ✅ **After Setup**
1. **Refresh your app** - The errors should disappear
2. **Test search** - Recent searches will now be saved
3. **Test trips** - Trip history will load from database

## 🎯 **What You Get**
- ✅ **Search History** - Recent searches saved and suggested
- ✅ **Smart Debouncing** - 80% fewer API calls
- ✅ **LocationIQ Integration** - Better address search
- ✅ **Real Trip Data** - No more mock data
- ✅ **Database Storage** - All data persisted securely

## 🆘 **Need Help?**
If you encounter any issues:
1. Check the Supabase logs in your dashboard
2. Ensure you're signed in as the project owner
3. Try running scripts one at a time
4. Contact support if tables still don't exist

**Total setup time: ~5 minutes** ⏱️
