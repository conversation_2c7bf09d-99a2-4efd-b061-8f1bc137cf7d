# 🚀 Supabase Setup Guide for SheMove

## Step 1: Create Supabase Project

1. **Go to Supabase Dashboard**
   - Visit [https://app.supabase.com](https://app.supabase.com)
   - Sign in or create an account

2. **Create New Project**
   - Click "New Project"
   - Choose your organization
   - Enter project details:
     - **Name**: `shemove-app`
     - **Database Password**: Create a strong password (save it!)
     - **Region**: Choose closest to your users
   - Click "Create new project"

3. **Wait for Setup**
   - Project creation takes 2-3 minutes
   - You'll see a progress indicator

## Step 2: Get API Keys

1. **Navigate to Settings**
   - Go to Settings → API in your project dashboard

2. **Copy Required Keys**
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (long string)

3. **Update Environment Variables**
   ```bash
   # Copy .env.example to .env if you haven't already
   cp .env.example .env
   ```
   
   Then update your `.env` file:
   ```env
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_ANON_KEY=your_actual_anon_key_here
   ```

## Step 3: Create Database Schema

1. **Go to SQL Editor**
   - In your Supabase dashboard, go to SQL Editor

2. **Run the Schema Creation Script**
   - Copy and paste the following SQL script:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_type AS ENUM ('passenger', 'driver', 'admin');
CREATE TYPE trip_status AS ENUM ('requested', 'accepted', 'in_progress', 'completed', 'cancelled');
CREATE TYPE ride_type AS ENUM ('SheRide', 'ShePool', 'SheXL');
CREATE TYPE verification_status AS ENUM ('pending', 'approved', 'rejected');

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone_number TEXT,
    avatar_url TEXT,
    user_type user_type DEFAULT 'passenger',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create drivers table
CREATE TABLE drivers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE UNIQUE NOT NULL,
    license_number TEXT UNIQUE NOT NULL,
    vehicle_make TEXT NOT NULL,
    vehicle_model TEXT NOT NULL,
    vehicle_year INTEGER NOT NULL,
    vehicle_color TEXT NOT NULL,
    vehicle_plate TEXT UNIQUE NOT NULL,
    verification_status verification_status DEFAULT 'pending',
    is_online BOOLEAN DEFAULT FALSE,
    current_location POINT,
    rating DECIMAL(3,2) DEFAULT 5.0,
    total_trips INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trips table
CREATE TABLE trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    passenger_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    pickup_location TEXT NOT NULL,
    pickup_coordinates POINT NOT NULL,
    destination_location TEXT NOT NULL,
    destination_coordinates POINT NOT NULL,
    ride_type ride_type NOT NULL,
    status trip_status DEFAULT 'requested',
    fare_amount DECIMAL(10,2) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_ratings table
CREATE TABLE trip_ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
    passenger_rating INTEGER CHECK (passenger_rating >= 1 AND passenger_rating <= 5),
    driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
    passenger_comment TEXT,
    driver_comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function on user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at
    BEFORE UPDATE ON drivers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_trips_updated_at
    BEFORE UPDATE ON trips
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

3. **Click "Run" to execute the script**

## Step 4: Set Up Row Level Security (RLS)

Run this additional SQL script to enable security:

```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_ratings ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- Drivers policies
CREATE POLICY "Drivers can view their own data" ON drivers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own data" ON drivers
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view approved drivers" ON drivers
    FOR SELECT USING (verification_status = 'approved');

-- Trips policies
CREATE POLICY "Passengers can view their own trips" ON trips
    FOR SELECT USING (auth.uid() = passenger_id);

CREATE POLICY "Drivers can view their assigned trips" ON trips
    FOR SELECT USING (auth.uid() = (SELECT user_id FROM drivers WHERE id = driver_id));

CREATE POLICY "Passengers can create trips" ON trips
    FOR INSERT WITH CHECK (auth.uid() = passenger_id);

CREATE POLICY "Passengers can update their own trips" ON trips
    FOR UPDATE USING (auth.uid() = passenger_id);

CREATE POLICY "Drivers can update assigned trips" ON trips
    FOR UPDATE USING (auth.uid() = (SELECT user_id FROM drivers WHERE id = driver_id));

-- Trip ratings policies
CREATE POLICY "Users can view ratings for their trips" ON trip_ratings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_ratings.trip_id 
            AND (trips.passenger_id = auth.uid() OR trips.driver_id = (SELECT id FROM drivers WHERE user_id = auth.uid()))
        )
    );

CREATE POLICY "Users can create ratings for their trips" ON trip_ratings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_ratings.trip_id 
            AND (trips.passenger_id = auth.uid() OR trips.driver_id = (SELECT id FROM drivers WHERE user_id = auth.uid()))
        )
    );
```

## Step 5: Configure Authentication

1. **Go to Authentication → Settings**
2. **Enable Email Confirmations** (optional for development)
3. **Configure OAuth Providers** (optional):
   - Google
   - Apple
   - Facebook

## Step 6: Test Your Setup

1. **Restart your Expo app**
   ```bash
   # Stop the current process (Ctrl+C) and restart
   npm start
   ```

2. **Test Signup Flow**
   - Go through the onboarding
   - Try signing up with a test email
   - Check if user appears in Authentication → Users

## Troubleshooting

### Common Issues:

1. **"Missing SUPABASE_URL" Error**
   - Make sure you copied `.env.example` to `.env`
   - Verify the URL format: `https://your-project-id.supabase.co`

2. **"Missing SUPABASE_ANON_KEY" Error**
   - Double-check you copied the full anon key (it's very long)
   - Make sure there are no extra spaces

3. **Database Connection Issues**
   - Verify your project is fully set up (green status in dashboard)
   - Check if SQL scripts ran without errors

4. **RLS Policy Issues**
   - Make sure all RLS policies were created successfully
   - Check the Supabase logs for policy violations

## Next Steps

Once setup is complete, the app will:
- ✅ Create real user accounts in Supabase
- ✅ Store user profiles in the database
- ✅ Maintain secure authentication sessions
- ✅ Enable real-time features (coming soon)

Your SheMove app is now connected to a production-ready backend! 🎉
