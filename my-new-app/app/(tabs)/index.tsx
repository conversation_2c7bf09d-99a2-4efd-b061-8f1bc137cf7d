import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';

export default function HomeRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Add a small delay to ensure the router is ready
    const timer = setTimeout(() => {
      router.replace('/SplashPage');
    }, 100);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#1E1E1E" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF0FF',
  },
});
