/**
 * Active Trip Screen for SheMove Passenger App
 * Displays real-time trip progress, driver tracking, and trip management
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  SafeAreaView,
  ScrollView,
  Linking,
  ActivityIndicator
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../contexts/AuthContext';
import { TripManagementService, TripStatusUpdate, DriverResponse } from '../services/TripManagementService';
import { Trip, TripStatus } from '../shared/types';

// Color constants matching SheMove branding
const COLORS = {
  PRIMARY_PINK: '#FFF0FF',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#F9E6F7',
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY: '#666666',
  LIGHT_GRAY: '#F5F5F5',
  BORDER: '#E0E0E0',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  DANGER: '#F44336',
};

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

const ActiveTripScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const { tripId } = useLocalSearchParams<{ tripId: string }>();
  
  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [driverLocation, setDriverLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [estimatedArrival, setEstimatedArrival] = useState<number | null>(null);
  const [tripStatus, setTripStatus] = useState<TripStatus>('requested');
  
  const tripServiceRef = useRef<TripManagementService | null>(null);
  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    if (tripId && user) {
      initializeTripService();
    } else {
      setError('Missing trip ID or user authentication');
      setIsLoading(false);
    }

    return () => {
      // Cleanup on unmount
      if (tripServiceRef.current) {
        tripServiceRef.current.cleanup();
      }
    };
  }, [tripId, user]);

  const initializeTripService = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Initialize trip management service
      const service = new TripManagementService({
        onTripStatusUpdate: handleTripStatusUpdate,
        onDriverResponse: handleDriverResponse,
        onDriverLocationUpdate: handleDriverLocationUpdate,
        onTripCompleted: handleTripCompleted,
        onTripCancelled: handleTripCancelled,
        onError: handleServiceError
      });

      const initialized = await service.initialize(user!.id);
      if (!initialized) {
        throw new Error('Failed to initialize trip service');
      }

      tripServiceRef.current = service;

      // Load initial trip data
      const tripData = await service.getTripById(tripId!);
      if (!tripData) {
        throw new Error('Trip not found');
      }

      setTrip(tripData);
      setTripStatus(tripData.status);

      // Start monitoring the trip
      const monitoringStarted = await service.startTripMonitoring(tripId!);
      if (!monitoringStarted) {
        throw new Error('Failed to start trip monitoring');
      }

      console.log('ActiveTripScreen: Trip service initialized successfully');
    } catch (error) {
      console.error('ActiveTripScreen: Failed to initialize trip service:', error);
      setError(error instanceof Error ? error.message : 'Failed to initialize trip service');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTripStatusUpdate = (update: TripStatusUpdate) => {
    console.log('ActiveTripScreen: Trip status update:', update);
    setTripStatus(update.status);
    setEstimatedArrival(update.estimatedArrival || null);
    
    if (update.driverLocation) {
      setDriverLocation(update.driverLocation);
    }

    // Provide haptic feedback for important status changes
    if (update.status === 'accepted' || update.status === 'in_progress') {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };

  const handleDriverResponse = (response: DriverResponse) => {
    console.log('ActiveTripScreen: Driver response:', response);
    
    if (response.response === 'accepted') {
      Alert.alert(
        'Driver Found! 🚗',
        'Your driver has accepted the trip and is on the way.',
        [{ text: 'OK', style: 'default' }]
      );
    } else if (response.response === 'declined') {
      Alert.alert(
        'Finding Another Driver',
        'The driver declined your trip. We\'re finding another driver for you.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  const handleDriverLocationUpdate = (location: { lat: number; lng: number }) => {
    console.log('ActiveTripScreen: Driver location update:', location);
    setDriverLocation(location);
    
    // Update map with driver location
    if (webViewRef.current) {
      const script = `
        if (window.updateDriverLocation) {
          window.updateDriverLocation(${location.lat}, ${location.lng});
        }
      `;
      webViewRef.current.postMessage(script);
    }
  };

  const handleTripCompleted = (completedTrip: Trip) => {
    console.log('ActiveTripScreen: Trip completed:', completedTrip);
    
    Alert.alert(
      'Trip Completed! ✅',
      `Your trip has been completed successfully.\nFare: R${completedTrip.fare_amount?.toFixed(2)}`,
      [
        {
          text: 'Rate Trip',
          onPress: () => router.push(`/TripRatingScreen?tripId=${completedTrip.id}`)
        }
      ]
    );
  };

  const handleTripCancelled = (cancelledTrip: Trip, reason: string) => {
    console.log('ActiveTripScreen: Trip cancelled:', cancelledTrip, reason);
    
    Alert.alert(
      'Trip Cancelled',
      `Your trip has been cancelled.\nReason: ${reason}`,
      [
        {
          text: 'OK',
          onPress: () => router.push('/HomePage')
        }
      ]
    );
  };

  const handleServiceError = (error: Error) => {
    console.error('ActiveTripScreen: Service error:', error);
    setError(error.message);
  };

  const handleCancelTrip = () => {
    Alert.alert(
      'Cancel Trip',
      'Are you sure you want to cancel this trip? You may be charged a cancellation fee.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            if (tripServiceRef.current) {
              const success = await tripServiceRef.current.cancelTrip('Cancelled by passenger');
              if (success) {
                router.push('/HomePage');
              } else {
                Alert.alert('Error', 'Failed to cancel trip. Please try again.');
              }
            }
          }
        }
      ]
    );
  };

  const handleCallDriver = () => {
    if (trip?.driver_id) {
      // In a real app, you'd get the driver's phone number from the database
      Alert.alert(
        'Contact Driver',
        'Would you like to call your driver?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Call',
            onPress: () => {
              // For demo purposes, show a placeholder
              Alert.alert('Demo', 'In a real app, this would call the driver.');
            }
          }
        ]
      );
    }
  };

  const handleEmergency = () => {
    Alert.alert(
      'Emergency',
      'Do you need emergency assistance?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call Emergency',
          style: 'destructive',
          onPress: () => {
            Linking.openURL('tel:10111'); // South African emergency number
          }
        }
      ]
    );
  };

  const getStatusText = (status: TripStatus): string => {
    switch (status) {
      case 'requested':
        return 'Finding your driver...';
      case 'accepted':
        return 'Driver is on the way';
      case 'in_progress':
        return 'Trip in progress';
      case 'completed':
        return 'Trip completed';
      case 'cancelled':
        return 'Trip cancelled';
      default:
        return 'Unknown status';
    }
  };

  const getStatusColor = (status: TripStatus): string => {
    switch (status) {
      case 'requested':
        return COLORS.WARNING;
      case 'accepted':
        return COLORS.ACCENT_PINK;
      case 'in_progress':
        return COLORS.SUCCESS;
      case 'completed':
        return COLORS.SUCCESS;
      case 'cancelled':
        return COLORS.DANGER;
      default:
        return COLORS.GRAY;
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.ACCENT_PINK} />
          <Text style={styles.loadingText}>Loading trip details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !trip) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.GRAY} />
          <Text style={styles.errorTitle}>Oops!</Text>
          <Text style={styles.errorText}>{error || 'Trip not found'}</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => router.push('/HomePage')}>
            <Text style={styles.backButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Generate map HTML with trip route and driver tracking
  const generateMapHTML = () => {
    const pickupLat = trip.pickup_coordinates.lat;
    const pickupLng = trip.pickup_coordinates.lng;
    const destLat = trip.destination_coordinates.lat;
    const destLng = trip.destination_coordinates.lng;

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <style>
            body { margin: 0; padding: 0; }
            #map { height: 100vh; width: 100%; }
          </style>
          <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&libraries=geometry"></script>
        </head>
        <body>
          <div id="map"></div>
          <script>
            let map;
            let driverMarker;
            let pickupMarker;
            let destinationMarker;
            let routePath;

            function initMap() {
              // Initialize map centered between pickup and destination
              const center = {
                lat: (${pickupLat} + ${destLat}) / 2,
                lng: (${pickupLng} + ${destLng}) / 2
              };

              map = new google.maps.Map(document.getElementById('map'), {
                zoom: 13,
                center: center,
                styles: [
                  {
                    featureType: 'water',
                    elementType: 'geometry',
                    stylers: [{ color: '#b0d4f4' }]
                  },
                  {
                    featureType: 'landscape.natural',
                    elementType: 'geometry',
                    stylers: [{ color: '#b8dccc' }]
                  }
                ]
              });

              // Add pickup marker
              pickupMarker = new google.maps.Marker({
                position: { lat: ${pickupLat}, lng: ${pickupLng} },
                map: map,
                title: 'Pickup Location',
                icon: {
                  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="12" cy="12" r="8" fill="#4CAF50" stroke="white" stroke-width="2"/>
                      <circle cx="12" cy="12" r="3" fill="white"/>
                    </svg>
                  \`),
                  scaledSize: new google.maps.Size(24, 24)
                }
              });

              // Add destination marker
              destinationMarker = new google.maps.Marker({
                position: { lat: ${destLat}, lng: ${destLng} },
                map: map,
                title: 'Destination',
                icon: {
                  url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#E91E63"/>
                      <circle cx="12" cy="9" r="2.5" fill="white"/>
                    </svg>
                  \`),
                  scaledSize: new google.maps.Size(24, 24)
                }
              });

              // Draw route path
              const routeCoordinates = [
                { lat: ${pickupLat}, lng: ${pickupLng} },
                { lat: ${destLat}, lng: ${destLng} }
              ];

              routePath = new google.maps.Polyline({
                path: routeCoordinates,
                geodesic: true,
                strokeColor: '#C0C0C0',
                strokeOpacity: 1.0,
                strokeWeight: 4
              });

              routePath.setMap(map);

              // Fit map to show all markers
              const bounds = new google.maps.LatLngBounds();
              bounds.extend({ lat: ${pickupLat}, lng: ${pickupLng} });
              bounds.extend({ lat: ${destLat}, lng: ${destLng} });
              map.fitBounds(bounds);
            }

            // Function to update driver location
            window.updateDriverLocation = function(lat, lng) {
              if (driverMarker) {
                driverMarker.setPosition({ lat: lat, lng: lng });
              } else {
                driverMarker = new google.maps.Marker({
                  position: { lat: lat, lng: lng },
                  map: map,
                  title: 'Your Driver',
                  icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                      <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="16" cy="16" r="14" fill="#E91E63" stroke="white" stroke-width="2"/>
                        <path d="M10 14h12v4H10z" fill="white"/>
                        <circle cx="12" cy="20" r="2" fill="white"/>
                        <circle cx="20" cy="20" r="2" fill="white"/>
                      </svg>
                    \`),
                    scaledSize: new google.maps.Size(32, 32)
                  }
                });
              }
            };

            // Initialize map when page loads
            google.maps.event.addDomListener(window, 'load', initMap);
          </script>
        </body>
      </html>
    `;
  };

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.push('/HomePage')} style={styles.backIconButton}>
          <Ionicons name="arrow-back" size={24} color={COLORS.BLACK} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Active Trip</Text>
        <TouchableOpacity onPress={handleEmergency} style={styles.emergencyButton}>
          <Ionicons name="warning" size={24} color={COLORS.DANGER} />
        </TouchableOpacity>
      </View>

      {/* Map Container */}
      <View style={styles.mapContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: generateMapHTML() }}
          style={styles.map}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          renderLoading={() => (
            <View style={styles.mapLoadingContainer}>
              <ActivityIndicator size="large" color={COLORS.ACCENT_PINK} />
              <Text style={styles.mapLoadingText}>Loading map...</Text>
            </View>
          )}
        />
      </View>

      {/* Trip Info Panel */}
      <View style={styles.tripInfoPanel}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Status Card */}
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(tripStatus) }]} />
              <Text style={styles.statusText}>{getStatusText(tripStatus)}</Text>
            </View>
            {estimatedArrival && (
              <Text style={styles.etaText}>ETA: {estimatedArrival} minutes</Text>
            )}
          </View>

          {/* Trip Details */}
          <View style={styles.tripDetailsCard}>
            <View style={styles.locationContainer}>
              <View style={styles.locationItem}>
                <View style={styles.locationIcon}>
                  <Ionicons name="radio-button-on" size={16} color={COLORS.SUCCESS} />
                </View>
                <View style={styles.locationText}>
                  <Text style={styles.locationLabel}>Pickup</Text>
                  <Text style={styles.locationAddress}>
                    {trip.pickup_address_short || trip.pickup_location}
                  </Text>
                </View>
              </View>

              <View style={styles.routeLine} />

              <View style={styles.locationItem}>
                <View style={styles.locationIcon}>
                  <Ionicons name="location" size={16} color={COLORS.ACCENT_PINK} />
                </View>
                <View style={styles.locationText}>
                  <Text style={styles.locationLabel}>Destination</Text>
                  <Text style={styles.locationAddress}>
                    {trip.destination_address_short || trip.destination_location}
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.tripInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Ride Type</Text>
                <Text style={styles.infoValue}>{trip.ride_type}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Distance</Text>
                <Text style={styles.infoValue}>{trip.distance_km.toFixed(1)} km</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Fare</Text>
                <Text style={styles.infoValue}>R{trip.fare_amount.toFixed(2)}</Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {(tripStatus === 'requested' || tripStatus === 'accepted') && (
              <TouchableOpacity style={styles.cancelButton} onPress={handleCancelTrip}>
                <Ionicons name="close-circle" size={20} color={COLORS.DANGER} />
                <Text style={styles.cancelButtonText}>Cancel Trip</Text>
              </TouchableOpacity>
            )}

            {tripStatus === 'accepted' && (
              <TouchableOpacity style={styles.callButton} onPress={handleCallDriver}>
                <Ionicons name="call" size={20} color={COLORS.WHITE} />
                <Text style={styles.callButtonText}>Call Driver</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.GRAY,
    fontFamily: 'Urbanist-Medium',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: COLORS.WHITE,
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: COLORS.GRAY,
    textAlign: 'center',
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
    backgroundColor: COLORS.WHITE,
  },
  backIconButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    textAlign: 'center',
    flex: 1,
  },
  emergencyButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
    backgroundColor: COLORS.LIGHT_GRAY,
  },
  map: {
    flex: 1,
  },
  mapLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_GRAY,
  },
  mapLoadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.GRAY,
    fontFamily: 'Urbanist-Medium',
  },
  tripInfoPanel: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 16,
    paddingTop: 16,
    maxHeight: SCREEN_HEIGHT * 0.4,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  statusCard: {
    backgroundColor: COLORS.LIGHT_PINK,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    flex: 1,
  },
  etaText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    marginLeft: 24,
  },
  tripDetailsCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  locationText: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.BLACK,
    lineHeight: 20,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: COLORS.BORDER,
    marginLeft: 7,
    marginVertical: 8,
  },
  tripInfo: {
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
  },
  actionButtons: {
    marginBottom: 20,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.WHITE,
    borderWidth: 2,
    borderColor: COLORS.DANGER,
    paddingVertical: 12,
    borderRadius: 12,
    marginBottom: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DANGER,
    marginLeft: 8,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.ACCENT_PINK,
    paddingVertical: 12,
    borderRadius: 12,
  },
  callButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
    marginLeft: 8,
  },
});

export default ActiveTripScreen;
