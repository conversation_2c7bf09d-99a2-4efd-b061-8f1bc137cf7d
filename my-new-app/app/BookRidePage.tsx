import React, { useRef, useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Image, Dimensions, TouchableOpacity, TextInput, Animated, ScrollView } from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';
import SheBottomSheet, { SheBottomSheetRef } from '../components/SheBottomSheet';

// For now, we'll use static map background.
// MapView will be enabled when you create a development build
const USE_STATIC_MAP = true;

const { width, height } = Dimensions.get('window');

// Enhanced color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
};

// Simple transition configuration
const TRANSITION_DURATION = 300; // Simple animation duration in ms

// Mock data for ride types with valid MaterialIcons
const RIDE_TYPES = [
  {
    id: 'standard',
    name: 'SheStandard',
    description: 'Comfortable & affordable',
    price: '$12.50',
    estimatedTime: '3-5 min',
    capacity: '4 seats',
    icon: 'directions-car', // Valid MaterialIcons name
    savings: null,
  },
  {
    id: 'premium',
    name: 'ShePremium',
    description: 'Extra comfort & space',
    price: '$18.75',
    estimatedTime: '2-4 min',
    capacity: '4 seats',
    icon: 'airport-shuttle', // Valid MaterialIcons name for premium
    savings: null,
  },
  {
    id: 'luxury',
    name: 'SheLuxury',
    description: 'Premium vehicles only',
    price: '$28.90',
    estimatedTime: '5-8 min',
    capacity: '4 seats',
    icon: 'local-taxi', // Valid MaterialIcons name for luxury
    savings: 'Most popular',
  },
];

// Map configuration
const INITIAL_REGION = {
  latitude: 37.78825, // San Francisco coordinates as default
  longitude: -122.4324,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

// Sample markers for demonstration
const SAMPLE_MARKERS = [
  {
    id: 'pickup',
    coordinate: { latitude: 37.78825, longitude: -122.4324 },
    title: 'Pickup Location',
    description: 'Your current location',
    pinColor: COLORS.ACCENT_PINK,
  },
  {
    id: 'destination',
    coordinate: { latitude: 37.7749, longitude: -122.4194 },
    title: 'Destination',
    description: 'Where you want to go',
    pinColor: COLORS.DEEP_PINK,
  },
];

export default function BookRidePage() {
  const insets = useSafeAreaInsets();
  const bottomSheetRef = useRef<SheBottomSheetRef>(null);

  // State management
  const [selectedRide, setSelectedRide] = useState(RIDE_TYPES[0]);
  const [pickupLocation, setPickupLocation] = useState('');
  const [destination, setDestination] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSnapPoint, setCurrentSnapPoint] = useState(0);

  // Simple animations for UI feedback
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Initialize animations with simple entrance
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: TRANSITION_DURATION,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: TRANSITION_DURATION,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Handle bottom sheet snap point changes
  const handleSheetChange = useCallback((index: number) => {
    setCurrentSnapPoint(index);
    if (index > 0) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  }, []);

  const handleRideSelect = (ride: any) => {
    setSelectedRide(ride);
    // Add simple haptic feedback animation
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.9,
        duration: TRANSITION_DURATION / 2,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: TRANSITION_DURATION / 2,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleBookRide = () => {
    setIsLoading(true);
    // Simulate booking process
    setTimeout(() => {
      setIsLoading(false);
      // Navigate to next screen or show confirmation
    }, 2000);
  };

  // Map handlers will be implemented in development build

  return (
    <View style={styles.container}>
      {/* Map Background - Static for Expo Go, Interactive for Development Build */}
      <Image source={require('../assets/image 21.png')} style={styles.mapBg} resizeMode="cover" />

      {/* Map Ready Indicator - Shows when using static map */}
      {USE_STATIC_MAP && (
        <View style={styles.mapStatusOverlay}>
          <View style={styles.mapStatusBadge}>
            <Ionicons name="map" size={16} color={COLORS.ACCENT_PINK} />
            <Text style={styles.mapStatusText}>Interactive map available in development build</Text>
          </View>
        </View>
      )}

      {/* Enhanced Location Input Section */}
      <Animated.View
        style={[
          styles.locationContainer,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            top: insets.top + 16,
          }
        ]}
      >
        {/* Pickup Location */}
        <View style={styles.locationInputWrapper}>
          <View style={styles.locationDot} />
          <TextInput
            style={styles.locationInput}
            placeholder="Pickup location"
            placeholderTextColor={COLORS.LIGHT_TEXT}
            value={pickupLocation}
            onChangeText={setPickupLocation}
            onFocus={() => bottomSheetRef.current?.expand()}
          />
          <TouchableOpacity style={styles.locationButton}>
            <Ionicons name="locate" size={20} color={COLORS.ACCENT_PINK} />
          </TouchableOpacity>
        </View>

        {/* Destination */}
        <View style={styles.locationInputWrapper}>
          <View style={[styles.locationDot, { backgroundColor: COLORS.ACCENT_PINK }]} />
          <TextInput
            style={styles.locationInput}
            placeholder="Where to?"
            placeholderTextColor={COLORS.LIGHT_TEXT}
            value={destination}
            onChangeText={setDestination}
            onFocus={() => bottomSheetRef.current?.expand()}
          />
          <TouchableOpacity style={styles.locationButton}>
            <Ionicons name="add" size={20} color={COLORS.ACCENT_PINK} />
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="home" size={16} color={COLORS.ACCENT_PINK} />
            <Text style={styles.quickActionText}>Home</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="business" size={16} color={COLORS.ACCENT_PINK} />
            <Text style={styles.quickActionText}>Work</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.quickActionButton}>
            <Ionicons name="star" size={16} color={COLORS.ACCENT_PINK} />
            <Text style={styles.quickActionText}>Saved</Text>
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* SheMove Bottom Sheet with Industry-Standard Gesture Handling */}
      <SheBottomSheet
        ref={bottomSheetRef}
        snapPoints={['30%', '60%', '90%']}
        initialSnapIndex={0}
        onSnapPointChange={handleSheetChange}
        keyboardBehavior="interactive"
      >
        {/* Trip Summary */}
        <View style={styles.tripSummary}>
          <View style={styles.tripInfo}>
            <Text style={styles.tripDistance}>2.5 km</Text>
            <Text style={styles.tripDuration}>8-12 min</Text>
          </View>
          <View style={styles.safetyBadge}>
            <Ionicons name="shield-checkmark" size={16} color={COLORS.SUCCESS} />
            <Text style={styles.safetyText}>Women-only drivers</Text>
          </View>
        </View>

        <Text style={styles.sheetTitle}>Choose your ride</Text>

        {/* Enhanced Ride Cards with Smooth Scrolling */}
        <ScrollView
          style={styles.rideCardsContainer}
          contentContainerStyle={styles.rideCardsContent}
          showsVerticalScrollIndicator={true}
          bounces={true}
        >
          {RIDE_TYPES.map((ride) => (
            <TouchableOpacity
              key={ride.id}
              style={[
                styles.rideCard,
                selectedRide.id === ride.id && styles.selectedRideCard,
              ]}
              onPress={() => handleRideSelect(ride)}
              activeOpacity={0.8}
            >
              <View style={styles.rideCardLeft}>
                <View style={[
                  styles.rideIconContainer,
                  selectedRide.id === ride.id && styles.selectedRideIconContainer
                ]}>
                  {/* Use MaterialIcons with fallback to tesla.png */}
                  {ride.icon ? (
                    <MaterialIcons
                      name={ride.icon as any}
                      size={32}
                      color={selectedRide.id === ride.id ? COLORS.WHITE : COLORS.ACCENT_PINK}
                    />
                  ) : (
                    <Image
                      source={require('../assets/tesla.png')}
                      style={[
                        styles.rideCarImage,
                        { tintColor: selectedRide.id === ride.id ? COLORS.WHITE : COLORS.ACCENT_PINK }
                      ]}
                      resizeMode="contain"
                    />
                  )}
                </View>
                <View style={styles.rideInfo}>
                  <View style={styles.rideHeader}>
                    <Text style={[
                      styles.rideName,
                      selectedRide.id === ride.id && styles.selectedRideText
                    ]}>
                      {ride.name}
                    </Text>
                    {ride.savings && (
                      <View style={styles.savingsBadge}>
                        <Text style={styles.savingsText}>{ride.savings}</Text>
                      </View>
                    )}
                  </View>
                  <Text style={[
                    styles.rideDescription,
                    selectedRide.id === ride.id && styles.selectedRideDescription
                  ]}>
                    {ride.description}
                  </Text>
                  <View style={styles.rideDetails}>
                    <Text style={[
                      styles.rideDetailText,
                      selectedRide.id === ride.id && styles.selectedRideDetailText
                    ]}>
                      {ride.estimatedTime} • {ride.capacity}
                    </Text>
                  </View>
                </View>
              </View>
              <View style={styles.rideCardRight}>
                <Text style={[
                  styles.ridePrice,
                  selectedRide.id === ride.id && styles.selectedRidePrice
                ]}>
                  {ride.price}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Enhanced Book Button with Safe Area */}
        <View style={[styles.bookButtonContainer, { paddingBottom: Math.max(insets.bottom, 16) }]}>
          <TouchableOpacity
            style={[
              styles.bookButton,
              isLoading && styles.bookButtonLoading
            ]}
            onPress={handleBookRide}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <Animated.View style={[styles.loadingDot, { opacity: fadeAnim }]} />
                <Animated.View style={[styles.loadingDot, { opacity: fadeAnim }]} />
                <Animated.View style={[styles.loadingDot, { opacity: fadeAnim }]} />
              </View>
            ) : (
              <>
                <Text style={styles.bookButtonText}>Book {selectedRide.name}</Text>
                <Text style={styles.bookButtonSubtext}>Estimated arrival: {selectedRide.estimatedTime}</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </SheBottomSheet>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'relative',
    backgroundColor: COLORS.WHITE,
  },
  mapBg: {
    position: 'absolute',
    width: width,
    height: height,
    top: 0,
    left: 0,
    zIndex: 0,
  },
  // Map status overlay
  mapStatusOverlay: {
    position: 'absolute',
    top: 100,
    left: 20,
    right: 20,
    zIndex: 1,
    alignItems: 'center',
  },
  mapStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 4,
  },
  mapStatusText: {
    marginLeft: 8,
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
  },
  // Enhanced Location Input Styles
  locationContainer: {
    position: 'absolute',
    left: 20,
    right: 20,
    zIndex: 2,
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
  },
  locationInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },
  locationDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.MEDIUM_TEXT,
    marginRight: 16,
  },
  locationInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DARK_TEXT,
    paddingVertical: 12,
    paddingHorizontal: 0,
  },
  locationButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_PINK,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: COLORS.SOFT_PINK,
    borderRadius: 20,
    flex: 1,
    marginHorizontal: 4,
    justifyContent: 'center',
  },
  quickActionText: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
    marginLeft: 4,
  },
  // Enhanced Bottom Sheet Styles with improved safe area handling
  sheet: {
    position: 'absolute',
    left: 0,
    right: 0,
    // Dynamic height that extends beyond screen to eliminate gaps
    height: height * 1.2, // Increased to ensure full coverage
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 12,
    paddingHorizontal: 20,
    zIndex: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowRadius: 20,
    shadowOpacity: 0.15,
    elevation: 20,
    // Ensure the sheet extends beyond the bottom edge
    bottom: -height * 0.2, // Negative bottom to extend beyond screen
    // Remove overflow hidden to allow scrolling
    overflow: 'visible',
  },
  sheetHandle: {
    alignSelf: 'center',
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.MEDIUM_TEXT,
    marginBottom: 20,
    opacity: 0.6,
  },
  tripSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  tripInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tripDistance: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DARK_TEXT,
    marginRight: 12,
  },
  tripDuration: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
  },
  safetyBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_PINK,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  safetyText: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.SUCCESS,
    marginLeft: 4,
  },
  sheetTitle: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 22,
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  rideCardsContainer: {
    flex: 1,
    maxHeight: height * 0.4, // Increased height for better scrolling
    marginBottom: 20,
  },
  rideCardsContent: {
    flexGrow: 1,
    paddingBottom: 20, // Extra space at bottom for better scrolling
  },
  // Enhanced Ride Card Styles
  rideCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    marginBottom: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  selectedRideCard: {
    backgroundColor: COLORS.ACCENT_PINK,
    borderColor: COLORS.DEEP_PINK,
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  rideCardLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rideIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  selectedRideIconContainer: {
    backgroundColor: COLORS.DEEP_PINK,
  },
  rideCarImage: {
    width: 32,
    height: 32,
  },
  rideInfo: {
    flex: 1,
  },
  rideHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  rideName: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 16,
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginRight: 8,
  },
  selectedRideText: {
    color: COLORS.WHITE,
  },
  savingsBadge: {
    backgroundColor: COLORS.WARNING,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  savingsText: {
    fontSize: 10,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
    fontWeight: '600',
  },
  rideDescription: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 13,
    color: COLORS.MEDIUM_TEXT,
    marginBottom: 4,
  },
  selectedRideDescription: {
    color: COLORS.LIGHT_PINK,
  },
  rideDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rideDetailText: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 12,
    color: COLORS.LIGHT_TEXT,
  },
  selectedRideDetailText: {
    color: COLORS.LIGHT_PINK,
  },
  rideCardRight: {
    alignItems: 'flex-end',
  },
  ridePrice: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
  },
  selectedRidePrice: {
    color: COLORS.WHITE,
  },
  // Enhanced Book Button Styles
  bookButtonContainer: {
    paddingHorizontal: 4,
    paddingTop: 16,
    backgroundColor: COLORS.WHITE,
  },
  bookButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.ACCENT_PINK,
    shadowOpacity: 0.3,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
    minHeight: 56,
  },
  bookButtonLoading: {
    backgroundColor: COLORS.MEDIUM_TEXT,
  },
  bookButtonText: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.WHITE,
    letterSpacing: 0.5,
  },
  bookButtonSubtext: {
    fontFamily: 'Urbanist-Bold',
    fontSize: 12,
    color: COLORS.LIGHT_PINK,
    marginTop: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.WHITE,
    marginHorizontal: 2,
  },
});