import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Animated as RNAnimated,
  Easing,
  Dimensions,
  SafeAreaView,
  Image,
  TouchableWithoutFeedback,
  ActivityIndicator
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import SheBottomSheet, { SheBottomSheetRef } from '../components/SheBottomSheet';
import { WebView } from 'react-native-webview';
import * as Location from 'expo-location';
import NewIcon from '../assets/new.svg';
import { geocodingService, SearchResult } from '../services/geocodingService';
import { GooglePlacesService } from '../services/googleMapsService';
import SearchResults from '../components/SearchResults';
import RecentSearches from '../components/RecentSearches';
import TripPreview from '../components/TripPreview';
import { distanceService, Coordinates } from '../services/distanceService';
import { fareService, RideType, FareBreakdown } from '../services/fareService';
import { Driver } from '../services/driverService';
import { routingService, RouteCoordinate } from '../services/routingService';
import { useAuth } from '../contexts/AuthContext';
import { debounceSearch } from '../utils/debounce';
import { searchHistoryService, SearchHistoryEntry } from '../services/searchHistoryService';
import { recentTripsService, TripDisplayData } from '../services/recentTripsService';
import { tripBookingService, BookingRequest, BookingResult } from '../services/tripBookingService';
import { DriverMapService, MapDriver } from '../services/DriverMapService';

const { width, height } = Dimensions.get('window');

// Bottom sheet configuration with improved snap positions
const SHEET_HEIGHT = height * 0.9; // Total sheet height

// Simple bottom sheet positions
const SNAP_POINTS = [
  height - 150, // Collapsed - show just the "Let's go places" text and search bar
  height * 0.4,  // Expanded - show search results and more content (40% from top)
];

console.log('📐 Screen dimensions:', { width, height });
console.log('📐 SNAP_POINTS calculated:', SNAP_POINTS);

// Simple transition configuration - no complex springs
const TRANSITION_DURATION = 300; // Simple animation duration in ms
const VELOCITY_THRESHOLD = 300; // Minimum velocity to trigger snap to next position

// Enhanced color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
  SUCCESS: '#4CAF50',
  ERROR: '#F44336',
};

// Recent trips will be loaded from database

export default function HomePage() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { signOut, user } = useAuth();

  // Initialize services with user ID
  useEffect(() => {
    if (user?.id) {
      searchHistoryService.setUserId(user.id);
      recentTripsService.setUserId(user.id);
      loadRecentSearches();
      loadRecentTrips();
    }
  }, [user?.id]);

  // Initialize driver map service when location is available
  useEffect(() => {
    if (userLocation && userLocation[0] !== 0 && userLocation[1] !== 0) {
      const coordinates = {
        lat: userLocation[1], // userLocation is [lng, lat]
        lng: userLocation[0]
      };

      console.log('HomePage: Initializing driver map service with location:', coordinates);
      driverMapService.initialize(coordinates);
    }

    // Cleanup on unmount
    return () => {
      driverMapService.cleanup();
    };
  }, [userLocation]);

  // Ensure bottom sheet ref is ready for reliable expansion
  useEffect(() => {
    const checkBottomSheetRef = () => {
      if (bottomSheetRef.current) {
        console.log('✅ Bottom sheet ref is ready');
      } else {
        console.warn('🚨 Bottom sheet ref not ready, will retry...');
        setTimeout(checkBottomSheetRef, 500);
      }
    };

    // Check after component mount
    setTimeout(checkBottomSheetRef, 100);
  }, []);

  const loadRecentSearches = async () => {
    try {
      const searches = await searchHistoryService.getRecentSearches(5);
      setRecentSearches(searches);
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  const loadRecentTrips = async () => {
    setIsLoadingTrips(true);
    try {
      const trips = await recentTripsService.getRecentTrips(3); // Show 3 recent trips
      setRecentTrips(trips);
    } catch (error) {
      console.error('Error loading recent trips:', error);
    } finally {
      setIsLoadingTrips(false);
    }
  };

  // Refs for gesture handling
  const webViewRef = useRef<WebView>(null);
  const bottomSheetRef = useRef<SheBottomSheetRef>(null);

  // Animation values
  const fadeAnim = useRef(new RNAnimated.Value(0)).current;
  const slideAnim = useRef(new RNAnimated.Value(50)).current;

  // State
  const [searchText, setSearchText] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentSnapPoint, setCurrentSnapPoint] = useState(0); // Start collapsed to match translateY

  // Search history state
  const [recentSearches, setRecentSearches] = useState<any[]>([]);
  const [showRecentSearches, setShowRecentSearches] = useState(false);

  // Recent trips state
  const [recentTrips, setRecentTrips] = useState<TripDisplayData[]>([]);
  const [isLoadingTrips, setIsLoadingTrips] = useState(false);

  // Location state
  const [userLocation, setUserLocation] = useState([28.0473, -26.2041]); // Default to Johannesburg, South Africa [lng, lat]
  const [locationPermission, setLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(true);

  // Search state
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<SearchResult | null>(null);
  const [mapMarkers, setMapMarkers] = useState<Array<{ lat: number; lng: number; name: string }>>([]);

  // Trip preview state
  const [showTripPreview, setShowTripPreview] = useState(false);
  const [selectedRideType, setSelectedRideType] = useState<RideType>('SheRide');
  const [pickupAddress, setPickupAddress] = useState('Current location');
  const [pickupAddressData, setPickupAddressData] = useState<SearchResult | null>(null);

  // Simplified state - no route animations
  const [routeData, setRouteData] = useState<any>(null); // Store route data for trip preview
  const [googleRouteData, setGoogleRouteData] = useState<any>(null); // Store Google's route data

  // Booking state
  const [isBookingRide, setIsBookingRide] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);

  // Driver map state
  const [nearbyDrivers, setNearbyDrivers] = useState<MapDriver[]>([]);
  const [driverMapService] = useState(() => new DriverMapService({
    onDriversUpdated: (drivers) => {
      console.log('HomePage: Drivers updated:', drivers.length);
      setNearbyDrivers(drivers);
      updateMapWithDrivers(drivers);
    },
    onDriverAdded: (driver) => {
      console.log('HomePage: Driver added:', driver.name);
      setNearbyDrivers(prev => [...prev, driver]);
      addDriverToMap(driver);
    },
    onDriverRemoved: (driverId) => {
      console.log('HomePage: Driver removed:', driverId);
      setNearbyDrivers(prev => prev.filter(d => d.id !== driverId));
      removeDriverFromMap(driverId);
    },
    onDriverMoved: (driver) => {
      console.log('HomePage: Driver moved:', driver.name);
      setNearbyDrivers(prev => prev.map(d => d.id === driver.id ? driver : d));
      updateDriverOnMap(driver);
    },
    onError: (error) => {
      console.error('HomePage: Driver map service error:', error);
    }
  }));

  // Location functions
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        setLocationPermission(false);
        setIsLoadingLocation(false);
        console.log('Location permission denied');

        // Fallback: try to detect if user is in South Africa based on timezone
        try {
          const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          if (timezone.includes('Africa/Johannesburg') ||
              timezone.includes('Africa/Cape_Town') ||
              timezone.includes('Africa/Durban')) {
            geocodingService.setUserCountry('za');
            console.log('Detected South African timezone, prioritizing local results');
          }
        } catch (error) {
          console.warn('Could not detect timezone:', error);
        }
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setLocationPermission(false);
      setIsLoadingLocation(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      const { latitude, longitude } = location.coords;
      setUserLocation([longitude, latitude]); // Note: Leaflet uses [lng, lat] format

      // Set user location in geocoding service for location-based search prioritization
      await geocodingService.setUserLocation(latitude, longitude);

      // Get the actual address for pickup location with full address data
      try {
        const addressResult = await geocodingService.reverseGeocode(latitude, longitude);
        if (addressResult) {
          const shortAddress = geocodingService.getShortAddress(addressResult);
          setPickupAddress(shortAddress);
          setPickupAddressData(addressResult); // Store full address data for map bubble formatting
        }
      } catch (error) {
        console.warn('Could not get pickup address:', error);
        setPickupAddress('Current location');
        setPickupAddressData(null);
      }

      setIsLoadingLocation(false);
    } catch (error) {
      console.error('Error getting current location:', error);
      setIsLoadingLocation(false);
    }
  };

  // Driver map management functions
  const updateMapWithDrivers = (drivers: MapDriver[]) => {
    if (webViewRef.current) {
      const driversData = drivers.map(driver => ({
        id: driver.id,
        name: driver.name,
        lat: driver.location.lat,
        lng: driver.location.lng,
        heading: driver.heading,
        vehicleType: driver.vehicleType,
        rating: driver.rating,
        estimatedArrival: driver.estimatedArrival
      }));

      const script = `
        if (window.updateDriverMarkers) {
          window.updateDriverMarkers(${JSON.stringify(driversData)});
        }
      `;
      webViewRef.current.postMessage(script);
    }
  };

  const addDriverToMap = (driver: MapDriver) => {
    if (webViewRef.current) {
      const driverData = {
        id: driver.id,
        name: driver.name,
        lat: driver.location.lat,
        lng: driver.location.lng,
        heading: driver.heading,
        vehicleType: driver.vehicleType,
        rating: driver.rating,
        estimatedArrival: driver.estimatedArrival
      };

      const script = `
        if (window.addDriverMarker) {
          window.addDriverMarker(${JSON.stringify(driverData)});
        }
      `;
      webViewRef.current.postMessage(script);
    }
  };

  const removeDriverFromMap = (driverId: string) => {
    if (webViewRef.current) {
      const script = `
        if (window.removeDriverMarker) {
          window.removeDriverMarker('${driverId}');
        }
      `;
      webViewRef.current.postMessage(script);
    }
  };

  const updateDriverOnMap = (driver: MapDriver) => {
    if (webViewRef.current) {
      const driverData = {
        id: driver.id,
        name: driver.name,
        lat: driver.location.lat,
        lng: driver.location.lng,
        heading: driver.heading,
        vehicleType: driver.vehicleType,
        rating: driver.rating,
        estimatedArrival: driver.estimatedArrival
      };

      const script = `
        if (window.updateDriverMarker) {
          window.updateDriverMarker(${JSON.stringify(driverData)});
        }
      `;
      webViewRef.current.postMessage(script);
    }
  };

  // Search functionality with smart debouncing

  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setIsSearching(true);
    setShowSearchResults(true);

    try {
      // Store search query in history
      if (user?.id) {
        await searchHistoryService.storeSearch({
          searchQuery: query,
          searchContext: 'destination',
          locationContext: [userLocation[1], userLocation[0]], // [lat, lng]
        });
      }

      // Use Google Places API for better search results when testing Google Maps
      try {
        // Get API key from multiple sources
        const googleApiKey = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || process.env.GOOGLE_MAPS_API_KEY;

        // Try Google Places API first if available
        if (googleApiKey && googleApiKey !== 'your_google_maps_api_key_here' && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
          const googlePlacesService = new GooglePlacesService(googleApiKey);
          console.log('🔍 Using Google Places API for search');
          const googleResults = await googlePlacesService.searchPlaces(
            query,
            { lat: userLocation[1], lng: userLocation[0] },
            50000 // 50km radius
          );

          if (googleResults && googleResults.length > 0) {
            // Convert Google Places results to SearchResult format
            const convertedResults = await Promise.all(
              googleResults.slice(0, 5).map(async (place) => {
                try {
                  const details = await googlePlacesService.getPlaceDetails(place.placeId);
                  return {
                    place_id: place.placeId,
                    lat: details.coordinates.lat.toString(),
                    lon: details.coordinates.lng.toString(),
                    display_name: details.formattedAddress,
                    type: 'place',
                    importance: 0.8, // High importance for Google Places results
                    address: {
                      house_number: '',
                      road: place.mainText,
                      suburb: place.secondaryText,
                      city: place.secondaryText,
                      country: 'South Africa'
                    }
                  };
                } catch (error) {
                  console.warn('Error getting place details:', error);
                  return null;
                }
              })
            );

            const validResults = convertedResults.filter(result => result !== null);
            if (validResults.length > 0) {
              setSearchResults(validResults);
              setShowSearchResults(true); // Ensure search results are shown
              console.log(`🔍 Search "${query}" - Provider: Google Places - Results: ${validResults.length}`);
              return;
            }
          }
        }
      } catch (error) {
        console.warn('Google Places API error, falling back to free services:', error);
      }

      // Fallback to enhanced search with LocationIQ for better house number results
      const response = await geocodingService.searchLocationsEnhanced(query, 5);
      if (response.results && response.results.length > 0) {
        setSearchResults(response.results);
        setShowSearchResults(true); // Ensure search results are shown

        // Log provider usage for monitoring
        if (response.provider) {
          console.log(`🔍 Search "${query}" - Provider: ${response.provider} - Results: ${response.results.length}`);
        }
      } else {
        setSearchResults([]);
        setShowSearchResults(false);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Create smart debounced search function
  const debouncedSearch = useCallback(
    debounceSearch((query: string) => {
      performSearch(query);
    }, 300, 2), // 300ms delay, minimum 2 characters
    []
  );

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);

    // Always ensure bottom sheet is fully expanded when user is typing
    // Use the robust expansion function for consistency
    ensureBottomSheetExpanded();

    // Clear search results if text is empty
    if (!text.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    // Use smart debounced search
    debouncedSearch(text);
  };

  const handleSearchResultPress = async (result: SearchResult) => {
    // Validate coordinates before processing
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);

    if (isNaN(lat) || isNaN(lng)) {
      console.error('🚨 Invalid coordinates in search result:', { lat: result.lat, lng: result.lon, result });
      alert('Invalid location coordinates. Please try another search.');
      return;
    }

    console.log('📍 Valid coordinates found:', { lat, lng });

    setSelectedLocation(result);
    setSearchText(geocodingService.getShortAddress(result));
    setShowSearchResults(false);

    // Mark search result as clicked in history
    if (user?.id && searchText) {
      await searchHistoryService.markResultClicked(searchText, result);
      // Update recent destinations
      await searchHistoryService.updateRecentDestination(
        result.display_name,
        [lat, lng] // Use validated coordinates
      );
      // Reload recent searches to show updated data
      loadRecentSearches();
    }

    // Update map with selected location
    // Use smart address formatting for map marker (will be formatted again in generateMapHTML for consistency)
    const newMarker = { lat, lng, name: result.display_name };
    setMapMarkers([newMarker]);

    // Fetch real route with validated coordinates
    await fetchRoute({ lat: userLocation[1], lng: userLocation[0] }, { lat, lng });

    // Show trip preview (will use route data if available)
    setShowTripPreview(true);

    console.log('🎯 Trip preview activated with route data:', routeData ? 'Available' : 'Not available');

    // Expand bottom sheet to full height for trip preview
    setTimeout(() => {
      bottomSheetRef.current?.snapToIndex(2);
    }, 100);

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Simplified route fetching - no animations
  const fetchRoute = async (start: RouteCoordinate, end: RouteCoordinate) => {
    try {
      const route = await routingService.getRoute(start, end);
      if (route) {
        // Store route data for trip preview calculations
        setRouteData(route);
      } else {
        setRouteData(null);
      }
    } catch (error) {
      console.error('Error fetching route:', error);
      setRouteData(null);
    }
  };

  useEffect(() => {
    // Initialize animations
    RNAnimated.parallel([
      RNAnimated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      RNAnimated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: true,
      }),
    ]).start();

    // Bottom sheet starts collapsed - no automatic expansion

    // Request location permission and get current location
    requestLocationPermission();
  }, []);

  // Simple snap point detection - find closest position
  const findClosestSnapPoint = useCallback((y: number, velocity: number) => {
    'worklet';

    // Find the closest snap point
    let closest = 0;
    let minDistance = Math.abs(SNAP_POINTS[0] - y);

    for (let i = 1; i < SNAP_POINTS.length; i++) {
      const distance = Math.abs(SNAP_POINTS[i] - y);
      if (distance < minDistance) {
        minDistance = distance;
        closest = i;
      }
    }

    // If velocity is high enough, move in velocity direction
    if (Math.abs(velocity) > VELOCITY_THRESHOLD) {
      if (velocity > 0 && closest < SNAP_POINTS.length - 1) {
        return closest + 1; // Move down (more collapsed)
      } else if (velocity < 0 && closest > 0) {
        return closest - 1; // Move up (more expanded)
      }
    }

    return closest;
  }, []);

  // Handle bottom sheet snap point changes
  const handleSheetChange = useCallback((index: number) => {
    setCurrentSnapPoint(index);
    setIsExpanded(index > 0);

    // Only provide haptic feedback - no map recentering
    // Uber-style haptic feedback - different intensities for different states
    switch (index) {
      case 2: // Fully expanded
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 1: // Medium position
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 0: // Collapsed
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
        break;
    }
  }, []);

  // Basic bottom sheet state
  const [isExpanded, setIsExpanded] = useState(false);

  // Simple toggle function
  const toggleBottomSheet = () => {
    const newExpanded = !isExpanded;
    if (newExpanded) {
      bottomSheetRef.current?.expand();
    } else {
      bottomSheetRef.current?.collapse();
    }
  };



  const handleRidePress = () => {
    router.push('/BookRidePage');
  };

  const handleSchedulePress = () => {
    setShowDatePicker(true);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.replace('/SplashPage');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Handle recent search selection
  const handleRecentSearchPress = (searchHistory: any) => {
    if (searchHistory.result_address && searchHistory.result_coordinates) {
      // Create a SearchResult-like object from search history
      const result: SearchResult = {
        place_id: searchHistory.id,
        display_name: searchHistory.result_address,
        lat: searchHistory.result_coordinates[0].toString(),
        lon: searchHistory.result_coordinates[1].toString(),
        type: 'recent',
        importance: 1,
        address: {}
      };

      // Use the existing search result handler
      handleSearchResultPress(result);
    } else {
      // If no coordinates, just set the search text and perform search
      setSearchText(searchHistory.search_query);
      performSearch(searchHistory.search_query);
    }
    setShowRecentSearches(false);
  };

  // Handle tapping outside search results to close them
  const handleContainerPress = () => {
    if (showSearchResults) {
      setShowSearchResults(false);
    }
  };

  // Robust function to ensure bottom sheet expansion
  const ensureBottomSheetExpanded = useCallback(() => {
    const expandBottomSheet = () => {
      if (bottomSheetRef.current) {
        bottomSheetRef.current.snapToIndex(2);
        console.log('✅ Bottom sheet expanded to index 2 (90%)');
        return true;
      }
      return false;
    };

    // Try immediate expansion first
    if (expandBottomSheet()) {
      return;
    }

    // If immediate expansion fails, try with increasing delays
    const delays = [10, 50, 100, 200];
    delays.forEach((delay, index) => {
      setTimeout(() => {
        if (!expandBottomSheet()) {
          console.warn(`🚨 Bottom sheet expansion attempt ${index + 1} failed`);
        }
      }, delay);
    });
  }, []);

  // Handle search input focus - always expand bottom sheet fully for best typing experience
  const handleSearchFocus = () => {
    console.log('🔍 Search focus triggered - expanding bottom sheet fully');
    console.log('Current snap point:', currentSnapPoint);

    // Use robust expansion function
    ensureBottomSheetExpanded();

    // Provide haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Show search results if we have any, otherwise show recent searches
    if (searchResults.length > 0) {
      setShowSearchResults(true);
      setShowRecentSearches(false);
    } else if (recentSearches.length > 0 && !searchText.trim()) {
      setShowRecentSearches(true);
      setShowRecentSearches(false);
    }
  };

  // Handle search input blur - minimal interference with typing experience
  const handleSearchBlur = () => {
    // Hide recent searches when losing focus
    setShowRecentSearches(false);

    // Only collapse if completely empty search and no trip preview
    // Remove aggressive collapsing to avoid interfering with typing flow
    if (!searchText.trim() && !showTripPreview && !showSearchResults) {
      setTimeout(() => {
        bottomSheetRef.current?.snapToIndex(0);
      }, 500); // Longer delay to avoid interference
    }
  };

  // Trip preview handlers
  const handleBackFromTripPreview = () => {
    setShowTripPreview(false);
    setSelectedLocation(null);
    setSearchText('');
    setMapMarkers([]);
    setRouteData(null); // Clear route data

    // Collapse to collapsed position
    bottomSheetRef.current?.snapToIndex(0);
  };

  const handleRideTypeChange = (rideType: RideType) => {
    setSelectedRideType(rideType);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBookRide = async (driver: Driver, fare: FareBreakdown) => {
    if (!user || !selectedLocation) {
      alert('Please ensure you are logged in and have selected a destination');
      return;
    }

    // Clear any previous errors
    setBookingError(null);
    setIsBookingRide(true);

    try {
      // Validate destination coordinates before booking
      const destLat = parseFloat(selectedLocation.lat);
      const destLng = parseFloat(selectedLocation.lon);

      if (isNaN(destLat) || isNaN(destLng)) {
        console.error('🚨 Invalid destination coordinates for booking:', { lat: selectedLocation.lat, lng: selectedLocation.lon });
        setBookingError('Invalid destination coordinates');
        alert('Invalid destination coordinates. Please select a valid location.');
        return;
      }

      // Prepare booking request
      const bookingRequest: BookingRequest = {
        pickupLocation: {
          lat: userLocation[1], // userLocation is [lng, lat]
          lng: userLocation[0],
          address: pickupAddress,
          shortAddress: pickupAddress === 'Current location' ? 'Current location' : undefined
        },
        destination: {
          lat: destLat,
          lng: destLng,
          address: selectedLocation.display_name,
          shortAddress: selectedLocation.display_name.split(',')[0]?.trim() || selectedLocation.display_name
        },
        rideType: selectedRideType,
        driver,
        fare,
        passengerNotes: undefined // Could add a notes field to UI later
      };

      console.log('Creating trip booking:', {
        driver: driver.name,
        rideType: selectedRideType,
        fare: fare.totalFare,
        pickup: pickupAddress,
        destination: selectedLocation.display_name,
      });

      // Create the trip
      const result: BookingResult = await tripBookingService.createTrip(bookingRequest, user.id);

      if (result.success && result.trip) {
        console.log('Trip booked successfully:', result.trip);

        // Navigate to trip confirmation screen
        router.push(`/TripConfirmationScreen?tripId=${result.trip.id}`);
      } else {
        // Handle booking error
        const errorMessage = result.error || 'Failed to book ride';
        setBookingError(errorMessage);

        if (result.errorCode === 'DRIVER_UNAVAILABLE') {
          alert('Driver is no longer available. Please select another driver.');
        } else {
          alert(`Booking failed: ${errorMessage}`);
        }
      }
    } catch (error) {
      console.error('Booking error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setBookingError(errorMessage);
      alert(`Booking failed: ${errorMessage}`);
    } finally {
      setIsBookingRide(false);
    }
  };

  // Google-style address formatting for map bubbles with house number support
  const formatMapBubbleAddress = (displayName: string, addressData?: any): string => {
    // If we have structured address data, use it for smart formatting
    if (addressData) {
      return formatStructuredMapAddress(addressData, displayName);
    }

    // Fallback to display_name parsing with house number extraction
    return formatDisplayNameMapAddress(displayName);
  };

  const formatStructuredMapAddress = (address: any, displayName: string): string => {
    // Strategy 1: Specific Address (House + Street) - PRIORITY for addresses like "3 Aries Road"
    if (address.house_number && address.road) {
      const primary = `${address.house_number} ${address.road}`;
      const city = address.city || address.town || address.suburb;
      return city ? `${primary}, ${city}` : primary;
    }

    // Strategy 2: Street/Road Only (but check if house number is in display_name)
    if (address.road) {
      const houseNumberFromDisplay = extractHouseNumberFromDisplay(displayName, address.road);
      const primary = houseNumberFromDisplay ? `${houseNumberFromDisplay} ${address.road}` : address.road;
      const city = address.city || address.town || address.suburb;
      return city ? `${primary}, ${city}` : primary;
    }

    // Strategy 3: Business/POI Name (from display_name first part)
    const displayParts = displayName.split(',').map(part => part.trim());
    if (displayParts.length > 0 && !isGenericLocationName(displayParts[0])) {
      const city = address.city || address.town || address.suburb;
      return city ? `${displayParts[0]}, ${city}` : displayParts[0];
    }

    // Fallback to display name parsing
    return formatDisplayNameMapAddress(displayName);
  };

  const formatDisplayNameMapAddress = (displayName: string): string => {
    const parts = displayName.split(',').map(part => part.trim());

    if (parts.length === 0) return 'Unknown location';
    if (parts.length === 1) return parts[0];

    // For map bubbles, show primary location + city (max 2 parts)
    // Filter out obvious/redundant information for South African users
    const filteredParts = parts.filter(part => {
      const lowerPart = part.toLowerCase();
      return !lowerPart.includes('south africa') &&
             !lowerPart.includes('gauteng') &&
             !lowerPart.includes('western cape') &&
             !lowerPart.includes('kwazulu-natal') &&
             !lowerPart.includes('eastern cape') &&
             !lowerPart.includes('free state') &&
             !lowerPart.includes('limpopo') &&
             !lowerPart.includes('mpumalanga') &&
             !lowerPart.includes('north west') &&
             !lowerPart.includes('northern cape');
    });

    return filteredParts.slice(0, 2).join(', ');
  };

  const extractHouseNumberFromDisplay = (displayName: string, roadName: string): string | null => {
    // Look for house number in the first part of display_name
    const firstPart = displayName.split(',')[0].trim();

    // Check if first part contains both number and road name
    if (firstPart.toLowerCase().includes(roadName.toLowerCase())) {
      // Extract number from the beginning of the string
      const numberMatch = firstPart.match(/^(\d+)\s+/);
      return numberMatch ? numberMatch[1] : null;
    }

    return null;
  };

  const isGenericLocationName = (text: string): boolean => {
    const genericTerms = [
      'city', 'town', 'village', 'municipality', 'ward', 'district',
      'province', 'region', 'area', 'zone', 'sector'
    ];

    const lowerText = text.toLowerCase();
    return genericTerms.some(term => lowerText.includes(term));
  };

  // Generate HTML for WebView to avoid JSX parsing issues
  const generateMapHTML = () => {
    let html = '';

    // Add clean user location marker when no destination is selected (Uber-style)
    if (!selectedLocation) {
      // Use pickup address data for smart house number formatting
      const formattedPickupAddress = formatMapBubbleAddress(pickupAddress, pickupAddressData?.address);
      html += `
        var userLocationIcon = L.divIcon({
          className: 'custom-div-icon',
          html: '<div style="background-color: #1976D2; width: 16px; height: 16px; border-radius: 50%; border: 4px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });

        var userLocationMarker = L.marker([${userLocation[1]}, ${userLocation[0]}], {icon: userLocationIcon})
          .addTo(map)
          .bindPopup('📍 ${formattedPickupAddress.replace(/'/g, "\\'")}');
      `;
    }

    // Add clean start and destination markers when location is selected (Uber-style)
    if (mapMarkers.length > 0) {
      mapMarkers.forEach(marker => {
        // Use pickup address data for smart house number formatting
        const formattedPickupAddress = formatMapBubbleAddress(pickupAddress, pickupAddressData?.address);
        // Pass selectedLocation's address data for smart house number formatting
        const formattedDestinationAddress = formatMapBubbleAddress(marker.name, selectedLocation?.address);

        html += `
          // Clean Starting Point Marker (Uber-style blue dot)
          var startIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #1976D2; width: 12px; height: 12px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
            iconSize: [18, 18],
            iconAnchor: [9, 9]
          });

          var startMarker = L.marker([${userLocation[1]}, ${userLocation[0]}], {icon: startIcon})
            .addTo(map)
            .bindPopup('📍 ${formattedPickupAddress.replace(/'/g, "\\'")}');

          // Clean Destination Marker (Uber-style with SheMove pink)
          var endIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #E91E63; width: 14px; height: 14px; border-radius: 2px; border: 2px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
            iconSize: [18, 18],
            iconAnchor: [9, 9]
          });

          var endMarker = L.marker([${marker.lat}, ${marker.lng}], {icon: endIcon})
            .addTo(map)
            .bindPopup('🎯 ${formattedDestinationAddress.replace(/'/g, "\\'")}')
            .openPopup();

          // Add road-following route line using actual route coordinates
          var routeCoordinates = [];

          // Use real route data if available, otherwise fallback to straight line
          if (window.routeData && window.routeData.coordinates && window.routeData.coordinates.length > 0) {
            // Convert route coordinates to Leaflet format [lat, lng]
            routeCoordinates = window.routeData.coordinates.map(function(coord) {
              return [coord.lat, coord.lng];
            });
            console.log('🛣️ Using real route with', routeCoordinates.length, 'points');
          } else {
            // Fallback to straight line if no route data
            routeCoordinates = [
              [${userLocation[1]}, ${userLocation[0]}],
              [${marker.lat}, ${marker.lng}]
            ];
            console.log('📏 Using straight line fallback');
          }

          // Create base silver route line
          var routeLine = L.polyline(routeCoordinates, {
            color: '#A8A8A8',
            weight: 6,
            opacity: 0.8,
            smoothFactor: 1,
            lineCap: 'round',
            lineJoin: 'round'
          }).addTo(map);

          // Create animated shimmer overlay
          var shimmerLine = L.polyline(routeCoordinates, {
            color: '#FFFFFF',
            weight: 4,
            opacity: 0,
            smoothFactor: 1,
            lineCap: 'round',
            lineJoin: 'round'
          }).addTo(map);

          // Create smooth sweeping shimmer effect from pickup to destination
          var shimmerProgress = 0;
          var shimmerSpeed = 0.008; // Much slower for smoother effect
          var shimmerPause = 0;
          var pauseDuration = 60; // Pause between sweeps

          function animateShimmer() {
            if (shimmerPause > 0) {
              shimmerPause--;
              requestAnimationFrame(animateShimmer);
              return;
            }

            shimmerProgress += shimmerSpeed;

            if (shimmerProgress >= 1) {
              shimmerProgress = 0;
              shimmerPause = pauseDuration; // Pause before next sweep
            }

            // Create sweeping effect: opacity peaks at current progress point
            var peakOpacity = 0.8;
            var sweepWidth = 0.3; // Width of the light sweep

            // Calculate opacity based on distance from sweep center
            var sweepCenter = shimmerProgress;
            var distanceFromCenter = Math.abs(0.5 - sweepCenter);
            var opacity = Math.max(0, peakOpacity * (1 - distanceFromCenter / sweepWidth));

            shimmerLine.setStyle({
              opacity: opacity,
              color: '#F0F0F0' // Bright silver-white for the sweep
            });

            requestAnimationFrame(animateShimmer);
          }

          // Start the smooth sweeping animation
          animateShimmer();

          // Fit map to show both markers and route
          var group = new L.featureGroup([startMarker, endMarker, routeLine]);
          map.fitBounds(group.getBounds().pad(0.1));
        `;
      });
    }

    // Add clean driver markers for trip preview (Uber-style)
    if (showTripPreview && mapMarkers.length > 0) {
      html += `
        var driverIcon = L.divIcon({
          className: 'custom-div-icon',
          html: '<div style="background-color: #000000; width: 8px; height: 8px; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 4px rgba(0,0,0,0.3);"></div>',
          iconSize: [12, 12],
          iconAnchor: [6, 6]
        });

        var sampleDrivers = [
          {lat: ${userLocation[1] + 0.01}, lng: ${userLocation[0] + 0.01}, name: 'Nomsa - 3 min away'},
          {lat: ${userLocation[1] - 0.008}, lng: ${userLocation[0] + 0.012}, name: 'Thandi - 5 min away'},
          {lat: ${userLocation[1] + 0.015}, lng: ${userLocation[0] - 0.005}, name: 'Zanele - 7 min away'},
        ];

        sampleDrivers.forEach(function(driver) {
          L.marker([driver.lat, driver.lng], {icon: driverIcon}).addTo(map)
            .bindPopup(driver.name);
        });
      `;
    }

    return html;
  };

  // Generate the HTML content
  const mapMarkersHTML = generateMapHTML();

  return (
    <View style={styles.container}>
      {/* Google Maps for Testing UX */}
      <WebView
        ref={webViewRef}
        style={styles.mapBackground}
        onMessage={(event) => {
          try {
            const data = JSON.parse(event.nativeEvent.data);
            if (data.type === 'googleRouteData') {
              console.log('📍 Received Google route data:', data.data);
              setGoogleRouteData(data.data);
            } else if (data.type === 'routeError') {
              console.error('🚨 Route calculation error from WebView:', data.error);
              console.error('🚨 Status:', data.status);

              // Show user-friendly error message
              if (data.status === 'REQUEST_DENIED') {
                console.error('🔑 Google Maps API key issue - check permissions for Directions API');
              } else if (data.status === 'OVER_QUERY_LIMIT') {
                console.error('📊 Google Maps API quota exceeded');
              }

              // Clear any existing route data since route calculation failed
              setGoogleRouteData(null);
            } else if (data.type === 'mapInitError') {
              console.error('🚨 Map initialization error from WebView:', data.error);
              // Could show a user-friendly error message here if needed
            } else if (data.type === 'coordinateError') {
              console.error('🚨 Coordinate error from WebView:', data.error);
              // Clear invalid location data
              setSelectedLocation(null);
              setMapMarkers([]);
              alert('Invalid location coordinates. Please select a different location.');
            }
          } catch (error) {
            console.error('Error parsing WebView message:', error);
          }
        }}
        source={{
          html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
              <style>
                * {
                  box-sizing: border-box;
                }
                html, body {
                  margin: 0;
                  padding: 0;
                  height: 100%;
                  width: 100%;
                  overflow: hidden;
                  touch-action: none;
                  -webkit-touch-callout: none;
                  -webkit-user-select: none;
                  -khtml-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;
                  -webkit-tap-highlight-color: transparent;
                }
                #map {
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 100vh;
                  width: 100vw;
                  touch-action: none;
                  cursor: grab;
                  outline: none;
                  -webkit-tap-highlight-color: transparent;
                }
                #map:active {
                  cursor: grabbing;
                }
                .loading-overlay {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  background: rgba(255, 255, 255, 0.95);
                  padding: 20px;
                  border-radius: 12px;
                  text-align: center;
                  font-family: Arial, sans-serif;
                  color: #E91E63;
                  font-weight: 600;
                  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                  z-index: 1000;
                  ${!isLoadingLocation ? 'display: none;' : ''}
                }

                /* Custom Google Maps styling */
                .gm-style {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                }

                /* Hide Google Maps controls for cleaner look */
                .gm-style .gm-style-cc {
                  display: none;
                }
                .gm-style .gmnoprint {
                  display: none;
                }
                .gm-style .gm-bundled-control {
                  display: none;
                }

                /* Silver shimmer route animation */
                @keyframes shimmer {
                  0% {
                    background-position: -200% 0;
                  }
                  100% {
                    background-position: 200% 0;
                  }
                }

                .shimmer-route {
                  position: relative;
                }

                .shimmer-overlay {
                  animation: shimmer 2s ease-in-out infinite;
                  background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(255, 255, 255, 0.8) 50%,
                    transparent 100%
                  );
                  background-size: 200% 100%;
                  filter: blur(0.5px);
                }
              </style>
            </head>
            <body>
              <div id="map"></div>
              ${isLoadingLocation ? '<div class="loading-overlay">📍 Getting your location...</div>' : ''}
              <script async defer src="https://maps.googleapis.com/maps/api/js?key=${process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || 'AIzaSyDF7xHCtkm2uQdfK0C4REDGvuCdhSJo158'}&callback=initMap&libraries=geometry"></script>
              <script>
                let map;
                let directionsService;
                let directionsRenderer;
                let pickupMarker;
                let destinationMarker;
                let routePolylines = []; // Store route polylines for cleanup

                // Fixed map center - no automatic adjustment based on bottom sheet position
                // Map should only move when user directly interacts with it
                var mapCenterLat = ${userLocation[1]};
                var mapCenterLng = ${userLocation[0]};

                // Function to get and display route with animation
                function displayRouteWithAnimation(origin, destination) {
                  // Clear existing route polylines
                  routePolylines.forEach(polyline => polyline.setMap(null));
                  routePolylines = [];

                  // Initialize directions service if not already done
                  if (!directionsService) {
                    directionsService = new google.maps.DirectionsService();
                  }

                  // Request route from Google Directions API with enhanced error handling
                  console.log('🗺️ Requesting route from Google Directions API...');
                  console.log('Origin:', origin);
                  console.log('Destination:', destination);

                  directionsService.route({
                    origin: origin,
                    destination: destination,
                    travelMode: google.maps.TravelMode.DRIVING,
                    unitSystem: google.maps.UnitSystem.METRIC,
                    region: 'ZA'
                  }, function(result, status) {
                    console.log('🗺️ Google Directions API response status:', status);

                    if (status === google.maps.DirectionsStatus.OK) {
                      // Extract the route path and duration data from the directions result
                      var route = result.routes[0];
                      var leg = route.legs[0];
                      var routePath = route.overview_path;

                      // Send Google's route data back to React Native for trip preview
                      var googleRouteData = {
                        distance: leg.distance.value, // in meters
                        duration: leg.duration.value, // in seconds
                        distanceText: leg.distance.text,
                        durationText: leg.duration.text,
                        startAddress: leg.start_address,
                        endAddress: leg.end_address,
                        coordinates: routePath.map(function(point) {
                          return { lat: point.lat(), lng: point.lng() };
                        })
                      };

                      // Post message to React Native with Google's route data
                      if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'googleRouteData',
                          data: googleRouteData
                        }));
                      }

                      // Create base silver route line following actual roads
                      var baseRoutePolyline = new google.maps.Polyline({
                        path: routePath,
                        geodesic: false, // Follow roads, not geodesic lines
                        strokeColor: '#A8A8A8',
                        strokeOpacity: 0.8,
                        strokeWeight: 6,
                        zIndex: 1
                      });
                      baseRoutePolyline.setMap(map);
                      routePolylines.push(baseRoutePolyline);

                      // Create animated shimmer overlay polyline
                      var shimmerPolyline = new google.maps.Polyline({
                        path: routePath,
                        geodesic: false,
                        strokeColor: '#FFFFFF',
                        strokeOpacity: 0,
                        strokeWeight: 4,
                        zIndex: 2
                      });
                      shimmerPolyline.setMap(map);
                      routePolylines.push(shimmerPolyline);

                      // Implement sweeping shimmer animation
                      var shimmerProgress = 0;
                      var shimmerSpeed = 0.008; // Much slower for smoother effect
                      var shimmerPause = 0;
                      var pauseDuration = 60; // Pause between sweeps

                      function animateShimmer() {
                        if (shimmerPause > 0) {
                          shimmerPause--;
                          requestAnimationFrame(animateShimmer);
                          return;
                        }

                        shimmerProgress += shimmerSpeed;

                        if (shimmerProgress >= 1) {
                          shimmerProgress = 0;
                          shimmerPause = pauseDuration; // Pause before next sweep
                        }

                        // Create sweeping effect: opacity peaks at current progress point
                        var peakOpacity = 0.8;
                        var sweepWidth = 0.3; // Width of the light sweep

                        // Calculate opacity based on distance from sweep center
                        var sweepCenter = shimmerProgress;
                        var distanceFromCenter = Math.abs(0.5 - sweepCenter);
                        var opacity = Math.max(0, peakOpacity * (1 - distanceFromCenter / sweepWidth));

                        // Update shimmer polyline opacity and color
                        shimmerPolyline.setOptions({
                          strokeOpacity: opacity,
                          strokeColor: '#F0F0F0' // Bright silver-white for the sweep
                        });

                        requestAnimationFrame(animateShimmer);
                      }

                      // Start the smooth sweeping animation
                      animateShimmer();

                      // Fit map to show route with padding
                      var bounds = new google.maps.LatLngBounds();
                      routePath.forEach(function(point) {
                        bounds.extend(point);
                      });
                      map.fitBounds(bounds, { top: 100, right: 50, bottom: 300, left: 50 });
                    } else {
                      console.error('🚨 Google Directions request failed with status:', status);

                      // Provide detailed error information
                      var errorMessage = 'Route calculation failed';
                      switch(status) {
                        case google.maps.DirectionsStatus.NOT_FOUND:
                          errorMessage = 'Route not found - one or more locations could not be geocoded';
                          break;
                        case google.maps.DirectionsStatus.ZERO_RESULTS:
                          errorMessage = 'No route could be found between the origin and destination';
                          break;
                        case google.maps.DirectionsStatus.MAX_WAYPOINTS_EXCEEDED:
                          errorMessage = 'Too many waypoints in the request';
                          break;
                        case google.maps.DirectionsStatus.INVALID_REQUEST:
                          errorMessage = 'Invalid directions request';
                          break;
                        case google.maps.DirectionsStatus.OVER_QUERY_LIMIT:
                          errorMessage = 'API query limit exceeded';
                          break;
                        case google.maps.DirectionsStatus.REQUEST_DENIED:
                          errorMessage = 'Directions request denied - check API key permissions';
                          break;
                        case google.maps.DirectionsStatus.UNKNOWN_ERROR:
                          errorMessage = 'Unknown error occurred';
                          break;
                        default:
                          errorMessage = 'Directions request failed: ' + status;
                      }

                      console.error('🚨 Error details:', errorMessage);

                      // Send error information back to React Native
                      if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'routeError',
                          error: errorMessage,
                          status: status
                        }));
                      }

                      // Fallback to straight line if directions fail
                      console.log('🔄 Falling back to straight line route...');
                      displayFallbackRoute(origin, destination);
                    }
                  });
                }

                // Fallback function for straight line route
                function displayFallbackRoute(origin, destination) {
                  var fallbackPath = [origin, destination];

                  // Create base silver route line for fallback
                  var baseFallbackPolyline = new google.maps.Polyline({
                    path: fallbackPath,
                    geodesic: true,
                    strokeColor: '#A8A8A8',
                    strokeOpacity: 0.8,
                    strokeWeight: 6,
                    zIndex: 1
                  });
                  baseFallbackPolyline.setMap(map);
                  routePolylines.push(baseFallbackPolyline);

                  // Create animated shimmer overlay for fallback
                  var shimmerFallbackPolyline = new google.maps.Polyline({
                    path: fallbackPath,
                    geodesic: true,
                    strokeColor: '#FFFFFF',
                    strokeOpacity: 0,
                    strokeWeight: 4,
                    zIndex: 2
                  });
                  shimmerFallbackPolyline.setMap(map);
                  routePolylines.push(shimmerFallbackPolyline);

                  // Implement sweeping shimmer animation for fallback
                  var fallbackShimmerProgress = 0;
                  var fallbackShimmerSpeed = 0.008;
                  var fallbackShimmerPause = 0;
                  var fallbackPauseDuration = 60;

                  function animateFallbackShimmer() {
                    if (fallbackShimmerPause > 0) {
                      fallbackShimmerPause--;
                      requestAnimationFrame(animateFallbackShimmer);
                      return;
                    }

                    fallbackShimmerProgress += fallbackShimmerSpeed;

                    if (fallbackShimmerProgress >= 1) {
                      fallbackShimmerProgress = 0;
                      fallbackShimmerPause = fallbackPauseDuration;
                    }

                    // Create sweeping effect for fallback
                    var peakOpacity = 0.8;
                    var sweepWidth = 0.3;
                    var sweepCenter = fallbackShimmerProgress;
                    var distanceFromCenter = Math.abs(0.5 - sweepCenter);
                    var opacity = Math.max(0, peakOpacity * (1 - distanceFromCenter / sweepWidth));

                    shimmerFallbackPolyline.setOptions({
                      strokeOpacity: opacity,
                      strokeColor: '#F0F0F0'
                    });

                    requestAnimationFrame(animateFallbackShimmer);
                  }

                  // Start fallback animation
                  animateFallbackShimmer();

                  // Fit map to show fallback route
                  var fallbackBounds = new google.maps.LatLngBounds();
                  fallbackPath.forEach(function(point) {
                    fallbackBounds.extend(point);
                  });
                  map.fitBounds(fallbackBounds, { top: 100, right: 50, bottom: 300, left: 50 });
                }

                // Driver markers management
                let driverMarkers = new Map();

                function initializeDriverMarkers() {
                  console.log('🚗 Initializing driver markers system...');

                  // Create car icon for drivers
                  window.createDriverIcon = function(vehicleType, heading) {
                    return {
                      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(\`
                        <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                              <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.3"/>
                            </filter>
                          </defs>
                          <!-- Car body -->
                          <ellipse cx="16" cy="20" rx="12" ry="6" fill="#E91E63" stroke="#fff" stroke-width="2" filter="url(#shadow)"/>
                          <!-- Car top -->
                          <ellipse cx="16" cy="16" rx="8" ry="4" fill="#F9E6F7" stroke="#E91E63" stroke-width="1"/>
                          <!-- Windshield -->
                          <ellipse cx="16" cy="14" rx="5" ry="2" fill="#87CEEB" opacity="0.7"/>
                          <!-- Direction indicator -->
                          <circle cx="16" cy="12" r="2" fill="#fff"/>
                        </svg>
                      \`),
                      scaledSize: new google.maps.Size(32, 32),
                      anchor: new google.maps.Point(16, 16),
                      rotation: heading || 0
                    };
                  };

                  // Function to update all driver markers
                  window.updateDriverMarkers = function(drivers) {
                    console.log('🚗 Updating driver markers:', drivers.length);

                    // Clear existing markers
                    driverMarkers.forEach(marker => marker.setMap(null));
                    driverMarkers.clear();

                    // Add new markers
                    drivers.forEach(driver => {
                      addDriverMarkerToMap(driver);
                    });
                  };

                  // Function to add a single driver marker
                  window.addDriverMarker = function(driver) {
                    console.log('🚗 Adding driver marker:', driver.name);
                    addDriverMarkerToMap(driver);
                  };

                  // Function to remove a driver marker
                  window.removeDriverMarker = function(driverId) {
                    console.log('🚗 Removing driver marker:', driverId);
                    const marker = driverMarkers.get(driverId);
                    if (marker) {
                      marker.setMap(null);
                      driverMarkers.delete(driverId);
                    }
                  };

                  // Function to update a driver marker position
                  window.updateDriverMarker = function(driver) {
                    console.log('🚗 Updating driver marker:', driver.name);
                    const marker = driverMarkers.get(driver.id);
                    if (marker) {
                      marker.setPosition({ lat: driver.lat, lng: driver.lng });
                      marker.setIcon(window.createDriverIcon(driver.vehicleType, driver.heading));
                    } else {
                      addDriverMarkerToMap(driver);
                    }
                  };
                }

                function addDriverMarkerToMap(driver) {
                  const marker = new google.maps.Marker({
                    position: { lat: driver.lat, lng: driver.lng },
                    map: map,
                    icon: window.createDriverIcon(driver.vehicleType, driver.heading),
                    title: \`\${driver.name} • \${driver.estimatedArrival} min away\`,
                    zIndex: 1000,
                    animation: google.maps.Animation.DROP
                  });

                  // Create info window for driver details
                  const infoWindow = new google.maps.InfoWindow({
                    content: \`
                      <div style="padding: 8px; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
                        <div style="font-weight: 600; color: #E91E63; margin-bottom: 4px;">\${driver.name}</div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 2px;">⭐ \${driver.rating}/5.0</div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 2px;">🚗 \${driver.vehicleType}</div>
                        <div style="font-size: 12px; color: #E91E63; font-weight: 500;">📍 \${driver.estimatedArrival} min away</div>
                      </div>
                    \`
                  });

                  marker.addListener('click', () => {
                    // Close other info windows
                    driverMarkers.forEach(otherMarker => {
                      if (otherMarker.infoWindow) {
                        otherMarker.infoWindow.close();
                      }
                    });

                    infoWindow.open(map, marker);
                  });

                  marker.infoWindow = infoWindow;
                  driverMarkers.set(driver.id, marker);

                  // Add subtle bounce animation
                  setTimeout(() => {
                    marker.setAnimation(null);
                  }, 1000);
                }

                function initMap() {
                  try {
                    console.log('🗺️ Initializing Google Maps...');

                    // Check if Google Maps API is loaded
                    if (typeof google === 'undefined' || !google.maps) {
                      throw new Error('Google Maps API not loaded');
                    }

                    // Initialize Google Map with Uber-style clean design
                    map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: mapCenterLat, lng: mapCenterLng },
                    zoom: ${selectedLocation ? 16 : 15},
                    styles: [
                      {
                        "featureType": "all",
                        "elementType": "geometry.fill",
                        "stylers": [{"color": "#f5f5f5"}]
                      },
                      {
                        "featureType": "water",
                        "elementType": "geometry.fill",
                        "stylers": [{"color": "#b0d4f4"}]
                      },
                      {
                        "featureType": "landscape.natural",
                        "elementType": "geometry.fill",
                        "stylers": [{"color": "#b8dccc"}]
                      },
                      {
                        "featureType": "road",
                        "elementType": "geometry.fill",
                        "stylers": [{"color": "#ffffff"}]
                      },
                      {
                        "featureType": "road",
                        "elementType": "geometry.stroke",
                        "stylers": [{"color": "#e0e0e0"}, {"weight": 1}]
                      },
                      {
                        "featureType": "poi",
                        "elementType": "labels",
                        "stylers": [{"visibility": "off"}]
                      },
                      {
                        "featureType": "transit",
                        "elementType": "labels",
                        "stylers": [{"visibility": "off"}]
                      }
                    ],
                    disableDefaultUI: true,
                    zoomControl: false,
                    mapTypeControl: false,
                    streetViewControl: false,
                    fullscreenControl: false,
                    gestureHandling: 'greedy'
                  });

                  // Initialize directions service and renderer
                  directionsService = new google.maps.DirectionsService();
                  directionsRenderer = new google.maps.DirectionsRenderer({
                    suppressMarkers: true,
                    polylineOptions: {
                      strokeColor: '#C0C0C0',
                      strokeWeight: 4,
                      strokeOpacity: 0.8
                    }
                  });
                  directionsRenderer.setMap(map);

                  // Add pickup location marker (current location)
                  pickupMarker = new google.maps.Marker({
                    position: { lat: ${userLocation[1]}, lng: ${userLocation[0]} },
                    map: map,
                    icon: {
                      path: google.maps.SymbolPath.CIRCLE,
                      fillColor: '#4CAF50',
                      fillOpacity: 1,
                      strokeColor: '#ffffff',
                      strokeWeight: 3,
                      scale: 8
                    },
                    title: 'Your location'
                  });

                  // Add destination marker if location is selected
                  ${selectedLocation ? `
                    // Validate destination coordinates before using them
                    var destLat = ${parseFloat(selectedLocation.lat)};
                    var destLng = ${parseFloat(selectedLocation.lon)};

                    if (isNaN(destLat) || isNaN(destLng)) {
                      console.error('🚨 Invalid destination coordinates in WebView:', { lat: destLat, lng: destLng });
                      // Send error back to React Native
                      if (window.ReactNativeWebView) {
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'coordinateError',
                          error: 'Invalid destination coordinates'
                        }));
                      }
                    } else {
                      destinationMarker = new google.maps.Marker({
                        position: { lat: destLat, lng: destLng },
                        map: map,
                        icon: {
                          path: google.maps.SymbolPath.CIRCLE,
                          fillColor: '#E91E63',
                          fillOpacity: 1,
                          strokeColor: '#ffffff',
                          strokeWeight: 3,
                          scale: 10
                        },
                        title: '${selectedLocation.display_name.replace(/'/g, "\\'")}'
                      });

                      // Display route with sweeping animation using Google Directions API
                      displayRouteWithAnimation(
                        { lat: ${userLocation[1]}, lng: ${userLocation[0]} },
                        { lat: destLat, lng: destLng }
                      );
                    }
                  ` : ''}

                  console.log('✅ Google Maps initialized successfully');

                  // Initialize driver markers system
                  initializeDriverMarkers();

                } catch (error) {
                  console.error('🚨 Error initializing Google Maps:', error);

                  // Send error back to React Native
                  if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'mapInitError',
                      error: error.message || 'Failed to initialize Google Maps'
                    }));
                  }

                  // Show error message on map
                  document.getElementById('map').innerHTML =
                    '<div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; color: #666; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">' +
                    '<div style="text-align: center; padding: 20px;">' +
                    '<div style="font-size: 48px; margin-bottom: 16px;">🗺️</div>' +
                    '<div style="font-size: 18px; font-weight: 500; margin-bottom: 8px;">Map Loading Error</div>' +
                    '<div style="font-size: 14px;">Please check your internet connection</div>' +
                    '</div>' +
                    '</div>';
                }
                }

                // Map position is now fixed - no automatic recentering based on bottom sheet position

                // Pass route data to window for road-following routes
                window.routeData = ${routeData ? JSON.stringify(routeData) : 'null'};
              </script>
            </body>
            </html>
          `
        }}
        javaScriptEnabled={true}
        // Minimal configuration - no interference
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />

      {/* Header with Hamburger Menu */}
      <RNAnimated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            paddingTop: insets.top + 16,
          }
        ]}
      >
        <TouchableOpacity style={styles.hamburgerButton}>
          <Ionicons name="menu" size={28} color={COLORS.WHITE} />
        </TouchableOpacity>
        <View style={styles.headerSpacer} />
        <TouchableOpacity style={styles.profileButton}>
          <Ionicons name="person-circle" size={32} color={COLORS.WHITE} />
        </TouchableOpacity>
      </RNAnimated.View>

      {/* SheMove Bottom Sheet with Industry-Standard Gesture Handling */}
      <SheBottomSheet
        ref={bottomSheetRef}
        snapPoints={['20%', '60%', '90%']}
        initialSnapIndex={0}
        onSnapPointChange={handleSheetChange}
        keyboardBehavior="interactive"
        enableScrollableContent={true}
        showScrollIndicator={true}
      >

          {/* Simple Content */}
          {showTripPreview && selectedLocation ? (
            <TripPreview
              pickupLocation={{ lat: userLocation[1], lng: userLocation[0] }}
              pickupAddress={pickupAddress}
              destination={selectedLocation}
              selectedRideType={selectedRideType}
              routeData={routeData}
              googleRouteData={googleRouteData}
              onRideTypeChange={handleRideTypeChange}
              onBookRide={handleBookRide}
              onBack={handleBackFromTripPreview}
              isBookingRide={isBookingRide}
              bookingError={bookingError}
              disableInternalScroll={true}
            />
          ) : (
            <View>
              <Text style={styles.sheetTitle}>Let's go places 🌸</Text>

              {/* Simple Search Bar */}
              <TouchableOpacity
                style={styles.searchContainer}
                activeOpacity={1}
                onPress={() => {
                  console.log('🔍 Search container tapped - ensuring expansion');
                  ensureBottomSheetExpanded();
                }}
              >
                <View style={styles.searchBar}>
                  <Ionicons name="search" size={20} color={COLORS.MEDIUM_TEXT} style={styles.searchIcon} />
                  <TextInput
                    style={styles.searchInput}
                    placeholder="Where to?"
                    placeholderTextColor={COLORS.MEDIUM_TEXT}
                    value={searchText}
                    onChangeText={handleSearchTextChange}
                    onFocus={handleSearchFocus}
                    onSubmitEditing={() => performSearch(searchText)}
                    returnKeyType="search"
                  />
                </View>
              </TouchableOpacity>

              {/* Search Results */}
              <SearchResults
                results={searchResults}
                isLoading={isSearching}
                onResultPress={handleSearchResultPress}
                visible={showSearchResults}
              />

              {/* Recent Searches */}
              <RecentSearches
                recentSearches={recentSearches}
                onRecentSearchPress={handleRecentSearchPress}
                visible={showRecentSearches}
              />
            </View>
          )}
      </SheBottomSheet>

      {/* Mobile Navigation Bar */}
      <View style={[styles.bottomNav, { paddingBottom: insets.bottom }]}>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="home" size={24} color={COLORS.ACCENT_PINK} />
          <Text style={[styles.navText, styles.navTextActive]}>Home</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.navItem} onPress={() => router.push('/BookRidePage')}>
          <Ionicons name="car" size={24} color={COLORS.MEDIUM_TEXT} />
          <Text style={styles.navText}>Rides</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.navItem} onPress={handleLogout}>
          <Ionicons name="person" size={24} color={COLORS.MEDIUM_TEXT} />
          <Text style={styles.navText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.DARK_TEXT,
  },
  mapBackground: {
    position: 'absolute',
    width: width,
    height: height,
    top: 0,
    left: 0,
    zIndex: 0,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
    zIndex: 2,
  },
  hamburgerButton: {
    padding: 8,
  },
  headerSpacer: {
    flex: 1,
  },
  profileButton: {
    padding: 4,
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  serviceCard: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  cardIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    textAlign: 'center',
  },
  searchContainer: {
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  searchResultsContainer: {
    minHeight: 200, // Ensure minimum visible height
    maxHeight: height * 0.4, // Allow reasonable height for scrolling
    marginTop: 8,
    zIndex: 1000, // Very high z-index to ensure visibility
    backgroundColor: 'transparent', // Let child components handle background
    // Force visibility and proper positioning
    position: 'relative',
    overflow: 'visible',
    opacity: 1,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DARK_TEXT,
  },
  scheduleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_PINK,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  scheduleText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
    marginLeft: 4,
  },
  recentTripsHeader: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  recentTripsContainer: {
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginBottom: 16,
  },
  tripItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  tripIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  tripDetails: {
    flex: 1,
  },
  tripDestination: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 4,
  },
  tripDate: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
  },
  tripPrice: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  // Enhanced Bottom Sheet Styles with Reanimated
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
    zIndex: 0,
  },
  bottomSheet: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: SHEET_HEIGHT,
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 12,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowRadius: 20,
    shadowOpacity: 0.15,
    elevation: 20,
    zIndex: 2,
    // Remove overflow hidden to allow scrolling
    overflow: 'visible',
  },
  sheetHandle: {
    alignSelf: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
  },
  sheetHandleBar: {
    width: 36,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.BORDER,
    opacity: 0.6,
  },
  sheetTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    textAlign: 'center',
    marginBottom: 24,
  },
  sheetScrollView: {
    flex: 1,
    marginTop: 16,
    // Ensure ScrollView can expand beyond visible area
    maxHeight: height * 0.6, // Allow scrolling within reasonable bounds
  },
  sheetScrollContent: {
    paddingBottom: 120, // Increased space for bottom navigation and safe area
    flexGrow: 1, // Allow content to grow and be scrollable
  },

  // Bottom Navigation Styles
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    paddingTop: 12,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: -2 },
    elevation: 10,
    zIndex: 3,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 4,
  },
  navTextActive: {
    color: COLORS.ACCENT_PINK,
    fontWeight: '600',
  },
  tripMeta: {
    fontSize: 12,
    color: COLORS.LIGHT_TEXT,
    marginTop: 2,
  },
  tripPriceContainer: {
    alignItems: 'flex-end',
  },
  tripCancelled: {
    fontSize: 12,
    color: COLORS.ERROR,
    marginTop: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
  },
  emptyTripsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTripsText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 8,
  },
  emptyTripsSubtext: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
    marginTop: 4,
  },

});
