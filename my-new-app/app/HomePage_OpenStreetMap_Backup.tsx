import React, { useRef, useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Animated,
  Easing,
  Dimensions,
  SafeAreaView,
  Image,
  TouchableWithoutFeedback,
  ActivityIndicator
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { PanGestureHandler } from 'react-native-gesture-handler';
import ReanimatedAnimated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  runOnJS,
  withTiming,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { WebView } from 'react-native-webview';
import * as Location from 'expo-location';
import NewIcon from '../assets/new.svg';
import { geocodingService, SearchResult } from '../services/geocodingService';
import SearchResults from '../components/SearchResults';
import RecentSearches from '../components/RecentSearches';
import TripPreview from '../components/TripPreview';
import { distanceService, Coordinates } from '../services/distanceService';
import { fareService, RideType, FareBreakdown } from '../services/fareService';
import { Driver } from '../services/driverService';
import { routingService, RouteCoordinate } from '../services/routingService';
import { useAuth } from '../contexts/AuthContext';
import { debounceSearch } from '../utils/debounce';
import { searchHistoryService, SearchHistoryEntry } from '../services/searchHistoryService';
import { recentTripsService, TripDisplayData } from '../services/recentTripsService';

const { width, height } = Dimensions.get('window');

// Bottom sheet configuration with improved snap positions
const SHEET_HEIGHT = height * 0.9; // Total sheet height

// Redesigned snap positions for better UX
const SNAP_POINTS = [
  height - 100, // Position 1 (Collapsed): Only "Let's go places" text
  height * 0.45, // Position 2 (Default/Medium): Cards + search + recent trips header
  height * 0.15, // Position 3 (Expanded): Everything including full trip list
];

// Simple transition configuration - no complex springs
const TRANSITION_DURATION = 300; // Simple animation duration in ms
const VELOCITY_THRESHOLD = 300; // Minimum velocity to trigger snap to next position

// Enhanced color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
  SUCCESS: '#4CAF50',
  ERROR: '#F44336',
};

// Recent trips will be loaded from database

export default function HomePage() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { signOut, user } = useAuth();

  // Initialize services with user ID
  useEffect(() => {
    if (user?.id) {
      searchHistoryService.setUserId(user.id);
      recentTripsService.setUserId(user.id);
      loadRecentSearches();
      loadRecentTrips();
    }
  }, [user?.id]);

  const loadRecentSearches = async () => {
    try {
      const searches = await searchHistoryService.getRecentSearches(5);
      setRecentSearches(searches);
    } catch (error) {
      console.error('Error loading recent searches:', error);
    }
  };

  const loadRecentTrips = async () => {
    setIsLoadingTrips(true);
    try {
      const trips = await recentTripsService.getRecentTrips(3); // Show 3 recent trips
      setRecentTrips(trips);
    } catch (error) {
      console.error('Error loading recent trips:', error);
    } finally {
      setIsLoadingTrips(false);
    }
  };

  // Refs for gesture handling
  const webViewRef = useRef<WebView>(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Reanimated values for bottom sheet
  const translateY = useSharedValue(SNAP_POINTS[1]); // Start at medium position (default)
  const context = useSharedValue({ y: 0 });

  // State
  const [searchText, setSearchText] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentSnapPoint, setCurrentSnapPoint] = useState(1); // Start at medium position

  // Search history state
  const [recentSearches, setRecentSearches] = useState<any[]>([]);
  const [showRecentSearches, setShowRecentSearches] = useState(false);

  // Recent trips state
  const [recentTrips, setRecentTrips] = useState<TripDisplayData[]>([]);
  const [isLoadingTrips, setIsLoadingTrips] = useState(false);

  // Location state
  const [userLocation, setUserLocation] = useState([28.0473, -26.2041]); // Default to Johannesburg, South Africa [lng, lat]
  const [locationPermission, setLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(true);

  // Search state
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<SearchResult | null>(null);
  const [mapMarkers, setMapMarkers] = useState<Array<{ lat: number; lng: number; name: string }>>([]);

  // Trip preview state
  const [showTripPreview, setShowTripPreview] = useState(false);
  const [selectedRideType, setSelectedRideType] = useState<RideType>('SheRide');
  const [pickupAddress, setPickupAddress] = useState('Current location');
  const [pickupAddressData, setPickupAddressData] = useState<SearchResult | null>(null);

  // Simplified state - no route animations
  const [routeData, setRouteData] = useState<any>(null); // Store route data for trip preview

  // Location functions
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        setLocationPermission(false);
        setIsLoadingLocation(false);
        console.log('Location permission denied');

        // Fallback: try to detect if user is in South Africa based on timezone
        try {
          const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          if (timezone.includes('Africa/Johannesburg') ||
              timezone.includes('Africa/Cape_Town') ||
              timezone.includes('Africa/Durban')) {
            geocodingService.setUserCountry('za');
            console.log('Detected South African timezone, prioritizing local results');
          }
        } catch (error) {
          console.warn('Could not detect timezone:', error);
        }
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setLocationPermission(false);
      setIsLoadingLocation(false);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });
      const { latitude, longitude } = location.coords;
      setUserLocation([longitude, latitude]); // Note: Leaflet uses [lng, lat] format

      // Set user location in geocoding service for location-based search prioritization
      await geocodingService.setUserLocation(latitude, longitude);

      // Get the actual address for pickup location with full address data
      try {
        const addressResult = await geocodingService.reverseGeocode(latitude, longitude);
        if (addressResult) {
          const shortAddress = geocodingService.getShortAddress(addressResult);
          setPickupAddress(shortAddress);
          setPickupAddressData(addressResult); // Store full address data for map bubble formatting
        }
      } catch (error) {
        console.warn('Could not get pickup address:', error);
        setPickupAddress('Current location');
        setPickupAddressData(null);
      }

      setIsLoadingLocation(false);
    } catch (error) {
      console.error('Error getting current location:', error);
      setIsLoadingLocation(false);
    }
  };

  // Search functionality with smart debouncing

  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setIsSearching(true);
    setShowSearchResults(true);

    try {
      // Store search query in history
      if (user?.id) {
        await searchHistoryService.storeSearch({
          searchQuery: query,
          searchContext: 'destination',
          locationContext: [userLocation[1], userLocation[0]], // [lat, lng]
        });
      }

      // Use enhanced search with LocationIQ fallback for better house number results
      const response = await geocodingService.searchLocationsEnhanced(query, 5);
      if (response.results) {
        setSearchResults(response.results);

        // Log provider usage for monitoring
        if (response.provider) {
          console.log(`🔍 Search "${query}" - Provider: ${response.provider} - Results: ${response.results.length}`);
        }
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Create smart debounced search function
  const debouncedSearch = useCallback(
    debounceSearch((query: string) => {
      performSearch(query);
    }, 300, 2), // 300ms delay, minimum 2 characters
    []
  );

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);

    // Clear search results if text is empty
    if (!text.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    // Use smart debounced search
    debouncedSearch(text);
  };

  const handleSearchResultPress = async (result: SearchResult) => {
    setSelectedLocation(result);
    setSearchText(geocodingService.getShortAddress(result));
    setShowSearchResults(false);

    // Mark search result as clicked in history
    if (user?.id && searchText) {
      await searchHistoryService.markResultClicked(searchText, result);
      // Update recent destinations
      await searchHistoryService.updateRecentDestination(
        result.display_name,
        [parseFloat(result.lat), parseFloat(result.lon)]
      );
      // Reload recent searches to show updated data
      loadRecentSearches();
    }

    // Update map with selected location
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);
    // Use smart address formatting for map marker (will be formatted again in generateMapHTML for consistency)
    const newMarker = { lat, lng, name: result.display_name };
    setMapMarkers([newMarker]);

    // Fetch real route
    await fetchRoute({ lat: userLocation[1], lng: userLocation[0] }, { lat, lng });

    // Show trip preview (will use route data if available)
    setShowTripPreview(true);

    console.log('🎯 Trip preview activated with route data:', routeData ? 'Available' : 'Not available');

    // Expand bottom sheet to full height for trip preview
    setTimeout(() => {
      translateY.value = withTiming(SNAP_POINTS[2], { duration: TRANSITION_DURATION });
      updateSnapPoint(2);
    }, 100);

    // Haptic feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Simplified route fetching - no animations
  const fetchRoute = async (start: RouteCoordinate, end: RouteCoordinate) => {
    try {
      const route = await routingService.getRoute(start, end);
      if (route) {
        // Store route data for trip preview calculations
        setRouteData(route);
      } else {
        setRouteData(null);
      }
    } catch (error) {
      console.error('Error fetching route:', error);
      setRouteData(null);
    }
  };

  useEffect(() => {
    // Initialize animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        easing: Easing.out(Easing.back(1.1)),
        useNativeDriver: true,
      }),
    ]).start();

    // Ensure bottom sheet starts at medium position with simple animation
    setTimeout(() => {
      translateY.value = withTiming(SNAP_POINTS[1], { duration: TRANSITION_DURATION });
    }, 100);

    // Request location permission and get current location
    requestLocationPermission();
  }, []);

  // Simple snap point detection - find closest position
  const findClosestSnapPoint = useCallback((y: number, velocity: number) => {
    'worklet';

    // Find the closest snap point
    let closest = 0;
    let minDistance = Math.abs(SNAP_POINTS[0] - y);

    for (let i = 1; i < SNAP_POINTS.length; i++) {
      const distance = Math.abs(SNAP_POINTS[i] - y);
      if (distance < minDistance) {
        minDistance = distance;
        closest = i;
      }
    }

    // If velocity is high enough, move in velocity direction
    if (Math.abs(velocity) > VELOCITY_THRESHOLD) {
      if (velocity > 0 && closest < SNAP_POINTS.length - 1) {
        return closest + 1; // Move down (more collapsed)
      } else if (velocity < 0 && closest > 0) {
        return closest - 1; // Move up (more expanded)
      }
    }

    return closest;
  }, []);

  // Uber-like haptic feedback system
  const updateSnapPoint = useCallback((index: number) => {
    setCurrentSnapPoint(index);

    // Recenter map based on new bottom sheet position
    if (webViewRef.current) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'recenterMap',
        snapPoint: index
      }));
    }

    // Uber-style haptic feedback - different intensities for different states
    switch (index) {
      case 2: // Fully expanded
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 1: // Medium position
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 0: // Collapsed
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Soft);
        break;
    }
  }, []);

  // Simple gesture handler - basic swipe functionality
  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.y = translateY.value;
    },
    onActive: (event, context) => {
      // Simple drag with basic constraints
      const newY = context.y + event.translationY;

      // Basic bounds checking - prevent dragging beyond limits
      if (newY >= SNAP_POINTS[2] && newY <= SNAP_POINTS[0]) {
        translateY.value = newY;
      }
    },
    onEnd: (event) => {
      // Simple snap to closest position
      const targetIndex = findClosestSnapPoint(translateY.value, event.velocityY);

      // Simple timing-based animation instead of spring
      translateY.value = withTiming(SNAP_POINTS[targetIndex], {
        duration: TRANSITION_DURATION,
      });

      runOnJS(updateSnapPoint)(targetIndex);
    },
  });

  // Animated style for bottom sheet
  const bottomSheetStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  // Animated style for backdrop opacity
  const backdropStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      translateY.value,
      [SNAP_POINTS[2], SNAP_POINTS[0]],
      [0.3, 0],
      Extrapolate.CLAMP
    );
    return {
      opacity,
    };
  });

  const handleRidePress = () => {
    router.push('/BookRidePage');
  };

  const handleSchedulePress = () => {
    setShowDatePicker(true);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.replace('/SplashPage');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Handle recent search selection
  const handleRecentSearchPress = (searchHistory: any) => {
    if (searchHistory.result_address && searchHistory.result_coordinates) {
      // Create a SearchResult-like object from search history
      const result: SearchResult = {
        place_id: searchHistory.id,
        display_name: searchHistory.result_address,
        lat: searchHistory.result_coordinates[0].toString(),
        lon: searchHistory.result_coordinates[1].toString(),
        type: 'recent',
        importance: 1,
        address: {}
      };

      // Use the existing search result handler
      handleSearchResultPress(result);
    } else {
      // If no coordinates, just set the search text and perform search
      setSearchText(searchHistory.search_query);
      performSearch(searchHistory.search_query);
    }
    setShowRecentSearches(false);
  };

  // Handle tapping outside search results to close them
  const handleContainerPress = () => {
    if (showSearchResults) {
      setShowSearchResults(false);
    }
  };

  // Handle search input focus - expand bottom sheet for better visibility
  const handleSearchFocus = () => {
    // Expand to the top position (index 2) when user starts typing
    if (currentSnapPoint !== 2) {
      translateY.value = withTiming(SNAP_POINTS[2], { duration: TRANSITION_DURATION });
      updateSnapPoint(2);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Show search results if we have any, otherwise show recent searches
    if (searchResults.length > 0) {
      setShowSearchResults(true);
      setShowRecentSearches(false);
    } else if (recentSearches.length > 0 && !searchText.trim()) {
      setShowRecentSearches(true);
      setShowSearchResults(false);
    }
  };

  // Handle search input blur - optionally collapse back to medium position
  const handleSearchBlur = () => {
    // Hide recent searches when losing focus
    setShowRecentSearches(false);

    // Only collapse if no search results are showing and search text is empty and not in trip preview
    if (!showSearchResults && !searchText.trim() && !showTripPreview) {
      setTimeout(() => {
        if (currentSnapPoint === 2) {
          translateY.value = withTiming(SNAP_POINTS[1], { duration: TRANSITION_DURATION });
          updateSnapPoint(1);
        }
      }, 200); // Small delay to allow for result selection
    }
  };

  // Trip preview handlers
  const handleBackFromTripPreview = () => {
    setShowTripPreview(false);
    setSelectedLocation(null);
    setSearchText('');
    setMapMarkers([]);
    setRouteData(null); // Clear route data

    // Collapse to medium position
    translateY.value = withTiming(SNAP_POINTS[1], { duration: TRANSITION_DURATION });
    updateSnapPoint(1);
  };

  const handleRideTypeChange = (rideType: RideType) => {
    setSelectedRideType(rideType);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBookRide = (driver: Driver, fare: FareBreakdown) => {
    // TODO: Implement actual booking logic
    console.log('Booking ride:', {
      driver: driver.name,
      rideType: selectedRideType,
      fare: fare.totalFare,
      pickup: pickupAddress,
      destination: selectedLocation?.display_name,
    });

    // For now, show an alert
    alert(`Ride booked with ${driver.name} for ${fareService.formatPrice(fare.totalFare)}`);

    // Reset state
    handleBackFromTripPreview();
  };

  // Google-style address formatting for map bubbles with house number support
  const formatMapBubbleAddress = (displayName: string, addressData?: any): string => {
    // If we have structured address data, use it for smart formatting
    if (addressData) {
      return formatStructuredMapAddress(addressData, displayName);
    }

    // Fallback to display_name parsing with house number extraction
    return formatDisplayNameMapAddress(displayName);
  };

  const formatStructuredMapAddress = (address: any, displayName: string): string => {
    // Strategy 1: Specific Address (House + Street) - PRIORITY for addresses like "3 Aries Road"
    if (address.house_number && address.road) {
      const primary = `${address.house_number} ${address.road}`;
      const city = address.city || address.town || address.suburb;
      return city ? `${primary}, ${city}` : primary;
    }

    // Strategy 2: Street/Road Only (but check if house number is in display_name)
    if (address.road) {
      const houseNumberFromDisplay = extractHouseNumberFromDisplay(displayName, address.road);
      const primary = houseNumberFromDisplay ? `${houseNumberFromDisplay} ${address.road}` : address.road;
      const city = address.city || address.town || address.suburb;
      return city ? `${primary}, ${city}` : primary;
    }

    // Strategy 3: Business/POI Name (from display_name first part)
    const displayParts = displayName.split(',').map(part => part.trim());
    if (displayParts.length > 0 && !isGenericLocationName(displayParts[0])) {
      const city = address.city || address.town || address.suburb;
      return city ? `${displayParts[0]}, ${city}` : displayParts[0];
    }

    // Fallback to display name parsing
    return formatDisplayNameMapAddress(displayName);
  };

  const formatDisplayNameMapAddress = (displayName: string): string => {
    const parts = displayName.split(',').map(part => part.trim());

    if (parts.length === 0) return 'Unknown location';
    if (parts.length === 1) return parts[0];

    // For map bubbles, show primary location + city (max 2 parts)
    // Filter out obvious/redundant information for South African users
    const filteredParts = parts.filter(part => {
      const lowerPart = part.toLowerCase();
      return !lowerPart.includes('south africa') &&
             !lowerPart.includes('gauteng') &&
             !lowerPart.includes('western cape') &&
             !lowerPart.includes('kwazulu-natal') &&
             !lowerPart.includes('eastern cape') &&
             !lowerPart.includes('free state') &&
             !lowerPart.includes('limpopo') &&
             !lowerPart.includes('mpumalanga') &&
             !lowerPart.includes('north west') &&
             !lowerPart.includes('northern cape');
    });

    return filteredParts.slice(0, 2).join(', ');
  };

  const extractHouseNumberFromDisplay = (displayName: string, roadName: string): string | null => {
    // Look for house number in the first part of display_name
    const firstPart = displayName.split(',')[0].trim();

    // Check if first part contains both number and road name
    if (firstPart.toLowerCase().includes(roadName.toLowerCase())) {
      // Extract number from the beginning of the string
      const numberMatch = firstPart.match(/^(\d+)\s+/);
      return numberMatch ? numberMatch[1] : null;
    }

    return null;
  };

  const isGenericLocationName = (text: string): boolean => {
    const genericTerms = [
      'city', 'town', 'village', 'municipality', 'ward', 'district',
      'province', 'region', 'area', 'zone', 'sector'
    ];

    const lowerText = text.toLowerCase();
    return genericTerms.some(term => lowerText.includes(term));
  };

  // Generate HTML for WebView to avoid JSX parsing issues
  const generateMapHTML = () => {
    let html = '';

    // Add clean user location marker when no destination is selected (Uber-style)
    if (!selectedLocation) {
      // Use pickup address data for smart house number formatting
      const formattedPickupAddress = formatMapBubbleAddress(pickupAddress, pickupAddressData?.address);
      html += `
        var userLocationIcon = L.divIcon({
          className: 'custom-div-icon',
          html: '<div style="background-color: #1976D2; width: 16px; height: 16px; border-radius: 50%; border: 4px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
          iconSize: [24, 24],
          iconAnchor: [12, 12]
        });

        var userLocationMarker = L.marker([${userLocation[1]}, ${userLocation[0]}], {icon: userLocationIcon})
          .addTo(map)
          .bindPopup('📍 ${formattedPickupAddress.replace(/'/g, "\\'")}');
      `;
    }

    // Add clean start and destination markers when location is selected (Uber-style)
    if (mapMarkers.length > 0) {
      mapMarkers.forEach(marker => {
        // Use pickup address data for smart house number formatting
        const formattedPickupAddress = formatMapBubbleAddress(pickupAddress, pickupAddressData?.address);
        // Pass selectedLocation's address data for smart house number formatting
        const formattedDestinationAddress = formatMapBubbleAddress(marker.name, selectedLocation?.address);

        html += `
          // Clean Starting Point Marker (Uber-style blue dot)
          var startIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #1976D2; width: 12px; height: 12px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
            iconSize: [18, 18],
            iconAnchor: [9, 9]
          });

          var startMarker = L.marker([${userLocation[1]}, ${userLocation[0]}], {icon: startIcon})
            .addTo(map)
            .bindPopup('📍 ${formattedPickupAddress.replace(/'/g, "\\'")}');

          // Clean Destination Marker (Uber-style with SheMove pink)
          var endIcon = L.divIcon({
            className: 'custom-div-icon',
            html: '<div style="background-color: #E91E63; width: 14px; height: 14px; border-radius: 2px; border: 2px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.2);"></div>',
            iconSize: [18, 18],
            iconAnchor: [9, 9]
          });

          var endMarker = L.marker([${marker.lat}, ${marker.lng}], {icon: endIcon})
            .addTo(map)
            .bindPopup('🎯 ${formattedDestinationAddress.replace(/'/g, "\\'")}')
            .openPopup();

          // Add road-following route line using actual route coordinates
          var routeCoordinates = [];

          // Use real route data if available, otherwise fallback to straight line
          if (window.routeData && window.routeData.coordinates && window.routeData.coordinates.length > 0) {
            // Convert route coordinates to Leaflet format [lat, lng]
            routeCoordinates = window.routeData.coordinates.map(function(coord) {
              return [coord.lat, coord.lng];
            });
            console.log('🛣️ Using real route with', routeCoordinates.length, 'points');
          } else {
            // Fallback to straight line if no route data
            routeCoordinates = [
              [${userLocation[1]}, ${userLocation[0]}],
              [${marker.lat}, ${marker.lng}]
            ];
            console.log('📏 Using straight line fallback');
          }

          // Create base silver route line
          var routeLine = L.polyline(routeCoordinates, {
            color: '#A8A8A8',
            weight: 6,
            opacity: 0.8,
            smoothFactor: 1,
            lineCap: 'round',
            lineJoin: 'round'
          }).addTo(map);

          // Create animated shimmer overlay
          var shimmerLine = L.polyline(routeCoordinates, {
            color: '#FFFFFF',
            weight: 4,
            opacity: 0,
            smoothFactor: 1,
            lineCap: 'round',
            lineJoin: 'round'
          }).addTo(map);

          // Create smooth sweeping shimmer effect from pickup to destination
          var shimmerProgress = 0;
          var shimmerSpeed = 0.008; // Much slower for smoother effect
          var shimmerPause = 0;
          var pauseDuration = 60; // Pause between sweeps

          function animateShimmer() {
            if (shimmerPause > 0) {
              shimmerPause--;
              requestAnimationFrame(animateShimmer);
              return;
            }

            shimmerProgress += shimmerSpeed;

            if (shimmerProgress >= 1) {
              shimmerProgress = 0;
              shimmerPause = pauseDuration; // Pause before next sweep
            }

            // Create sweeping effect: opacity peaks at current progress point
            var peakOpacity = 0.8;
            var sweepWidth = 0.3; // Width of the light sweep

            // Calculate opacity based on distance from sweep center
            var sweepCenter = shimmerProgress;
            var distanceFromCenter = Math.abs(0.5 - sweepCenter);
            var opacity = Math.max(0, peakOpacity * (1 - distanceFromCenter / sweepWidth));

            shimmerLine.setStyle({
              opacity: opacity,
              color: '#F0F0F0' // Bright silver-white for the sweep
            });

            requestAnimationFrame(animateShimmer);
          }

          // Start the smooth sweeping animation
          animateShimmer();

          // Fit map to show both markers and route
          var group = new L.featureGroup([startMarker, endMarker, routeLine]);
          map.fitBounds(group.getBounds().pad(0.1));
        `;
      });
    }

    // Add clean driver markers for trip preview (Uber-style)
    if (showTripPreview && mapMarkers.length > 0) {
      html += `
        var driverIcon = L.divIcon({
          className: 'custom-div-icon',
          html: '<div style="background-color: #000000; width: 8px; height: 8px; border-radius: 50%; border: 2px solid white; box-shadow: 0 1px 4px rgba(0,0,0,0.3);"></div>',
          iconSize: [12, 12],
          iconAnchor: [6, 6]
        });

        var sampleDrivers = [
          {lat: ${userLocation[1] + 0.01}, lng: ${userLocation[0] + 0.01}, name: 'Nomsa - 3 min away'},
          {lat: ${userLocation[1] - 0.008}, lng: ${userLocation[0] + 0.012}, name: 'Thandi - 5 min away'},
          {lat: ${userLocation[1] + 0.015}, lng: ${userLocation[0] - 0.005}, name: 'Zanele - 7 min away'},
        ];

        sampleDrivers.forEach(function(driver) {
          L.marker([driver.lat, driver.lng], {icon: driverIcon}).addTo(map)
            .bindPopup(driver.name);
        });
      `;
    }

    return html;
  };

  // Generate the HTML content
  const mapMarkersHTML = generateMapHTML();

  return (
    <View style={styles.container}>
      {/* Enhanced OpenStreetMap with Search Results */}
      <WebView
        ref={webViewRef}
        style={styles.mapBackground}
        source={{
          html: `
            <!DOCTYPE html>
            <html>
            <head>
              <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
              <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
              <style>
                * {
                  box-sizing: border-box;
                }
                html, body {
                  margin: 0;
                  padding: 0;
                  height: 100%;
                  width: 100%;
                  overflow: hidden;
                  touch-action: none;
                  -webkit-touch-callout: none;
                  -webkit-user-select: none;
                  -khtml-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;
                  -webkit-tap-highlight-color: transparent;
                }
                #map {
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 100vh;
                  width: 100vw;
                  touch-action: none;
                  cursor: grab;
                  outline: none;
                  -webkit-tap-highlight-color: transparent;
                }
                #map:active {
                  cursor: grabbing;
                }
                /* Ensure Leaflet controls are interactive */
                .leaflet-control-container {
                  pointer-events: auto;
                }
                .leaflet-control {
                  pointer-events: auto;
                  touch-action: manipulation;
                }
                /* Ensure map tiles are not selectable */
                .leaflet-tile {
                  pointer-events: none;
                  -webkit-user-select: none;
                  -moz-user-select: none;
                  -ms-user-select: none;
                  user-select: none;
                }
                /* Fix for mobile touch events */
                .leaflet-container {
                  -webkit-tap-highlight-color: transparent;
                  tap-highlight-color: transparent;
                }
                .loading-overlay {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  background: rgba(255, 255, 255, 0.95);
                  padding: 20px;
                  border-radius: 12px;
                  text-align: center;
                  font-family: Arial, sans-serif;
                  color: #E91E63;
                  font-weight: 600;
                  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                  z-index: 1000;
                  ${!isLoadingLocation ? 'display: none;' : ''}
                }

                /* Uber-style clean map styling */
                .custom-div-icon {
                  background: transparent !important;
                  border: none !important;
                }

                /* Clean map container */
                #map {
                  background-color: #f5f5f5;
                }

                /* Hide Leaflet attribution and zoom controls for cleaner look */
                .leaflet-control-attribution {
                  display: none !important;
                }

                .leaflet-control-zoom {
                  border: none !important;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                }

                .leaflet-control-zoom a {
                  background-color: white !important;
                  border: none !important;
                  color: #333 !important;
                  font-size: 18px !important;
                  line-height: 26px !important;
                  width: 26px !important;
                  height: 26px !important;
                }

                .leaflet-control-zoom a:hover {
                  background-color: #f5f5f5 !important;
                }

                /* Clean popup styling */
                .leaflet-popup-content-wrapper {
                  border-radius: 8px !important;
                  box-shadow: 0 2px 12px rgba(0,0,0,0.15) !important;
                }

                .leaflet-popup-content {
                  margin: 12px 16px !important;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                  font-size: 14px !important;
                  color: #333 !important;
                }

                /* Silver shimmer route animation */
                @keyframes shimmer {
                  0% {
                    background-position: -200% 0;
                  }
                  100% {
                    background-position: 200% 0;
                  }
                }

                .shimmer-route {
                  position: relative;
                }

                .shimmer-overlay {
                  animation: shimmer 2s ease-in-out infinite;
                  background: linear-gradient(
                    90deg,
                    transparent 0%,
                    rgba(255, 255, 255, 0.8) 50%,
                    transparent 100%
                  );
                  background-size: 200% 100%;
                  filter: blur(0.5px);
                }
              </style>
            </head>
            <body>
              <div id="map"></div>
              ${isLoadingLocation ? '<div class="loading-overlay">📍 Getting your location...</div>' : ''}
              <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
              <script>
                // Calculate map center offset to account for bottom sheet
                // Bottom sheet covers different amounts based on current position
                var bottomSheetHeight = ${currentSnapPoint === 0 ? 100 : (currentSnapPoint === 1 ? height * 0.55 : height * 0.85)};
                var visibleMapHeight = ${height} - bottomSheetHeight;
                var mapCenterOffset = (bottomSheetHeight / 2) / ${height} * 0.01; // Convert to lat offset

                // Adjust center to show pickup location in visible area
                var adjustedLat = ${userLocation[1]} + mapCenterOffset;

                // Uber-style clean map initialization with adjusted center
                var map = L.map('map', {
                  center: [adjustedLat, ${userLocation[0]}],
                  zoom: ${selectedLocation ? 16 : 15},
                  zoomControl: true,
                  attributionControl: false // Remove attribution for cleaner look
                });

                // Use CartoDB Positron as base and apply custom color styling
                L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                  attribution: '',
                  maxZoom: 19,
                  subdomains: 'abcd'
                }).addTo(map);

                // Apply your custom colors: grass (#b8dccc) and water (#b0d4f4)
                map.whenReady(function() {
                  var style = document.createElement('style');
                  style.textContent = \`
                    /* Custom color overlay for grass and water */
                    .leaflet-tile-pane {
                      position: relative;
                    }

                    .leaflet-tile-pane::after {
                      content: '';
                      position: absolute;
                      top: 0;
                      left: 0;
                      right: 0;
                      bottom: 0;
                      background:
                        radial-gradient(circle at 30% 70%, rgba(184, 220, 204, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(176, 212, 244, 0.3) 0%, transparent 50%),
                        linear-gradient(135deg,
                          rgba(184, 220, 204, 0.15) 0%,
                          rgba(176, 212, 244, 0.15) 100%);
                      mix-blend-mode: overlay;
                      pointer-events: none;
                      z-index: 1;
                    }

                    /* Enhance the natural colors */
                    .leaflet-tile-pane img {
                      filter: saturate(0.9) brightness(1.05) contrast(1.1);
                    }
                  \`;
                  document.head.appendChild(style);
                });

                // Function to recenter map based on bottom sheet position
                window.recenterMap = function(snapPoint) {
                  var bottomSheetHeight = snapPoint === 0 ? 100 : (snapPoint === 1 ? ${height * 0.55} : ${height * 0.85});
                  var mapCenterOffset = (bottomSheetHeight / 2) / ${height} * 0.01;
                  var adjustedLat = ${userLocation[1]} + mapCenterOffset;

                  map.setView([adjustedLat, ${userLocation[0]}], map.getZoom(), {
                    animate: true,
                    duration: 0.3
                  });
                };

                // Listen for messages from React Native
                window.addEventListener('message', function(event) {
                  try {
                    var data = JSON.parse(event.data);
                    if (data.type === 'recenterMap') {
                      window.recenterMap(data.snapPoint);
                    }
                  } catch (e) {
                    // Ignore invalid messages
                  }
                });

                // Pass route data to window for road-following routes
                window.routeData = ${routeData ? JSON.stringify(routeData) : 'null'};

                // Add markers and trip preview elements
                ${mapMarkersHTML}
              </script>
            </body>
            </html>
          `
        }}
        javaScriptEnabled={true}
        // Minimal configuration - no interference
        scrollEnabled={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />

      {/* Header with Hamburger Menu */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
            paddingTop: insets.top + 16,
          }
        ]}
      >
        <TouchableOpacity style={styles.hamburgerButton}>
          <Ionicons name="menu" size={28} color={COLORS.WHITE} />
        </TouchableOpacity>
        <View style={styles.headerSpacer} />
        <TouchableOpacity style={styles.profileButton}>
          <Ionicons name="person-circle" size={32} color={COLORS.WHITE} />
        </TouchableOpacity>
      </Animated.View>

      {/* Backdrop */}
      <ReanimatedAnimated.View style={[styles.backdrop, backdropStyle]} pointerEvents="none" />

      {/* Uber-like Smooth Bottom Sheet */}
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <ReanimatedAnimated.View
          style={[
            styles.bottomSheet,
            bottomSheetStyle,
            {
              // Enhanced safe area handling to prevent gaps
              paddingBottom: Math.max(insets.bottom + 20, 40),
              // Ensure sheet extends beyond screen bottom
              minHeight: height + insets.bottom + 100,
            }
          ]}
        >
          {/* Uber-like Handle */}
          <View style={styles.sheetHandle} />

          {/* Conditional Content: Trip Preview or Search Interface */}
          {showTripPreview && selectedLocation ? (
            <TripPreview
              pickupLocation={{ lat: userLocation[1], lng: userLocation[0] }}
              pickupAddress={pickupAddress}
              destination={selectedLocation}
              selectedRideType={selectedRideType}
              routeData={routeData} // Pass route data to trip preview
              onRideTypeChange={handleRideTypeChange}
              onBookRide={handleBookRide}
              onBack={handleBackFromTripPreview}
            />
          ) : (
            <>
              <Text style={styles.sheetTitle}>Let's go places</Text>

              {/* Position 2 & 3: Service Cards (shown in medium and expanded) */}
              {currentSnapPoint >= 1 && (
                <View style={styles.cardsContainer}>
                  <TouchableOpacity style={styles.serviceCard} onPress={handleRidePress}>
                    <View style={styles.cardIconContainer}>
                      <NewIcon width={40} height={40} />
                    </View>
                    <Text style={styles.cardTitle}>Rides</Text>
                    <Text style={styles.cardSubtitle}>Book a ride now</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.serviceCard} onPress={handleSchedulePress}>
                    <View style={styles.cardIconContainer}>
                      <MaterialIcons name="schedule" size={40} color={COLORS.ACCENT_PINK} />
                    </View>
                    <Text style={styles.cardTitle}>Schedule</Text>
                    <Text style={styles.cardSubtitle}>Plan ahead</Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Position 2 & 3: Enhanced Search Bar with Results (shown in medium and expanded) */}
              {currentSnapPoint >= 1 && (
                <View style={styles.searchContainer}>
                  <View style={styles.searchBar}>
                    <Ionicons name="search" size={20} color={COLORS.MEDIUM_TEXT} style={styles.searchIcon} />
                    <TextInput
                      style={styles.searchInput}
                      placeholder="Where to?"
                      placeholderTextColor={COLORS.LIGHT_TEXT}
                      value={searchText}
                      onChangeText={handleSearchTextChange}
                      onFocus={handleSearchFocus}
                      onBlur={handleSearchBlur}
                      returnKeyType="search"
                      onSubmitEditing={() => performSearch(searchText)}
                    />
                    <TouchableOpacity
                      style={styles.scheduleButton}
                      onPress={() => setShowDatePicker(!showDatePicker)}
                    >
                      <MaterialIcons name="schedule" size={20} color={COLORS.ACCENT_PINK} />
                      <Text style={styles.scheduleText}>Later</Text>
                    </TouchableOpacity>
                  </View>

                  {/* Search Results Dropdown */}
                  <SearchResults
                    results={searchResults}
                    isLoading={isSearching}
                    onResultPress={handleSearchResultPress}
                    visible={showSearchResults}
                  />

                  {/* Recent Searches Dropdown */}
                  <RecentSearches
                    recentSearches={recentSearches}
                    onRecentSearchPress={handleRecentSearchPress}
                    visible={showRecentSearches}
                  />
                </View>
              )}

              {/* Position 2 & 3: Recent Trips Header (shown in medium and expanded) */}
              {currentSnapPoint >= 1 && (
                <View style={styles.recentTripsHeader}>
                  <Text style={styles.sectionTitle}>Recent trips</Text>
                </View>
              )}

              {/* Position 3: Full Recent Trips List (only shown when fully expanded) */}
              {currentSnapPoint === 2 && (
                <ScrollView
                  style={styles.sheetScrollView}
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.sheetScrollContent}
                >
                  <View style={styles.recentTripsContainer}>
                    {isLoadingTrips ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="small" color={COLORS.ACCENT_PINK} />
                        <Text style={styles.loadingText}>Loading trips...</Text>
                      </View>
                    ) : recentTrips.length > 0 ? (
                      recentTrips.map((trip) => (
                        <TouchableOpacity key={trip.id} style={styles.tripItem}>
                          <View style={styles.tripIconContainer}>
                            <Ionicons name="location" size={20} color={COLORS.ACCENT_PINK} />
                          </View>
                          <View style={styles.tripDetails}>
                            <Text style={styles.tripDestination}>{trip.destination}</Text>
                            <Text style={styles.tripDate}>{trip.date}</Text>
                            {trip.distance && trip.duration && (
                              <Text style={styles.tripMeta}>{trip.distance} • {trip.duration}</Text>
                            )}
                          </View>
                          <View style={styles.tripPriceContainer}>
                            <Text style={styles.tripPrice}>{trip.price}</Text>
                            {trip.status === 'cancelled' && (
                              <Text style={styles.tripCancelled}>Cancelled</Text>
                            )}
                          </View>
                        </TouchableOpacity>
                      ))
                    ) : (
                      <View style={styles.emptyTripsContainer}>
                        <Ionicons name="car" size={32} color={COLORS.LIGHT_TEXT} />
                        <Text style={styles.emptyTripsText}>No recent trips</Text>
                        <Text style={styles.emptyTripsSubtext}>Your trip history will appear here</Text>
                      </View>
                    )}
                  </View>
                </ScrollView>
              )}
            </>
          )}
        </ReanimatedAnimated.View>
      </PanGestureHandler>

      {/* Mobile Navigation Bar */}
      <View style={[styles.bottomNav, { paddingBottom: insets.bottom }]}>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="home" size={24} color={COLORS.ACCENT_PINK} />
          <Text style={[styles.navText, styles.navTextActive]}>Home</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.navItem} onPress={() => router.push('/BookRidePage')}>
          <Ionicons name="car" size={24} color={COLORS.MEDIUM_TEXT} />
          <Text style={styles.navText}>Rides</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.navItem} onPress={handleLogout}>
          <Ionicons name="person" size={24} color={COLORS.MEDIUM_TEXT} />
          <Text style={styles.navText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.DARK_TEXT,
  },
  mapBackground: {
    position: 'absolute',
    width: width,
    height: height,
    top: 0,
    left: 0,
    zIndex: 0,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
    zIndex: 2,
  },
  hamburgerButton: {
    padding: 8,
  },
  headerSpacer: {
    flex: 1,
  },
  profileButton: {
    padding: 4,
  },
  cardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  serviceCard: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  cardIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    textAlign: 'center',
  },
  searchContainer: {
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 6,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DARK_TEXT,
  },
  scheduleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_PINK,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  scheduleText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
    marginLeft: 4,
  },
  recentTripsHeader: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  recentTripsContainer: {
    paddingHorizontal: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    marginBottom: 16,
  },
  tripItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  tripIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  tripDetails: {
    flex: 1,
  },
  tripDestination: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 4,
  },
  tripDate: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
  },
  tripPrice: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  // Enhanced Bottom Sheet Styles with Reanimated
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
    zIndex: 0,
  },
  bottomSheet: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: SHEET_HEIGHT,
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 12,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowRadius: 20,
    shadowOpacity: 0.15,
    elevation: 20,
    zIndex: 2,
    // Ensure no gaps at bottom edge
    overflow: 'hidden',
  },
  sheetHandle: {
    alignSelf: 'center',
    width: 36,
    height: 4,
    borderRadius: 2,
    backgroundColor: COLORS.BORDER,
    marginBottom: 16,
    marginTop: 8,
    opacity: 0.6,
  },
  sheetTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
    textAlign: 'center',
    marginBottom: 24,
  },
  sheetScrollView: {
    flex: 1,
    marginTop: 16,
  },
  sheetScrollContent: {
    paddingBottom: 100, // Space for bottom navigation
  },

  // Bottom Navigation Styles
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    paddingTop: 12,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: -2 },
    elevation: 10,
    zIndex: 3,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 4,
  },
  navTextActive: {
    color: COLORS.ACCENT_PINK,
    fontWeight: '600',
  },
  tripMeta: {
    fontSize: 12,
    color: COLORS.LIGHT_TEXT,
    marginTop: 2,
  },
  tripPriceContainer: {
    alignItems: 'flex-end',
  },
  tripCancelled: {
    fontSize: 12,
    color: COLORS.ERROR,
    marginTop: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
  },
  emptyTripsContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyTripsText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 8,
  },
  emptyTripsSubtext: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
    marginTop: 4,
  },
});
