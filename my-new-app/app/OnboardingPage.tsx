import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Animated, Easing } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import SMLogo from '../assets/SMLogo.svg';
import { useRouter } from 'expo-router';

export default function OnboardingPage() {
  const router = useRouter();

  // Animation values
  const headerAnim = useRef(new Animated.Value(0)).current;
  const descAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.stagger(200, [
      Animated.timing(headerAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(descAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();
  }, [headerAnim, descAnim]);

  return (
    <SafeAreaView style={styles.safeArea} edges={['bottom']}>
      <View style={styles.container}>
        <SMLogo width={150} height={150} style={styles.logo} />
        <Image source={require('../assets/tesla.png')} style={styles.car} resizeMode="contain" />
        <View style={styles.textContainer}>
          <Animated.Text
            style={[
              styles.title1,
              {
                opacity: headerAnim,
                transform: [
                  {
                    translateY: headerAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            The safest way to
          </Animated.Text>
          <Text style={styles.title2}>find your next ride <Text style={styles.emoji}>👌</Text></Text>
          <Animated.Text
            style={[
              styles.desc,
              {
                opacity: descAnim,
                transform: [
                  {
                    translateY: descAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            Enjoy peace of mind with every ride, knowing your safety is our top priority.
          </Animated.Text>
        </View>
        <View style={styles.flexSpacer} />
        <View style={styles.bottomContainer}>
          <View style={styles.dotsContainer}>
            <View style={styles.dotActive} />
            <View style={styles.dot} />
          </View>
          <TouchableOpacity style={styles.nextButton} onPress={() => router.replace('/OnboardingStep2')}>
            <Text style={styles.nextText}>Next</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 60,
  },
  logo: {
    marginBottom: 12,
  },
  car: {
    width: 200,
    height: 120,
    marginBottom: 24,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 24,
  },
  title1: {
    fontFamily: 'Poppins-Bold',
    fontSize: 28,
    color: '#1E1E1E',
    textAlign: 'center',
    marginBottom: 2,
    fontWeight: 'bold',
  },
  title2: {
    fontFamily: 'Poppins-Medium',
    fontSize: 18,
    color: '#1E1E1E',
    textAlign: 'center',
    marginBottom: 16,
  },
  emoji: {
    fontSize: 18,
  },
  desc: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#444',
    textAlign: 'center',
    marginBottom: 8,
    paddingTop: 38,
  },
  flexSpacer: {
    flex: 1,
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingBottom: 0,
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginLeft: 0,
  },
  dot: {
    width: 32,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#F3EFFF',
    marginHorizontal: 2,
  },
  dotActive: {
    width: 32,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFF',
    marginHorizontal: 2,
  },
  nextButton: {
    position: 'absolute',
    right: 24,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  nextText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#1E1E1E',
  },
}); 