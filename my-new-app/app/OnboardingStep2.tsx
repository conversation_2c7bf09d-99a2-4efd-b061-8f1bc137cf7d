import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Easing } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

export default function OnboardingStep2() {
  const router = useRouter();

  // Animation values
  const headerAnim = useRef(new Animated.Value(0)).current;
  const descAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.stagger(200, [
      Animated.timing(headerAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.timing(descAnim, {
        toValue: 1,
        duration: 700,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
    ]).start();
  }, [headerAnim, descAnim]);

  return (
    <SafeAreaView style={styles.safeArea} edges={['bottom']}>
      <View style={styles.container}>
        <View style={styles.textContainer}>
          <Animated.Text
            style={[
              styles.title1,
              {
                opacity: headerAnim,
                transform: [
                  {
                    translateY: headerAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            Female Only Drivers
          </Animated.Text>
          <Text style={styles.title2}>your safety comes first! <Text style={styles.emoji}>🌸</Text></Text>
          <Animated.Text
            style={[
              styles.desc,
              {
                opacity: descAnim,
                transform: [
                  {
                    translateY: descAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [20, 0],
                    }),
                  },
                ],
              },
            ]}
          >
            All our drivers are women, so you can always feel comfortable and secure on your journey.
          </Animated.Text>
        </View>
        <View style={styles.flexSpacer} />
        <View style={styles.bottomContainer}>
          <TouchableOpacity style={styles.prevButton} onPress={() => router.replace('/OnboardingPage')}>
            <Text style={styles.prevText}>Previous</Text>
          </TouchableOpacity>
          <View style={styles.dotsContainer}>
            <View style={styles.dot} />
            <View style={styles.dotActive} />
          </View>
          <TouchableOpacity style={styles.nextButton} onPress={() => router.replace('/AuthChoicePage')}>
            <Text style={styles.nextText}>Get Started</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 60, // Match first onboarding screen
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 24,
    paddingTop: 260, // Add a lot of top padding
  },
  title1: {
    fontFamily: 'Poppins-Bold',
    fontSize: 28,
    color: '#1E1E1E',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  title2: {
    fontFamily: 'Poppins-Medium',
    fontSize: 18,
    color: '#1E1E1E',
    textAlign: 'center',
    marginBottom: 16,
  },
  emoji: {
    fontSize: 18,
  },
  desc: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#444',
    textAlign: 'center',
    marginBottom: 8,
    paddingTop: 38,
  },
  flexSpacer: {
    flex: 1,
  },
  bottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingBottom: 0,
  },
  prevButton: {
    position: 'absolute',
    left: 24,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  prevText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#1E1E1E',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginLeft: 0,
  },
  dot: {
    width: 32,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#F3EFFF',
    marginHorizontal: 2,
  },
  dotActive: {
    width: 32,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#F9E6FF',
    marginHorizontal: 2,
  },
  nextButton: {
    position: 'absolute',
    right: 24,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  nextText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#1E1E1E',
  },
}); 