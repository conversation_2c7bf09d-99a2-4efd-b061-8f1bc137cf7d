import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import SMLogo from '../assets/SMLogo.svg';
import { useRouter } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';

export default function SplashPage() {
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const router = useRouter();
  const { isSignedUp, isLoading } = useAuth();

  useEffect(() => {
    // Start logo animation
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: 700,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 700,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start(() => {
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 400,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }).start();
    });
  }, [scaleAnim, opacityAnim]);

  // Handle navigation based on auth status
  useEffect(() => {
    if (!isLoading) {
      const timeout = setTimeout(() => {
        if (isSignedUp) {
          // User is already authenticated, go directly to HomePage
          router.replace('/HomePage');
        } else {
          // Check if user has seen onboarding before
          // For now, always show onboarding for new users
          // In production, you might want to store this in AsyncStorage
          router.replace('/OnboardingPage');
        }
      }, 2000); // Show splash for 2 seconds minimum

      return () => clearTimeout(timeout);
    }
  }, [isLoading, isSignedUp, router]);

  return (
    <View style={styles.container}>
      <Animated.View
        style={{
          opacity: opacityAnim,
          transform: [{ scale: scaleAnim }],
        }}
      >
        <SMLogo width={220} height={220} />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 