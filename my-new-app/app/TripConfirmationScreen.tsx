import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { tripBookingService } from '../services/tripBookingService';
import { Trip, TripStatus } from '../shared/types';
import { fareService } from '../services/fareService';
import { TripManagementService, TripStatusUpdate } from '../services/TripManagementService';
import { useAuth } from '../contexts/AuthContext';

// Color constants matching SheMove branding
const COLORS = {
  PRIMARY_PINK: '#FFF0FF',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#F9E6F7',
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY: '#666666',
  LIGHT_GRAY: '#F5F5F5',
  BORDER: '#E0E0E0',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
};

const TripConfirmationScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const { tripId } = useLocalSearchParams<{ tripId: string }>();

  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentStatus, setCurrentStatus] = useState<TripStatus>('requested');

  const tripServiceRef = useRef<TripManagementService | null>(null);

  useEffect(() => {
    if (tripId && user) {
      loadTripDetails();
      initializeTripMonitoring();
    } else {
      setError('No trip ID provided or user not authenticated');
      setIsLoading(false);
    }

    return () => {
      // Cleanup on unmount
      if (tripServiceRef.current) {
        tripServiceRef.current.cleanup();
      }
    };
  }, [tripId, user]);

  const loadTripDetails = async () => {
    try {
      setIsLoading(true);
      const tripData = await tripBookingService.getTripById(tripId!);

      if (tripData) {
        setTrip(tripData);
        setCurrentStatus(tripData.status);
      } else {
        setError('Trip not found');
      }
    } catch (error) {
      console.error('Failed to load trip details:', error);
      setError('Failed to load trip details');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTripMonitoring = async () => {
    try {
      console.log('TripConfirmationScreen: Initializing trip monitoring for:', tripId);

      // Initialize trip management service
      const service = new TripManagementService({
        onTripStatusUpdate: handleTripStatusUpdate,
        onDriverResponse: handleDriverResponse,
        onTripCompleted: handleTripCompleted,
        onTripCancelled: handleTripCancelled,
        onError: handleServiceError
      });

      const initialized = await service.initialize(user!.id);
      if (!initialized) {
        console.error('TripConfirmationScreen: Failed to initialize trip service');
        return;
      }

      tripServiceRef.current = service;

      // Start monitoring the trip
      const monitoringStarted = await service.startTripMonitoring(tripId!);
      if (!monitoringStarted) {
        console.error('TripConfirmationScreen: Failed to start trip monitoring');
        return;
      }

      console.log('TripConfirmationScreen: Trip monitoring initialized successfully');
    } catch (error) {
      console.error('TripConfirmationScreen: Failed to initialize trip monitoring:', error);
    }
  };

  const handleTripStatusUpdate = (update: TripStatusUpdate) => {
    console.log('TripConfirmationScreen: Trip status update:', update);
    setCurrentStatus(update.status);

    // Navigate to ActiveTripScreen when driver accepts
    if (update.status === 'accepted') {
      console.log('TripConfirmationScreen: Driver accepted, navigating to ActiveTripScreen');
      router.push(`/ActiveTripScreen?tripId=${update.tripId}`);
    }
  };

  const handleDriverResponse = (response: any) => {
    console.log('TripConfirmationScreen: Driver response:', response);

    if (response.response === 'accepted') {
      Alert.alert(
        'Driver Found! 🚗',
        'Your driver has accepted the trip and is on the way.',
        [
          {
            text: 'View Trip',
            onPress: () => router.push(`/ActiveTripScreen?tripId=${tripId}`)
          }
        ]
      );
    } else if (response.response === 'declined') {
      Alert.alert(
        'Finding Another Driver',
        'The driver declined your trip. We\'re finding another driver for you.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  const handleTripCompleted = (completedTrip: Trip) => {
    console.log('TripConfirmationScreen: Trip completed:', completedTrip);
    router.push(`/TripRatingScreen?tripId=${completedTrip.id}`);
  };

  const handleTripCancelled = (cancelledTrip: Trip, reason: string) => {
    console.log('TripConfirmationScreen: Trip cancelled:', cancelledTrip, reason);

    Alert.alert(
      'Trip Cancelled',
      `Your trip has been cancelled.\nReason: ${reason}`,
      [
        {
          text: 'OK',
          onPress: () => router.push('/HomePage')
        }
      ]
    );
  };

  const handleServiceError = (error: Error) => {
    console.error('TripConfirmationScreen: Service error:', error);
    // Don't show error to user unless it's critical
  };

  const handleBackToHome = () => {
    router.replace('/HomePage');
  };

  const handleCancelTrip = () => {
    if (!trip) return;

    Alert.alert(
      'Cancel Trip',
      'Are you sure you want to cancel this trip? This action cannot be undone.',
      [
        {
          text: 'Keep Trip',
          style: 'cancel',
        },
        {
          text: 'Cancel Trip',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await tripBookingService.cancelTrip(trip.id, 'Cancelled by passenger');
              if (success) {
                Alert.alert('Trip Cancelled', 'Your trip has been cancelled successfully.', [
                  { text: 'OK', onPress: handleBackToHome }
                ]);
              } else {
                Alert.alert('Error', 'Failed to cancel trip. Please try again.');
              }
            } catch (error) {
              console.error('Failed to cancel trip:', error);
              Alert.alert('Error', 'Failed to cancel trip. Please try again.');
            }
          },
        },
      ]
    );
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'requested':
        return COLORS.WARNING;
      case 'accepted':
        return COLORS.SUCCESS;
      case 'in_progress':
        return COLORS.ACCENT_PINK;
      case 'completed':
        return COLORS.SUCCESS;
      case 'cancelled':
        return COLORS.GRAY;
      default:
        return COLORS.GRAY;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'requested':
        return 'Finding Driver...';
      case 'accepted':
        return 'Driver Assigned';
      case 'in_progress':
        return 'Trip in Progress';
      case 'completed':
        return 'Trip Completed';
      case 'cancelled':
        return 'Trip Cancelled';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.ACCENT_PINK} />
          <Text style={styles.loadingText}>Loading trip details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !trip) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.GRAY} />
          <Text style={styles.errorTitle}>Oops!</Text>
          <Text style={styles.errorText}>{error || 'Trip not found'}</Text>
          <TouchableOpacity style={styles.backButton} onPress={handleBackToHome}>
            <Text style={styles.backButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackToHome} style={styles.backIconButton}>
          <Ionicons name="arrow-back" size={24} color={COLORS.BLACK} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Trip Confirmation</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Card */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(currentStatus) }]} />
            <Text style={styles.statusText}>{getStatusText(currentStatus)}</Text>
          </View>
          <Text style={styles.tripId}>Trip ID: {trip.id.slice(0, 8)}...</Text>
          {currentStatus === 'requested' && (
            <Text style={styles.statusSubtext}>We're finding the best driver for you...</Text>
          )}
          {currentStatus === 'accepted' && (
            <Text style={styles.statusSubtext}>Your driver is on the way!</Text>
          )}
        </View>

        {/* Trip Details Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Trip Details</Text>
          
          <View style={styles.locationContainer}>
            <View style={styles.locationItem}>
              <View style={styles.locationIcon}>
                <Ionicons name="radio-button-on" size={16} color={COLORS.SUCCESS} />
              </View>
              <View style={styles.locationText}>
                <Text style={styles.locationLabel}>Pickup</Text>
                <Text style={styles.locationAddress}>
                  {trip.pickup_address_short || trip.pickup_location}
                </Text>
              </View>
            </View>

            <View style={styles.routeLine} />

            <View style={styles.locationItem}>
              <View style={styles.locationIcon}>
                <Ionicons name="location" size={16} color={COLORS.ACCENT_PINK} />
              </View>
              <View style={styles.locationText}>
                <Text style={styles.locationLabel}>Destination</Text>
                <Text style={styles.locationAddress}>
                  {trip.destination_address_short || trip.destination_location}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.tripInfo}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Ride Type</Text>
              <Text style={styles.infoValue}>{trip.ride_type}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Distance</Text>
              <Text style={styles.infoValue}>{trip.distance_km.toFixed(1)} km</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Duration</Text>
              <Text style={styles.infoValue}>{trip.duration_minutes} min</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Fare</Text>
              <Text style={[styles.infoValue, styles.fareValue]}>
                {fareService.formatPrice(trip.fare_amount)}
              </Text>
            </View>
          </View>
        </View>

        {/* Booking Time Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Booking Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Booked on</Text>
            <Text style={styles.infoValue}>{formatDate(trip.created_at)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Time</Text>
            <Text style={styles.infoValue}>{formatTime(trip.created_at)}</Text>
          </View>
          {trip.scheduled_time && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Scheduled for</Text>
              <Text style={styles.infoValue}>
                {formatDate(trip.scheduled_time)} at {formatTime(trip.scheduled_time)}
              </Text>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {currentStatus === 'requested' && (
            <TouchableOpacity style={styles.cancelButton} onPress={handleCancelTrip}>
              <Text style={styles.cancelButtonText}>Cancel Trip</Text>
            </TouchableOpacity>
          )}

          {currentStatus === 'accepted' && (
            <TouchableOpacity
              style={styles.viewTripButton}
              onPress={() => router.push(`/ActiveTripScreen?tripId=${tripId}`)}
            >
              <Text style={styles.viewTripButtonText}>View Active Trip</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.homeButton} onPress={handleBackToHome}>
            <Text style={styles.homeButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY_PINK,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    textAlign: 'center',
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  backIconButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    textAlign: 'center',
    marginRight: 40, // Compensate for back button
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  statusCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
  },
  tripId: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  statusSubtext: {
    fontSize: 14,
    color: COLORS.GRAY,
    fontFamily: 'Urbanist-Medium',
    marginTop: 8,
    fontStyle: 'italic',
  },
  card: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 16,
  },
  locationContainer: {
    marginBottom: 20,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  locationText: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.BLACK,
    marginBottom: 16,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: COLORS.BORDER,
    marginLeft: 7,
    marginBottom: 8,
  },
  tripInfo: {
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    paddingTop: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  infoValue: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
  },
  fareValue: {
    color: COLORS.ACCENT_PINK,
    fontSize: 16,
  },
  actionButtons: {
    marginTop: 20,
    marginBottom: 40,
  },
  cancelButton: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 2,
    borderColor: COLORS.ACCENT_PINK,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  cancelButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
  },
  homeButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  homeButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
  },
  viewTripButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  viewTripButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
  },
});

export default TripConfirmationScreen;
