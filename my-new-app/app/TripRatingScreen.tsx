/**
 * Trip Rating Screen for SheMove Passenger App
 * Allows passengers to rate their completed trip and provide feedback
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Alert,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../contexts/AuthContext';
import { Trip } from '../shared/types';
import { tripBookingService } from '../services/tripBookingService';

// Color constants matching SheMove branding
const COLORS = {
  PRIMARY_PINK: '#FFF0FF',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#F9E6F7',
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  GRAY: '#666666',
  LIGHT_GRAY: '#F5F5F5',
  BORDER: '#E0E0E0',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  DANGER: '#F44336',
  GOLD: '#FFD700',
};

interface TripRating {
  trip_id: string;
  passenger_id: string;
  driver_rating: number;
  service_rating: number;
  cleanliness_rating: number;
  punctuality_rating: number;
  overall_rating: number;
  feedback_text?: string;
  tip_amount?: number;
  would_recommend: boolean;
  safety_rating: number;
  created_at: string;
}

const TripRatingScreen: React.FC = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const { tripId } = useLocalSearchParams<{ tripId: string }>();
  
  const [trip, setTrip] = useState<Trip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Rating states
  const [driverRating, setDriverRating] = useState(0);
  const [serviceRating, setServiceRating] = useState(0);
  const [cleanlinessRating, setCleanlinessRating] = useState(0);
  const [punctualityRating, setPunctualityRating] = useState(0);
  const [safetyRating, setSafetyRating] = useState(0);
  const [feedbackText, setFeedbackText] = useState('');
  const [tipAmount, setTipAmount] = useState('');
  const [wouldRecommend, setWouldRecommend] = useState<boolean | null>(null);

  useEffect(() => {
    if (tripId && user) {
      loadTripDetails();
    } else {
      setError('Missing trip ID or user authentication');
      setIsLoading(false);
    }
  }, [tripId, user]);

  const loadTripDetails = async () => {
    try {
      setIsLoading(true);
      const tripData = await tripBookingService.getTripById(tripId!);
      
      if (tripData) {
        setTrip(tripData);
        
        // Check if trip is completed
        if (tripData.status !== 'completed') {
          setError('This trip is not yet completed');
        }
      } else {
        setError('Trip not found');
      }
    } catch (error) {
      console.error('Failed to load trip details:', error);
      setError('Failed to load trip details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStarPress = (rating: number, setter: (value: number) => void) => {
    setter(rating);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderStarRating = (
    currentRating: number,
    onRatingChange: (rating: number) => void,
    label: string
  ) => {
    return (
      <View style={styles.ratingContainer}>
        <Text style={styles.ratingLabel}>{label}</Text>
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => handleStarPress(star, onRatingChange)}
              style={styles.starButton}
            >
              <Ionicons
                name={star <= currentRating ? 'star' : 'star-outline'}
                size={32}
                color={star <= currentRating ? COLORS.GOLD : COLORS.GRAY}
              />
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const calculateOverallRating = (): number => {
    const ratings = [driverRating, serviceRating, cleanlinessRating, punctualityRating, safetyRating];
    const validRatings = ratings.filter(rating => rating > 0);
    
    if (validRatings.length === 0) return 0;
    
    return Math.round(validRatings.reduce((sum, rating) => sum + rating, 0) / validRatings.length);
  };

  const validateRating = (): boolean => {
    if (driverRating === 0) {
      Alert.alert('Rating Required', 'Please rate your driver');
      return false;
    }
    
    if (serviceRating === 0) {
      Alert.alert('Rating Required', 'Please rate the service quality');
      return false;
    }
    
    if (safetyRating === 0) {
      Alert.alert('Rating Required', 'Please rate how safe you felt during the trip');
      return false;
    }
    
    if (wouldRecommend === null) {
      Alert.alert('Recommendation Required', 'Please let us know if you would recommend SheMove');
      return false;
    }
    
    return true;
  };

  const handleSubmitRating = async () => {
    if (!validateRating()) {
      return;
    }

    try {
      setIsSubmitting(true);
      
      const ratingData: TripRating = {
        trip_id: tripId!,
        passenger_id: user!.id,
        driver_rating: driverRating,
        service_rating: serviceRating,
        cleanliness_rating: cleanlinessRating || 0,
        punctuality_rating: punctualityRating || 0,
        safety_rating: safetyRating,
        overall_rating: calculateOverallRating(),
        feedback_text: feedbackText.trim() || undefined,
        tip_amount: tipAmount ? parseFloat(tipAmount) : undefined,
        would_recommend: wouldRecommend!,
        created_at: new Date().toISOString(),
      };

      // Submit rating to database
      const { error } = await tripBookingService.supabase
        .from('trip_ratings')
        .insert(ratingData);

      if (error) {
        throw error;
      }

      // Show success message
      Alert.alert(
        'Thank You! 🌟',
        'Your rating has been submitted successfully. We appreciate your feedback!',
        [
          {
            text: 'Done',
            onPress: () => router.push('/HomePage')
          }
        ]
      );

    } catch (error) {
      console.error('Failed to submit rating:', error);
      Alert.alert(
        'Error',
        'Failed to submit your rating. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkipRating = () => {
    Alert.alert(
      'Skip Rating?',
      'Are you sure you want to skip rating this trip? Your feedback helps us improve our service.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Skip',
          style: 'destructive',
          onPress: () => router.push('/HomePage')
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.ACCENT_PINK} />
          <Text style={styles.loadingText}>Loading trip details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !trip) {
    return (
      <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={COLORS.GRAY} />
          <Text style={styles.errorTitle}>Oops!</Text>
          <Text style={styles.errorText}>{error || 'Trip not found'}</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => router.push('/HomePage')}>
            <Text style={styles.backButtonText}>Back to Home</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.push('/HomePage')} style={styles.backIconButton}>
          <Ionicons name="arrow-back" size={24} color={COLORS.BLACK} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Rate Your Trip</Text>
        <TouchableOpacity onPress={handleSkipRating} style={styles.skipButton}>
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Trip Summary */}
        <View style={styles.tripSummaryCard}>
          <Text style={styles.tripSummaryTitle}>Trip Completed! ✅</Text>
          <View style={styles.tripDetails}>
            <View style={styles.locationContainer}>
              <View style={styles.locationItem}>
                <Ionicons name="radio-button-on" size={16} color={COLORS.SUCCESS} />
                <Text style={styles.locationText}>
                  {trip.pickup_address_short || trip.pickup_location}
                </Text>
              </View>
              <View style={styles.routeLine} />
              <View style={styles.locationItem}>
                <Ionicons name="location" size={16} color={COLORS.ACCENT_PINK} />
                <Text style={styles.locationText}>
                  {trip.destination_address_short || trip.destination_location}
                </Text>
              </View>
            </View>
            <View style={styles.tripInfo}>
              <Text style={styles.tripInfoText}>
                {trip.distance_km.toFixed(1)} km • R{trip.fare_amount.toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        {/* Rating Section */}
        <View style={styles.ratingsCard}>
          <Text style={styles.ratingsTitle}>How was your experience?</Text>

          {renderStarRating(driverRating, setDriverRating, 'Driver')}
          {renderStarRating(serviceRating, setServiceRating, 'Service Quality')}
          {renderStarRating(safetyRating, setSafetyRating, 'Safety')}
          {renderStarRating(cleanlinessRating, setCleanlinessRating, 'Vehicle Cleanliness (Optional)')}
          {renderStarRating(punctualityRating, setPunctualityRating, 'Punctuality (Optional)')}
        </View>

        {/* Recommendation Section */}
        <View style={styles.recommendationCard}>
          <Text style={styles.recommendationTitle}>Would you recommend SheMove?</Text>
          <View style={styles.recommendationButtons}>
            <TouchableOpacity
              style={[
                styles.recommendationButton,
                wouldRecommend === true && styles.recommendationButtonSelected
              ]}
              onPress={() => {
                setWouldRecommend(true);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Ionicons
                name="thumbs-up"
                size={24}
                color={wouldRecommend === true ? COLORS.WHITE : COLORS.SUCCESS}
              />
              <Text style={[
                styles.recommendationButtonText,
                wouldRecommend === true && styles.recommendationButtonTextSelected
              ]}>
                Yes
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.recommendationButton,
                wouldRecommend === false && styles.recommendationButtonSelected
              ]}
              onPress={() => {
                setWouldRecommend(false);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
            >
              <Ionicons
                name="thumbs-down"
                size={24}
                color={wouldRecommend === false ? COLORS.WHITE : COLORS.DANGER}
              />
              <Text style={[
                styles.recommendationButtonText,
                wouldRecommend === false && styles.recommendationButtonTextSelected
              ]}>
                No
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Feedback Section */}
        <View style={styles.feedbackCard}>
          <Text style={styles.feedbackTitle}>Additional Feedback (Optional)</Text>
          <TextInput
            style={styles.feedbackInput}
            placeholder="Tell us more about your experience..."
            placeholderTextColor={COLORS.GRAY}
            value={feedbackText}
            onChangeText={setFeedbackText}
            multiline
            numberOfLines={4}
            maxLength={500}
            textAlignVertical="top"
          />
          <Text style={styles.characterCount}>{feedbackText.length}/500</Text>
        </View>

        {/* Tip Section */}
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>Add a tip for your driver? (Optional)</Text>
          <Text style={styles.tipSubtitle}>Show your appreciation for great service</Text>
          <View style={styles.tipInputContainer}>
            <Text style={styles.tipCurrency}>R</Text>
            <TextInput
              style={styles.tipInput}
              placeholder="0.00"
              placeholderTextColor={COLORS.GRAY}
              value={tipAmount}
              onChangeText={setTipAmount}
              keyboardType="decimal-pad"
              maxLength={6}
            />
          </View>
          <View style={styles.quickTipButtons}>
            {[10, 20, 50].map((amount) => (
              <TouchableOpacity
                key={amount}
                style={styles.quickTipButton}
                onPress={() => setTipAmount(amount.toString())}
              >
                <Text style={styles.quickTipButtonText}>R{amount}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Submit Button */}
        <View style={styles.submitContainer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!validateRating() || isSubmitting) && styles.submitButtonDisabled
            ]}
            onPress={handleSubmitRating}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator size="small" color={COLORS.WHITE} />
            ) : (
              <>
                <Ionicons name="checkmark-circle" size={24} color={COLORS.WHITE} />
                <Text style={styles.submitButtonText}>Submit Rating</Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.GRAY,
    fontFamily: 'Urbanist-Medium',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: COLORS.WHITE,
  },
  errorTitle: {
    fontSize: 24,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: COLORS.GRAY,
    textAlign: 'center',
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
    backgroundColor: COLORS.WHITE,
  },
  backIconButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    textAlign: 'center',
    flex: 1,
  },
  skipButton: {
    padding: 8,
  },
  skipButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  tripSummaryCard: {
    backgroundColor: COLORS.LIGHT_PINK,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  tripSummaryTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 12,
    textAlign: 'center',
  },
  tripDetails: {
    alignItems: 'center',
  },
  locationContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.BLACK,
    marginLeft: 8,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: COLORS.BORDER,
    marginVertical: 4,
  },
  tripInfo: {
    alignItems: 'center',
  },
  tripInfoText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
  },
  ratingsCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  ratingsTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 20,
    textAlign: 'center',
  },
  ratingContainer: {
    marginBottom: 20,
  },
  ratingLabel: {
    fontSize: 16,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.BLACK,
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  starButton: {
    padding: 4,
    marginHorizontal: 2,
  },
  recommendationCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  recommendationTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 16,
    textAlign: 'center',
  },
  recommendationButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  recommendationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    backgroundColor: COLORS.WHITE,
  },
  recommendationButtonSelected: {
    backgroundColor: COLORS.ACCENT_PINK,
    borderColor: COLORS.ACCENT_PINK,
  },
  recommendationButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginLeft: 8,
  },
  recommendationButtonTextSelected: {
    color: COLORS.WHITE,
  },
  feedbackCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  feedbackTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 12,
  },
  feedbackInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    fontFamily: 'Urbanist-Regular',
    color: COLORS.BLACK,
    minHeight: 100,
    backgroundColor: COLORS.LIGHT_GRAY,
  },
  characterCount: {
    fontSize: 12,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    textAlign: 'right',
    marginTop: 8,
  },
  tipCard: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginBottom: 4,
  },
  tipSubtitle: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.GRAY,
    marginBottom: 16,
  },
  tipInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    backgroundColor: COLORS.LIGHT_GRAY,
    paddingHorizontal: 12,
    marginBottom: 16,
  },
  tipCurrency: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.BLACK,
    marginRight: 8,
  },
  tipInput: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Urbanist-Medium',
    color: COLORS.BLACK,
    paddingVertical: 12,
  },
  quickTipButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickTipButton: {
    backgroundColor: COLORS.LIGHT_PINK,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.ACCENT_PINK,
  },
  quickTipButtonText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
  },
  submitContainer: {
    marginBottom: 32,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.ACCENT_PINK,
    paddingVertical: 16,
    borderRadius: 12,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.GRAY,
  },
  submitButtonText: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.WHITE,
    marginLeft: 8,
  },
});

export default TripRatingScreen;
