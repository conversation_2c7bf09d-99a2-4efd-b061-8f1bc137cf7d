/**
 * Tests for ActiveTripScreen
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ActiveTripScreen from '../ActiveTripScreen';
import { TripManagementService } from '../../services/TripManagementService';
import { Trip } from '../../shared/types';

// Mock dependencies
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useLocalSearchParams: () => ({
    tripId: 'test-trip-id',
  }),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
  }),
}));

jest.mock('../../services/TripManagementService');
jest.mock('react-native-webview', () => ({
  WebView: 'WebView',
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  NotificationFeedbackType: {
    Success: 'success',
  },
}));

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('ActiveTripScreen', () => {
  let mockTripService: jest.Mocked<TripManagementService>;
  let mockTrip: Trip;

  beforeEach(() => {
    mockTrip = {
      id: 'test-trip-id',
      passenger_id: 'test-user-id',
      driver_id: 'test-driver-id',
      pickup_location: 'Test Pickup Location',
      pickup_coordinates: { lat: -26.2041, lng: 28.0473 },
      destination_location: 'Test Destination Location',
      destination_coordinates: { lat: -26.1951, lng: 28.0567 },
      ride_type: 'SheRide',
      status: 'requested',
      fare_amount: 50.00,
      distance_km: 5.2,
      duration_minutes: 15,
      surge_multiplier: 1.0,
      pickup_address_short: 'Test Pickup',
      destination_address_short: 'Test Destination',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockTripService = {
      initialize: jest.fn().mockResolvedValue(true),
      startTripMonitoring: jest.fn().mockResolvedValue(true),
      stopTripMonitoring: jest.fn(),
      getTripById: jest.fn().mockResolvedValue(mockTrip),
      cancelTrip: jest.fn().mockResolvedValue(true),
      cleanup: jest.fn(),
    } as any;

    (TripManagementService as jest.Mock).mockImplementation(() => mockTripService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should render loading state initially', () => {
      const { getByText } = render(<ActiveTripScreen />);
      expect(getByText('Loading trip details...')).toBeTruthy();
    });

    it('should initialize trip service and load trip data', async () => {
      render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalledWith('test-user-id');
        expect(mockTripService.getTripById).toHaveBeenCalledWith('test-trip-id');
        expect(mockTripService.startTripMonitoring).toHaveBeenCalledWith('test-trip-id');
      });
    });

    it('should display error when trip service initialization fails', async () => {
      mockTripService.initialize.mockResolvedValue(false);

      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Oops!')).toBeTruthy();
        expect(getByText('Failed to initialize trip service')).toBeTruthy();
      });
    });

    it('should display error when trip is not found', async () => {
      mockTripService.getTripById.mockResolvedValue(null);

      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Oops!')).toBeTruthy();
        expect(getByText('Trip not found')).toBeTruthy();
      });
    });
  });

  describe('trip display', () => {
    it('should display trip details correctly', async () => {
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Active Trip')).toBeTruthy();
        expect(getByText('Finding your driver...')).toBeTruthy();
        expect(getByText('Test Pickup')).toBeTruthy();
        expect(getByText('Test Destination')).toBeTruthy();
        expect(getByText('SheRide')).toBeTruthy();
        expect(getByText('5.2 km')).toBeTruthy();
        expect(getByText('R50.00')).toBeTruthy();
      });
    });

    it('should display correct status for different trip states', async () => {
      mockTrip.status = 'accepted';
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Driver is on the way')).toBeTruthy();
      });
    });

    it('should show cancel button for requested trips', async () => {
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Cancel Trip')).toBeTruthy();
      });
    });

    it('should show call driver button for accepted trips', async () => {
      mockTrip.status = 'accepted';
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(getByText('Call Driver')).toBeTruthy();
      });
    });
  });

  describe('trip actions', () => {
    it('should handle trip cancellation', async () => {
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        const cancelButton = getByText('Cancel Trip');
        fireEvent.press(cancelButton);
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Cancel Trip',
        'Are you sure you want to cancel this trip? You may be charged a cancellation fee.',
        expect.any(Array)
      );
    });

    it('should handle emergency button press', async () => {
      const { getByTestId } = render(<ActiveTripScreen />);

      await waitFor(() => {
        // Note: In a real test, you'd need to add testID to the emergency button
        // For now, this is a placeholder test structure
        expect(true).toBe(true);
      });
    });

    it('should handle call driver action', async () => {
      mockTrip.status = 'accepted';
      const { getByText } = render(<ActiveTripScreen />);

      await waitFor(() => {
        const callButton = getByText('Call Driver');
        fireEvent.press(callButton);
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Contact Driver',
        'Would you like to call your driver?',
        expect.any(Array)
      );
    });
  });

  describe('trip status updates', () => {
    it('should handle trip completion', async () => {
      render(<ActiveTripScreen />);

      await waitFor(() => {
        // Simulate trip completion callback
        const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
        callbacks.onTripCompleted(mockTrip);
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Trip Completed! ✅',
        expect.stringContaining('Your trip has been completed successfully'),
        expect.any(Array)
      );
    });

    it('should handle trip cancellation', async () => {
      render(<ActiveTripScreen />);

      await waitFor(() => {
        // Simulate trip cancellation callback
        const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
        callbacks.onTripCancelled(mockTrip, 'Driver cancelled');
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Trip Cancelled',
        expect.stringContaining('Your trip has been cancelled'),
        expect.any(Array)
      );
    });

    it('should handle driver response', async () => {
      render(<ActiveTripScreen />);

      await waitFor(() => {
        // Simulate driver acceptance callback
        const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
        callbacks.onDriverResponse({
          tripId: 'test-trip-id',
          driverId: 'test-driver-id',
          response: 'accepted',
          timestamp: new Date().toISOString(),
        });
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Driver Found! 🚗',
        'Your driver has accepted the trip and is on the way.',
        expect.any(Array)
      );
    });
  });

  describe('cleanup', () => {
    it('should cleanup trip service on unmount', async () => {
      const { unmount } = render(<ActiveTripScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      unmount();

      expect(mockTripService.cleanup).toHaveBeenCalled();
    });
  });
});
