/**
 * Tests for Navigation Flow between Trip screens
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import TripConfirmationScreen from '../TripConfirmationScreen';
import { TripManagementService } from '../../services/TripManagementService';
import { Trip } from '../../shared/types';

// Mock dependencies
const mockPush = jest.fn();
const mockReplace = jest.fn();

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
  }),
  useLocalSearchParams: () => ({
    tripId: 'test-trip-id',
  }),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
  }),
}));

jest.mock('../../services/tripBookingService', () => ({
  tripBookingService: {
    getTripById: jest.fn(),
    cancelTrip: jest.fn(),
  },
}));

jest.mock('../../services/TripManagementService');

// Mock Alert
jest.spyOn(Alert, 'alert');

describe('Navigation Flow', () => {
  let mockTripService: jest.Mocked<TripManagementService>;
  let mockTrip: Trip;

  beforeEach(() => {
    mockTrip = {
      id: 'test-trip-id',
      passenger_id: 'test-user-id',
      driver_id: 'test-driver-id',
      pickup_location: 'Test Pickup Location',
      pickup_coordinates: { lat: -26.2041, lng: 28.0473 },
      destination_location: 'Test Destination Location',
      destination_coordinates: { lat: -26.1951, lng: 28.0567 },
      ride_type: 'SheRide',
      status: 'requested',
      fare_amount: 50.00,
      distance_km: 5.2,
      duration_minutes: 15,
      surge_multiplier: 1.0,
      pickup_address_short: 'Test Pickup',
      destination_address_short: 'Test Destination',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    mockTripService = {
      initialize: jest.fn().mockResolvedValue(true),
      startTripMonitoring: jest.fn().mockResolvedValue(true),
      stopTripMonitoring: jest.fn(),
      getTripById: jest.fn().mockResolvedValue(mockTrip),
      cancelTrip: jest.fn().mockResolvedValue(true),
      cleanup: jest.fn(),
    } as any;

    (TripManagementService as jest.Mock).mockImplementation(() => mockTripService);

    // Mock tripBookingService
    require('../../services/tripBookingService').tripBookingService.getTripById.mockResolvedValue(mockTrip);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('TripConfirmation to ActiveTrip navigation', () => {
    it('should navigate to ActiveTripScreen when driver accepts trip', async () => {
      render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalledWith('test-user-id');
        expect(mockTripService.startTripMonitoring).toHaveBeenCalledWith('test-trip-id');
      });

      // Simulate driver acceptance
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripStatusUpdate({
        tripId: 'test-trip-id',
        status: 'accepted',
        timestamp: new Date().toISOString(),
      });

      expect(mockPush).toHaveBeenCalledWith('/ActiveTripScreen?tripId=test-trip-id');
    });

    it('should show View Active Trip button when trip is accepted', async () => {
      const { getByText } = render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate driver acceptance
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripStatusUpdate({
        tripId: 'test-trip-id',
        status: 'accepted',
        timestamp: new Date().toISOString(),
      });

      await waitFor(() => {
        expect(getByText('View Active Trip')).toBeTruthy();
        expect(getByText('Your driver is on the way!')).toBeTruthy();
      });
    });

    it('should navigate to ActiveTripScreen when View Active Trip is pressed', async () => {
      const { getByText } = render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate driver acceptance
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripStatusUpdate({
        tripId: 'test-trip-id',
        status: 'accepted',
        timestamp: new Date().toISOString(),
      });

      await waitFor(() => {
        const viewTripButton = getByText('View Active Trip');
        fireEvent.press(viewTripButton);
      });

      expect(mockPush).toHaveBeenCalledWith('/ActiveTripScreen?tripId=test-trip-id');
    });

    it('should navigate to TripRatingScreen when trip is completed', async () => {
      render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate trip completion
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripCompleted({
        ...mockTrip,
        status: 'completed',
      });

      expect(mockPush).toHaveBeenCalledWith('/TripRatingScreen?tripId=test-trip-id');
    });

    it('should navigate to HomePage when trip is cancelled', async () => {
      render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate trip cancellation
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripCancelled(
        { ...mockTrip, status: 'cancelled' },
        'Driver cancelled'
      );

      expect(Alert.alert).toHaveBeenCalledWith(
        'Trip Cancelled',
        expect.stringContaining('Your trip has been cancelled'),
        expect.any(Array)
      );
    });
  });

  describe('Driver response handling', () => {
    it('should show alert and navigation option when driver accepts', async () => {
      render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate driver acceptance response
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onDriverResponse({
        tripId: 'test-trip-id',
        driverId: 'test-driver-id',
        response: 'accepted',
        timestamp: new Date().toISOString(),
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Driver Found! 🚗',
        'Your driver has accepted the trip and is on the way.',
        expect.any(Array)
      );
    });

    it('should show finding another driver message when driver declines', async () => {
      render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      // Simulate driver decline response
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onDriverResponse({
        tripId: 'test-trip-id',
        driverId: 'test-driver-id',
        response: 'declined',
        timestamp: new Date().toISOString(),
      });

      expect(Alert.alert).toHaveBeenCalledWith(
        'Finding Another Driver',
        'The driver declined your trip. We\'re finding another driver for you.',
        expect.any(Array)
      );
    });
  });

  describe('Status display updates', () => {
    it('should update status text when trip status changes', async () => {
      const { getByText } = render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(getByText('Finding your driver...')).toBeTruthy();
        expect(getByText('We\'re finding the best driver for you...')).toBeTruthy();
      });

      // Simulate status change to accepted
      const callbacks = (TripManagementService as jest.Mock).mock.calls[0][0];
      callbacks.onTripStatusUpdate({
        tripId: 'test-trip-id',
        status: 'accepted',
        timestamp: new Date().toISOString(),
      });

      await waitFor(() => {
        expect(getByText('Driver is on the way')).toBeTruthy();
        expect(getByText('Your driver is on the way!')).toBeTruthy();
      });
    });
  });

  describe('Service cleanup', () => {
    it('should cleanup trip service on unmount', async () => {
      const { unmount } = render(<TripConfirmationScreen />);

      await waitFor(() => {
        expect(mockTripService.initialize).toHaveBeenCalled();
      });

      unmount();

      expect(mockTripService.cleanup).toHaveBeenCalled();
    });
  });
});
