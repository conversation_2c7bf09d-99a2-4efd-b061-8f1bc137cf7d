import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { useColorScheme } from '@/hooks/useColorScheme';
import { AuthProvider } from '../contexts/AuthContext';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <AuthProvider>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <Stack>
            <Stack.Screen name="SplashPage" options={{ headerShown: false }} />
            <Stack.Screen name="OnboardingPage" options={{ headerShown: false }} />
            <Stack.Screen name="OnboardingStep2" options={{ headerShown: false }} />
            <Stack.Screen name="AuthChoicePage" options={{ headerShown: false }} />
            <Stack.Screen name="SignupPage" options={{ headerShown: false }} />
            <Stack.Screen name="LoginPage" options={{ headerShown: false }} />
            <Stack.Screen name="ForgotPasswordPage" options={{ headerShown: false }} />
            <Stack.Screen name="ResetPasswordPage" options={{ headerShown: false }} />
            <Stack.Screen name="HomePage" options={{ headerShown: false }} />
            <Stack.Screen name="BookRidePage" options={{ headerShown: false }} />
            <Stack.Screen name="TripConfirmationScreen" options={{ headerShown: false }} />
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen name="+not-found" />
          </Stack>
          <StatusBar style="auto" />
        </ThemeProvider>
      </AuthProvider>
    </GestureHandlerRootView>
  );
}
