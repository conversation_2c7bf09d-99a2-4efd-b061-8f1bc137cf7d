/**
 * Address Confirmation Modal
 * Helps users confirm their exact pickup/drop-off location when house numbers are approximate
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { SmartSuggestion } from '../services/smartAddressService';

const COLORS = {
  PRIMARY_PINK: '#E91E63',
  ACCENT_PINK: '#F06292',
  LIGHT_PINK: '#FFF0FF',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#2C2C2C',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  SUCCESS_GREEN: '#4CAF50',
  WARNING_ORANGE: '#FF9800',
  ERROR_RED: '#F44336',
};

interface AddressConfirmationModalProps {
  visible: boolean;
  suggestion: SmartSuggestion | null;
  onConfirm: (suggestion: SmartSuggestion, instructions?: string) => void;
  onCancel: () => void;
  type: 'pickup' | 'destination';
}

const AddressConfirmationModal: React.FC<AddressConfirmationModalProps> = ({
  visible,
  suggestion,
  onConfirm,
  onCancel,
  type,
}) => {
  const [additionalInstructions, setAdditionalInstructions] = useState('');
  const [selectedOption, setSelectedOption] = useState<'exact' | 'nearby' | 'landmark'>('exact');

  if (!suggestion) return null;

  const handleConfirm = () => {
    onConfirm(suggestion, additionalInstructions);
    setAdditionalInstructions('');
    setSelectedOption('exact');
  };

  const getConfidenceMessage = () => {
    switch (suggestion.confidence) {
      case 'high':
        return {
          text: 'Exact address found',
          color: COLORS.SUCCESS_GREEN,
          icon: 'check-circle',
        };
      case 'medium':
        return {
          text: 'Approximate location',
          color: COLORS.WARNING_ORANGE,
          icon: 'warning',
        };
      case 'low':
        return {
          text: 'General area only',
          color: COLORS.ERROR_RED,
          icon: 'error',
        };
      default:
        return {
          text: 'Location found',
          color: COLORS.MEDIUM_TEXT,
          icon: 'place',
        };
    }
  };

  const confidenceInfo = getConfidenceMessage();

  const locationOptions = [
    {
      id: 'exact',
      title: 'This is the exact location',
      subtitle: 'Driver will come to this precise spot',
      icon: 'my-location',
    },
    {
      id: 'nearby',
      title: 'Somewhere nearby',
      subtitle: 'I\'ll provide more details to the driver',
      icon: 'near-me',
    },
    {
      id: 'landmark',
      title: 'Use as a landmark',
      subtitle: 'I\'ll meet the driver at a nearby landmark',
      icon: 'place',
    },
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onCancel}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>
              Confirm {type === 'pickup' ? 'Pickup' : 'Drop-off'} Location
            </Text>
            <TouchableOpacity onPress={onCancel} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={COLORS.MEDIUM_TEXT} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Address Info */}
            <View style={styles.addressContainer}>
              <View style={styles.addressHeader}>
                <MaterialIcons 
                  name={suggestion.icon === 'house' ? 'home' : 'place'} 
                  size={24} 
                  color={COLORS.ACCENT_PINK} 
                />
                <View style={styles.addressInfo}>
                  <Text style={styles.addressPrimary}>{suggestion.displayText}</Text>
                  <Text style={styles.addressSecondary}>{suggestion.secondaryText}</Text>
                </View>
              </View>

              {/* Confidence Indicator */}
              <View style={[styles.confidenceContainer, { backgroundColor: `${confidenceInfo.color}15` }]}>
                <MaterialIcons name={confidenceInfo.icon} size={16} color={confidenceInfo.color} />
                <Text style={[styles.confidenceText, { color: confidenceInfo.color }]}>
                  {confidenceInfo.text}
                </Text>
              </View>
            </View>

            {/* Location Accuracy Options */}
            {suggestion.confidence !== 'high' && (
              <View style={styles.optionsContainer}>
                <Text style={styles.optionsTitle}>How accurate is this location?</Text>
                
                {locationOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionItem,
                      selectedOption === option.id && styles.optionSelected,
                    ]}
                    onPress={() => setSelectedOption(option.id as any)}
                  >
                    <MaterialIcons 
                      name={option.icon} 
                      size={20} 
                      color={selectedOption === option.id ? COLORS.PRIMARY_PINK : COLORS.MEDIUM_TEXT} 
                    />
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionTitle,
                        selectedOption === option.id && styles.optionTitleSelected,
                      ]}>
                        {option.title}
                      </Text>
                      <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
                    </View>
                    <View style={[
                      styles.radioButton,
                      selectedOption === option.id && styles.radioButtonSelected,
                    ]}>
                      {selectedOption === option.id && (
                        <View style={styles.radioButtonInner} />
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Additional Instructions */}
            <View style={styles.instructionsContainer}>
              <Text style={styles.instructionsTitle}>
                Additional instructions for driver (optional)
              </Text>
              <View style={styles.instructionsInput}>
                <MaterialIcons name="edit" size={16} color={COLORS.MEDIUM_TEXT} />
                <Text style={styles.instructionsPlaceholder}>
                  e.g., "Blue gate", "Next to the pharmacy", "Call when you arrive"
                </Text>
              </View>
            </View>

            {/* Helpful Tips */}
            {suggestion.type === 'interpolated' && (
              <View style={styles.tipsContainer}>
                <MaterialIcons name="lightbulb-outline" size={16} color={COLORS.WARNING_ORANGE} />
                <Text style={styles.tipsText}>
                  <Text style={styles.tipsTitle}>Tip: </Text>
                  Since we couldn't find the exact house number, the driver will be directed to this street. 
                  Consider adding landmarks or specific instructions above.
                </Text>
              </View>
            )}
          </ScrollView>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
              <Text style={styles.confirmButtonText}>
                Confirm {type === 'pickup' ? 'Pickup' : 'Drop-off'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  addressContainer: {
    paddingVertical: 20,
  },
  addressHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  addressInfo: {
    flex: 1,
    marginLeft: 12,
  },
  addressPrimary: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  addressSecondary: {
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  confidenceText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  optionsContainer: {
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#F8F8F8',
  },
  optionSelected: {
    backgroundColor: COLORS.LIGHT_PINK,
    borderWidth: 1,
    borderColor: COLORS.ACCENT_PINK,
  },
  optionContent: {
    flex: 1,
    marginLeft: 12,
  },
  optionTitle: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.DARK_TEXT,
  },
  optionTitleSelected: {
    color: COLORS.PRIMARY_PINK,
  },
  optionSubtitle: {
    fontSize: 13,
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.MEDIUM_TEXT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: COLORS.PRIMARY_PINK,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.PRIMARY_PINK,
  },
  instructionsContainer: {
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 12,
  },
  instructionsInput: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  instructionsPlaceholder: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
    marginLeft: 8,
    flex: 1,
  },
  tipsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    marginVertical: 16,
  },
  tipsText: {
    fontSize: 13,
    color: COLORS.MEDIUM_TEXT,
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },
  tipsTitle: {
    fontWeight: '600',
    color: COLORS.WARNING_ORANGE,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    alignItems: 'center',
    borderRadius: 12,
    marginRight: 8,
    backgroundColor: '#F8F8F8',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.MEDIUM_TEXT,
  },
  confirmButton: {
    flex: 2,
    paddingVertical: 14,
    alignItems: 'center',
    borderRadius: 12,
    marginLeft: 8,
    backgroundColor: COLORS.PRIMARY_PINK,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.WHITE,
  },
});

export default AddressConfirmationModal;
