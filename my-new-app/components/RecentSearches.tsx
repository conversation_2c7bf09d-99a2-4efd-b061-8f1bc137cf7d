import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// SheMove color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
};

interface RecentSearchesProps {
  recentSearches: any[];
  onRecentSearchPress: (searchHistory: any) => void;
  visible: boolean;
}

const RecentSearches: React.FC<RecentSearchesProps> = ({
  recentSearches,
  onRecentSearchPress,
  visible,
}) => {
  if (!visible || recentSearches.length === 0) {
    return null;
  }

  const formatSearchText = (searchHistory: any) => {
    return searchHistory.search_query || 'Unknown search';
  };

  const formatAddress = (searchHistory: any) => {
    if (searchHistory.result_address) {
      // Clean up the address for display
      const parts = searchHistory.result_address.split(',');
      if (parts.length > 2) {
        return `${parts[0]}, ${parts[1]}`;
      }
      return searchHistory.result_address;
    }
    return 'Tap to search again';
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const searchTime = new Date(timestamp);
    const diffMs = now.getTime() - searchTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays}d ago`;
    } else if (diffHours > 0) {
      return `${diffHours}h ago`;
    } else {
      return 'Recent';
    }
  };

  const renderRecentSearch = ({ item }: { item: any }) => {
    const searchText = formatSearchText(item);
    const address = formatAddress(item);
    const timeAgo = getTimeAgo(item.search_timestamp);

    return (
      <TouchableOpacity
        style={styles.recentItem}
        onPress={() => onRecentSearchPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.recentIcon}>
          <Ionicons name="time" size={18} color={COLORS.MEDIUM_TEXT} />
        </View>
        <View style={styles.recentContent}>
          <Text style={styles.recentPrimary} numberOfLines={1}>
            {searchText}
          </Text>
          <Text style={styles.recentSecondary} numberOfLines={1}>
            {address}
          </Text>
        </View>
        <View style={styles.recentTime}>
          <Text style={styles.timeText}>{timeAgo}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerText}>Recent searches</Text>
      </View>
      <FlatList
        data={recentSearches}
        renderItem={renderRecentSearch}
        keyExtractor={(item) => item.id}
        style={styles.list}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  headerText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.MEDIUM_TEXT,
  },
  list: {
    // Remove maxHeight to allow full scrolling of recent searches
    flex: 1,
  },
  recentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  recentIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.SOFT_PINK,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  recentContent: {
    flex: 1,
    marginRight: 8,
  },
  recentPrimary: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.DARK_TEXT,
    marginBottom: 2,
  },
  recentSecondary: {
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
  },
  recentTime: {
    alignItems: 'flex-end',
  },
  timeText: {
    fontSize: 12,
    color: COLORS.LIGHT_TEXT,
  },
});

export default RecentSearches;
