import React, { forwardRef, useMemo, ReactNode } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import BottomSheet, { BottomSheetView, BottomSheetHandle } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface SheBottomSheetProps {
  children: ReactNode;
  snapPoints?: (string | number)[];
  initialSnapIndex?: number;
  enablePanDownToClose?: boolean;
  backgroundStyle?: object;
  handleStyle?: object;
  onSnapPointChange?: (index: number) => void;
  keyboardBehavior?: 'extend' | 'fillParent' | 'interactive';
  keyboardBlurBehavior?: 'none' | 'restore';
  android_keyboardInputMode?: 'adjustPan' | 'adjustResize';
}

export interface SheBottomSheetRef {
  snapToIndex: (index: number) => void;
  snapToPosition: (position: string | number) => void;
  expand: () => void;
  collapse: () => void;
  close: () => void;
  forceClose: () => void;
}

const SheBottomSheet = forwardRef<SheBottomSheetRef, SheBottomSheetProps>(
  (
    {
      children,
      snapPoints = ['25%', '50%', '90%'],
      initialSnapIndex = 0,
      enablePanDownToClose = false,
      backgroundStyle,
      handleStyle,
      onSnapPointChange,
      keyboardBehavior = 'interactive',
      keyboardBlurBehavior = 'restore',
      android_keyboardInputMode = 'adjustResize',
    },
    ref
  ) => {
    const insets = useSafeAreaInsets();

    // Memoize snap points for performance
    const bottomSheetSnapPoints = useMemo(() => snapPoints, [snapPoints]);

    // Custom handle component with SheMove branding
    const CustomHandle = () => (
      <BottomSheetHandle
        style={[styles.handle, handleStyle]}
        indicatorStyle={styles.handleIndicator}
      />
    );

    return (
      <BottomSheet
        ref={ref}
        snapPoints={bottomSheetSnapPoints}
        index={initialSnapIndex}
        enablePanDownToClose={enablePanDownToClose}
        backgroundStyle={[styles.background, backgroundStyle]}
        handleComponent={CustomHandle}
        onChange={onSnapPointChange}
        keyboardBehavior={keyboardBehavior}
        keyboardBlurBehavior={keyboardBlurBehavior}
        android_keyboardInputMode={android_keyboardInputMode}
        // Enhanced gesture handling
        enableHandlePanningGesture={true}
        enableContentPanningGesture={false} // Prevent conflicts with content scrolling
        // Accessibility
        accessibilityRole="adjustable"
        accessibilityLabel="Bottom sheet"
        accessibilityHint="Swipe up or down to adjust sheet position"
      >
        <BottomSheetView style={[styles.contentContainer, { paddingBottom: insets.bottom }]}>
          {children}
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

SheBottomSheet.displayName = 'SheBottomSheet';

const styles = StyleSheet.create({
  background: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  handle: {
    backgroundColor: 'transparent',
    paddingVertical: 12,
  },
  handleIndicator: {
    backgroundColor: '#E91E63', // SheMove pink
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
});

export default SheBottomSheet;
