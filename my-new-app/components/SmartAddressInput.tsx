/**
 * Smart Address Input Component
 * Provides enhanced UX for address input with helpful suggestions and guidance
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { smartAddressService, SmartSuggestion, AddressContext } from '../services/smartAddressService';

const COLORS = {
  PRIMARY_PINK: '#E91E63',
  ACCENT_PINK: '#F06292',
  LIGHT_PINK: '#FFF0FF',
  BACKGROUND_PINK: '#F9E6F7',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#2C2C2C',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  SUCCESS_GREEN: '#4CAF50',
  WARNING_ORANGE: '#FF9800',
  ERROR_RED: '#F44336',
};

interface SmartAddressInputProps {
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
  onAddressSelect: (suggestion: SmartSuggestion) => void;
  context?: AddressContext;
  showHints?: boolean;
}

const SmartAddressInput: React.FC<SmartAddressInputProps> = ({
  placeholder,
  value,
  onChangeText,
  onAddressSelect,
  context = { recentSearches: [], commonAreas: [] },
  showHints = true,
}) => {
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [inputFocused, setInputFocused] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (showSuggestions) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [showSuggestions]);

  const performSmartSearch = async (query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    setIsLoading(true);
    setShowSuggestions(true);

    try {
      const smartSuggestions = await smartAddressService.getSmartSuggestions(query, context);
      setSuggestions(smartSuggestions);
    } catch (error) {
      console.error('Smart search error:', error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTextChange = (text: string) => {
    onChangeText(text);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      performSmartSearch(text);
    }, 300);
  };

  const handleSuggestionPress = (suggestion: SmartSuggestion) => {
    onAddressSelect(suggestion);
    setShowSuggestions(false);
    setInputFocused(false);
  };

  const handleFocus = () => {
    setInputFocused(true);
    if (value.trim()) {
      performSmartSearch(value);
    }
  };

  const handleBlur = () => {
    // Delay hiding suggestions to allow for suggestion tap
    setTimeout(() => {
      setInputFocused(false);
      setShowSuggestions(false);
    }, 200);
  };

  const getIconForSuggestion = (suggestion: SmartSuggestion) => {
    switch (suggestion.icon) {
      case 'house': return 'home';
      case 'building': return 'business';
      case 'street': return 'road';
      case 'area': return 'location-city';
      default: return 'place';
    }
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return COLORS.SUCCESS_GREEN;
      case 'medium': return COLORS.WARNING_ORANGE;
      case 'low': return COLORS.ERROR_RED;
      default: return COLORS.MEDIUM_TEXT;
    }
  };

  const showHouseNumberHelp = () => {
    Alert.alert(
      "House Number Tips",
      "• Try searching for just the street name first\n• Many residential addresses may not have exact house numbers\n• We'll show you the closest location on the street\n• For better accuracy, include your suburb or area",
      [{ text: "Got it", style: "default" }]
    );
  };

  const isHouseNumberQuery = value.match(/^\d+\s+/);
  const hasLowConfidenceResults = suggestions.some(s => s.confidence === 'low');

  return (
    <View style={styles.container}>
      {/* Input Field */}
      <View style={[styles.inputContainer, inputFocused && styles.inputFocused]}>
        <Ionicons name="search" size={20} color={COLORS.MEDIUM_TEXT} style={styles.searchIcon} />
        <TextInput
          style={styles.textInput}
          placeholder={placeholder}
          placeholderTextColor={COLORS.LIGHT_TEXT}
          value={value}
          onChangeText={handleTextChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          returnKeyType="search"
          autoCapitalize="words"
          autoCorrect={false}
        />
        
        {/* Help Button for House Numbers */}
        {isHouseNumberQuery && showHints && (
          <TouchableOpacity onPress={showHouseNumberHelp} style={styles.helpButton}>
            <MaterialIcons name="help-outline" size={20} color={COLORS.ACCENT_PINK} />
          </TouchableOpacity>
        )}
      </View>

      {/* House Number Hint */}
      {isHouseNumberQuery && showHints && hasLowConfidenceResults && (
        <View style={styles.hintContainer}>
          <MaterialIcons name="info-outline" size={16} color={COLORS.WARNING_ORANGE} />
          <Text style={styles.hintText}>
            Exact house number not found. Showing nearby locations on this street.
          </Text>
        </View>
      )}

      {/* Smart Suggestions */}
      {showSuggestions && (
        <Animated.View style={[styles.suggestionsContainer, { opacity: fadeAnim }]}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Finding addresses...</Text>
            </View>
          ) : suggestions.length > 0 ? (
            suggestions.map((suggestion) => (
              <TouchableOpacity
                key={suggestion.id}
                style={styles.suggestionItem}
                onPress={() => handleSuggestionPress(suggestion)}
                activeOpacity={0.7}
              >
                <View style={styles.suggestionIcon}>
                  <MaterialIcons 
                    name={getIconForSuggestion(suggestion)} 
                    size={20} 
                    color={COLORS.ACCENT_PINK} 
                  />
                </View>
                
                <View style={styles.suggestionContent}>
                  <Text style={styles.suggestionPrimary}>{suggestion.displayText}</Text>
                  <Text style={styles.suggestionSecondary}>{suggestion.secondaryText}</Text>
                </View>
                
                <View style={styles.suggestionMeta}>
                  <View style={[styles.confidenceDot, { backgroundColor: getConfidenceColor(suggestion.confidence) }]} />
                  {suggestion.type === 'interpolated' && (
                    <Text style={styles.approximateText}>~</Text>
                  )}
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.noResultsContainer}>
              <MaterialIcons name="location-off" size={24} color={COLORS.LIGHT_TEXT} />
              <Text style={styles.noResultsText}>No addresses found</Text>
              <Text style={styles.noResultsSubtext}>Try a different search or check spelling</Text>
            </View>
          )}
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  inputFocused: {
    borderColor: COLORS.ACCENT_PINK,
    shadowOpacity: 0.15,
  },
  searchIcon: {
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.DARK_TEXT,
    paddingVertical: 0,
  },
  helpButton: {
    padding: 4,
  },
  hintContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginTop: 4,
    borderRadius: 8,
  },
  hintText: {
    fontSize: 12,
    color: COLORS.WARNING_ORANGE,
    marginLeft: 6,
    flex: 1,
  },
  suggestionsContainer: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    marginTop: 4,
    maxHeight: 300,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1001,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  suggestionIcon: {
    width: 32,
    alignItems: 'center',
    marginRight: 12,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionPrimary: {
    fontSize: 16,
    color: COLORS.DARK_TEXT,
    fontWeight: '500',
  },
  suggestionSecondary: {
    fontSize: 14,
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  suggestionMeta: {
    alignItems: 'center',
  },
  confidenceDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 2,
  },
  approximateText: {
    fontSize: 12,
    color: COLORS.WARNING_ORANGE,
    fontWeight: 'bold',
  },
  noResultsContainer: {
    padding: 20,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: COLORS.MEDIUM_TEXT,
    marginTop: 8,
    fontWeight: '500',
  },
  noResultsSubtext: {
    fontSize: 14,
    color: COLORS.LIGHT_TEXT,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default SmartAddressInput;
