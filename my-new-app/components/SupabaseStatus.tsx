import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { supabase, isSupabaseConfigured } from '../lib/supabase';

interface SupabaseStatusProps {
  onClose: () => void;
}

export default function SupabaseStatus({ onClose }: SupabaseStatusProps) {
  const [status, setStatus] = useState<'checking' | 'configured' | 'not-configured' | 'error'>('checking');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkSupabaseStatus();
  }, []);

  const checkSupabaseStatus = async () => {
    try {
      if (!isSupabaseConfigured()) {
        setStatus('not-configured');
        return;
      }

      // Test connection by trying to get session
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        setStatus('error');
        setError(error.message);
      } else {
        setStatus('configured');
      }
    } catch (err) {
      setStatus('error');
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'configured':
        return '#4CAF50'; // Green
      case 'not-configured':
        return '#FF9800'; // Orange
      case 'error':
        return '#F44336'; // Red
      default:
        return '#9E9E9E'; // Gray
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'checking':
        return 'Checking Supabase connection...';
      case 'configured':
        return '✅ Supabase is configured and connected!';
      case 'not-configured':
        return '⚠️ Supabase not configured. Please follow SUPABASE_SETUP.md';
      case 'error':
        return `❌ Supabase connection error: ${error}`;
      default:
        return 'Unknown status';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.modal}>
        <Text style={styles.title}>Supabase Status</Text>
        
        <View style={[styles.statusContainer, { backgroundColor: getStatusColor() + '20' }]}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor() }]} />
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>

        {status === 'not-configured' && (
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsTitle}>Setup Instructions:</Text>
            <Text style={styles.instructionsText}>
              1. Create a Supabase project at app.supabase.com{'\n'}
              2. Copy your project URL and anon key{'\n'}
              3. Update your .env file with the keys{'\n'}
              4. Restart the app{'\n'}
              {'\n'}
              See SUPABASE_SETUP.md for detailed instructions.
            </Text>
          </View>
        )}

        {status === 'configured' && (
          <View style={styles.successContainer}>
            <Text style={styles.successText}>
              🎉 Your app is now connected to Supabase!{'\n'}
              {'\n'}
              You can now:{'\n'}
              • Sign up new users{'\n'}
              • Store user profiles{'\n'}
              • Use real-time features{'\n'}
              • Secure data with RLS
            </Text>
          </View>
        )}

        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>Close</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    margin: 20,
    maxWidth: 400,
    width: '90%',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#2D1B69',
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
  },
  instructionsContainer: {
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E65100',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: '#BF360C',
    lineHeight: 20,
  },
  successContainer: {
    backgroundColor: '#E8F5E8',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  successText: {
    fontSize: 14,
    color: '#2E7D32',
    lineHeight: 20,
  },
  closeButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
