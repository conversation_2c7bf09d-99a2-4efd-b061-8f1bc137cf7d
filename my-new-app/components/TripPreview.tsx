import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Image,
  ActivityIndicator,
  Platform,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SearchResult } from '../services/geocodingService';
import { distanceService, Coordinates, DistanceResult } from '../services/distanceService';
import { fareService, RideType, FareBreakdown } from '../services/fareService';
import { driverService, Driver, DriverSearchResult } from '../services/driverService';
import { realDriverService } from '../services/realDriverService';
import { RouteResponse } from '../services/routingService';
import { useAuth } from '../contexts/AuthContext';

// SheMove color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  ERROR: '#F44336',
};

interface GoogleRouteData {
  distance: number; // in meters
  duration: number; // in seconds
  distanceText: string;
  durationText: string;
  startAddress: string;
  endAddress: string;
  coordinates: { lat: number; lng: number }[];
}

interface TripPreviewProps {
  pickupLocation: Coordinates;
  pickupAddress: string;
  destination: SearchResult;
  selectedRideType: RideType;
  routeData: RouteResponse | null; // Real route data from routing service
  googleRouteData?: GoogleRouteData | null; // Google's route data for accurate timing
  onRideTypeChange: (rideType: RideType) => void;
  onBookRide: (driver: Driver, fare: FareBreakdown) => void;
  onBack: () => void;
  isBookingRide?: boolean; // Loading state for booking
  bookingError?: string | null; // Error message if booking fails
}

const TripPreview: React.FC<TripPreviewProps> = ({
  pickupLocation,
  pickupAddress,
  destination,
  selectedRideType,
  routeData,
  googleRouteData,
  onRideTypeChange,
  onBookRide,
  onBack,
  isBookingRide = false,
  bookingError = null,
}) => {
  const { user } = useAuth();
  const [distance, setDistance] = useState<DistanceResult | null>(null);
  const [driverSearch, setDriverSearch] = useState<DriverSearchResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [useRealDrivers, setUseRealDrivers] = useState(false);
  const [driverServiceInitialized, setDriverServiceInitialized] = useState(false);

  const destinationCoords: Coordinates = {
    lat: parseFloat(destination.lat),
    lng: parseFloat(destination.lon),
  };

  // Initialize real driver service
  useEffect(() => {
    const initializeDriverService = async () => {
      if (user?.id && !driverServiceInitialized) {
        try {
          console.log('🔧 Initializing Real Driver Service...');
          await realDriverService.initialize(user.id);
          setDriverServiceInitialized(true);
          setUseRealDrivers(true);
          console.log('✅ Real Driver Service initialized successfully');
        } catch (error) {
          console.error('❌ Failed to initialize Real Driver Service:', error);
          console.log('🔄 Falling back to mock driver service');
          setUseRealDrivers(false);
          setDriverServiceInitialized(true);
        }
      }
    };

    initializeDriverService();
  }, [user?.id, driverServiceInitialized]);

  useEffect(() => {
    if (driverServiceInitialized) {
      calculateTripDetails();
    }
  }, [pickupLocation, destination, selectedRideType, routeData, googleRouteData, driverServiceInitialized]);

  const calculateTripDetails = async () => {
    setIsLoading(true);

    try {
      let distanceResult: DistanceResult;

      // Prioritize Google's route data for most accurate timing
      if (googleRouteData && googleRouteData.distance > 0 && googleRouteData.duration > 0) {
        // Convert Google's route data to DistanceResult format
        const distanceKm = googleRouteData.distance / 1000; // Convert meters to kilometers
        const durationMinutes = Math.round(googleRouteData.duration / 60); // Convert seconds to minutes

        // Validate the data makes sense
        if (distanceKm > 0 && durationMinutes > 0) {
          distanceResult = {
            distanceKm: Math.round(distanceKm * 100) / 100, // Round to 2 decimal places
            distanceMiles: Math.round(distanceKm * 0.621371 * 100) / 100,
            durationMinutes,
            durationText: formatDuration(durationMinutes),
          };

          console.log('🗺️ Using Google Maps route data (most accurate):', {
            distance: `${distanceResult.distanceKm}km`,
            duration: distanceResult.durationText,
            googleDistance: googleRouteData.distanceText,
            googleDuration: googleRouteData.durationText
          });
        } else {
          console.warn('⚠️ Invalid Google route data, falling back to OSRM data');
          // Fallback to OSRM/GraphHopper data
          if (routeData && routeData.distance > 0 && routeData.duration > 0) {
            const distanceKm = routeData.distance / 1000;
            const durationMinutes = Math.round(routeData.duration / 60);
            distanceResult = {
              distanceKm: Math.round(distanceKm * 100) / 100,
              distanceMiles: Math.round(distanceKm * 0.621371 * 100) / 100,
              durationMinutes,
              durationText: formatDuration(durationMinutes),
            };
          } else {
            const routeInfo = distanceService.getRouteInfo(pickupLocation, destinationCoords);
            distanceResult = routeInfo.distance;
          }
        }
      } else if (routeData && routeData.distance > 0 && routeData.duration > 0) {
        // Use OSRM/GraphHopper route data as secondary option
        const distanceKm = routeData.distance / 1000; // Convert meters to kilometers
        const durationMinutes = Math.round(routeData.duration / 60); // Convert seconds to minutes

        // Validate the data makes sense
        if (distanceKm > 0 && durationMinutes > 0) {
          distanceResult = {
            distanceKm: Math.round(distanceKm * 100) / 100, // Round to 2 decimal places
            distanceMiles: Math.round(distanceKm * 0.621371 * 100) / 100,
            durationMinutes,
            durationText: formatDuration(durationMinutes),
          };

          console.log('🚗 Using OSRM/GraphHopper route data:', {
            distance: `${distanceResult.distanceKm}km`,
            duration: distanceResult.durationText,
            originalDistance: routeData.distance,
            originalDuration: routeData.duration
          });
        } else {
          console.warn('⚠️ Invalid route data, falling back to straight-line calculation');
          const routeInfo = distanceService.getRouteInfo(pickupLocation, destinationCoords);
          distanceResult = routeInfo.distance;
        }
      } else {
        // Fallback to straight-line calculation
        const routeInfo = distanceService.getRouteInfo(pickupLocation, destinationCoords);
        distanceResult = routeInfo.distance;
        console.log('📏 Using straight-line calculation as fallback');
      }

      setDistance(distanceResult);

      // Find nearby drivers - use real or mock service
      let drivers: DriverSearchResult;

      if (useRealDrivers && realDriverService.isInitialized()) {
        console.log('🔍 Searching for real drivers...');
        try {
          drivers = await realDriverService.getNearbyDrivers(pickupLocation, selectedRideType);
          console.log('✅ Real driver search completed:', {
            found: drivers.availableDrivers.length,
            nearest: drivers.nearestDriver?.name
          });
        } catch (error) {
          console.error('❌ Real driver search failed, falling back to mock:', error);
          drivers = driverService.generateNearbyDrivers(pickupLocation, selectedRideType);
        }
      } else {
        console.log('🎭 Using mock driver service');
        drivers = driverService.generateNearbyDrivers(pickupLocation, selectedRideType);
      }

      setDriverSearch(drivers);

      // Auto-select nearest driver
      if (drivers.nearestDriver) {
        setSelectedDriver(drivers.nearestDriver);
      }
    } catch (error) {
      console.error('Error calculating trip details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to format duration
  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} min`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) {
      return `${hours} hr`;
    }

    return `${hours} hr ${remainingMinutes} min`;
  };

  const getRideOptions = () => {
    if (!distance) return [];
    return fareService.getAllRideOptions(distance);
  };

  const getCurrentFare = (): FareBreakdown | null => {
    if (!distance) return null;
    return fareService.calculateFare(selectedRideType, distance);
  };

  const handleBookRide = () => {
    if (selectedDriver && distance) {
      const fare = fareService.calculateFare(selectedRideType, distance);
      onBookRide(selectedDriver, fare);
    }
  };

  const formatAddress = (address: string) => {
    // Google-style smart address formatting for trip preview
    const parts = address.split(',').map(part => part.trim());

    if (parts.length === 0) return 'Unknown location';
    if (parts.length === 1) return parts[0];

    // For trip preview, show primary location + city (max 2 parts)
    // Filter out obvious/redundant information
    const filteredParts = parts.filter(part =>
      !part.toLowerCase().includes('south africa') &&
      !part.toLowerCase().includes('gauteng') &&
      !part.toLowerCase().includes('western cape')
    );

    return filteredParts.slice(0, 2).join(', ');
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.ACCENT_PINK} />
        <Text style={styles.loadingText}>Finding your ride...</Text>
      </View>
    );
  }

  const currentFare = getCurrentFare();
  const rideOptions = getRideOptions();
  const surgeStatus = fareService.getSurgeStatus();

  const { height } = Dimensions.get('window');

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={true}
      contentContainerStyle={styles.scrollContent}
      nestedScrollEnabled={true}
      scrollEnabled={true}
      bounces={Platform.OS === 'ios'}
      overScrollMode={Platform.OS === 'android' ? 'always' : undefined}
      keyboardShouldPersistTaps="handled"
      removeClippedSubviews={Platform.OS === 'android'}
      scrollEventThrottle={16}
    >
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={COLORS.DARK_TEXT} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Trip Preview</Text>
        {/* Debug indicators */}
        {__DEV__ && (
          <View style={styles.debugContainer}>
            <View style={styles.debugIndicator}>
              <Text style={styles.debugText}>
                {routeData ? '🚗 Real Route' : '📏 Straight Line'}
              </Text>
            </View>
            <View style={styles.debugIndicator}>
              <Text style={styles.debugText}>
                {useRealDrivers ? '👥 Real Drivers' : '🎭 Mock Drivers'}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Trip Summary */}
      <View style={styles.tripSummary}>
        <View style={styles.routeContainer}>
          <View style={styles.routePoint}>
            <View style={[styles.routeDot, { backgroundColor: COLORS.SUCCESS }]} />
            <Text style={styles.routeText} numberOfLines={1}>
              {formatAddress(pickupAddress)}
            </Text>
          </View>
          
          <View style={styles.routeLine} />
          
          <View style={styles.routePoint}>
            <View style={[styles.routeDot, { backgroundColor: COLORS.ACCENT_PINK }]} />
            <Text style={styles.routeText} numberOfLines={1}>
              {formatAddress(destination.display_name)}
            </Text>
          </View>
        </View>

        {distance && (
          <View style={styles.tripStats}>
            <View style={styles.statItem}>
              <Ionicons name="location" size={16} color={COLORS.MEDIUM_TEXT} />
              <Text style={styles.statText}>{distance.distanceKm.toFixed(1)} km</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="time" size={16} color={COLORS.MEDIUM_TEXT} />
              <Text style={styles.statText}>{distance.durationText}</Text>
            </View>
            {currentFare && (
              <View style={styles.statItem}>
                <Ionicons name="card" size={16} color={COLORS.MEDIUM_TEXT} />
                <Text style={styles.statText}>{fareService.formatPrice(currentFare.totalFare)}</Text>
              </View>
            )}
          </View>
        )}
      </View>

      {/* Surge Pricing Alert */}
      {surgeStatus.isActive && (
        <View style={[styles.surgeAlert, { borderLeftColor: surgeStatus.color }]}>
          <Ionicons name="trending-up" size={20} color={surgeStatus.color} />
          <View style={styles.surgeContent}>
            <Text style={styles.surgeTitle}>
              {surgeStatus.multiplier}x {surgeStatus.message}
            </Text>
            <Text style={styles.surgeSubtitle}>
              Fares are temporarily higher due to increased demand
            </Text>
          </View>
        </View>
      )}

      {/* Ride Type Selection */}
      <View style={styles.rideTypesContainer}>
        <Text style={styles.sectionTitle}>Choose your ride</Text>
        {rideOptions.map((option) => (
          <TouchableOpacity
            key={option.rideType}
            style={[
              styles.rideTypeCard,
              selectedRideType === option.rideType && styles.selectedRideType,
            ]}
            onPress={() => onRideTypeChange(option.rideType)}
          >
            <View style={styles.rideTypeInfo}>
              <View style={styles.rideTypeHeader}>
                <Ionicons 
                  name={option.icon as any} 
                  size={24} 
                  color={selectedRideType === option.rideType ? COLORS.ACCENT_PINK : COLORS.MEDIUM_TEXT} 
                />
                <View style={styles.rideTypeDetails}>
                  <Text style={[
                    styles.rideTypeName,
                    selectedRideType === option.rideType && styles.selectedText
                  ]}>
                    {option.displayName}
                  </Text>
                  <Text style={styles.rideTypeDescription}>{option.description}</Text>
                </View>
              </View>
              <View style={styles.rideTypeFeatures}>
                {option.features.slice(0, 2).map((feature, index) => (
                  <Text key={index} style={styles.featureText}>• {feature}</Text>
                ))}
              </View>
            </View>
            <View style={styles.rideTypePrice}>
              <Text style={[
                styles.priceText,
                selectedRideType === option.rideType && styles.selectedText
              ]}>
                {option.formattedPrice}
              </Text>
              <Text style={styles.estimatedTime}>{option.estimatedTime}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Available Drivers */}
      {driverSearch && driverSearch.availableDrivers.length > 0 && (
        <View style={styles.driversContainer}>
          <Text style={styles.sectionTitle}>
            Available drivers ({driverSearch.totalDriversInArea})
          </Text>
          <Text style={styles.averageWaitText}>
            Average wait time: {driverSearch.averageWaitTime} minutes
          </Text>
          
          {driverSearch.availableDrivers
            .filter(driver => driver.vehicleInfo.type === selectedRideType)
            .slice(0, 3)
            .map((driver) => (
            <TouchableOpacity
              key={driver.id}
              style={[
                styles.driverCard,
                selectedDriver?.id === driver.id && styles.selectedDriver,
              ]}
              onPress={() => setSelectedDriver(driver)}
            >
              <Image source={{ uri: driver.profileImage }} style={styles.driverImage} />
              <View style={styles.driverInfo}>
                <View style={styles.driverHeader}>
                  <Text style={styles.driverName}>{driver.name}</Text>
                  <View style={styles.ratingContainer}>
                    <Ionicons name="star" size={14} color={COLORS.WARNING} />
                    <Text style={styles.ratingText}>{driver.rating}</Text>
                  </View>
                </View>
                <Text style={styles.vehicleInfo}>
                  {driver.vehicleInfo.color} {driver.vehicleInfo.make} {driver.vehicleInfo.model}
                </Text>
                <Text style={styles.licensePlate}>{driver.vehicleInfo.licensePlate}</Text>
                {driver.specialFeatures.length > 0 && (
                  <Text style={styles.specialFeatures}>
                    {driver.specialFeatures.slice(0, 2).join(' • ')}
                  </Text>
                )}
              </View>
              <View style={styles.driverStats}>
                <Text style={styles.arrivalTime}>
                  {driverService.formatArrivalTime(driver.estimatedArrival)}
                </Text>
                <Text style={styles.driverDistance}>
                  {driverService.formatDriverDistance(driver.distanceFromUser)}
                </Text>
                {driver.isMoving && (
                  <View style={styles.movingIndicator}>
                    <View style={styles.movingDot} />
                    <Text style={styles.movingText}>Moving</Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Booking Error Message */}
      {bookingError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{bookingError}</Text>
        </View>
      )}

      {/* Book Ride Button */}
      <TouchableOpacity
        style={[
          styles.bookButton,
          (!selectedDriver || !currentFare || isBookingRide) && styles.bookButtonDisabled,
        ]}
        onPress={handleBookRide}
        disabled={!selectedDriver || !currentFare || isBookingRide}
      >
        {isBookingRide ? (
          <View style={styles.bookButtonLoading}>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text style={[styles.bookButtonText, { marginLeft: 8 }]}>
              Booking ride...
            </Text>
          </View>
        ) : (
          <Text style={styles.bookButtonText}>
            {selectedDriver
              ? `Book ${selectedRideType} • ${currentFare ? fareService.formatPrice(currentFare.totalFare) : ''}`
              : 'Select a driver to continue'
            }
          </Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
    // Ensure ScrollView can scroll beyond visible bounds
    minHeight: '100%',
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 140 : 120, // Platform-specific spacing
    minHeight: Dimensions.get('window').height * 0.8, // Ensure content is scrollable
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    flex: 1,
  },
  debugIndicator: {
    backgroundColor: COLORS.LIGHT_PINK,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  debugText: {
    fontSize: 10,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
    fontWeight: '600',
  },
  tripSummary: {
    padding: 20,
    backgroundColor: COLORS.SOFT_PINK,
    marginBottom: 8,
  },
  routeContainer: {
    marginBottom: 16,
  },
  routePoint: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  routeDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: COLORS.BORDER,
    marginLeft: 5,
    marginVertical: 2,
  },
  routeText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.DARK_TEXT,
    flex: 1,
  },
  tripStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginLeft: 4,
  },
  surgeAlert: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.LIGHT_PINK,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
  },
  surgeContent: {
    marginLeft: 12,
    flex: 1,
  },
  surgeTitle: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  surgeSubtitle: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  rideTypesContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 16,
  },
  rideTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
  },
  selectedRideType: {
    borderColor: COLORS.ACCENT_PINK,
    backgroundColor: COLORS.SOFT_PINK,
  },
  rideTypeInfo: {
    flex: 1,
  },
  rideTypeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  rideTypeDetails: {
    marginLeft: 12,
    flex: 1,
  },
  rideTypeName: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  rideTypeDescription: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  rideTypeFeatures: {
    marginLeft: 36,
  },
  featureText: {
    fontSize: 11,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.LIGHT_TEXT,
    marginBottom: 1,
  },
  rideTypePrice: {
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 18,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '700',
    color: COLORS.DARK_TEXT,
  },
  estimatedTime: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 2,
  },
  selectedText: {
    color: COLORS.ACCENT_PINK,
  },
  driversContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  averageWaitText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginBottom: 16,
  },
  driverCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  selectedDriver: {
    borderColor: COLORS.ACCENT_PINK,
    backgroundColor: COLORS.SOFT_PINK,
  },
  driverImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  driverInfo: {
    flex: 1,
  },
  driverHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  driverName: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginLeft: 2,
  },
  vehicleInfo: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginBottom: 2,
  },
  licensePlate: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.LIGHT_TEXT,
    marginBottom: 4,
  },
  specialFeatures: {
    fontSize: 11,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.ACCENT_PINK,
  },
  driverStats: {
    alignItems: 'flex-end',
  },
  arrivalTime: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.ACCENT_PINK,
    marginBottom: 2,
  },
  driverDistance: {
    fontSize: 12,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginBottom: 4,
  },
  movingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  movingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.SUCCESS,
    marginRight: 4,
  },
  movingText: {
    fontSize: 10,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.SUCCESS,
  },
  bookButton: {
    backgroundColor: COLORS.ACCENT_PINK,
    marginHorizontal: 20,
    marginBottom: 32,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  bookButtonDisabled: {
    backgroundColor: COLORS.BORDER,
  },
  bookButtonText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.WHITE,
  },
  bookButtonLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    backgroundColor: '#FFE6E6',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFB3B3',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Medium',
    color: '#D32F2F',
    textAlign: 'center',
  },
  debugContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});

export default TripPreview;
