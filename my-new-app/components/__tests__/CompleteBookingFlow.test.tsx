/**
 * Complete Booking Flow Test
 * Tests the end-to-end booking process with driver map and database integration
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import HomePage from '../../app/HomePage';
import { tripBookingService } from '../../services/tripBookingService';
import { DriverMapService } from '../../services/DriverMapService';

// Mock dependencies
jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
    signOut: jest.fn(),
  }),
}));

jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getCurrentPositionAsync: jest.fn(() => Promise.resolve({
    coords: { latitude: -26.2041, longitude: 28.0473 }
  })),
}));

jest.mock('../../services/tripBookingService', () => ({
  tripBookingService: {
    createTrip: jest.fn(),
    sendTripRequest: jest.fn(),
    getTripById: jest.fn(),
  },
}));

jest.mock('../../services/DriverMapService', () => ({
  DriverMapService: jest.fn().mockImplementation(() => ({
    initialize: jest.fn(() => Promise.resolve(true)),
    loadNearbyDrivers: jest.fn(() => Promise.resolve([
      {
        id: 'driver-1',
        name: 'Sarah Johnson',
        location: { lat: -26.2041, lng: 28.0473 },
        heading: 45,
        isOnline: true,
        vehicleType: 'SheRide',
        rating: 4.8,
        estimatedArrival: 5
      }
    ])),
    cleanup: jest.fn(),
    getCurrentDrivers: jest.fn(() => []),
    updateUserLocation: jest.fn(),
  })),
}));

describe('Complete Booking Flow Integration', () => {
  const mockTripBookingService = tripBookingService as jest.Mocked<typeof tripBookingService>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ISSUE 1: Driver Map Service Database Function', () => {
    it('should handle missing get_nearby_drivers function gracefully', async () => {
      // Mock the DriverMapService to simulate function not found error
      const mockDriverService = new DriverMapService();
      const loadDriversSpy = jest.spyOn(mockDriverService, 'loadNearbyDrivers');
      
      // Simulate database function not found error
      loadDriversSpy.mockRejectedValue(new Error('function public.get_nearby_drivers does not exist'));

      const { getByTestId } = render(<HomePage />);

      // Should still render without crashing
      await waitFor(() => {
        expect(getByTestId('bottom-sheet')).toBeTruthy();
      });

      // Should log appropriate error message
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('get_nearby_drivers function not found')
      );
    });

    it('should use fallback method when database function is missing', async () => {
      const mockDriverService = new DriverMapService();
      const loadDriversSpy = jest.spyOn(mockDriverService, 'loadNearbyDrivers');
      
      // First call fails, second call (fallback) succeeds
      loadDriversSpy
        .mockRejectedValueOnce(new Error('function does not exist'))
        .mockResolvedValueOnce([
          {
            id: 'driver-1',
            name: 'Sarah Johnson',
            location: { lat: -26.2041, lng: 28.0473 },
            heading: 45,
            isOnline: true,
            vehicleType: 'SheRide',
            rating: 4.8,
            estimatedArrival: 5
          }
        ]);

      await mockDriverService.loadNearbyDrivers();

      // Should have attempted fallback
      expect(loadDriversSpy).toHaveBeenCalledTimes(1);
    });

    it('should display car icons on map when drivers are loaded', async () => {
      const { getByTestId } = render(<HomePage />);

      // Wait for map to initialize
      await waitFor(() => {
        expect(getByTestId('map-container')).toBeTruthy();
      });

      // Verify DriverMapService was initialized
      expect(DriverMapService).toHaveBeenCalledWith(
        expect.objectContaining({
          onDriversUpdated: expect.any(Function),
          onDriverAdded: expect.any(Function),
          onDriverRemoved: expect.any(Function),
          onDriverMoved: expect.any(Function),
        })
      );
    });
  });

  describe('ISSUE 3: Trip Booking Database Error', () => {
    it('should handle trip creation with proper error messages', async () => {
      const mockBookingRequest = {
        pickupLocation: {
          lat: -26.2041,
          lng: 28.0473,
          address: 'Current Location',
          shortAddress: 'Current Location'
        },
        destination: {
          lat: -26.1951,
          lng: 28.0567,
          address: 'Sandton City, Johannesburg',
          shortAddress: 'Sandton City'
        },
        rideType: 'SheRide' as const,
        fare: {
          baseFare: 25.00,
          distanceFare: 15.60,
          timeFare: 9.40,
          totalFare: 50.00,
          surgeFare: 0,
          surgeMultiplier: 1.0
        },
        scheduledTime: null,
        passengerNotes: null
      };

      // Mock successful trip creation
      mockTripBookingService.createTrip.mockResolvedValue({
        success: true,
        trip: {
          id: 'trip-123',
          passenger_id: 'test-user-id',
          pickup_location: 'Current Location',
          pickup_coordinates: { lat: -26.2041, lng: 28.0473 },
          destination_location: 'Sandton City, Johannesburg',
          destination_coordinates: { lat: -26.1951, lng: 28.0567 },
          ride_type: 'SheRide',
          status: 'requested',
          fare_amount: 50.00,
          distance_km: 5.2,
          duration_minutes: 15,
          surge_multiplier: 1.0,
          scheduled_time: null,
          driver_accepted_at: null,
          driver_arrived_at: null,
          trip_started_at: null,
          completed_at: null,
          cancelled_at: null,
          cancellation_reason: null,
          cancelled_by: null,
          pickup_address_short: 'Current Location',
          destination_address_short: 'Sandton City',
          driver_notes: null,
          passenger_notes: null,
          estimated_arrival_time: null,
          actual_pickup_time: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        message: 'Trip created successfully'
      });

      const result = await tripBookingService.createTrip(mockBookingRequest, 'test-user-id');

      expect(result).toBeTruthy();
      expect(result?.trip?.id).toBe('trip-123');
      expect(result?.trip?.status).toBe('requested');
    });

    it('should handle database constraint errors properly', async () => {
      const mockBookingRequest = {
        pickupLocation: {
          lat: -26.2041,
          lng: 28.0473,
          address: 'Current Location',
          shortAddress: 'Current Location'
        },
        destination: {
          lat: -26.1951,
          lng: 28.0567,
          address: 'Sandton City, Johannesburg',
          shortAddress: 'Sandton City'
        },
        rideType: 'SheRide' as const,
        fare: {
          baseFare: 25.00,
          distanceFare: 15.60,
          timeFare: 9.40,
          totalFare: 50.00,
          surgeFare: 0,
          surgeMultiplier: 1.0
        },
        scheduledTime: null,
        passengerNotes: null
      };

      // Mock database constraint error
      mockTripBookingService.createTrip.mockResolvedValue({
        success: false,
        trip: null,
        message: 'Database constraint violation'
      });

      const result = await tripBookingService.createTrip(mockBookingRequest, 'test-user-id');

      expect(result?.success).toBe(false);
      expect(mockTripBookingService.createTrip).toHaveBeenCalledWith(mockBookingRequest, 'test-user-id');
    });

    it('should validate trip data before database insertion', async () => {
      const invalidBookingRequest = {
        pickupLocation: {
          lat: 0, // Invalid coordinates
          lng: 0,
          address: '',
          shortAddress: ''
        },
        destination: {
          lat: 0,
          lng: 0,
          address: '',
          shortAddress: ''
        },
        rideType: 'SheRide' as const,
        fare: {
          baseFare: 0,
          distanceFare: 0,
          timeFare: 0,
          totalFare: 0,
          surgeFare: 0,
          surgeMultiplier: 1.0
        },
        scheduledTime: null,
        passengerNotes: null
      };

      mockTripBookingService.createTrip.mockResolvedValue({
        success: false,
        trip: null,
        message: 'Invalid booking data'
      });

      const result = await tripBookingService.createTrip(invalidBookingRequest, 'test-user-id');

      expect(result?.success).toBe(false);
    });
  });

  describe('Complete End-to-End Booking Flow', () => {
    it('should complete the full booking process', async () => {
      const { getByText, getByTestId } = render(<HomePage />);

      // Step 1: Wait for map and drivers to load
      await waitFor(() => {
        expect(getByTestId('bottom-sheet')).toBeTruthy();
      });

      // Step 2: Search for destination
      const searchInput = getByTestId('search-input');
      fireEvent.changeText(searchInput, 'Sandton City');

      // Step 3: Select destination (mock)
      await waitFor(() => {
        // This would trigger the trip preview
        expect(searchInput).toBeTruthy();
      });

      // Step 4: Select ride type
      const sheRideOption = getByText('SheRide');
      fireEvent.press(sheRideOption);

      // Step 5: Select driver (mock)
      const driverCard = getByText('Sarah Johnson');
      fireEvent.press(driverCard);

      // Step 6: Book ride
      const bookButton = getByText(/Book SheRide/);
      fireEvent.press(bookButton);

      // Verify booking process initiated
      await waitFor(() => {
        expect(mockTripBookingService.createTrip).toHaveBeenCalled();
      });
    });

    it('should handle booking errors gracefully', async () => {
      const { getByText, getByTestId } = render(<HomePage />);

      // Mock booking failure
      mockTripBookingService.createTrip.mockResolvedValue({
        success: false,
        trip: null,
        message: 'Booking failed'
      });

      // Go through booking process
      await waitFor(() => {
        expect(getByTestId('bottom-sheet')).toBeTruthy();
      });

      // Attempt to book
      const bookButton = getByText(/Book/);
      fireEvent.press(bookButton);

      // Should show error message
      await waitFor(() => {
        expect(getByText(/booking failed/i)).toBeTruthy();
      });
    });

    it('should maintain bottom sheet scroll functionality during booking', async () => {
      const { getByTestId } = render(<HomePage />);

      await waitFor(() => {
        const bottomSheet = getByTestId('bottom-sheet');
        expect(bottomSheet).toBeTruthy();
      });

      // Verify scrollable content is enabled
      const scrollView = getByTestId('bottom-sheet-scroll-view');
      expect(scrollView).toBeTruthy();

      // Test scroll gesture (mock)
      fireEvent.scroll(scrollView, {
        nativeEvent: {
          contentOffset: { y: 100 },
          contentSize: { height: 1000 },
          layoutMeasurement: { height: 500 }
        }
      });

      // Should handle scroll without interfering with booking
      expect(scrollView).toBeTruthy();
    });
  });

  describe('Error Recovery and User Feedback', () => {
    it('should provide clear feedback when database is unavailable', async () => {
      // Mock complete database failure
      mockTripBookingService.createTrip.mockRejectedValue(new Error('Database connection failed'));

      const { getByText } = render(<HomePage />);

      // Should still render the app
      await waitFor(() => {
        expect(getByText(/Let's go places/)).toBeTruthy();
      });
    });

    it('should retry failed operations automatically', async () => {
      const mockDriverService = new DriverMapService();
      const loadDriversSpy = jest.spyOn(mockDriverService, 'loadNearbyDrivers');
      
      // First call fails, second succeeds
      loadDriversSpy
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce([]);

      // Should attempt retry
      await mockDriverService.loadNearbyDrivers();
      expect(loadDriversSpy).toHaveBeenCalledTimes(1);
    });
  });
});
