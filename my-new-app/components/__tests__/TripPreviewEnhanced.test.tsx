/**
 * Tests for Enhanced TripPreview Component
 * Verifies the improved UX, animations, and booking flow
 */

import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Animated } from 'react-native';
import TripPreview from '../TripPreview';
import { SearchResult } from '../../services/geocodingService';
import { Driver, FareBreakdown } from '../../services/fareService';
import * as Haptics from 'expo-haptics';

// Mock dependencies
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
}));

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
  }),
}));

jest.mock('../../services/driverService', () => ({
  driverService: {
    generateNearbyDrivers: jest.fn(() => ({
      availableDrivers: [
        {
          id: 'driver-1',
          name: '<PERSON>',
          rating: 4.8,
          estimatedArrival: '5 min',
          vehicle: { make: 'Toyota', model: 'Corolla', color: 'White' },
          location: { lat: -26.2041, lng: 28.0473 },
        },
      ],
      nearestDriver: {
        id: 'driver-1',
        name: 'Sarah Johnson',
        rating: 4.8,
        estimatedArrival: '5 min',
      },
      totalDriversInArea: 5,
      averageWaitTime: 6,
    })),
  },
}));

jest.mock('../../services/realDriverService', () => ({
  realDriverService: {
    isInitialized: jest.fn(() => false),
    getNearbyDrivers: jest.fn(),
  },
}));

jest.mock('../../services/distanceService', () => ({
  distanceService: {
    calculateDistance: jest.fn(() => Promise.resolve({
      distanceKm: 5.2,
      durationMinutes: 15,
      durationText: '15 min',
    })),
  },
}));

jest.mock('../../services/fareService', () => ({
  fareService: {
    calculateFare: jest.fn(() => ({
      baseFare: 25.00,
      distanceFare: 15.60,
      timeFare: 9.40,
      totalFare: 50.00,
      surgeFare: 0,
      surgeMultiplier: 1.0,
    })),
    formatPrice: jest.fn((price) => `R${price.toFixed(2)}`),
    getSurgeStatus: jest.fn(() => ({
      isActive: false,
      multiplier: 1.0,
      message: '',
      color: '#4CAF50',
    })),
  },
}));

describe('Enhanced TripPreview Component', () => {
  const mockProps = {
    pickupLocation: { lat: -26.2041, lng: 28.0473 },
    pickupAddress: 'Current location',
    destination: {
      lat: '-26.1951',
      lon: '28.0567',
      display_name: 'Sandton City, Johannesburg',
    } as SearchResult,
    selectedRideType: 'SheRide' as const,
    routeData: null,
    googleRouteData: null,
    onRideTypeChange: jest.fn(),
    onBookRide: jest.fn(),
    onBack: jest.fn(),
    isBookingRide: false,
    bookingError: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Enhanced UI Components', () => {
    it('should render enhanced trip summary with proper information hierarchy', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        expect(getByText('From')).toBeTruthy();
        expect(getByText('To')).toBeTruthy();
        expect(getByText('Current location')).toBeTruthy();
        expect(getByText('Sandton City, Johannesburg')).toBeTruthy();
        expect(getByText('Distance')).toBeTruthy();
        expect(getByText('Duration')).toBeTruthy();
        expect(getByText('Women-only drivers • Verified & Safe')).toBeTruthy();
      });
    });

    it('should display ride type options with enhanced styling', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        expect(getByText('Choose your ride')).toBeTruthy();
        expect(getByText('SheRide')).toBeTruthy();
        expect(getByText('ShePool')).toBeTruthy();
        expect(getByText('SheXL')).toBeTruthy();
      });
    });

    it('should show enhanced book button with proper styling', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        // Initially should show "Select a driver to continue"
        expect(getByText('Select a driver to continue')).toBeTruthy();
      });
    });
  });

  describe('Enhanced Ride Type Selection', () => {
    it('should provide haptic feedback when selecting ride type', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        const shePoolOption = getByText('ShePool');
        fireEvent.press(shePoolOption);
      });

      expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Medium);
      expect(mockProps.onRideTypeChange).toHaveBeenCalledWith('ShePool');
    });

    it('should not allow multiple rapid selections', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        const shePoolOption = getByText('ShePool');
        
        // Rapid fire clicks
        fireEvent.press(shePoolOption);
        fireEvent.press(shePoolOption);
        fireEvent.press(shePoolOption);
      });

      // Should only be called once due to debouncing
      expect(mockProps.onRideTypeChange).toHaveBeenCalledTimes(1);
    });

    it('should show visual feedback during ride type change', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        const sheXLOption = getByText('SheXL');
        fireEvent.press(sheXLOption);
      });

      // Verify animation was triggered (in a real test, you'd check animation values)
      expect(mockProps.onRideTypeChange).toHaveBeenCalledWith('SheXL');
    });
  });

  describe('Enhanced Booking Flow', () => {
    it('should show enhanced book button when driver is selected', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        // Wait for drivers to load and select one
        const driverCard = getByText('Sarah Johnson');
        fireEvent.press(driverCard);
      });

      await waitFor(() => {
        expect(getByText(/Book SheRide • R50.00/)).toBeTruthy();
        expect(getByText('Estimated arrival: 5 min')).toBeTruthy();
      });
    });

    it('should show loading state during booking', async () => {
      const { getByText } = render(
        <TripPreview {...mockProps} isBookingRide={true} />
      );

      await waitFor(() => {
        expect(getByText('Booking ride...')).toBeTruthy();
      });
    });

    it('should display booking error when provided', async () => {
      const { getByText } = render(
        <TripPreview {...mockProps} bookingError="Driver is no longer available" />
      );

      await waitFor(() => {
        expect(getByText('Driver is no longer available')).toBeTruthy();
      });
    });
  });

  describe('Enhanced Visual Feedback', () => {
    it('should show proper visual states for different ride types', async () => {
      const { getByText, rerender } = render(<TripPreview {...mockProps} />);

      // Test SheRide selection
      await waitFor(() => {
        expect(getByText('SheRide')).toBeTruthy();
      });

      // Test ShePool selection
      rerender(<TripPreview {...mockProps} selectedRideType="ShePool" />);
      await waitFor(() => {
        expect(getByText('ShePool')).toBeTruthy();
      });

      // Test SheXL selection
      rerender(<TripPreview {...mockProps} selectedRideType="SheXL" />);
      await waitFor(() => {
        expect(getByText('SheXL')).toBeTruthy();
      });
    });

    it('should show proper disabled states', async () => {
      const { getByText } = render(
        <TripPreview {...mockProps} isBookingRide={true} />
      );

      await waitFor(() => {
        // Book button should be disabled during booking
        const bookButton = getByText('Booking ride...');
        expect(bookButton).toBeTruthy();
      });
    });
  });

  describe('Accessibility and UX', () => {
    it('should provide clear visual hierarchy', async () => {
      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        // Check that all important information is displayed
        expect(getByText('From')).toBeTruthy();
        expect(getByText('To')).toBeTruthy();
        expect(getByText('Choose your ride')).toBeTruthy();
        expect(getByText('Distance')).toBeTruthy();
        expect(getByText('Duration')).toBeTruthy();
      });
    });

    it('should handle back navigation properly', async () => {
      const { getByTestId } = render(<TripPreview {...mockProps} />);

      // Note: In a real implementation, you'd add testID to the back button
      // This is a placeholder for the test structure
      expect(mockProps.onBack).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing driver data gracefully', async () => {
      // Mock empty driver response
      require('../../services/driverService').driverService.generateNearbyDrivers.mockReturnValue({
        availableDrivers: [],
        nearestDriver: null,
        totalDriversInArea: 0,
        averageWaitTime: 0,
      });

      const { getByText } = render(<TripPreview {...mockProps} />);

      await waitFor(() => {
        expect(getByText('Select a driver to continue')).toBeTruthy();
      });
    });

    it('should handle distance calculation errors', async () => {
      // Mock distance service error
      require('../../services/distanceService').distanceService.calculateDistance.mockRejectedValue(
        new Error('Distance calculation failed')
      );

      const { getByText } = render(<TripPreview {...mockProps} />);

      // Should still render the component without crashing
      await waitFor(() => {
        expect(getByText('Choose your ride')).toBeTruthy();
      });
    });
  });
});
