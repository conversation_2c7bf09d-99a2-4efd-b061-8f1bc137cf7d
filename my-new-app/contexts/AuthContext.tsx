import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User as SupabaseUser, Session, AuthError } from '@supabase/supabase-js';
import { supabase, Profile, isSupabaseConfigured } from '../lib/supabase';

// Types
interface User {
  id: string;
  email: string;
  createdAt: string;
  profile?: Profile;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isSignedUp: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSignedUp, setIsSignedUp] = useState(false);

  // Helper function to get user profile
  const getUserProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle(); // Use maybeSingle() instead of single() to handle no rows gracefully

      if (error) {
        if (__DEV__) {
          console.error('Error fetching user profile:', error);
        }
        return null;
      }

      return data;
    } catch (error) {
      if (__DEV__) {
        console.error('Error in getUserProfile:', error);
      }
      return null;
    }
  };

  // Helper function to create user profile if it doesn't exist
  const createUserProfile = async (userId: string, email: string, fullName?: string): Promise<Profile | null> => {
    try {
      if (__DEV__) {
        console.log('Creating user profile for:', userId, email);
      }

      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: userId,
          email: email,
          full_name: fullName || '',
          user_type: 'passenger'
        })
        .select()
        .single();

      if (error) {
        if (__DEV__) {
          console.error('Error creating user profile:', error);
        }
        return null;
      }

      if (__DEV__) {
        console.log('User profile created successfully:', data);
      }
      return data;
    } catch (error) {
      if (__DEV__) {
        console.error('Error in createUserProfile:', error);
      }
      return null;
    }
  };

  // Helper function to get or create user profile
  const getOrCreateUserProfile = async (userId: string, email: string, fullName?: string): Promise<Profile | null> => {
    // First try to get existing profile
    let profile = await getUserProfile(userId);

    // If no profile exists, try to create one
    if (!profile) {
      if (__DEV__) {
        console.log('No profile found, attempting to create one...');
      }
      profile = await createUserProfile(userId, email, fullName);

      // If creation failed, wait a bit and try to fetch again (in case trigger is slow)
      if (!profile) {
        if (__DEV__) {
          console.log('Profile creation failed, waiting and retrying fetch...');
        }
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        profile = await getUserProfile(userId);
      }
    }

    return profile;
  };

  // Check authentication status on app start
  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);

      if (!isSupabaseConfigured()) {
        console.warn('Supabase not configured, using fallback auth');
        setUser(null);
        setSession(null);
        setIsSignedUp(false);
        return;
      }

      // Get current session from Supabase
      if (!supabase) {
        setUser(null);
        setSession(null);
        setIsSignedUp(false);
        return;
      }

      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        if (__DEV__) {
          console.error('Error getting session:', error);
        }
        setUser(null);
        setSession(null);
        setIsSignedUp(false);
        return;
      }

      if (session?.user) {
        // Get or create user profile
        const profile = await getOrCreateUserProfile(
          session.user.id,
          session.user.email || '',
          session.user.user_metadata?.full_name
        );

        const userData: User = {
          id: session.user.id,
          email: session.user.email || '',
          createdAt: session.user.created_at,
          profile: profile || undefined,
        };

        setUser(userData);
        setSession(session);
        setIsSignedUp(true);
      } else {
        setUser(null);
        setSession(null);
        setIsSignedUp(false);
      }
    } catch (error) {
      if (__DEV__) {
        console.error('Error checking auth status:', error);
      }
      setUser(null);
      setSession(null);
      setIsSignedUp(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Sign up function
  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      setIsLoading(true);

      if (!isSupabaseConfigured() || !supabase) {
        throw new Error('Authentication service not configured. Please check your setup.');
      }

      // Sign up with Supabase
      const { data, error } = await supabase.auth.signUp({
        email: email.toLowerCase().trim(),
        password: password,
        options: {
          data: {
            full_name: fullName || '',
          },
        },
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // User created successfully, now get or create profile
        const profile = await getOrCreateUserProfile(
          data.user.id,
          data.user.email || '',
          fullName
        );

        const userData: User = {
          id: data.user.id,
          email: data.user.email || '',
          createdAt: data.user.created_at,
          profile: profile || undefined,
        };

        setUser(userData);
        setSession(data.session);
        setIsSignedUp(true);
      } else {
        throw new Error('Failed to create user account');
      }
    } catch (error) {
      if (__DEV__) {
        console.error('Sign up error:', error);
      }
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create account. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);

      if (!isSupabaseConfigured() || !supabase) {
        throw new Error('Authentication service not configured. Please check your setup.');
      }

      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.toLowerCase().trim(),
        password: password,
      });

      if (error) {
        throw error;
      }

      if (data.user && data.session) {
        // Get user profile
        const profile = await getUserProfile(data.user.id);

        const userData: User = {
          id: data.user.id,
          email: data.user.email || '',
          createdAt: data.user.created_at,
          profile: profile || undefined,
        };

        setUser(userData);
        setSession(data.session);
        setIsSignedUp(true);
      } else {
        throw new Error('Failed to sign in');
      }
    } catch (error) {
      if (__DEV__) {
        console.error('Sign in error:', error);
      }
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to sign in. Please check your credentials.');
    } finally {
      setIsLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      setIsLoading(true);

      if (isSupabaseConfigured() && supabase) {
        const { error } = await supabase.auth.signOut();
        if (error) {
          if (__DEV__) {
            console.error('Supabase sign out error:', error);
          }
        }
      }

      // Clear local state
      setUser(null);
      setSession(null);
      setIsSignedUp(false);
    } catch (error) {
      if (__DEV__) {
        console.error('Sign out error:', error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    try {
      setIsLoading(true);

      if (!isSupabaseConfigured() || !supabase) {
        throw new Error('Authentication service not configured. Please check your setup.');
      }

      // Send password reset email
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'shemove://ResetPasswordPage', // Deep link for mobile app
      });

      if (error) {
        throw error;
      }

      // Success - email sent
    } catch (error) {
      if (__DEV__) {
        console.error('Forgot password error:', error);
      }
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (token: string, newPassword: string) => {
    try {
      setIsLoading(true);

      if (!isSupabaseConfigured() || !supabase) {
        throw new Error('Authentication service not configured. Please check your setup.');
      }

      // Update password with the reset token
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        throw error;
      }

      // Success - password updated
    } catch (error) {
      if (__DEV__) {
        console.error('Reset password error:', error);
      }
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Check auth status on mount and listen for auth changes
  useEffect(() => {
    checkAuthStatus();

    // Listen for auth state changes (only if Supabase is configured)
    let subscription: any = null;

    if (supabase) {
      const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // Only log auth state changes in development mode
        if (__DEV__) {
          console.log('Auth state changed:', event, session?.user?.email);
        }

        try {
          if (session?.user) {
            // Get or create user profile
            const profile = await getOrCreateUserProfile(
              session.user.id,
              session.user.email || '',
              session.user.user_metadata?.full_name
            );

            const userData: User = {
              id: session.user.id,
              email: session.user.email || '',
              createdAt: session.user.created_at,
              profile: profile || undefined,
            };

            setUser(userData);
            setSession(session);
            setIsSignedUp(true);
          } else {
            setUser(null);
            setSession(null);
            setIsSignedUp(false);
          }
        } catch (error) {
          // Silent error handling - log only in development
          if (__DEV__) {
            console.error('Auth state change error:', error);
          }
          // Ensure clean state on error
          setUser(null);
          setSession(null);
          setIsSignedUp(false);
        }

        setIsLoading(false);
      }
      );

      subscription = authSubscription;
    }

    // Cleanup subscription on unmount
    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    isLoading,
    isSignedUp,
    signUp,
    signIn,
    signOut,
    forgotPassword,
    resetPassword,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper function to get current session
export const getCurrentSession = async (): Promise<Session | null> => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      if (__DEV__) {
        console.error('Error getting current session:', error);
      }
      return null;
    }
    return session;
  } catch (error) {
    if (__DEV__) {
      console.error('Error in getCurrentSession:', error);
    }
    return null;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const session = await getCurrentSession();
    return session !== null && session.user !== null;
  } catch (error) {
    if (__DEV__) {
      console.error('Error checking authentication:', error);
    }
    return false;
  }
};
