-- =====================================================
-- CHECK EXISTING DATABASE STRUCTURE
-- =====================================================
-- Run this first to see what you already have

-- 1. Check if drivers table exists and its structure
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'drivers' 
ORDER BY ordinal_position;

-- 2. Check existing indexes on drivers table
SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename = 'drivers';

-- 3. Check if the function already exists
SELECT 
  routine_name,
  routine_type
FROM information_schema.routines 
WHERE routine_name = 'get_nearby_drivers';

-- 4. Check for any drivers data
SELECT 
  COUNT(*) as total_drivers,
  COUNT(CASE WHEN is_online = true THEN 1 END) as online_drivers,
  COUNT(CASE WHEN current_location IS NOT NULL THEN 1 END) as drivers_with_location
FROM drivers;

-- 5. Check current_location data type and sample data
SELECT 
  id,
  current_location,
  pg_typeof(current_location) as location_data_type,
  is_online,
  verification_status
FROM drivers 
LIMIT 3;
