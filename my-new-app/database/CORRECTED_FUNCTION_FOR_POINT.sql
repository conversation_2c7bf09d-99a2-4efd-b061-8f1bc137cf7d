-- =====================================================
-- CORRECTED FUNCTION FOR POINT DATA TYPE
-- =====================================================
-- This function works with your existing POINT data format

CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    -- Convert POINT to JSONB format for the app
    CASE 
      WHEN d.current_location IS NULL THEN NULL
      ELSE jsonb_build_object(
        'lat', ST_Y(d.current_location),
        'lng', ST_X(d.current_location)
      )
    END as current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Calculate distance using POINT coordinates
    CASE 
      WHEN d.current_location IS NULL THEN 999999.0
      ELSE
        -- Haversine formula using POINT data
        (
          6371 * acos(
            GREATEST(-1, LEAST(1,
              cos(radians(user_lat)) * 
              cos(radians(ST_Y(d.current_location))) * 
              cos(radians(ST_X(d.current_location)) - radians(user_lng)) + 
              sin(radians(user_lat)) * 
              sin(radians(ST_Y(d.current_location)))
            ))
          )
        )
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    -- Simple bounding box filter using POINT coordinates
    AND ST_Y(d.current_location) BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND ST_X(d.current_location) BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
  ORDER BY distance_km ASC
  LIMIT 20;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Test with your actual driver location
SELECT 'Updated function for POINT data!' as status;

-- Test the function with coordinates near your drivers
SELECT 
  id,
  full_name,
  current_location,
  vehicle_make,
  vehicle_model,
  is_online,
  distance_km
FROM get_nearby_drivers(-26.3019906, 27.8769885, 10.0);

-- Show what the function returns
SELECT 'Function test completed - check results above' as test_status;
