-- =====================================================
-- EXECUTE THIS SQL IN SUPABASE SQL EDITOR
-- =====================================================
-- This SQL creates the get_nearby_drivers function needed for the driver map feature
-- Copy and paste this entire file into your Supabase SQL Editor and run it

-- Step 1: Create the get_nearby_drivers function
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    d.current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Calculate distance using Haversine formula
    -- Handle both JSONB and POINT data types for current_location
    CASE
      WHEN d.current_location IS NULL THEN NULL
      WHEN jsonb_typeof(d.current_location) = 'object' THEN
        -- Handle JSONB format: {"lat": -26.2041, "lng": 28.0473}
        (
          6371 * acos(
            cos(radians(user_lat)) *
            cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) *
            cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) +
            sin(radians(user_lat)) *
            sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
          )
        )
      ELSE
        -- Handle POINT format: POINT(lng lat)
        (
          6371 * acos(
            cos(radians(user_lat)) *
            cos(radians(ST_Y(d.current_location))) *
            cos(radians(ST_X(d.current_location)) - radians(user_lng)) +
            sin(radians(user_lat)) *
            sin(radians(ST_Y(d.current_location)))
          )
        )
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    AND (
      -- Handle JSONB format
      (jsonb_typeof(d.current_location) = 'object'
       AND d.current_location->>'lat' IS NOT NULL
       AND d.current_location->>'lng' IS NOT NULL
       AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
       AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
      )
      OR
      -- Handle POINT format
      (jsonb_typeof(d.current_location) != 'object'
       AND ST_Y(d.current_location) BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
       AND ST_X(d.current_location) BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
      )
    )
  ORDER BY distance_km ASC
  LIMIT 20; -- Limit to 20 nearest drivers for performance
END;
$$;

-- Step 2: Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Step 3: Create indexes for better performance on driver location queries
-- Note: Using GIST index for spatial data (POINT type) instead of GIN
CREATE INDEX IF NOT EXISTS idx_drivers_location_online
ON drivers USING GIST (current_location)
WHERE is_online = true AND verification_status = 'approved';

-- Step 4: Create index for driver availability queries
CREATE INDEX IF NOT EXISTS idx_drivers_online_status
ON drivers (is_online, verification_status)
WHERE is_online = true;

-- Step 5: Create additional indexes for better performance
CREATE INDEX IF NOT EXISTS idx_drivers_current_location
ON drivers USING GIST (current_location)
WHERE current_location IS NOT NULL;

-- Step 5: Ensure trips table exists with correct structure
CREATE TABLE IF NOT EXISTS trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    passenger_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    pickup_location TEXT NOT NULL,
    pickup_coordinates POINT NOT NULL,
    destination_location TEXT NOT NULL,
    destination_coordinates POINT NOT NULL,
    ride_type TEXT NOT NULL DEFAULT 'SheRide',
    status TEXT DEFAULT 'requested',
    fare_amount DECIMAL(10,2) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.0,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    driver_accepted_at TIMESTAMP WITH TIME ZONE,
    driver_arrived_at TIMESTAMP WITH TIME ZONE,
    trip_started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancelled_by TEXT,
    pickup_address_short TEXT,
    destination_address_short TEXT,
    driver_notes TEXT,
    passenger_notes TEXT,
    estimated_arrival_time INTEGER,
    actual_pickup_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create trip indexes for performance
CREATE INDEX IF NOT EXISTS idx_trips_passenger_id ON trips(passenger_id);
CREATE INDEX IF NOT EXISTS idx_trips_driver_id ON trips(driver_id);
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_created_at ON trips(created_at DESC);

-- Step 7: Enable RLS on trips table
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Step 8: Create RLS policies for trips
CREATE POLICY "Users can view their own trips as passenger" ON trips
    FOR SELECT USING (passenger_id = auth.uid());

CREATE POLICY "Users can insert their own trips" ON trips
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

CREATE POLICY "Drivers can view assigned trips" ON trips
    FOR SELECT USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

CREATE POLICY "Drivers can update assigned trips" ON trips
    FOR UPDATE USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

-- Step 9: Test the function (optional - you can run this to verify it works)
-- SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these queries to verify everything is working:

-- 1. Check if the function exists
SELECT 
  routine_name, 
  routine_type, 
  data_type 
FROM information_schema.routines 
WHERE routine_name = 'get_nearby_drivers' 
  AND routine_schema = 'public';

-- 2. Check for online drivers
SELECT 
  COUNT(*) as total_drivers,
  COUNT(CASE WHEN is_online = true THEN 1 END) as online_drivers,
  COUNT(CASE WHEN is_online = true AND verification_status = 'approved' THEN 1 END) as online_approved_drivers
FROM drivers;

-- 3. Check driver locations
SELECT 
  id,
  current_location,
  is_online,
  verification_status,
  vehicle_make,
  vehicle_model,
  vehicle_type
FROM drivers 
WHERE is_online = true 
  AND verification_status = 'approved'
  AND current_location IS NOT NULL
LIMIT 5;

-- =====================================================
-- TROUBLESHOOTING
-- =====================================================
-- If you get errors, check these common issues:

-- Issue 1: "relation drivers does not exist"
-- Solution: Make sure the drivers table exists. Check with:
-- SELECT table_name FROM information_schema.tables WHERE table_name = 'drivers';

-- Issue 2: "column current_location does not exist"
-- Solution: Make sure the drivers table has the current_location column. Check with:
-- SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'drivers';

-- Issue 3: "permission denied for function get_nearby_drivers"
-- Solution: Make sure you ran the GRANT statement above

-- Issue 4: No drivers returned
-- Solution: Make sure you have:
-- 1. Created driver accounts using the driver app
-- 2. Set drivers to online status (is_online = true)
-- 3. Approved drivers in the admin dashboard (verification_status = 'approved')
-- 4. Drivers have valid location data in current_location column

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DRIVER MAP FUNCTION SETUP COMPLETED';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created:';
    RAISE NOTICE '1. get_nearby_drivers() function';
    RAISE NOTICE '2. Performance indexes';
    RAISE NOTICE '3. Proper permissions';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test the passenger app driver map feature';
    RAISE NOTICE '2. Create test drivers if needed';
    RAISE NOTICE '3. Verify car icons appear on the map';
    RAISE NOTICE '=================================================';
END $$;
