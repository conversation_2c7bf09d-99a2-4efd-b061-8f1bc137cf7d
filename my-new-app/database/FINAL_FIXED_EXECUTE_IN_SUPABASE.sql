-- =====================================================
-- FINAL FIXED SQL FOR SUPABASE - NO POINT INDEX ISSUES
-- =====================================================
-- This S<PERSON> creates the get_nearby_drivers function needed for the driver map feature
-- COMPLETELY FIXED: Removed ALL problematic indexes on POINT data types

-- Step 1: Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 2: Create the get_nearby_drivers function (simplified version)
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    d.current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Simplified distance calculation for JSONB location data
    CASE 
      WHEN d.current_location IS NULL THEN 999999.0
      WHEN d.current_location->>'lat' IS NULL OR d.current_location->>'lng' IS NULL THEN 999999.0
      ELSE
        -- Calculate distance using Haversine formula
        (
          6371 * acos(
            GREATEST(-1, LEAST(1,
              cos(radians(user_lat)) * 
              cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
              cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
              sin(radians(user_lat)) * 
              sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
            ))
          )
        )
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    AND d.current_location->>'lat' IS NOT NULL
    AND d.current_location->>'lng' IS NOT NULL
    -- Simple bounding box filter for performance
    AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
  ORDER BY distance_km ASC
  LIMIT 20; -- Limit to 20 nearest drivers for performance
END;
$$;

-- Step 3: Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Step 4: Create ONLY safe indexes (NO POINT or spatial indexes)
-- Use B-tree indexes for boolean and text columns only
CREATE INDEX IF NOT EXISTS idx_drivers_online_status 
ON drivers (is_online, verification_status) 
WHERE is_online = true;

-- Create index for JSONB location data (this should work)
CREATE INDEX IF NOT EXISTS idx_drivers_location_jsonb 
ON drivers USING GIN (current_location) 
WHERE is_online = true AND verification_status = 'approved' AND current_location IS NOT NULL;

-- Step 5: Ensure trips table exists with correct structure (using TEXT for coordinates to avoid POINT issues)
CREATE TABLE IF NOT EXISTS trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    passenger_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    pickup_location TEXT NOT NULL,
    pickup_coordinates TEXT NOT NULL, -- Changed from POINT to TEXT to avoid index issues
    destination_location TEXT NOT NULL,
    destination_coordinates TEXT NOT NULL, -- Changed from POINT to TEXT to avoid index issues
    ride_type TEXT NOT NULL DEFAULT 'SheRide',
    status TEXT DEFAULT 'requested',
    fare_amount DECIMAL(10,2) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.0,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    driver_accepted_at TIMESTAMP WITH TIME ZONE,
    driver_arrived_at TIMESTAMP WITH TIME ZONE,
    trip_started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancelled_by TEXT,
    pickup_address_short TEXT,
    destination_address_short TEXT,
    driver_notes TEXT,
    passenger_notes TEXT,
    estimated_arrival_time INTEGER,
    actual_pickup_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create ONLY safe trip indexes (NO spatial indexes)
CREATE INDEX IF NOT EXISTS idx_trips_passenger_id ON trips(passenger_id);
CREATE INDEX IF NOT EXISTS idx_trips_driver_id ON trips(driver_id);
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_created_at ON trips(created_at DESC);

-- Step 7: Enable RLS on trips table
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Step 8: Create RLS policies for trips (drop existing first to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own trips as passenger" ON trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON trips;
DROP POLICY IF EXISTS "Drivers can view assigned trips" ON trips;
DROP POLICY IF EXISTS "Drivers can update assigned trips" ON trips;

CREATE POLICY "Users can view their own trips as passenger" ON trips
    FOR SELECT USING (passenger_id = auth.uid());

CREATE POLICY "Users can insert their own trips" ON trips
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

CREATE POLICY "Drivers can view assigned trips" ON trips
    FOR SELECT USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

CREATE POLICY "Drivers can update assigned trips" ON trips
    FOR UPDATE USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

-- Step 9: Test the function (run this to verify it works)
SELECT 'Testing get_nearby_drivers function...' as test_message;
-- Uncomment the line below to test with actual coordinates:
-- SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- 1. Check if the function exists
SELECT 
  'Function Check' as check_type,
  routine_name, 
  routine_type
FROM information_schema.routines 
WHERE routine_name = 'get_nearby_drivers' 
  AND routine_schema = 'public';

-- 2. Check for drivers table
SELECT 
  'Table Check' as check_type,
  table_name,
  'exists' as status
FROM information_schema.tables 
WHERE table_name IN ('drivers', 'profiles', 'trips');

-- 3. Check for online drivers (if drivers table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'drivers') THEN
    RAISE NOTICE 'Drivers table exists - checking for online drivers...';
  ELSE
    RAISE NOTICE 'Drivers table does not exist yet - this is normal for new setups';
  END IF;
END $$;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DRIVER MAP FUNCTION SETUP COMPLETED (FINAL FIX)';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Successfully created:';
    RAISE NOTICE '1. ✅ get_nearby_drivers() function';
    RAISE NOTICE '2. ✅ Safe performance indexes (no POINT issues)';
    RAISE NOTICE '3. ✅ Trips table with TEXT coordinates';
    RAISE NOTICE '4. ✅ Proper permissions and RLS policies';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Fixed Issues:';
    RAISE NOTICE '1. ✅ Removed ALL problematic POINT indexes';
    RAISE NOTICE '2. ✅ Changed coordinates to TEXT format';
    RAISE NOTICE '3. ✅ Simplified to avoid spatial index issues';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test: SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);';
    RAISE NOTICE '2. Start the passenger app to test driver map';
    RAISE NOTICE '3. Create test drivers if needed';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '🎉 SETUP COMPLETE - NO MORE ERRORS EXPECTED!';
    RAISE NOTICE '=================================================';
END $$;
