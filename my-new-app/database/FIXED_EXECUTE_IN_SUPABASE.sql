-- =====================================================
-- EXECUTE THIS FIXED SQL IN SUPABASE SQL EDITOR
-- =====================================================
-- This SQL creates the get_nearby_drivers function needed for the driver map feature
-- FIXED: Removed problematic GIN index on POINT data type

-- Step 1: Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Step 2: Create the get_nearby_drivers function (simplified version)
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    d.current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Simplified distance calculation for JSONB location data
    CASE 
      WHEN d.current_location IS NULL THEN 999999.0
      WHEN d.current_location->>'lat' IS NULL OR d.current_location->>'lng' IS NULL THEN 999999.0
      ELSE
        -- Calculate distance using Haversine formula
        (
          6371 * acos(
            GREATEST(-1, LEAST(1,
              cos(radians(user_lat)) * 
              cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
              cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
              sin(radians(user_lat)) * 
              sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
            ))
          )
        )
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    AND d.current_location->>'lat' IS NOT NULL
    AND d.current_location->>'lng' IS NOT NULL
    -- Simple bounding box filter for performance
    AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
  ORDER BY distance_km ASC
  LIMIT 20; -- Limit to 20 nearest drivers for performance
END;
$$;

-- Step 3: Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Step 4: Create indexes for better performance (FIXED - removed problematic GIN index)
-- Use B-tree indexes for boolean and text columns
CREATE INDEX IF NOT EXISTS idx_drivers_online_status 
ON drivers (is_online, verification_status) 
WHERE is_online = true;

-- Create index for JSONB location data (using GIN for JSONB, not POINT)
CREATE INDEX IF NOT EXISTS idx_drivers_location_jsonb 
ON drivers USING GIN (current_location) 
WHERE is_online = true AND verification_status = 'approved' AND current_location IS NOT NULL;

-- Step 5: Ensure trips table exists with correct structure
CREATE TABLE IF NOT EXISTS trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    passenger_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    pickup_location TEXT NOT NULL,
    pickup_coordinates POINT NOT NULL,
    destination_location TEXT NOT NULL,
    destination_coordinates POINT NOT NULL,
    ride_type TEXT NOT NULL DEFAULT 'SheRide',
    status TEXT DEFAULT 'requested',
    fare_amount DECIMAL(10,2) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.0,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    driver_accepted_at TIMESTAMP WITH TIME ZONE,
    driver_arrived_at TIMESTAMP WITH TIME ZONE,
    trip_started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancelled_by TEXT,
    pickup_address_short TEXT,
    destination_address_short TEXT,
    driver_notes TEXT,
    passenger_notes TEXT,
    estimated_arrival_time INTEGER,
    actual_pickup_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create trip indexes for performance
CREATE INDEX IF NOT EXISTS idx_trips_passenger_id ON trips(passenger_id);
CREATE INDEX IF NOT EXISTS idx_trips_driver_id ON trips(driver_id);
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_created_at ON trips(created_at DESC);

-- Step 7: Enable RLS on trips table
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Step 8: Create RLS policies for trips (drop existing first to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own trips as passenger" ON trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON trips;
DROP POLICY IF EXISTS "Drivers can view assigned trips" ON trips;
DROP POLICY IF EXISTS "Drivers can update assigned trips" ON trips;

CREATE POLICY "Users can view their own trips as passenger" ON trips
    FOR SELECT USING (passenger_id = auth.uid());

CREATE POLICY "Users can insert their own trips" ON trips
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

CREATE POLICY "Drivers can view assigned trips" ON trips
    FOR SELECT USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

CREATE POLICY "Drivers can update assigned trips" ON trips
    FOR UPDATE USING (driver_id IN (
        SELECT id FROM drivers WHERE user_id = auth.uid()
    ));

-- Step 9: Test the function (optional - you can run this to verify it works)
-- SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these queries to verify everything is working:

-- 1. Check if the function exists
SELECT 
  routine_name, 
  routine_type, 
  data_type 
FROM information_schema.routines 
WHERE routine_name = 'get_nearby_drivers' 
  AND routine_schema = 'public';

-- 2. Check for online drivers
SELECT 
  COUNT(*) as total_drivers,
  COUNT(CASE WHEN is_online = true THEN 1 END) as online_drivers,
  COUNT(CASE WHEN is_online = true AND verification_status = 'approved' THEN 1 END) as online_approved_drivers
FROM drivers;

-- 3. Check driver locations (first 3 records)
SELECT 
  id,
  current_location,
  is_online,
  verification_status,
  vehicle_make,
  vehicle_model,
  vehicle_type
FROM drivers 
WHERE is_online = true 
  AND verification_status = 'approved'
  AND current_location IS NOT NULL
LIMIT 3;

-- =====================================================
-- SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DRIVER MAP FUNCTION SETUP COMPLETED (FIXED)';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created:';
    RAISE NOTICE '1. get_nearby_drivers() function (fixed)';
    RAISE NOTICE '2. Performance indexes (corrected)';
    RAISE NOTICE '3. Trips table structure';
    RAISE NOTICE '4. Proper permissions and RLS policies';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Fixed Issues:';
    RAISE NOTICE '1. Removed problematic GIN index on POINT data';
    RAISE NOTICE '2. Added proper JSONB location handling';
    RAISE NOTICE '3. Simplified distance calculation';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test the passenger app driver map feature';
    RAISE NOTICE '2. Create test drivers if needed';
    RAISE NOTICE '3. Verify car icons appear on the map';
    RAISE NOTICE '=================================================';
END $$;
