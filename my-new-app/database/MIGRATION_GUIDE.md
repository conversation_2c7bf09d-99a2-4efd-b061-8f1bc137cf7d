# 🗄️ SheMove Database Migration Guide

## 📋 **Phase 1: Fix Current Issues - Database Setup**

This guide covers the database migrations for Phase 1 improvements including search history, recent trips enhancements, and LocationIQ integration fixes.

---

## 🚀 **Quick Start (Recommended)**

### **Option 1: Run Master Migration (All-in-One)**
```sql
-- Run this in Supabase SQL Editor
-- This includes all tables, indexes, policies, and functions
\i 000_master_migration.sql
\i 001_rls_policies.sql
\i 003_search_history.sql
\i 004_recent_trips_enhancements.sql
```

### **Option 2: Step-by-Step Migration**
If you prefer to run migrations individually:

1. **Core Setup** (if not already done)
   ```sql
   \i 000_master_migration.sql
   ```

2. **Security Policies**
   ```sql
   \i 001_rls_policies.sql
   ```

3. **Search History Features**
   ```sql
   \i 003_search_history.sql
   ```

4. **Recent Trips Enhancements**
   ```sql
   \i 004_recent_trips_enhancements.sql
   ```

---

## 📊 **What Gets Created**

### **New Tables**
- `search_history` - User search patterns and suggestions
- `favorite_locations` - Home, work, and saved places
- `recent_destinations` - Frequently visited locations

### **Enhanced Tables**
- `trips` - Added completion timestamps and address shortcuts

### **New Functions**
- `get_search_suggestions()` - Smart search suggestions
- `get_recent_trips()` - Formatted recent trip history
- `get_frequent_destinations()` - Popular destinations
- `get_trip_statistics()` - User trip analytics

### **Performance Indexes**
- Search history optimization
- Trip querying improvements
- Location-based searches

### **Security (RLS)**
- Row-level security for all new tables
- User isolation and data protection

---

## 🔧 **Manual Migration Steps**

### **Step 1: Backup Current Data**
```sql
-- Create backup of existing data
CREATE TABLE trips_backup AS SELECT * FROM trips;
CREATE TABLE profiles_backup AS SELECT * FROM profiles;
```

### **Step 2: Run Core Migration**
```sql
-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Run master migration
\i 000_master_migration.sql
```

### **Step 3: Apply Security Policies**
```sql
\i 001_rls_policies.sql
```

### **Step 4: Add Search Features**
```sql
\i 003_search_history.sql
```

### **Step 5: Enhance Trip Management**
```sql
\i 004_recent_trips_enhancements.sql
```

### **Step 6: Verify Migration**
```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('search_history', 'favorite_locations', 'recent_destinations');

-- Check functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE 'get_%';

-- Test a function
SELECT * FROM get_search_suggestions('your-user-id', 'test', 5);
```

---

## 🔄 **Rollback Procedure**

If you need to rollback the changes:

```sql
-- WARNING: This will delete Phase 1 data!
\i 999_rollback.sql
```

**What rollback does:**
- ✅ Preserves core tables (profiles, drivers, trips, trip_ratings)
- ❌ Removes search_history, favorite_locations, recent_destinations
- ❌ Removes enhanced trip columns
- ❌ Removes Phase 1 functions and indexes

---

## 🧪 **Testing Your Migration**

### **Test Search History**
```sql
-- Insert test search
INSERT INTO search_history (user_id, search_query, result_clicked) 
VALUES ('your-user-id', 'test location', true);

-- Get suggestions
SELECT * FROM get_search_suggestions('your-user-id', 'test', 5);
```

### **Test Recent Trips**
```sql
-- Get recent trips
SELECT * FROM get_recent_trips('your-user-id', 10);

-- Get trip statistics
SELECT * FROM get_trip_statistics('your-user-id');
```

### **Test Favorite Locations**
```sql
-- Add favorite location
INSERT INTO favorite_locations (user_id, label, address, coordinates) 
VALUES ('your-user-id', 'Home', '123 Main St', POINT(28.0473, -26.2041));

-- Get favorites
SELECT * FROM favorite_locations WHERE user_id = 'your-user-id';
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

1. **"Extension does not exist" error**
   ```sql
   -- Run as superuser or contact Supabase support
   CREATE EXTENSION IF NOT EXISTS "postgis";
   ```

2. **"Permission denied" error**
   ```sql
   -- Check RLS policies are applied
   SELECT * FROM pg_policies WHERE tablename = 'search_history';
   ```

3. **"Function does not exist" error**
   ```sql
   -- Check function was created
   SELECT routine_name FROM information_schema.routines 
   WHERE routine_name = 'get_search_suggestions';
   ```

### **Performance Issues**

1. **Slow search queries**
   ```sql
   -- Check indexes exist
   SELECT indexname FROM pg_indexes 
   WHERE tablename = 'search_history';
   ```

2. **Slow trip queries**
   ```sql
   -- Analyze query performance
   EXPLAIN ANALYZE SELECT * FROM get_recent_trips('user-id', 10);
   ```

---

## 📈 **Post-Migration Steps**

### **Application Updates Required**

1. **Update TypeScript types** - New table types are in `lib/supabase.ts`
2. **Update services** - Use new `searchHistoryService` and `recentTripsService`
3. **Test LocationIQ integration** - Verify API key is working
4. **Test search debouncing** - Confirm reduced API calls

### **Monitoring**

1. **Check search performance**
   ```sql
   SELECT COUNT(*) FROM search_history;
   SELECT AVG(EXTRACT(EPOCH FROM (NOW() - search_timestamp))) as avg_age_seconds 
   FROM search_history;
   ```

2. **Monitor trip data**
   ```sql
   SELECT status, COUNT(*) FROM trips GROUP BY status;
   ```

---

## 🎯 **Success Criteria**

✅ **Migration is successful when:**
- All new tables exist and are accessible
- RLS policies prevent unauthorized access
- Search suggestions work for authenticated users
- Recent trips load from database (not mock data)
- LocationIQ fallback works (no more API key warnings)
- Search debouncing reduces API calls

✅ **Application is working when:**
- Users can search for locations
- Recent searches appear when focusing search input
- Recent trips show real data from database
- No console errors related to database operations

---

## 📞 **Support**

If you encounter issues:

1. **Check Supabase logs** in your dashboard
2. **Verify user authentication** is working
3. **Test with a simple query** first
4. **Check this guide** for troubleshooting steps
5. **Run rollback script** if needed to restore previous state

---

**Migration prepared for SheMove Phase 1 - Search History & Recent Trips Enhancement** 🌸
