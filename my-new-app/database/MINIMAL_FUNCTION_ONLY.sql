-- =====================================================
-- MINIMAL SQL - FUNCTION ONLY (NO PROBLEMATIC INDEXES)
-- =====================================================
-- This creates ONLY the get_nearby_drivers function
-- NO indexes, NO table modifications, NO spatial operations

-- Just create the function that works with your existing database structure
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    -- Handle both JSONB and POINT data types for current_location
    CASE 
      WHEN d.current_location IS NULL THEN NULL
      ELSE d.current_location
    END as current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Simple distance calculation (approximate)
    CASE 
      WHEN d.current_location IS NULL THEN 999999.0
      WHEN d.current_location->>'lat' IS NULL OR d.current_location->>'lng' IS NULL THEN 999999.0
      ELSE
        -- Simple distance approximation
        SQRT(
          POWER((d.current_location->>'lat')::DOUBLE PRECISION - user_lat, 2) + 
          POWER((d.current_location->>'lng')::DOUBLE PRECISION - user_lng, 2)
        ) * 111.0 -- Convert to approximate km
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
  ORDER BY distance_km ASC
  LIMIT 20;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Test the function
SELECT 'Function created successfully!' as status;

-- Simple test (uncomment to test):
-- SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);
