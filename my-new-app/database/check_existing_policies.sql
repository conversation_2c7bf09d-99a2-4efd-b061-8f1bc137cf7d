-- =====================================================
-- Check Existing Policies Before Running RLS Migration
-- Run this first to see what policies already exist
-- =====================================================

-- Check existing policies on trips table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'trips'
ORDER BY policyname;

-- Check existing policies on driver-related tables
SELECT 
    schemaname,
    tablename,
    policyname
FROM pg_policies 
WHERE tablename IN (
    'driver_documents',
    'driver_availability', 
    'driver_earnings',
    'driver_locations',
    'trip_requests',
    'incident_reports'
)
ORDER BY tablename, policyname;

-- Check if driver tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'driver_documents',
    'driver_availability',
    'driver_earnings', 
    'driver_locations',
    'trip_requests',
    'incident_reports'
)
ORDER BY table_name;

-- Check if driver functions exist
SELECT routine_name, routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%driver%'
ORDER BY routine_name;
