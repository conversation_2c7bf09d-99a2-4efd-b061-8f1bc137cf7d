-- Function to get nearby drivers for the passenger app map
-- This function returns online drivers within a specified radius

CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    d.current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Calculate distance using Haversine formula
    (
      6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
        cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
      )
    ) as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    AND d.current_location->>'lat' IS NOT NULL
    AND d.current_location->>'lng' IS NOT NULL
    -- Filter by radius using bounding box for performance
    AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
    -- Additional precise distance filter
    AND (
      6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
        cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
      )
    ) <= radius_km
  ORDER BY distance_km ASC
  LIMIT 20; -- Limit to 20 nearest drivers for performance
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Example usage:
-- SELECT * FROM get_nearby_drivers(-26.2041, 28.0473, 5.0);

-- Create index for better performance on driver location queries
CREATE INDEX IF NOT EXISTS idx_drivers_location_online 
ON drivers USING GIN (current_location) 
WHERE is_online = true AND verification_status = 'approved';

-- Create index for driver availability queries
CREATE INDEX IF NOT EXISTS idx_drivers_online_status 
ON drivers (is_online, verification_status) 
WHERE is_online = true;
