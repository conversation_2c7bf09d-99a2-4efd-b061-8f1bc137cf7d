-- =====================================================
-- SheMove Database Master Migration Script
-- Comprehensive setup for all tables, functions, and policies
-- Run this script in Supabase SQL Editor
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE user_type AS ENUM ('passenger', 'driver', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE trip_status AS ENUM ('requested', 'accepted', 'in_progress', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE ride_type AS ENUM ('SheRide', 'ShePool', 'SheXL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE verification_status AS ENUM ('pending', 'approved', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE driver_status AS ENUM ('offline', 'online', 'busy', 'break');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_type AS ENUM ('drivers_license', 'vehicle_registration', 'insurance', 'profile_photo', 'vehicle_photo');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_status AS ENUM ('pending', 'approved', 'rejected', 'expired');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- CORE TABLES (from original setup)
-- =====================================================

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone_number TEXT,
    avatar_url TEXT,
    user_type user_type DEFAULT 'passenger',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create drivers table
CREATE TABLE IF NOT EXISTS drivers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE UNIQUE NOT NULL,
    license_number TEXT UNIQUE NOT NULL,
    license_expiry DATE,
    vehicle_make TEXT NOT NULL,
    vehicle_model TEXT NOT NULL,
    vehicle_year INTEGER NOT NULL,
    vehicle_color TEXT NOT NULL,
    vehicle_plate TEXT UNIQUE NOT NULL,
    vehicle_type ride_type DEFAULT 'SheRide',
    insurance_policy TEXT,
    insurance_expiry DATE,
    verification_status verification_status DEFAULT 'pending',
    is_online BOOLEAN DEFAULT FALSE,
    current_location POINT,
    rating DECIMAL(3,2) DEFAULT 5.0,
    total_trips INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.0,
    acceptance_rate DECIMAL(5,2) DEFAULT 100.0,
    cancellation_rate DECIMAL(5,2) DEFAULT 0.0,
    average_rating DECIMAL(3,2) DEFAULT 5.0,
    onboarding_completed BOOLEAN DEFAULT FALSE,
    background_check_status verification_status DEFAULT 'pending',
    emergency_contact_name TEXT,
    emergency_contact_phone TEXT,
    preferred_language TEXT DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trips table with enhancements
CREATE TABLE IF NOT EXISTS trips (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    passenger_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    pickup_location TEXT NOT NULL,
    pickup_coordinates POINT NOT NULL,
    destination_location TEXT NOT NULL,
    destination_coordinates POINT NOT NULL,
    ride_type ride_type NOT NULL,
    status trip_status DEFAULT 'requested',
    fare_amount DECIMAL(10,2) NOT NULL,
    distance_km DECIMAL(8,2) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE,
    driver_accepted_at TIMESTAMP WITH TIME ZONE,
    driver_arrived_at TIMESTAMP WITH TIME ZONE,
    trip_started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancelled_by TEXT, -- 'passenger', 'driver', 'system'
    pickup_address_short TEXT,
    destination_address_short TEXT,
    driver_notes TEXT,
    passenger_notes TEXT,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.0,
    estimated_arrival_time INTEGER, -- minutes
    actual_pickup_time TIMESTAMP WITH TIME ZONE,
    route_data JSONB, -- Store route coordinates for tracking
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_ratings table
CREATE TABLE IF NOT EXISTS trip_ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
    passenger_rating INTEGER CHECK (passenger_rating >= 1 AND passenger_rating <= 5),
    driver_rating INTEGER CHECK (driver_rating >= 1 AND driver_rating <= 5),
    passenger_comment TEXT,
    driver_comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- DRIVER-SPECIFIC TABLES
-- =====================================================

-- Create driver_documents table for document verification
CREATE TABLE IF NOT EXISTS driver_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    document_type document_type NOT NULL,
    document_url TEXT NOT NULL,
    document_status document_status DEFAULT 'pending',
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES profiles(id),
    rejection_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, document_type)
);

-- Create driver_availability table for scheduling
CREATE TABLE IF NOT EXISTS driver_availability (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    status driver_status DEFAULT 'offline',
    available_from TIMESTAMP WITH TIME ZONE,
    available_until TIMESTAMP WITH TIME ZONE,
    preferred_areas JSONB, -- Array of preferred pickup areas
    max_distance_km INTEGER DEFAULT 15,
    last_location_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create driver_earnings table for financial tracking
CREATE TABLE IF NOT EXISTS driver_earnings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
    base_fare DECIMAL(10,2) NOT NULL,
    distance_fare DECIMAL(10,2) NOT NULL,
    time_fare DECIMAL(10,2) NOT NULL,
    surge_multiplier DECIMAL(3,2) DEFAULT 1.0,
    total_fare DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,4) DEFAULT 0.20, -- 20% commission
    commission_amount DECIMAL(10,2) NOT NULL,
    driver_payout DECIMAL(10,2) NOT NULL,
    tips DECIMAL(10,2) DEFAULT 0.0,
    bonuses DECIMAL(10,2) DEFAULT 0.0,
    payout_status TEXT DEFAULT 'pending', -- pending, processed, paid
    payout_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create driver_locations table for real-time tracking
CREATE TABLE IF NOT EXISTS driver_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    location POINT NOT NULL,
    heading DECIMAL(5,2), -- Bearing in degrees
    speed_kmh DECIMAL(5,2), -- Speed in km/h
    accuracy_meters DECIMAL(8,2),
    is_moving BOOLEAN DEFAULT FALSE,
    trip_id UUID REFERENCES trips(id) ON DELETE SET NULL,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trip_requests table for real-time trip matching
CREATE TABLE IF NOT EXISTS trip_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    trip_id UUID REFERENCES trips(id) ON DELETE CASCADE NOT NULL,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    request_sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    response_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    driver_response TEXT, -- 'accepted', 'declined', 'timeout'
    response_time TIMESTAMP WITH TIME ZONE,
    decline_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(trip_id, driver_id)
);

-- Create incident_reports table for safety
CREATE TABLE IF NOT EXISTS incident_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    reporter_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    trip_id UUID REFERENCES trips(id) ON DELETE SET NULL,
    incident_type TEXT NOT NULL, -- 'safety', 'vehicle', 'route', 'other'
    severity TEXT DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
    description TEXT NOT NULL,
    location POINT,
    photos JSONB, -- Array of photo URLs
    status TEXT DEFAULT 'open', -- 'open', 'investigating', 'resolved', 'closed'
    assigned_to UUID REFERENCES profiles(id),
    resolution_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SEARCH HISTORY TABLES
-- =====================================================

-- Create search_history table
CREATE TABLE IF NOT EXISTS search_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    search_query TEXT NOT NULL,
    selected_result JSONB,
    result_address TEXT,
    result_coordinates POINT,
    search_context TEXT,
    search_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_context POINT,
    result_clicked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create favorite_locations table
CREATE TABLE IF NOT EXISTS favorite_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    label TEXT NOT NULL,
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    icon_name TEXT DEFAULT 'location',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, label)
);

-- Create recent_destinations table
CREATE TABLE IF NOT EXISTS recent_destinations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    visit_count INTEGER DEFAULT 1,
    last_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Core table indexes
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_drivers_online ON drivers(is_online) WHERE is_online = TRUE;
CREATE INDEX IF NOT EXISTS idx_drivers_location ON drivers USING GIST(current_location) WHERE current_location IS NOT NULL;

-- Trip indexes
CREATE INDEX IF NOT EXISTS idx_trips_passenger_recent ON trips(passenger_id, created_at DESC) WHERE status IN ('completed', 'cancelled');
CREATE INDEX IF NOT EXISTS idx_trips_passenger_completed ON trips(passenger_id, completed_at DESC) WHERE status = 'completed';
CREATE INDEX IF NOT EXISTS idx_trips_destination_frequency ON trips(passenger_id, destination_location, status) WHERE status = 'completed';
CREATE INDEX IF NOT EXISTS idx_trips_status ON trips(status);
CREATE INDEX IF NOT EXISTS idx_trips_driver ON trips(driver_id) WHERE driver_id IS NOT NULL;

-- Search history indexes
CREATE INDEX IF NOT EXISTS idx_search_history_user_recent ON search_history(user_id, search_timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_search_history_user_clicked ON search_history(user_id, result_clicked, search_timestamp DESC) WHERE result_clicked = TRUE;
CREATE INDEX IF NOT EXISTS idx_search_history_query_text ON search_history USING gin(to_tsvector('english', search_query));

-- Favorite locations indexes
CREATE INDEX IF NOT EXISTS idx_favorite_locations_user ON favorite_locations(user_id, is_primary DESC, created_at DESC);

-- Recent destinations indexes
CREATE INDEX IF NOT EXISTS idx_recent_destinations_user_frequent ON recent_destinations(user_id, visit_count DESC, last_visited DESC);

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorite_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE recent_destinations ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'SheMove database migration completed successfully at %', NOW();
END $$;
