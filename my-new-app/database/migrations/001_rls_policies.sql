-- =====================================================
-- Row Level Security (RLS) Policies
-- Comprehensive security policies for all tables
-- =====================================================

-- =====================================================
-- PROFILES TABLE POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;

-- Create new policies
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =====================================================
-- DRIVERS TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Drivers can view their own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can update their own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can insert their own data" ON drivers;
DROP POLICY IF EXISTS "Passengers can view driver data for trips" ON drivers;

CREATE POLICY "Drivers can view their own data" ON drivers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own data" ON drivers
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Drivers can insert their own data" ON drivers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Passengers can view driver data for trips" ON drivers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.driver_id = drivers.id 
            AND trips.passenger_id = auth.uid()
        )
    );

-- =====================================================
-- TRIPS TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own trips as passenger" ON trips;
DROP POLICY IF EXISTS "Users can view their own trips as driver" ON trips;
DROP POLICY IF EXISTS "Users can insert trips as passenger" ON trips;
DROP POLICY IF EXISTS "Users can update their own trips as passenger" ON trips;
DROP POLICY IF EXISTS "Drivers can update assigned trips" ON trips;

CREATE POLICY "Users can view their own trips as passenger" ON trips
    FOR SELECT USING (auth.uid() = passenger_id);

CREATE POLICY "Users can view their own trips as driver" ON trips
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user_id FROM drivers WHERE drivers.id = trips.driver_id
        )
    );

CREATE POLICY "Users can insert trips as passenger" ON trips
    FOR INSERT WITH CHECK (auth.uid() = passenger_id);

CREATE POLICY "Users can update their own trips as passenger" ON trips
    FOR UPDATE USING (auth.uid() = passenger_id);

CREATE POLICY "Drivers can update assigned trips" ON trips
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT user_id FROM drivers WHERE drivers.id = trips.driver_id
        )
    );

-- =====================================================
-- TRIP RATINGS TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can view ratings for their trips" ON trip_ratings;
DROP POLICY IF EXISTS "Users can insert ratings for their trips" ON trip_ratings;
DROP POLICY IF EXISTS "Users can update their own ratings" ON trip_ratings;

CREATE POLICY "Users can view ratings for their trips" ON trip_ratings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_ratings.trip_id 
            AND (trips.passenger_id = auth.uid() OR 
                 trips.driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()))
        )
    );

CREATE POLICY "Users can insert ratings for their trips" ON trip_ratings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_ratings.trip_id 
            AND (trips.passenger_id = auth.uid() OR 
                 trips.driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()))
        )
    );

CREATE POLICY "Users can update their own ratings" ON trip_ratings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_ratings.trip_id 
            AND (trips.passenger_id = auth.uid() OR 
                 trips.driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()))
        )
    );

-- =====================================================
-- SEARCH HISTORY TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own search history" ON search_history;
DROP POLICY IF EXISTS "Users can insert their own search history" ON search_history;
DROP POLICY IF EXISTS "Users can update their own search history" ON search_history;
DROP POLICY IF EXISTS "Users can delete their own search history" ON search_history;

CREATE POLICY "Users can view their own search history" ON search_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own search history" ON search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own search history" ON search_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own search history" ON search_history
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- FAVORITE LOCATIONS TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own favorite locations" ON favorite_locations;
DROP POLICY IF EXISTS "Users can insert their own favorite locations" ON favorite_locations;
DROP POLICY IF EXISTS "Users can update their own favorite locations" ON favorite_locations;
DROP POLICY IF EXISTS "Users can delete their own favorite locations" ON favorite_locations;

CREATE POLICY "Users can view their own favorite locations" ON favorite_locations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorite locations" ON favorite_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own favorite locations" ON favorite_locations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorite locations" ON favorite_locations
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- RECENT DESTINATIONS TABLE POLICIES
-- =====================================================

DROP POLICY IF EXISTS "Users can view their own recent destinations" ON recent_destinations;
DROP POLICY IF EXISTS "Users can insert their own recent destinations" ON recent_destinations;
DROP POLICY IF EXISTS "Users can update their own recent destinations" ON recent_destinations;
DROP POLICY IF EXISTS "Users can delete their own recent destinations" ON recent_destinations;

CREATE POLICY "Users can view their own recent destinations" ON recent_destinations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own recent destinations" ON recent_destinations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recent destinations" ON recent_destinations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recent destinations" ON recent_destinations
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant permissions on tables
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Grant permissions on sequences
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- RLS POLICIES COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'RLS policies applied successfully at %', NOW();
END $$;
