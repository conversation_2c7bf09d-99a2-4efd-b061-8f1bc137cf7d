-- =====================================================
-- Search History Migration
-- Creates tables for storing user search patterns
-- Enables personalized suggestions and recent searches
-- =====================================================

-- Create search_history table for storing user search queries
CREATE TABLE IF NOT EXISTS search_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    search_query TEXT NOT NULL,
    selected_result JSONB, -- Store the selected location details
    result_address TEXT, -- Clean address for display
    result_coordinates POINT, -- Geographic coordinates
    search_context TEXT, -- 'pickup' or 'destination'
    search_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    location_context POINT, -- Where user was when searching
    result_clicked BOOLEAN DEFAULT FALSE, -- Whether user selected this result
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_search_history_user_recent 
    ON search_history(user_id, search_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_search_history_user_clicked 
    ON search_history(user_id, result_clicked, search_timestamp DESC) 
    WHERE result_clicked = TRUE;

CREATE INDEX IF NOT EXISTS idx_search_history_query_text 
    ON search_history USING gin(to_tsvector('english', search_query));

-- Create favorite_locations table for home, work, etc.
CREATE TABLE IF NOT EXISTS favorite_locations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    label TEXT NOT NULL, -- 'Home', 'Work', 'Gym', etc.
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE, -- For home/work quick access
    icon_name TEXT DEFAULT 'location', -- Icon identifier
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, label)
);

-- Create index for favorite locations
CREATE INDEX IF NOT EXISTS idx_favorite_locations_user 
    ON favorite_locations(user_id, is_primary DESC, created_at DESC);

-- Create recent_destinations table for frequently visited places
CREATE TABLE IF NOT EXISTS recent_destinations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    address TEXT NOT NULL,
    coordinates POINT NOT NULL,
    visit_count INTEGER DEFAULT 1,
    last_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_visited TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for recent destinations
CREATE INDEX IF NOT EXISTS idx_recent_destinations_user_frequent 
    ON recent_destinations(user_id, visit_count DESC, last_visited DESC);

-- Enable Row Level Security
ALTER TABLE search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorite_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE recent_destinations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for search_history
CREATE POLICY "Users can view their own search history" ON search_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own search history" ON search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own search history" ON search_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own search history" ON search_history
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for favorite_locations
CREATE POLICY "Users can view their own favorite locations" ON favorite_locations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own favorite locations" ON favorite_locations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own favorite locations" ON favorite_locations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own favorite locations" ON favorite_locations
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for recent_destinations
CREATE POLICY "Users can view their own recent destinations" ON recent_destinations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own recent destinations" ON recent_destinations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recent destinations" ON recent_destinations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recent destinations" ON recent_destinations
    FOR DELETE USING (auth.uid() = user_id);

-- Create trigger for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for updated_at
CREATE TRIGGER update_search_history_updated_at
    BEFORE UPDATE ON search_history
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_favorite_locations_updated_at
    BEFORE UPDATE ON favorite_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recent_destinations_updated_at
    BEFORE UPDATE ON recent_destinations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean old search history (keep last 100 per user)
CREATE OR REPLACE FUNCTION cleanup_old_search_history()
RETURNS void AS $$
BEGIN
    DELETE FROM search_history 
    WHERE id IN (
        SELECT id FROM (
            SELECT id, 
                   ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY search_timestamp DESC) as rn
            FROM search_history
        ) ranked
        WHERE rn > 100
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to get search suggestions for a user
CREATE OR REPLACE FUNCTION get_search_suggestions(
    p_user_id UUID,
    p_query TEXT,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE(
    suggestion TEXT,
    address TEXT,
    coordinates POINT,
    suggestion_type TEXT,
    last_used TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    -- Recent clicked searches
    SELECT DISTINCT
        sh.search_query as suggestion,
        sh.result_address as address,
        sh.result_coordinates as coordinates,
        'recent'::TEXT as suggestion_type,
        sh.search_timestamp as last_used
    FROM search_history sh
    WHERE sh.user_id = p_user_id
        AND sh.result_clicked = TRUE
        AND sh.search_query ILIKE p_query || '%'
        AND sh.result_address IS NOT NULL
    ORDER BY sh.search_timestamp DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_search_suggestions TO authenticated;
