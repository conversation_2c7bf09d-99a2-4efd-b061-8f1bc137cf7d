-- =====================================================
-- Recent Trips Enhancements Migration
-- Enhances trips table for better recent trips functionality
-- Adds indexes and functions for efficient querying
-- =====================================================

-- Add additional fields to trips table for better UX
ALTER TABLE trips ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS cancellation_reason TEXT;
ALTER TABLE trips ADD COLUMN IF NOT EXISTS pickup_address_short TEXT; -- Clean address for display
ALTER TABLE trips ADD COLUMN IF NOT EXISTS destination_address_short TEXT; -- Clean address for display

-- Create indexes for efficient recent trips querying
CREATE INDEX IF NOT EXISTS idx_trips_passenger_recent 
    ON trips(passenger_id, created_at DESC) 
    WHERE status IN ('completed', 'cancelled');

CREATE INDEX IF NOT EXISTS idx_trips_passenger_completed 
    ON trips(passenger_id, completed_at DESC) 
    WHERE status = 'completed';

CREATE INDEX IF NOT EXISTS idx_trips_destination_frequency 
    ON trips(passenger_id, destination_location, status) 
    WHERE status = 'completed';

-- Create function to get recent trips for a user
CREATE OR REPLACE FUNCTION get_recent_trips(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE(
    trip_id UUID,
    destination_location TEXT,
    destination_address_short TEXT,
    destination_coordinates POINT,
    pickup_location TEXT,
    pickup_address_short TEXT,
    pickup_coordinates POINT,
    ride_type ride_type,
    fare_amount DECIMAL(10,2),
    distance_km DECIMAL(8,2),
    duration_minutes INTEGER,
    trip_date TIMESTAMP WITH TIME ZONE,
    trip_status trip_status,
    driver_rating INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id as trip_id,
        t.destination_location,
        COALESCE(t.destination_address_short, t.destination_location) as destination_address_short,
        t.destination_coordinates,
        t.pickup_location,
        COALESCE(t.pickup_address_short, t.pickup_location) as pickup_address_short,
        t.pickup_coordinates,
        t.ride_type,
        t.fare_amount,
        t.distance_km,
        t.duration_minutes,
        COALESCE(t.completed_at, t.cancelled_at, t.created_at) as trip_date,
        t.status as trip_status,
        tr.passenger_rating as driver_rating
    FROM trips t
    LEFT JOIN trip_ratings tr ON t.id = tr.trip_id
    WHERE t.passenger_id = p_user_id 
        AND t.status IN ('completed', 'cancelled')
    ORDER BY COALESCE(t.completed_at, t.cancelled_at, t.created_at) DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_recent_trips TO authenticated;

-- Create function to get frequent destinations for a user
CREATE OR REPLACE FUNCTION get_frequent_destinations(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 5
)
RETURNS TABLE(
    destination_location TEXT,
    destination_address_short TEXT,
    destination_coordinates POINT,
    trip_count BIGINT,
    last_trip_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.destination_location,
        COALESCE(t.destination_address_short, t.destination_location) as destination_address_short,
        t.destination_coordinates,
        COUNT(*) as trip_count,
        MAX(COALESCE(t.completed_at, t.created_at)) as last_trip_date
    FROM trips t
    WHERE t.passenger_id = p_user_id 
        AND t.status = 'completed'
    GROUP BY t.destination_location, t.destination_address_short, t.destination_coordinates
    HAVING COUNT(*) > 1  -- Only show destinations visited more than once
    ORDER BY trip_count DESC, last_trip_date DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_frequent_destinations TO authenticated;

-- Create function to get trip statistics for a user
CREATE OR REPLACE FUNCTION get_trip_statistics(
    p_user_id UUID
)
RETURNS TABLE(
    total_trips BIGINT,
    completed_trips BIGINT,
    cancelled_trips BIGINT,
    total_distance_km DECIMAL(10,2),
    total_fare_amount DECIMAL(10,2),
    average_rating DECIMAL(3,2),
    favorite_ride_type ride_type
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_trips,
        COUNT(*) FILTER (WHERE t.status = 'completed') as completed_trips,
        COUNT(*) FILTER (WHERE t.status = 'cancelled') as cancelled_trips,
        COALESCE(SUM(t.distance_km) FILTER (WHERE t.status = 'completed'), 0) as total_distance_km,
        COALESCE(SUM(t.fare_amount) FILTER (WHERE t.status = 'completed'), 0) as total_fare_amount,
        COALESCE(AVG(tr.passenger_rating), 0) as average_rating,
        (
            SELECT t2.ride_type 
            FROM trips t2 
            WHERE t2.passenger_id = p_user_id AND t2.status = 'completed'
            GROUP BY t2.ride_type 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as favorite_ride_type
    FROM trips t
    LEFT JOIN trip_ratings tr ON t.id = tr.trip_id
    WHERE t.passenger_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_trip_statistics TO authenticated;

-- Create trigger to automatically update completed_at and cancelled_at
CREATE OR REPLACE FUNCTION update_trip_completion_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- Set completed_at when status changes to completed
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    
    -- Set cancelled_at when status changes to cancelled
    IF NEW.status = 'cancelled' AND OLD.status != 'cancelled' THEN
        NEW.cancelled_at = NOW();
    END IF;
    
    -- Update updated_at timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for trip completion timestamps
DROP TRIGGER IF EXISTS trigger_trip_completion_timestamps ON trips;
CREATE TRIGGER trigger_trip_completion_timestamps
    BEFORE UPDATE ON trips
    FOR EACH ROW EXECUTE FUNCTION update_trip_completion_timestamps();

-- Create function to clean old trip data (optional, for data management)
CREATE OR REPLACE FUNCTION cleanup_old_trips(
    p_days_to_keep INTEGER DEFAULT 365
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete trips older than specified days (except completed trips which we keep longer)
    DELETE FROM trips 
    WHERE created_at < NOW() - INTERVAL '1 day' * p_days_to_keep
        AND status IN ('cancelled')
        AND created_at < NOW() - INTERVAL '90 days'; -- Keep cancelled trips for 90 days minimum
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create view for recent trips with ratings (for easier querying)
CREATE OR REPLACE VIEW recent_trips_with_ratings AS
SELECT 
    t.id,
    t.passenger_id,
    t.destination_location,
    COALESCE(t.destination_address_short, t.destination_location) as destination_display,
    t.destination_coordinates,
    t.pickup_location,
    COALESCE(t.pickup_address_short, t.pickup_location) as pickup_display,
    t.pickup_coordinates,
    t.ride_type,
    t.status,
    t.fare_amount,
    t.distance_km,
    t.duration_minutes,
    COALESCE(t.completed_at, t.cancelled_at, t.created_at) as trip_date,
    tr.passenger_rating,
    tr.driver_rating,
    tr.passenger_comment,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    d.vehicle_plate,
    p.full_name as driver_name
FROM trips t
LEFT JOIN trip_ratings tr ON t.id = tr.trip_id
LEFT JOIN drivers d ON t.driver_id = d.id
LEFT JOIN profiles p ON d.user_id = p.id
WHERE t.status IN ('completed', 'cancelled')
ORDER BY trip_date DESC;

-- Grant permissions for the view
GRANT SELECT ON recent_trips_with_ratings TO authenticated;

-- Create RLS policy for the view
CREATE POLICY "Users can view their own recent trips" ON recent_trips_with_ratings
    FOR SELECT USING (auth.uid() = passenger_id);
