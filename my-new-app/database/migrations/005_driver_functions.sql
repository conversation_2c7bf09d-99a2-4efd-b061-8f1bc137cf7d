-- =====================================================
-- Driver-Specific Database Functions
-- Functions for driver app functionality and real-time features
-- =====================================================

-- =====================================================
-- DRIVER MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to get nearby available drivers
CREATE OR REPLACE FUNCTION get_nearby_drivers(
    p_pickup_lat DECIMAL,
    p_pickup_lng DECIMAL,
    p_max_distance_km INTEGER DEFAULT 15,
    p_ride_type_filter ride_type DEFAULT NULL
)
RETURNS TABLE (
    driver_id UUID,
    driver_name TEXT,
    vehicle_info JSONB,
    distance_km DECIMAL,
    rating DECIMAL,
    estimated_arrival INTEGER,
    current_location POINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        p.full_name,
        jsonb_build_object(
            'make', d.vehicle_make,
            'model', d.vehicle_model,
            'color', d.vehicle_color,
            'plate', d.vehicle_plate,
            'type', d.vehicle_type
        ),
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || p_pickup_lng || ' ' || p_pickup_lat || ')'),
                ST_GeogFromText('POINT(' || ST_X(d.current_location) || ' ' || ST_Y(d.current_location) || ')')
            ) / 1000.0, 2
        ),
        d.rating,
        ROUND(
            ST_Distance(
                ST_GeogFromText('POINT(' || p_pickup_lng || ' ' || p_pickup_lat || ')'),
                ST_GeogFromText('POINT(' || ST_X(d.current_location) || ' ' || ST_Y(d.current_location) || ')')
            ) / 1000.0 / 30.0 * 60.0
        )::INTEGER, -- Estimated arrival in minutes (assuming 30 km/h average speed)
        d.current_location
    FROM drivers d
    JOIN profiles p ON d.user_id = p.id
    JOIN driver_availability da ON d.id = da.driver_id
    WHERE 
        d.is_online = TRUE
        AND da.status = 'online'
        AND d.verification_status = 'approved'
        AND d.current_location IS NOT NULL
        AND (p_ride_type_filter IS NULL OR d.vehicle_type = p_ride_type_filter)
        AND ST_Distance(
            ST_GeogFromText('POINT(' || p_pickup_lng || ' ' || p_pickup_lat || ')'),
            ST_GeogFromText('POINT(' || ST_X(d.current_location) || ' ' || ST_Y(d.current_location) || ')')
        ) / 1000.0 <= p_max_distance_km
    ORDER BY
        ST_Distance(
            ST_GeogFromText('POINT(' || p_pickup_lng || ' ' || p_pickup_lat || ')'),
            ST_GeogFromText('POINT(' || ST_X(d.current_location) || ' ' || ST_Y(d.current_location) || ')')
        )
    LIMIT 10;
END;
$$ LANGUAGE plpgsql;

-- Function to update driver location
CREATE OR REPLACE FUNCTION update_driver_location(
    driver_id_param UUID,
    lat DECIMAL,
    lng DECIMAL,
    heading_param DECIMAL DEFAULT NULL,
    speed_param DECIMAL DEFAULT NULL,
    accuracy_param DECIMAL DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    location_point POINT;
    is_moving_calc BOOLEAN;
BEGIN
    -- Create point from coordinates
    location_point := POINT(lng, lat);
    
    -- Calculate if driver is moving based on speed
    is_moving_calc := COALESCE(speed_param > 5.0, FALSE);
    
    -- Update driver's current location
    UPDATE drivers 
    SET 
        current_location = location_point,
        updated_at = NOW()
    WHERE id = driver_id_param;
    
    -- Insert location history
    INSERT INTO driver_locations (
        driver_id,
        location,
        heading,
        speed_kmh,
        accuracy_meters,
        is_moving,
        recorded_at
    ) VALUES (
        driver_id_param,
        location_point,
        heading_param,
        speed_param,
        accuracy_param,
        is_moving_calc,
        NOW()
    );
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to get driver earnings summary
CREATE OR REPLACE FUNCTION get_driver_earnings_summary(
    driver_id_param UUID,
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    total_trips INTEGER,
    total_earnings DECIMAL,
    total_tips DECIMAL,
    total_bonuses DECIMAL,
    commission_paid DECIMAL,
    net_earnings DECIMAL,
    average_trip_earnings DECIMAL,
    period_start DATE,
    period_end DATE
) AS $$
DECLARE
    calc_start_date DATE;
    calc_end_date DATE;
BEGIN
    -- Set default date range if not provided (current month)
    calc_start_date := COALESCE(start_date, DATE_TRUNC('month', CURRENT_DATE)::DATE);
    calc_end_date := COALESCE(end_date, CURRENT_DATE);
    
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER,
        COALESCE(SUM(de.total_fare), 0.0),
        COALESCE(SUM(de.tips), 0.0),
        COALESCE(SUM(de.bonuses), 0.0),
        COALESCE(SUM(de.commission_amount), 0.0),
        COALESCE(SUM(de.driver_payout + de.tips + de.bonuses), 0.0),
        COALESCE(AVG(de.driver_payout + de.tips + de.bonuses), 0.0),
        calc_start_date,
        calc_end_date
    FROM driver_earnings de
    JOIN trips t ON de.trip_id = t.id
    WHERE 
        de.driver_id = driver_id_param
        AND t.completed_at::DATE BETWEEN calc_start_date AND calc_end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get driver performance metrics
CREATE OR REPLACE FUNCTION get_driver_performance_metrics(
    driver_id_param UUID,
    days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    acceptance_rate DECIMAL,
    cancellation_rate DECIMAL,
    average_rating DECIMAL,
    total_trips INTEGER,
    online_hours DECIMAL,
    trips_per_hour DECIMAL,
    earnings_per_hour DECIMAL
) AS $$
DECLARE
    start_date TIMESTAMP;
BEGIN
    start_date := NOW() - INTERVAL '1 day' * days_back;
    
    RETURN QUERY
    WITH trip_stats AS (
        SELECT 
            COUNT(*) FILTER (WHERE tr.driver_response = 'accepted') as accepted_trips,
            COUNT(*) as total_requests,
            COUNT(*) FILTER (WHERE t.status = 'cancelled' AND t.cancelled_by = 'driver') as cancelled_trips,
            COUNT(*) FILTER (WHERE t.status = 'completed') as completed_trips,
            AVG(tr_ratings.driver_rating) as avg_rating,
            SUM(de.driver_payout + de.tips + de.bonuses) as total_earnings
        FROM trip_requests tr
        LEFT JOIN trips t ON tr.trip_id = t.id
        LEFT JOIN trip_ratings tr_ratings ON t.id = tr_ratings.trip_id
        LEFT JOIN driver_earnings de ON t.id = de.trip_id
        WHERE 
            tr.driver_id = driver_id_param
            AND tr.request_sent_at >= start_date
    ),
    availability_stats AS (
        SELECT 
            EXTRACT(EPOCH FROM SUM(
                CASE 
                    WHEN da.status = 'online' THEN 
                        LEAST(da.updated_at, NOW()) - GREATEST(da.created_at, start_date)
                    ELSE INTERVAL '0'
                END
            )) / 3600.0 as online_hours
        FROM driver_availability da
        WHERE 
            da.driver_id = driver_id_param
            AND da.created_at >= start_date
    )
    SELECT 
        CASE 
            WHEN ts.total_requests > 0 THEN 
                ROUND((ts.accepted_trips::DECIMAL / ts.total_requests * 100), 2)
            ELSE 100.0
        END,
        CASE 
            WHEN ts.completed_trips > 0 THEN 
                ROUND((ts.cancelled_trips::DECIMAL / ts.completed_trips * 100), 2)
            ELSE 0.0
        END,
        COALESCE(ROUND(ts.avg_rating, 2), 5.0),
        COALESCE(ts.completed_trips, 0),
        COALESCE(ROUND(avs.online_hours, 2), 0.0),
        CASE 
            WHEN avs.online_hours > 0 THEN 
                ROUND((ts.completed_trips::DECIMAL / avs.online_hours), 2)
            ELSE 0.0
        END,
        CASE 
            WHEN avs.online_hours > 0 THEN 
                ROUND((ts.total_earnings / avs.online_hours), 2)
            ELSE 0.0
        END
    FROM trip_stats ts
    CROSS JOIN availability_stats avs;
END;
$$ LANGUAGE plpgsql;

-- Function to send trip request to driver
CREATE OR REPLACE FUNCTION send_trip_request_to_driver(
    trip_id_param UUID,
    driver_id_param UUID,
    timeout_minutes INTEGER DEFAULT 2
)
RETURNS BOOLEAN AS $$
BEGIN
    INSERT INTO trip_requests (
        trip_id,
        driver_id,
        response_deadline
    ) VALUES (
        trip_id_param,
        driver_id_param,
        NOW() + INTERVAL '1 minute' * timeout_minutes
    );
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Function to respond to trip request
CREATE OR REPLACE FUNCTION respond_to_trip_request(
    trip_id_param UUID,
    driver_id_param UUID,
    response_param TEXT,
    decline_reason_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Update trip request with driver response
    UPDATE trip_requests 
    SET 
        driver_response = response_param,
        response_time = NOW(),
        decline_reason = decline_reason_param
    WHERE 
        trip_id = trip_id_param 
        AND driver_id = driver_id_param;
    
    -- If accepted, update trip status and assign driver
    IF response_param = 'accepted' THEN
        UPDATE trips 
        SET 
            driver_id = driver_id_param,
            status = 'accepted',
            driver_accepted_at = NOW(),
            updated_at = NOW()
        WHERE id = trip_id_param;
        
        -- Update driver status to busy
        UPDATE driver_availability 
        SET 
            status = 'busy',
            updated_at = NOW()
        WHERE driver_id = driver_id_param;
    END IF;
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;
