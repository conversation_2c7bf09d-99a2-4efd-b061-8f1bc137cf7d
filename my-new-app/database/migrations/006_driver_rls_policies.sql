-- =====================================================
-- Driver-Specific Row Level Security (RLS) Policies
-- Security policies for driver tables and data access
-- =====================================================

-- =====================================================
-- ENABLE RLS ON NEW TABLES
-- =====================================================

ALTER TABLE driver_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_earnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE trip_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE incident_reports ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- DRIVER DOCUMENTS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Drivers can view their own documents" ON driver_documents;
DROP POLICY IF EXISTS "Drivers can insert their own documents" ON driver_documents;
DROP POLICY IF EXISTS "Drivers can update their own pending documents" ON driver_documents;
DROP POLICY IF EXISTS "Admins can view all driver documents" ON driver_documents;
DROP POLICY IF EXISTS "Admins can update document status" ON driver_documents;

-- Drivers can view their own documents
CREATE POLICY "Drivers can view their own documents" ON driver_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_documents.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- Drivers can insert their own documents
CREATE POLICY "Drivers can insert their own documents" ON driver_documents
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_documents.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- Drivers can update their own documents (only if pending)
CREATE POLICY "Drivers can update their own pending documents" ON driver_documents
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_documents.driver_id 
            AND drivers.user_id = auth.uid()
        )
        AND document_status = 'pending'
    );

-- Admins can view all documents
CREATE POLICY "Admins can view all driver documents" ON driver_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );

-- Admins can update document status
CREATE POLICY "Admins can update document status" ON driver_documents
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );

-- =====================================================
-- DRIVER AVAILABILITY POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Drivers can manage their own availability" ON driver_availability;
DROP POLICY IF EXISTS "System can view driver availability" ON driver_availability;

-- Drivers can manage their own availability
CREATE POLICY "Drivers can manage their own availability" ON driver_availability
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_availability.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- System can view driver availability for trip matching
CREATE POLICY "System can view driver availability" ON driver_availability
    FOR SELECT USING (true);

-- =====================================================
-- DRIVER EARNINGS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Drivers can view their own earnings" ON driver_earnings;
DROP POLICY IF EXISTS "System can insert earnings records" ON driver_earnings;
DROP POLICY IF EXISTS "Admins can view all earnings" ON driver_earnings;
DROP POLICY IF EXISTS "Admins can update payout status" ON driver_earnings;

-- Drivers can view their own earnings
CREATE POLICY "Drivers can view their own earnings" ON driver_earnings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_earnings.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- System can insert earnings records
CREATE POLICY "System can insert earnings records" ON driver_earnings
    FOR INSERT WITH CHECK (true);

-- Admins can view all earnings
CREATE POLICY "Admins can view all earnings" ON driver_earnings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );

-- Admins can update payout status
CREATE POLICY "Admins can update payout status" ON driver_earnings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );

-- =====================================================
-- DRIVER LOCATIONS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Drivers can insert their own location" ON driver_locations;
DROP POLICY IF EXISTS "Drivers can view their own location history" ON driver_locations;
DROP POLICY IF EXISTS "Passengers can view driver location during trips" ON driver_locations;
DROP POLICY IF EXISTS "System can view driver locations for matching" ON driver_locations;

-- Drivers can insert their own location updates
CREATE POLICY "Drivers can insert their own location" ON driver_locations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_locations.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- Drivers can view their own location history
CREATE POLICY "Drivers can view their own location history" ON driver_locations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = driver_locations.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- Passengers can view driver location during active trips
CREATE POLICY "Passengers can view driver location during trips" ON driver_locations
    FOR SELECT USING (
        trip_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = driver_locations.trip_id 
            AND trips.passenger_id = auth.uid()
            AND trips.status IN ('accepted', 'in_progress')
        )
    );

-- System can view driver locations for trip matching
CREATE POLICY "System can view driver locations for matching" ON driver_locations
    FOR SELECT USING (true);

-- =====================================================
-- TRIP REQUESTS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Drivers can view their trip requests" ON trip_requests;
DROP POLICY IF EXISTS "Drivers can respond to their trip requests" ON trip_requests;
DROP POLICY IF EXISTS "System can insert trip requests" ON trip_requests;
DROP POLICY IF EXISTS "Passengers can view trip requests for their trips" ON trip_requests;

-- Drivers can view trip requests sent to them
CREATE POLICY "Drivers can view their trip requests" ON trip_requests
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = trip_requests.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- Drivers can respond to their trip requests
CREATE POLICY "Drivers can respond to their trip requests" ON trip_requests
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = trip_requests.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

-- System can insert trip requests
CREATE POLICY "System can insert trip requests" ON trip_requests
    FOR INSERT WITH CHECK (true);

-- Passengers can view trip requests for their trips
CREATE POLICY "Passengers can view trip requests for their trips" ON trip_requests
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = trip_requests.trip_id 
            AND trips.passenger_id = auth.uid()
        )
    );

-- =====================================================
-- INCIDENT REPORTS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can create incident reports" ON incident_reports;
DROP POLICY IF EXISTS "Users can view their own incident reports" ON incident_reports;
DROP POLICY IF EXISTS "Trip participants can view trip incident reports" ON incident_reports;
DROP POLICY IF EXISTS "Admins can manage all incident reports" ON incident_reports;

-- Users can create incident reports
CREATE POLICY "Users can create incident reports" ON incident_reports
    FOR INSERT WITH CHECK (reporter_id = auth.uid());

-- Users can view their own incident reports
CREATE POLICY "Users can view their own incident reports" ON incident_reports
    FOR SELECT USING (reporter_id = auth.uid());

-- Drivers and passengers can view incident reports for their trips
CREATE POLICY "Trip participants can view trip incident reports" ON incident_reports
    FOR SELECT USING (
        trip_id IS NOT NULL
        AND (
            EXISTS (
                SELECT 1 FROM trips 
                WHERE trips.id = incident_reports.trip_id 
                AND (trips.passenger_id = auth.uid() OR trips.driver_id IN (
                    SELECT id FROM drivers WHERE user_id = auth.uid()
                ))
            )
        )
    );

-- Admins can view and manage all incident reports
CREATE POLICY "Admins can manage all incident reports" ON incident_reports
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );

-- =====================================================
-- UPDATE EXISTING POLICIES FOR ENHANCED TRIPS TABLE
-- =====================================================

-- Drop existing trip policies to recreate with new fields
DROP POLICY IF EXISTS "Users can view their own trips" ON trips;
DROP POLICY IF EXISTS "Users can update their own trips" ON trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON trips;
DROP POLICY IF EXISTS "Passengers can view their trips" ON trips;
DROP POLICY IF EXISTS "Drivers can view their assigned trips" ON trips;
DROP POLICY IF EXISTS "Passengers can insert their own trips" ON trips;
DROP POLICY IF EXISTS "Passengers can update their own trips" ON trips;
DROP POLICY IF EXISTS "Drivers can update their assigned trips" ON trips;
DROP POLICY IF EXISTS "System can update trips" ON trips;

-- Passengers can view their trips
CREATE POLICY "Passengers can view their trips" ON trips
    FOR SELECT USING (passenger_id = auth.uid());

-- Drivers can view their assigned trips
CREATE POLICY "Drivers can view their assigned trips" ON trips
    FOR SELECT USING (
        driver_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM drivers
            WHERE drivers.id = trips.driver_id
            AND drivers.user_id = auth.uid()
        )
    );

-- Passengers can insert their own trips
CREATE POLICY "Passengers can insert their own trips" ON trips
    FOR INSERT WITH CHECK (passenger_id = auth.uid());

-- Passengers can update their own trips (limited fields)
CREATE POLICY "Passengers can update their own trips" ON trips
    FOR UPDATE USING (passenger_id = auth.uid())
    WITH CHECK (passenger_id = auth.uid());

-- Drivers can update their assigned trips (limited fields)
CREATE POLICY "Drivers can update their assigned trips" ON trips
    FOR UPDATE USING (
        driver_id IS NOT NULL
        AND EXISTS (
            SELECT 1 FROM drivers
            WHERE drivers.id = trips.driver_id
            AND drivers.user_id = auth.uid()
        )
    );

-- System can update trips for status changes
CREATE POLICY "System can update trips" ON trips
    FOR UPDATE USING (true);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Driver availability indexes
CREATE INDEX IF NOT EXISTS idx_driver_availability_status ON driver_availability(driver_id, status);
CREATE INDEX IF NOT EXISTS idx_driver_availability_driver ON driver_availability(driver_id);

-- Driver locations indexes
CREATE INDEX IF NOT EXISTS idx_driver_locations_driver_time ON driver_locations(driver_id, recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_driver_locations_trip ON driver_locations(trip_id) WHERE trip_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_driver_locations_spatial ON driver_locations USING GIST(location);

-- Driver earnings indexes
CREATE INDEX IF NOT EXISTS idx_driver_earnings_driver_date ON driver_earnings(driver_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_driver_earnings_payout ON driver_earnings(payout_status, payout_date);

-- Trip requests indexes
CREATE INDEX IF NOT EXISTS idx_trip_requests_driver_time ON trip_requests(driver_id, request_sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_trip_requests_trip ON trip_requests(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_requests_deadline ON trip_requests(response_deadline) WHERE driver_response IS NULL;

-- Enhanced trips indexes
CREATE INDEX IF NOT EXISTS idx_trips_driver_status ON trips(driver_id, status) WHERE driver_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_trips_passenger_date ON trips(passenger_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_trips_status_created ON trips(status, created_at);

-- Driver documents indexes
CREATE INDEX IF NOT EXISTS idx_driver_documents_status ON driver_documents(driver_id, document_status);
CREATE INDEX IF NOT EXISTS idx_driver_documents_type ON driver_documents(driver_id, document_type);

-- Incident reports indexes
CREATE INDEX IF NOT EXISTS idx_incident_reports_reporter ON incident_reports(reporter_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_incident_reports_trip ON incident_reports(trip_id) WHERE trip_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_incident_reports_status ON incident_reports(status, severity);
