-- =====================================================
-- SheMove Database Rollback Script
-- Use this script to rollback all Phase 1 changes
-- WARNING: This will delete data - use with caution!
-- =====================================================

-- =====================================================
-- ROLLBACK CONFIRMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'Starting SheMove database rollback at %', NOW();
    RAISE NOTICE 'This will remove all Phase 1 enhancements';
    RAISE NOTICE 'Data in search_history, favorite_locations, and recent_destinations will be lost';
END $$;

-- =====================================================
-- DROP VIEWS
-- =====================================================

DROP VIEW IF EXISTS recent_trips_with_ratings;

-- =====================================================
-- DROP FUNCTIONS
-- =====================================================

DROP FUNCTION IF EXISTS get_search_suggestions(UUID, TEXT, INTEGER);
DROP FUNCTION IF EXISTS get_recent_trips(UUID, INTEGER);
DROP FUNCTION IF EXISTS get_frequent_destinations(UUID, INTEGER);
DROP FUNCTION IF EXISTS get_trip_statistics(UUID);
DROP FUNCTION IF EXISTS cleanup_old_search_history();
DROP FUNCTION IF EXISTS cleanup_old_trips(INTEGER);
DROP FUNCTION IF EXISTS update_trip_completion_timestamps();
DROP FUNCTION IF EXISTS update_updated_at_column();

-- =====================================================
-- DROP TRIGGERS
-- =====================================================

DROP TRIGGER IF EXISTS trigger_trip_completion_timestamps ON trips;
DROP TRIGGER IF EXISTS update_search_history_updated_at ON search_history;
DROP TRIGGER IF EXISTS update_favorite_locations_updated_at ON favorite_locations;
DROP TRIGGER IF EXISTS update_recent_destinations_updated_at ON recent_destinations;

-- =====================================================
-- DROP INDEXES (Phase 1 additions)
-- =====================================================

-- Search history indexes
DROP INDEX IF EXISTS idx_search_history_user_recent;
DROP INDEX IF EXISTS idx_search_history_user_clicked;
DROP INDEX IF EXISTS idx_search_history_query_text;

-- Favorite locations indexes
DROP INDEX IF EXISTS idx_favorite_locations_user;

-- Recent destinations indexes
DROP INDEX IF EXISTS idx_recent_destinations_user_frequent;

-- Enhanced trip indexes
DROP INDEX IF EXISTS idx_trips_passenger_recent;
DROP INDEX IF EXISTS idx_trips_passenger_completed;
DROP INDEX IF EXISTS idx_trips_destination_frequency;

-- =====================================================
-- REMOVE COLUMNS FROM EXISTING TABLES
-- =====================================================

-- Remove enhanced columns from trips table
ALTER TABLE trips DROP COLUMN IF EXISTS completed_at;
ALTER TABLE trips DROP COLUMN IF EXISTS cancelled_at;
ALTER TABLE trips DROP COLUMN IF EXISTS cancellation_reason;
ALTER TABLE trips DROP COLUMN IF EXISTS pickup_address_short;
ALTER TABLE trips DROP COLUMN IF EXISTS destination_address_short;

-- =====================================================
-- DROP NEW TABLES (Phase 1 additions)
-- =====================================================

-- Drop search and location tables
DROP TABLE IF EXISTS recent_destinations;
DROP TABLE IF EXISTS favorite_locations;
DROP TABLE IF EXISTS search_history;

-- =====================================================
-- RESTORE ORIGINAL STATE
-- =====================================================

-- Note: Core tables (profiles, drivers, trips, trip_ratings) are preserved
-- Only Phase 1 enhancements are removed

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check remaining tables
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name IN ('search_history', 'favorite_locations', 'recent_destinations');
    
    IF table_count = 0 THEN
        RAISE NOTICE 'SUCCESS: All Phase 1 tables have been removed';
    ELSE
        RAISE WARNING 'WARNING: Some Phase 1 tables still exist';
    END IF;
END $$;

-- Check remaining functions
DO $$
DECLARE
    function_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO function_count 
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name IN ('get_search_suggestions', 'get_recent_trips', 'get_frequent_destinations', 'get_trip_statistics');
    
    IF function_count = 0 THEN
        RAISE NOTICE 'SUCCESS: All Phase 1 functions have been removed';
    ELSE
        RAISE WARNING 'WARNING: Some Phase 1 functions still exist';
    END IF;
END $$;

-- =====================================================
-- ROLLBACK COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE 'SheMove database rollback completed at %', NOW();
    RAISE NOTICE 'Core functionality preserved, Phase 1 enhancements removed';
    RAISE NOTICE 'You may need to restart your application to clear any cached data';
END $$;

-- =====================================================
-- POST-ROLLBACK INSTRUCTIONS
-- =====================================================

/*
After running this rollback script:

1. Restart your application to clear any cached data
2. Update your TypeScript types to remove Phase 1 additions
3. Revert code changes that depend on Phase 1 features
4. Test core functionality to ensure it still works

To re-apply Phase 1 changes:
1. Run 000_master_migration.sql
2. Run 001_rls_policies.sql  
3. Run 003_search_history.sql
4. Run 004_recent_trips_enhancements.sql

Core tables that remain after rollback:
- profiles
- drivers  
- trips (original columns only)
- trip_ratings

Phase 1 tables removed:
- search_history
- favorite_locations
- recent_destinations

Phase 1 enhancements removed from trips:
- completed_at
- cancelled_at
- cancellation_reason
- pickup_address_short
- destination_address_short
*/
