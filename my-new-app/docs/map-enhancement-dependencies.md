# Map Enhancement Dependencies

## Required Package Installations

### Core Map Libraries
```bash
# MapLibre GL JS for vector tile rendering
npm install maplibre-gl

# React Native MapLibre GL (for native mobile support)
npm install @maplibre/maplibre-react-native

# Additional mapping utilities
npm install @turf/turf  # Geospatial analysis utilities
npm install @mapbox/polyline  # Polyline encoding/decoding
```

### Enhanced Services
```bash
# HTTP client for API calls
npm install axios

# Geolocation utilities
npm install geolib

# Debouncing for search optimization
npm install lodash.debounce
```

### Development Dependencies
```bash
# Type definitions
npm install --save-dev @types/maplibre-gl
npm install --save-dev @types/geojson
```

## Library Compatibility Matrix

| Library | React Native | Expo | Web | Notes |
|---------|-------------|------|-----|-------|
| MapLibre GL JS | ✅ | ✅ | ✅ | Primary map renderer |
| @maplibre/maplibre-react-native | ✅ | ⚠️ | ❌ | Requires custom dev client |
| @turf/turf | ✅ | ✅ | ✅ | Geospatial utilities |
| axios | ✅ | ✅ | ✅ | HTTP client |

## Installation Strategy

### Option A: Web-First Approach (Recommended)
- Use MapLibre GL JS in WebView for immediate compatibility
- Maintain existing WebView architecture
- Gradual migration to native components later

### Option B: Native Integration
- Requires Expo custom development build
- Better performance but more complex setup
- Recommended for production deployment

## Configuration Requirements

### MapLibre GL JS Setup
```javascript
// Basic MapLibre configuration
const mapConfig = {
  container: 'map',
  style: 'https://demotiles.maplibre.org/style.json', // Free style
  center: [28.0473, -26.2041], // Johannesburg coordinates
  zoom: 10,
  attributionControl: false
};
```

### South African Tile Sources
```javascript
// OpenStreetMap South Africa optimized tiles
const southAfricaTileSource = {
  "type": "raster",
  "tiles": [
    "https://tile.openstreetmap.org/{z}/{x}/{y}.png"
  ],
  "tileSize": 256,
  "attribution": "© OpenStreetMap contributors"
};
```

## Migration Timeline

### Week 1: Foundation Setup
- Install dependencies
- Create MapLibre service wrapper
- Implement basic map rendering

### Week 2: Service Integration
- Enhance geocoding with geographic bias
- Integrate GraphHopper routing
- Implement POI search

### Week 3: Feature Migration
- Port route animation to MapLibre
- Implement search functionality
- Add trip preview features

### Week 4: Testing & Optimization
- Comprehensive testing
- Performance optimization
- Bug fixes and refinements
