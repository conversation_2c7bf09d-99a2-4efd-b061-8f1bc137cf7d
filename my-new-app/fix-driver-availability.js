#!/usr/bin/env node

/**
 * Fix driver availability issue
 * The get_nearby_drivers function requires both driver.is_online = true 
 * AND driver_availability.status = 'online'
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function fixDriverAvailability() {
  console.log('🔧 Starting driver availability fix...\n');

  try {
    // 1. Check current driver availability status
    console.log('1. Checking current driver availability...');
    
    const { data: availability, error: availError } = await supabase
      .from('driver_availability')
      .select('*')
      .order('created_at', { ascending: false });

    if (availError) {
      console.error('❌ Error querying availability:', availError);
      return;
    }

    console.log(`📊 Found ${availability.length} availability records`);
    
    // Group by driver_id to get latest status for each driver
    const driverStatus = {};
    availability.forEach(record => {
      if (!driverStatus[record.driver_id] || 
          new Date(record.created_at) > new Date(driverStatus[record.driver_id].created_at)) {
        driverStatus[record.driver_id] = record;
      }
    });

    console.log('\n📋 Current driver availability status:');
    Object.entries(driverStatus).forEach(([driverId, status]) => {
      console.log(`   Driver ${driverId}: ${status.status} (${status.created_at})`);
    });

    // 2. Check drivers table
    console.log('\n2. Checking drivers table...');
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*');

    if (driversError) {
      console.error('❌ Error querying drivers:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} drivers`);
    drivers.forEach(driver => {
      console.log(`   Driver ${driver.id}: online=${driver.is_online}, verified=${driver.verification_status}`);
    });

    // 3. The issue: we need to check what the get_nearby_drivers function actually requires
    console.log('\n3. Analyzing the get_nearby_drivers function requirements...');
    console.log('   The function requires:');
    console.log('   ✓ d.is_online = TRUE');
    console.log('   ✓ da.status = \'online\'');
    console.log('   ✓ d.verification_status = \'approved\'');
    console.log('   ✓ d.current_location IS NOT NULL');
    console.log('   ✓ Profile must exist (JOIN profiles p ON d.user_id = p.id)');

    // 4. Check each requirement
    for (const driver of drivers) {
      console.log(`\n🔍 Checking driver ${driver.id}:`);
      
      // Check is_online
      console.log(`   ✓ is_online: ${driver.is_online}`);
      
      // Check verification_status
      console.log(`   ✓ verification_status: ${driver.verification_status}`);
      
      // Check current_location
      console.log(`   ✓ current_location: ${driver.current_location}`);
      
      // Check availability status
      const latestStatus = driverStatus[driver.id];
      console.log(`   ${latestStatus?.status === 'online' ? '✓' : '❌'} availability status: ${latestStatus?.status || 'NONE'}`);
      
      // Check profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', driver.user_id)
        .single();
        
      if (profileError && profileError.code !== 'PGRST116') {
        console.log(`   ❌ profile: ERROR - ${profileError.message}`);
      } else if (!profile) {
        console.log(`   ❌ profile: MISSING`);
      } else {
        console.log(`   ✓ profile: EXISTS (${profile.email}, type: ${profile.user_type})`);
      }
    }

    // 5. Try to create a profile for the driver if missing
    console.log('\n4. Attempting to create missing profile...');
    
    for (const driver of drivers) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', driver.user_id)
        .single();
        
      if (profileError && profileError.code === 'PGRST116') {
        console.log(`Creating profile for driver ${driver.id}...`);
        
        // We need to get the email from somewhere
        // Let's try to use a placeholder and see if we can update it later
        const profileData = {
          id: driver.user_id,
          email: `driver-${driver.user_id.substring(0, 8)}@shemove.co.za`,
          full_name: 'SheMove Driver',
          user_type: 'driver',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Try to insert using a different approach - maybe the RLS policy allows inserts
        try {
          const { data: newProfile, error: insertError } = await supabase
            .from('profiles')
            .insert(profileData)
            .select()
            .single();

          if (insertError) {
            console.log(`   ❌ Failed to create profile: ${insertError.message}`);
            
            // If RLS is blocking, let's try a different approach
            // Maybe we can use the auth.users table directly
            console.log('   Trying alternative approach...');
            
          } else {
            console.log(`   ✅ Profile created: ${newProfile.email}`);
          }
        } catch (error) {
          console.log(`   ❌ Insert failed: ${error.message}`);
        }
      }
    }

    // 6. Test the function one more time
    console.log('\n5. Final test of get_nearby_drivers function...');
    
    const testLat = -26.3018625;
    const testLng = 27.8769306;
    const maxDistance = 15;
    
    const { data: nearbyDrivers, error: functionError } = await supabase.rpc('get_nearby_drivers', {
      p_pickup_lat: testLat,
      p_pickup_lng: testLng,
      p_max_distance_km: maxDistance,
      p_ride_type_filter: 'SheRide'
    });

    if (functionError) {
      console.error('❌ Function error:', functionError);
    } else {
      console.log(`📊 Function result: ${nearbyDrivers ? nearbyDrivers.length : 0} drivers found`);
      
      if (nearbyDrivers && nearbyDrivers.length > 0) {
        console.log('🎉 SUCCESS! Drivers found:');
        nearbyDrivers.forEach((driver, index) => {
          console.log(`   ${index + 1}. ${driver.driver_name}`);
          console.log(`      Distance: ${driver.distance_km}km`);
        });
      } else {
        console.log('❌ Still no drivers found');
        console.log('\n💡 SOLUTION NEEDED:');
        console.log('   The main issue is likely the missing profile.');
        console.log('   You need to either:');
        console.log('   1. Use the Supabase service role key to create the profile');
        console.log('   2. Have the driver sign up again properly');
        console.log('   3. Manually create the profile in the Supabase dashboard');
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixDriverAvailability().then(() => {
  console.log('\n🏁 Availability fix complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Availability fix failed:', error);
  process.exit(1);
});
