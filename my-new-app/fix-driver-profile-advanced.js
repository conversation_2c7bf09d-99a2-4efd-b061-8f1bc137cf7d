#!/usr/bin/env node

/**
 * Advanced fix for missing driver profile issue
 * Uses service role key to bypass R<PERSON> and fix the data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// We need to use the service role key to bypass RLS
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.log('   Please add your Supabase service role key to .env file');
  console.log('   You can find it in your Supabase project settings > API');
  process.exit(1);
}

const supabase = createClient(
  process.env.SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY
);

async function fixDriverProfileAdvanced() {
  console.log('🔧 Starting advanced driver profile fix...\n');

  try {
    // 1. Get all drivers and their auth user data
    console.log('1. Getting driver and auth user data...');
    
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*');

    if (driversError) {
      console.error('❌ Error querying drivers:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} drivers total`);

    for (const driver of drivers) {
      console.log(`\n🔍 Processing driver: ${driver.id}`);
      console.log(`   User ID: ${driver.user_id}`);

      // Check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', driver.user_id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('❌ Error checking profile:', profileError);
        continue;
      }

      if (!profile) {
        console.log('❌ No profile found - attempting to get user data from auth.users');
        
        // Get user data from auth.users using admin API
        const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(driver.user_id);
        
        if (authError) {
          console.error('❌ Error getting auth user:', authError);
          
          // If user doesn't exist in auth.users, this is an orphaned driver record
          if (authError.message.includes('User not found')) {
            console.log('⚠️  User does not exist in auth.users - this is an orphaned driver record');
            console.log('🗑️  Deleting orphaned driver record...');
            
            const { error: deleteError } = await supabase
              .from('drivers')
              .delete()
              .eq('id', driver.id);
              
            if (deleteError) {
              console.error('❌ Error deleting orphaned driver:', deleteError);
            } else {
              console.log('✅ Orphaned driver record deleted');
            }
          }
          continue;
        }

        if (authUser?.user) {
          console.log('✅ Found auth user data:');
          console.log(`   Email: ${authUser.user.email}`);
          console.log(`   Created: ${authUser.user.created_at}`);
          console.log(`   Metadata: ${JSON.stringify(authUser.user.user_metadata)}`);

          // Create the missing profile
          const profileData = {
            id: authUser.user.id,
            email: authUser.user.email,
            full_name: authUser.user.user_metadata?.full_name || 'Driver User',
            user_type: 'driver',
            created_at: authUser.user.created_at,
            updated_at: new Date().toISOString()
          };

          console.log('📝 Creating profile with data:', profileData);

          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert(profileData)
            .select()
            .single();

          if (createError) {
            console.error('❌ Error creating profile:', createError);
          } else {
            console.log('✅ Profile created successfully:', newProfile);
          }
        }
      } else {
        console.log('✅ Profile exists');
        console.log(`   Email: ${profile.email}`);
        console.log(`   Name: ${profile.full_name}`);
        console.log(`   Type: ${profile.user_type}`);
        
        // Make sure the profile has the correct user_type
        if (profile.user_type !== 'driver') {
          console.log('🔧 Updating user_type to driver...');
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ user_type: 'driver' })
            .eq('id', profile.id);
            
          if (updateError) {
            console.error('❌ Error updating user_type:', updateError);
          } else {
            console.log('✅ User type updated to driver');
          }
        }
      }
    }

    // 2. Test the function again
    console.log('\n2. Testing get_nearby_drivers function after fix...');
    
    const testLat = -26.3018625;
    const testLng = 27.8769306;
    const maxDistance = 15;
    
    const { data: nearbyDrivers, error: functionError } = await supabase.rpc('get_nearby_drivers', {
      p_pickup_lat: testLat,
      p_pickup_lng: testLng,
      p_max_distance_km: maxDistance,
      p_ride_type_filter: 'SheRide'
    });

    if (functionError) {
      console.error('❌ Error calling get_nearby_drivers function:', functionError);
    } else {
      console.log(`✅ Function executed successfully`);
      console.log(`📊 Found ${nearbyDrivers ? nearbyDrivers.length : 0} nearby drivers`);
      
      if (nearbyDrivers && nearbyDrivers.length > 0) {
        console.log('🎉 SUCCESS! Drivers are now being found:');
        nearbyDrivers.forEach((driver, index) => {
          console.log(`   ${index + 1}. ${driver.driver_name}`);
          console.log(`      Distance: ${driver.distance_km}km`);
          console.log(`      Vehicle: ${JSON.stringify(driver.vehicle_info)}`);
          console.log('');
        });
      } else {
        console.log('❌ Still no drivers found - checking driver_availability table...');
        
        // Check driver_availability table
        const { data: availability, error: availError } = await supabase
          .from('driver_availability')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5);
          
        if (availError) {
          console.error('❌ Error checking availability:', availError);
        } else {
          console.log(`📊 Latest availability records (${availability.length}):`);
          availability.forEach((avail, index) => {
            console.log(`   ${index + 1}. Driver: ${avail.driver_id}`);
            console.log(`      Status: ${avail.status}`);
            console.log(`      Created: ${avail.created_at}`);
          });
          
          // The function requires driver_availability status to be 'online'
          // Let's check if we need to create an availability record
          if (drivers.length > 0) {
            const driver = drivers[0];
            const latestAvail = availability.find(a => a.driver_id === driver.id);
            
            if (!latestAvail || latestAvail.status !== 'online') {
              console.log('🔧 Creating online availability record...');
              
              const { error: availInsertError } = await supabase
                .from('driver_availability')
                .insert({
                  driver_id: driver.id,
                  status: 'online',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });
                
              if (availInsertError) {
                console.error('❌ Error creating availability record:', availInsertError);
              } else {
                console.log('✅ Online availability record created');
                
                // Test function again
                console.log('\n3. Testing function after availability fix...');
                const { data: finalTest, error: finalError } = await supabase.rpc('get_nearby_drivers', {
                  p_pickup_lat: testLat,
                  p_pickup_lng: testLng,
                  p_max_distance_km: maxDistance,
                  p_ride_type_filter: 'SheRide'
                });
                
                if (finalError) {
                  console.error('❌ Final test error:', finalError);
                } else {
                  console.log(`🎯 Final test: Found ${finalTest ? finalTest.length : 0} drivers`);
                  if (finalTest && finalTest.length > 0) {
                    console.log('🎉 SUCCESS! Driver search is now working!');
                  }
                }
              }
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixDriverProfileAdvanced().then(() => {
  console.log('\n🏁 Advanced fix complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Advanced fix failed:', error);
  process.exit(1);
});
