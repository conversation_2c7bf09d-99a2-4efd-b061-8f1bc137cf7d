#!/usr/bin/env node

/**
 * Fix missing driver profile issue
 * Creates the missing profile record for the driver
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function fixDriverProfile() {
  console.log('🔧 Starting driver profile fix...\n');

  try {
    // 1. Get the driver without a profile
    console.log('1. Finding drivers without profiles...');
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*');

    if (driversError) {
      console.error('❌ Error querying drivers:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} drivers total`);

    // 2. Check which drivers have profiles
    for (const driver of drivers) {
      console.log(`\n🔍 Checking driver: ${driver.id}`);
      console.log(`   User ID: ${driver.user_id}`);

      // Check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', driver.user_id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('❌ Error checking profile:', profileError);
        continue;
      }

      if (!profile) {
        console.log('❌ No profile found - need to create one');
        
        // Get user data from auth.users if possible
        console.log('🔍 Checking auth.users table...');
        
        // First, let's try to get the user email from auth metadata
        // Since we can't directly query auth.users, we'll create a basic profile
        
        const profileData = {
          id: driver.user_id,
          email: '<EMAIL>', // We'll need to update this manually
          full_name: 'Driver User',
          user_type: 'driver',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log('📝 Creating profile with data:', profileData);

        const { data: newProfile, error: createError } = await supabase
          .from('profiles')
          .insert(profileData)
          .select()
          .single();

        if (createError) {
          console.error('❌ Error creating profile:', createError);
          
          // If it's a foreign key constraint error, the user might not exist in auth.users
          if (createError.code === '23503') {
            console.log('⚠️  User does not exist in auth.users table');
            console.log('   This driver record is orphaned and should be cleaned up');
            
            // Option: Delete the orphaned driver record
            console.log('🗑️  Deleting orphaned driver record...');
            const { error: deleteError } = await supabase
              .from('drivers')
              .delete()
              .eq('id', driver.id);
              
            if (deleteError) {
              console.error('❌ Error deleting orphaned driver:', deleteError);
            } else {
              console.log('✅ Orphaned driver record deleted');
            }
          }
        } else {
          console.log('✅ Profile created successfully:', newProfile);
        }
      } else {
        console.log('✅ Profile exists');
        console.log(`   Email: ${profile.email}`);
        console.log(`   Name: ${profile.full_name}`);
        console.log(`   Type: ${profile.user_type}`);
        
        // Make sure the profile has the correct user_type
        if (profile.user_type !== 'driver') {
          console.log('🔧 Updating user_type to driver...');
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ user_type: 'driver' })
            .eq('id', profile.id);
            
          if (updateError) {
            console.error('❌ Error updating user_type:', updateError);
          } else {
            console.log('✅ User type updated to driver');
          }
        }
      }
    }

    // 3. Test the function again
    console.log('\n3. Testing get_nearby_drivers function after fix...');
    
    const testLat = -26.3018625;
    const testLng = 27.8769306;
    const maxDistance = 15;
    
    const { data: nearbyDrivers, error: functionError } = await supabase.rpc('get_nearby_drivers', {
      p_pickup_lat: testLat,
      p_pickup_lng: testLng,
      p_max_distance_km: maxDistance,
      p_ride_type_filter: 'SheRide'
    });

    if (functionError) {
      console.error('❌ Error calling get_nearby_drivers function:', functionError);
    } else {
      console.log(`✅ Function executed successfully`);
      console.log(`📊 Found ${nearbyDrivers ? nearbyDrivers.length : 0} nearby drivers`);
      
      if (nearbyDrivers && nearbyDrivers.length > 0) {
        console.log('🎉 SUCCESS! Drivers are now being found:');
        nearbyDrivers.forEach((driver, index) => {
          console.log(`   ${index + 1}. ${driver.driver_name}`);
          console.log(`      Distance: ${driver.distance_km}km`);
          console.log(`      Vehicle: ${JSON.stringify(driver.vehicle_info)}`);
          console.log('');
        });
      } else {
        console.log('❌ Still no drivers found - may need further investigation');
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixDriverProfile().then(() => {
  console.log('\n🏁 Fix complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fix failed:', error);
  process.exit(1);
});
