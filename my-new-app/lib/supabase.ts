import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Supabase configuration - get from Expo Constants with fallbacks
const getSupabaseConfig = () => {
  // Try Expo Constants first (for React Native)
  let supabaseUrl = Constants.expoConfig?.extra?.SUPABASE_URL;
  let supabaseAnonKey = Constants.expoConfig?.extra?.SUPABASE_ANON_KEY;

  // Fallback to process.env (for Node.js/development)
  if (!supabaseUrl) {
    supabaseUrl = process.env.SUPABASE_URL;
  }
  if (!supabaseAnonKey) {
    supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
  }

  return {
    url: supabaseUrl || '',
    anonKey: supabaseAnonKey || ''
  };
};

// Lazy initialization of Supabase client
let supabaseClient: any = null;

const getSupabaseClient = () => {
  if (supabaseClient) {
    return supabaseClient;
  }

  const { url: supabaseUrl, anonKey: supabaseAnonKey } = getSupabaseConfig();

  if (supabaseUrl && supabaseAnonKey &&
      supabaseUrl !== 'your_supabase_project_url_here' &&
      supabaseAnonKey !== 'your_supabase_anon_key_here') {

    // Configure storage based on platform
    const authConfig: any = {
      // Auto refresh tokens
      autoRefreshToken: true,
      // Persist session across app restarts
      persistSession: true,
      // Detect session in URL (useful for OAuth redirects)
      detectSessionInUrl: Platform.OS === 'web',
    };

    // Only use AsyncStorage on native platforms
    if (Platform.OS !== 'web') {
      authConfig.storage = AsyncStorage;
    }

    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: authConfig,
      // Global configuration
      global: {
        headers: {
          'X-Client-Info': 'shemove-app',
        },
      },
    });

    console.log('✅ Supabase client initialized');
  } else {
    console.warn('⚠️ Supabase not configured. Please check your environment variables.');
  }

  return supabaseClient;
};

// Export the client directly - it will be null if not configured
export const supabase = getSupabaseClient();

// Database types (will be generated from Supabase later)
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          phone_number: string | null;
          avatar_url: string | null;
          user_type: 'passenger' | 'driver' | 'admin';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          phone_number?: string | null;
          avatar_url?: string | null;
          user_type?: 'passenger' | 'driver' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          phone_number?: string | null;
          avatar_url?: string | null;
          user_type?: 'passenger' | 'driver' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
      };
      trips: {
        Row: {
          id: string;
          passenger_id: string;
          driver_id: string | null;
          pickup_location: string;
          pickup_coordinates: [number, number];
          destination_location: string;
          destination_coordinates: [number, number];
          ride_type: 'SheRide' | 'ShePool' | 'SheXL';
          status: 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
          fare_amount: number;
          distance_km: number;
          duration_minutes: number;
          scheduled_time: string | null;
          completed_at: string | null;
          cancelled_at: string | null;
          cancellation_reason: string | null;
          pickup_address_short: string | null;
          destination_address_short: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          passenger_id: string;
          driver_id?: string | null;
          pickup_location: string;
          pickup_coordinates: [number, number];
          destination_location: string;
          destination_coordinates: [number, number];
          ride_type: 'SheRide' | 'ShePool' | 'SheXL';
          status?: 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
          fare_amount: number;
          distance_km: number;
          duration_minutes: number;
          scheduled_time?: string | null;
          completed_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          pickup_address_short?: string | null;
          destination_address_short?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          passenger_id?: string;
          driver_id?: string | null;
          pickup_location?: string;
          pickup_coordinates?: [number, number];
          destination_location?: string;
          destination_coordinates?: [number, number];
          ride_type?: 'SheRide' | 'ShePool' | 'SheXL';
          status?: 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
          fare_amount?: number;
          distance_km?: number;
          duration_minutes?: number;
          scheduled_time?: string | null;
          completed_at?: string | null;
          cancelled_at?: string | null;
          cancellation_reason?: string | null;
          pickup_address_short?: string | null;
          destination_address_short?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      drivers: {
        Row: {
          id: string;
          user_id: string;
          license_number: string;
          vehicle_make: string;
          vehicle_model: string;
          vehicle_year: number;
          vehicle_color: string;
          vehicle_plate: string;
          verification_status: 'pending' | 'approved' | 'rejected';
          is_online: boolean;
          current_location: [number, number] | null;
          rating: number;
          total_trips: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          license_number: string;
          vehicle_make: string;
          vehicle_model: string;
          vehicle_year: number;
          vehicle_color: string;
          vehicle_plate: string;
          verification_status?: 'pending' | 'approved' | 'rejected';
          is_online?: boolean;
          current_location?: [number, number] | null;
          rating?: number;
          total_trips?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          license_number?: string;
          vehicle_make?: string;
          vehicle_model?: string;
          vehicle_year?: number;
          vehicle_color?: string;
          vehicle_plate?: string;
          verification_status?: 'pending' | 'approved' | 'rejected';
          is_online?: boolean;
          current_location?: [number, number] | null;
          rating?: number;
          total_trips?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      search_history: {
        Row: {
          id: string;
          user_id: string;
          search_query: string;
          selected_result: any | null;
          result_address: string | null;
          result_coordinates: [number, number] | null;
          search_context: string | null;
          search_timestamp: string;
          location_context: [number, number] | null;
          result_clicked: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          search_query: string;
          selected_result?: any | null;
          result_address?: string | null;
          result_coordinates?: [number, number] | null;
          search_context?: string | null;
          search_timestamp?: string;
          location_context?: [number, number] | null;
          result_clicked?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          search_query?: string;
          selected_result?: any | null;
          result_address?: string | null;
          result_coordinates?: [number, number] | null;
          search_context?: string | null;
          search_timestamp?: string;
          location_context?: [number, number] | null;
          result_clicked?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      favorite_locations: {
        Row: {
          id: string;
          user_id: string;
          label: string;
          address: string;
          coordinates: [number, number];
          is_primary: boolean;
          icon_name: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          label: string;
          address: string;
          coordinates: [number, number];
          is_primary?: boolean;
          icon_name?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          label?: string;
          address?: string;
          coordinates?: [number, number];
          is_primary?: boolean;
          icon_name?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      recent_destinations: {
        Row: {
          id: string;
          user_id: string;
          address: string;
          coordinates: [number, number];
          visit_count: number;
          last_visited: string;
          first_visited: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          address: string;
          coordinates: [number, number];
          visit_count?: number;
          last_visited?: string;
          first_visited?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          address?: string;
          coordinates?: [number, number];
          visit_count?: number;
          last_visited?: string;
          first_visited?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      get_search_suggestions: {
        Args: {
          p_user_id: string;
          p_query: string;
          p_limit?: number;
        };
        Returns: {
          suggestion: string;
          address: string;
          coordinates: [number, number];
          suggestion_type: string;
          last_used: string;
        }[];
      };
      get_recent_trips: {
        Args: {
          p_user_id: string;
          p_limit?: number;
        };
        Returns: {
          trip_id: string;
          destination_location: string;
          destination_address_short: string;
          destination_coordinates: [number, number];
          pickup_location: string;
          pickup_address_short: string;
          pickup_coordinates: [number, number];
          ride_type: 'SheRide' | 'ShePool' | 'SheXL';
          fare_amount: number;
          distance_km: number;
          duration_minutes: number;
          trip_date: string;
          trip_status: 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
          driver_rating: number;
        }[];
      };
      get_frequent_destinations: {
        Args: {
          p_user_id: string;
          p_limit?: number;
        };
        Returns: {
          destination_location: string;
          destination_address_short: string;
          destination_coordinates: [number, number];
          trip_count: number;
          last_trip_date: string;
        }[];
      };
      get_trip_statistics: {
        Args: {
          p_user_id: string;
        };
        Returns: {
          total_trips: number;
          completed_trips: number;
          cancelled_trips: number;
          total_distance_km: number;
          total_fare_amount: number;
          average_rating: number;
          favorite_ride_type: 'SheRide' | 'ShePool' | 'SheXL';
        }[];
      };
    };
    Enums: {
      user_type: 'passenger' | 'driver' | 'admin';
      trip_status: 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
      ride_type: 'SheRide' | 'ShePool' | 'SheXL';
      verification_status: 'pending' | 'approved' | 'rejected';
    };
  };
}

// Helper function to check if Supabase is properly configured
export const isSupabaseConfigured = (): boolean => {
  const config = getSupabaseConfig();
  return !!(config.url && config.anonKey &&
    config.url !== 'your_supabase_project_url_here' &&
    config.anonKey !== 'your_supabase_anon_key_here' &&
    supabase !== null);
};

// Helper function to get current user
export const getCurrentUser = async () => {
  if (!supabase) {
    console.warn('Supabase not configured');
    return null;
  }

  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }
  return user;
};

// Helper function to get user profile
export const getUserProfile = async (userId: string) => {
  if (!supabase) {
    console.warn('Supabase not configured');
    return null;
  }

  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error getting user profile:', error);
    return null;
  }

  return data;
};

// Helper function to get current session
export const getCurrentSession = async (): Promise<Session | null> => {
  try {
    if (!supabase) {
      console.warn('Supabase not configured');
      return null;
    }

    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.error('Error getting current session:', error);
      return null;
    }
    return session;
  } catch (error) {
    console.error('Error in getCurrentSession:', error);
    return null;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const session = await getCurrentSession();
    return session !== null && session.user !== null;
  } catch (error) {
    console.error('Error checking authentication:', error);
    return false;
  }
};

// Export types for use in other files
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Trip = Database['public']['Tables']['trips']['Row'];
export type Driver = Database['public']['Tables']['drivers']['Row'];
export type SearchHistory = Database['public']['Tables']['search_history']['Row'];
export type FavoriteLocation = Database['public']['Tables']['favorite_locations']['Row'];
export type RecentDestination = Database['public']['Tables']['recent_destinations']['Row'];
export type UserType = Database['public']['Enums']['user_type'];
export type TripStatus = Database['public']['Enums']['trip_status'];
export type RideType = Database['public']['Enums']['ride_type'];

// Database function return types
export type SearchSuggestion = Database['public']['Functions']['get_search_suggestions']['Returns'][0];
export type RecentTrip = Database['public']['Functions']['get_recent_trips']['Returns'][0];
export type FrequentDestination = Database['public']['Functions']['get_frequent_destinations']['Returns'][0];
export type TripStatistics = Database['public']['Functions']['get_trip_statistics']['Returns'][0];
