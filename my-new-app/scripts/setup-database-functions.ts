/**
 * <PERSON><PERSON><PERSON> to set up database functions for SheMove passenger app
 * This script creates the get_nearby_drivers function and verifies database schema
 */

import { supabase } from '../lib/supabase';
import * as fs from 'fs';
import * as path from 'path';

async function setupDatabaseFunctions() {
  console.log('🔧 Setting up database functions for SheMove...');

  try {
    // Read the SQL function file
    const sqlFilePath = path.join(__dirname, '../database/get_nearby_drivers.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('📄 Executing get_nearby_drivers.sql...');
    
    // Execute the SQL function creation
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: sqlContent
    });

    if (error) {
      console.error('❌ Error executing SQL:', error);
      
      // Try alternative approach - execute parts separately
      console.log('🔄 Trying alternative approach...');
      
      // Split SQL into individual statements
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.includes('CREATE OR REPLACE FUNCTION')) {
          console.log('📝 Creating get_nearby_drivers function...');
          const { error: funcError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          
          if (funcError) {
            console.error('❌ Function creation error:', funcError);
          } else {
            console.log('✅ Function created successfully');
          }
        } else if (statement.includes('GRANT EXECUTE')) {
          console.log('🔐 Setting permissions...');
          const { error: grantError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          
          if (grantError) {
            console.error('❌ Grant permission error:', grantError);
          } else {
            console.log('✅ Permissions set successfully');
          }
        } else if (statement.includes('CREATE INDEX')) {
          console.log('📊 Creating indexes...');
          const { error: indexError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          
          if (indexError) {
            console.error('❌ Index creation error:', indexError);
          } else {
            console.log('✅ Index created successfully');
          }
        }
      }
    } else {
      console.log('✅ SQL executed successfully:', data);
    }

    // Test the function
    console.log('🧪 Testing get_nearby_drivers function...');
    const { data: testData, error: testError } = await supabase
      .rpc('get_nearby_drivers', {
        user_lat: -26.2041,
        user_lng: 28.0473,
        radius_km: 5.0
      });

    if (testError) {
      console.error('❌ Function test error:', testError);
    } else {
      console.log('✅ Function test successful. Found drivers:', testData?.length || 0);
      if (testData && testData.length > 0) {
        console.log('📍 Sample driver:', testData[0]);
      }
    }

    // Verify database schema
    console.log('🔍 Verifying database schema...');
    await verifyDatabaseSchema();

  } catch (error) {
    console.error('💥 Setup failed:', error);
  }
}

async function verifyDatabaseSchema() {
  try {
    // Check if drivers table exists and has required columns
    const { data: driversSchema, error: driversError } = await supabase
      .from('drivers')
      .select('*')
      .limit(1);

    if (driversError) {
      console.error('❌ Drivers table issue:', driversError);
    } else {
      console.log('✅ Drivers table accessible');
    }

    // Check if profiles table exists
    const { data: profilesSchema, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profilesError) {
      console.error('❌ Profiles table issue:', profilesError);
    } else {
      console.log('✅ Profiles table accessible');
    }

    // Check for any online drivers
    const { data: onlineDrivers, error: onlineError } = await supabase
      .from('drivers')
      .select('id, user_id, current_location, is_online, verification_status')
      .eq('is_online', true)
      .eq('verification_status', 'approved')
      .limit(5);

    if (onlineError) {
      console.error('❌ Error checking online drivers:', onlineError);
    } else {
      console.log(`✅ Found ${onlineDrivers?.length || 0} online approved drivers`);
      if (onlineDrivers && onlineDrivers.length > 0) {
        console.log('📍 Sample online driver:', onlineDrivers[0]);
      } else {
        console.log('⚠️  No online approved drivers found. You may need to:');
        console.log('   1. Create driver accounts in the driver app');
        console.log('   2. Set drivers to online status');
        console.log('   3. Approve drivers in the admin dashboard');
      }
    }

    // Check database functions
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_schema_functions');

    if (!functionsError && functions) {
      const nearbyDriversFunc = functions.find((f: any) => f.name === 'get_nearby_drivers');
      if (nearbyDriversFunc) {
        console.log('✅ get_nearby_drivers function exists in database');
      } else {
        console.log('❌ get_nearby_drivers function not found in database');
      }
    }

  } catch (error) {
    console.error('💥 Schema verification failed:', error);
  }
}

// Alternative function creation using direct SQL execution
async function createFunctionDirectly() {
  console.log('🔧 Creating function directly...');
  
  const functionSQL = `
    CREATE OR REPLACE FUNCTION get_nearby_drivers(
      user_lat DOUBLE PRECISION,
      user_lng DOUBLE PRECISION,
      radius_km DOUBLE PRECISION DEFAULT 5.0
    )
    RETURNS TABLE (
      id UUID,
      user_id UUID,
      full_name TEXT,
      current_location JSONB,
      vehicle_make TEXT,
      vehicle_model TEXT,
      vehicle_color TEXT,
      vehicle_type TEXT,
      is_online BOOLEAN,
      rating NUMERIC,
      distance_km DOUBLE PRECISION
    ) 
    LANGUAGE plpgsql
    AS $$
    BEGIN
      RETURN QUERY
      SELECT 
        d.id,
        d.user_id,
        COALESCE(p.full_name, 'Driver') as full_name,
        d.current_location,
        d.vehicle_make,
        d.vehicle_model,
        d.vehicle_color,
        COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
        d.is_online,
        COALESCE(d.rating, 4.5) as rating,
        (
          6371 * acos(
            cos(radians(user_lat)) * 
            cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
            cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
            sin(radians(user_lat)) * 
            sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
          )
        ) as distance_km
      FROM drivers d
      LEFT JOIN profiles p ON d.user_id = p.id
      WHERE 
        d.is_online = true
        AND d.verification_status = 'approved'
        AND d.current_location IS NOT NULL
        AND d.current_location->>'lat' IS NOT NULL
        AND d.current_location->>'lng' IS NOT NULL
        AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
        AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
        AND (
          6371 * acos(
            cos(radians(user_lat)) * 
            cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
            cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
            sin(radians(user_lat)) * 
            sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
          )
        ) <= radius_km
      ORDER BY distance_km ASC
      LIMIT 20;
    END;
    $$;
  `;

  try {
    // Use the SQL editor approach
    console.log('📝 Please execute the following SQL in your Supabase SQL Editor:');
    console.log('=' * 80);
    console.log(functionSQL);
    console.log('=' * 80);
    
    // Also try to execute via RPC if available
    const { data, error } = await supabase.rpc('exec_sql', { sql: functionSQL });
    
    if (error) {
      console.error('❌ Direct execution failed:', error);
      console.log('📋 Please copy the SQL above and execute it manually in Supabase SQL Editor');
    } else {
      console.log('✅ Function created successfully via RPC');
    }

  } catch (error) {
    console.error('💥 Direct function creation failed:', error);
  }
}

// Run the setup
if (require.main === module) {
  setupDatabaseFunctions()
    .then(() => {
      console.log('🎉 Database setup completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Database setup failed:', error);
      console.log('\n🔧 Trying direct function creation...');
      createFunctionDirectly().then(() => {
        process.exit(1);
      });
    });
}

export { setupDatabaseFunctions, verifyDatabaseSchema, createFunctionDirectly };
