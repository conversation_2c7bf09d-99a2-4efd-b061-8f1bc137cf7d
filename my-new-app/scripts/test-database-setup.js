/**
 * Test and Setup Database Functions for SheMove
 * This script tests the database connection and creates necessary functions
 */

const { createClient } = require('@supabase/supabase-js');

// Get Supabase config from environment
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || 'your-anon-key';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabaseSetup() {
  console.log('🔧 Testing SheMove database setup...');

  if (!supabase) {
    console.error('❌ Supabase client not initialized');
    return false;
  }

  try {
    // Test basic connection
    console.log('🔌 Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError);
      return false;
    }

    console.log('✅ Database connection successful');

    // Check if get_nearby_drivers function exists
    console.log('🔍 Checking if get_nearby_drivers function exists...');
    const { data: testData, error: testError } = await supabase
      .rpc('get_nearby_drivers', {
        user_lat: -26.2041,
        user_lng: 28.0473,
        radius_km: 5.0
      });

    if (testError) {
      console.log('❌ Function not found or error:', testError.message);
      console.log('📝 Need to create get_nearby_drivers function...');
      
      // Print SQL for manual execution
      printSQLForManualExecution();
      
      return false;
    } else {
      console.log('✅ Function exists and working! Found drivers:', testData?.length || 0);
      if (testData && testData.length > 0) {
        console.log('📍 Sample driver:', testData[0]);
      }
    }

    // Check database schema
    await checkDatabaseSchema();

    return true;

  } catch (error) {
    console.error('💥 Database setup test failed:', error);
    return false;
  }
}

function printSQLForManualExecution() {
  console.log('📋 Please execute the following SQL in your Supabase SQL Editor:');
  console.log('='.repeat(80));
  
  const sql = `
-- Create the get_nearby_drivers function
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    d.current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    (
      6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
        cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
      )
    ) as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    AND d.current_location->>'lat' IS NOT NULL
    AND d.current_location->>'lng' IS NOT NULL
    AND (d.current_location->>'lat')::DOUBLE PRECISION BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND (d.current_location->>'lng')::DOUBLE PRECISION BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
    AND (
      6371 * acos(
        cos(radians(user_lat)) * 
        cos(radians((d.current_location->>'lat')::DOUBLE PRECISION)) * 
        cos(radians((d.current_location->>'lng')::DOUBLE PRECISION) - radians(user_lng)) + 
        sin(radians(user_lat)) * 
        sin(radians((d.current_location->>'lat')::DOUBLE PRECISION))
      )
    ) <= radius_km
  ORDER BY distance_km ASC
  LIMIT 20;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_drivers_location_online 
ON drivers USING GIN (current_location) 
WHERE is_online = true AND verification_status = 'approved';

CREATE INDEX IF NOT EXISTS idx_drivers_online_status 
ON drivers (is_online, verification_status) 
WHERE is_online = true;
`;

  console.log(sql);
  console.log('='.repeat(80));
}

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...');

  try {
    // Check drivers table
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('id, user_id, current_location, is_online, verification_status, vehicle_make, vehicle_model, vehicle_color, vehicle_type')
      .eq('is_online', true)
      .eq('verification_status', 'approved')
      .limit(5);

    if (driversError) {
      console.error('❌ Drivers table error:', driversError);
    } else {
      console.log(`✅ Found ${drivers?.length || 0} online approved drivers`);
      if (drivers && drivers.length > 0) {
        console.log('📍 Sample driver:', {
          id: drivers[0].id,
          location: drivers[0].current_location,
          vehicle: `${drivers[0].vehicle_make} ${drivers[0].vehicle_model}`,
          type: drivers[0].vehicle_type
        });
      } else {
        console.log('⚠️  No online approved drivers found.');
        console.log('💡 To test with drivers:');
        console.log('   1. Use the driver app to create driver accounts');
        console.log('   2. Set drivers to online status');
        console.log('   3. Approve drivers in the admin dashboard');
      }
    }

    // Check profiles table
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name, user_type')
      .eq('user_type', 'driver')
      .limit(3);

    if (profilesError) {
      console.error('❌ Profiles table error:', profilesError);
    } else {
      console.log(`✅ Found ${profiles?.length || 0} driver profiles`);
    }

  } catch (error) {
    console.error('💥 Schema check failed:', error);
  }
}

// Run the test
testDatabaseSetup()
  .then((success) => {
    if (success) {
      console.log('🎉 Database setup test completed successfully');
    } else {
      console.log('⚠️  Database setup needs manual intervention');
      console.log('📝 Please execute the SQL above in your Supabase SQL Editor');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
