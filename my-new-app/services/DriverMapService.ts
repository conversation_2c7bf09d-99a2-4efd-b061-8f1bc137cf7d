/**
 * Driver Map Service for SheMove Passenger App
 * Manages real-time driver locations and map markers
 */

import { supabase } from '../lib/supabase';
import { Coordinates } from './distanceService';

export interface MapDriver {
  id: string;
  name: string;
  location: Coordinates;
  heading: number;
  isOnline: boolean;
  vehicleType: 'SheRide' | 'ShePool' | 'SheXL';
  rating: number;
  estimatedArrival?: number;
}

export interface DriverMapCallbacks {
  onDriversUpdated?: (drivers: MapDriver[]) => void;
  onDriverAdded?: (driver: MapDriver) => void;
  onDriverRemoved?: (driverId: string) => void;
  onDriverMoved?: (driver: MapDriver) => void;
  onError?: (error: Error) => void;
}

export class DriverMapService {
  private supabase = supabase;
  private callbacks: DriverMapCallbacks = {};
  private subscriptions: any[] = [];
  private currentDrivers: Map<string, MapDriver> = new Map();
  private userLocation: Coordinates | null = null;
  private searchRadius: number = 5; // km

  constructor(callbacks: DriverMapCallbacks = {}) {
    this.callbacks = callbacks;
  }

  /**
   * Initialize the service with user location
   */
  async initialize(userLocation: Coordinates): Promise<boolean> {
    try {
      this.userLocation = userLocation;
      console.log('DriverMapService: Initializing with location:', userLocation);
      
      // Load initial nearby drivers
      await this.loadNearbyDrivers();
      
      // Subscribe to real-time driver updates
      await this.subscribeToDriverUpdates();
      
      return true;
    } catch (error) {
      console.error('DriverMapService: Failed to initialize:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Load nearby drivers from database
   */
  async loadNearbyDrivers(): Promise<MapDriver[]> {
    if (!this.userLocation) {
      console.error('DriverMapService: No user location set');
      return [];
    }

    try {
      console.log('DriverMapService: Loading nearby drivers...');
      
      // Query for online drivers within radius
      const { data: drivers, error } = await this.supabase
        .rpc('get_nearby_drivers', {
          user_lat: this.userLocation.lat,
          user_lng: this.userLocation.lng,
          radius_km: this.searchRadius
        });

      if (error) {
        console.error('DriverMapService: Error loading drivers:', error);
        return [];
      }

      const mapDrivers: MapDriver[] = (drivers || []).map(this.transformDatabaseDriver);
      
      // Update current drivers map
      this.currentDrivers.clear();
      mapDrivers.forEach(driver => {
        this.currentDrivers.set(driver.id, driver);
      });

      console.log(`DriverMapService: Loaded ${mapDrivers.length} nearby drivers`);
      this.callbacks.onDriversUpdated?.(mapDrivers);
      
      return mapDrivers;
    } catch (error) {
      console.error('DriverMapService: Error loading nearby drivers:', error);
      this.callbacks.onError?.(error as Error);
      return [];
    }
  }

  /**
   * Subscribe to real-time driver location updates
   */
  private async subscribeToDriverUpdates(): Promise<void> {
    try {
      // Subscribe to driver availability changes
      const availabilitySubscription = this.supabase
        .channel('driver-availability')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'driver_availability',
            filter: 'status=eq.online'
          },
          (payload) => {
            console.log('DriverMapService: Driver availability update:', payload);
            this.handleDriverAvailabilityUpdate(payload);
          }
        )
        .subscribe();

      // Subscribe to driver location updates
      const locationSubscription = this.supabase
        .channel('driver-locations')
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'drivers',
            filter: 'is_online=eq.true'
          },
          (payload) => {
            console.log('DriverMapService: Driver location update:', payload);
            this.handleDriverLocationUpdate(payload);
          }
        )
        .subscribe();

      this.subscriptions.push(availabilitySubscription, locationSubscription);
      console.log('DriverMapService: Subscribed to real-time driver updates');
    } catch (error) {
      console.error('DriverMapService: Failed to subscribe to driver updates:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Handle driver availability updates
   */
  private async handleDriverAvailabilityUpdate(payload: any): Promise<void> {
    const { eventType, new: newRecord, old: oldRecord } = payload;
    
    if (eventType === 'INSERT' && newRecord.status === 'online') {
      // New driver came online - fetch their details
      await this.addDriverToMap(newRecord.driver_id);
    } else if (eventType === 'DELETE' || (eventType === 'UPDATE' && newRecord.status !== 'online')) {
      // Driver went offline
      const driverId = oldRecord?.driver_id || newRecord?.driver_id;
      if (driverId) {
        this.removeDriverFromMap(driverId);
      }
    }
  }

  /**
   * Handle driver location updates
   */
  private handleDriverLocationUpdate(payload: any): void {
    const { new: updatedDriver } = payload;
    
    if (updatedDriver && this.currentDrivers.has(updatedDriver.id)) {
      const mapDriver = this.transformDatabaseDriver(updatedDriver);
      this.currentDrivers.set(mapDriver.id, mapDriver);
      this.callbacks.onDriverMoved?.(mapDriver);
    }
  }

  /**
   * Add a new driver to the map
   */
  private async addDriverToMap(driverId: string): Promise<void> {
    try {
      const { data: driver, error } = await this.supabase
        .from('drivers')
        .select(`
          id,
          user_id,
          current_location,
          vehicle_make,
          vehicle_model,
          vehicle_color,
          vehicle_type,
          is_online,
          profiles!inner(full_name)
        `)
        .eq('id', driverId)
        .eq('is_online', true)
        .single();

      if (error || !driver) {
        console.error('DriverMapService: Failed to fetch new driver:', error);
        return;
      }

      const mapDriver = this.transformDatabaseDriver(driver);
      
      // Check if driver is within radius
      if (this.isWithinRadius(mapDriver.location)) {
        this.currentDrivers.set(mapDriver.id, mapDriver);
        this.callbacks.onDriverAdded?.(mapDriver);
      }
    } catch (error) {
      console.error('DriverMapService: Error adding driver to map:', error);
    }
  }

  /**
   * Remove driver from map
   */
  private removeDriverFromMap(driverId: string): void {
    if (this.currentDrivers.has(driverId)) {
      this.currentDrivers.delete(driverId);
      this.callbacks.onDriverRemoved?.(driverId);
    }
  }

  /**
   * Transform database driver to map driver
   */
  private transformDatabaseDriver = (dbDriver: any): MapDriver => {
    const location: Coordinates = {
      lat: dbDriver.current_location?.y || dbDriver.current_location?.lat || 0,
      lng: dbDriver.current_location?.x || dbDriver.current_location?.lng || 0
    };

    return {
      id: dbDriver.id,
      name: dbDriver.profiles?.full_name || dbDriver.full_name || 'Driver',
      location,
      heading: Math.floor(Math.random() * 360), // Random heading for now
      isOnline: dbDriver.is_online || true,
      vehicleType: dbDriver.vehicle_type || 'SheRide',
      rating: dbDriver.rating || 4.5,
      estimatedArrival: this.calculateEstimatedArrival(location)
    };
  };

  /**
   * Check if location is within search radius
   */
  private isWithinRadius(driverLocation: Coordinates): boolean {
    if (!this.userLocation) return false;
    
    const distance = this.calculateDistance(this.userLocation, driverLocation);
    return distance <= this.searchRadius;
  }

  /**
   * Calculate distance between two coordinates (simplified)
   */
  private calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
    const R = 6371; // Earth's radius in km
    const dLat = (coord2.lat - coord1.lat) * Math.PI / 180;
    const dLng = (coord2.lng - coord1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(coord1.lat * Math.PI / 180) * Math.cos(coord2.lat * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Calculate estimated arrival time
   */
  private calculateEstimatedArrival(driverLocation: Coordinates): number {
    if (!this.userLocation) return 5;
    
    const distance = this.calculateDistance(this.userLocation, driverLocation);
    // Assume average speed of 30 km/h in city traffic
    return Math.max(2, Math.round(distance * 2)); // minimum 2 minutes
  }

  /**
   * Get current drivers
   */
  getCurrentDrivers(): MapDriver[] {
    return Array.from(this.currentDrivers.values());
  }

  /**
   * Update user location and refresh nearby drivers
   */
  async updateUserLocation(newLocation: Coordinates): Promise<void> {
    this.userLocation = newLocation;
    await this.loadNearbyDrivers();
  }

  /**
   * Stop all subscriptions and cleanup
   */
  cleanup(): void {
    console.log('DriverMapService: Cleaning up subscriptions');
    
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    
    this.subscriptions = [];
    this.currentDrivers.clear();
    this.callbacks = {};
  }
}
