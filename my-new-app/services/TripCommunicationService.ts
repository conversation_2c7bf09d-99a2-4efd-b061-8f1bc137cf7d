/**
 * Trip Communication Service for SheMove Passenger App
 * Handles real-time messaging between passenger and driver during trips
 */

import { supabase } from '../lib/supabase';

export type MessageType = 'text' | 'quick_action' | 'location' | 'system';
export type MessageSender = 'passenger' | 'driver' | 'system';
export type QuickActionType = 'arrival' | 'delay' | 'location' | 'contact' | 'emergency';

export interface TripMessage {
  id: string;
  trip_id: string;
  sender: MessageSender;
  sender_id: string;
  message_type: MessageType;
  content: string;
  metadata?: Record<string, any>;
  sent_at: string;
  read_at?: string;
  is_read: boolean;
}

export interface QuickAction {
  id: string;
  text: string;
  icon: string;
  action_type: QuickActionType;
}

export interface TripCommunicationCallbacks {
  onMessageReceived?: (message: TripMessage) => void;
  onQuickActionReceived?: (action: QuickAction, message: TripMessage) => void;
  onDriverArrival?: (location: { lat: number; lng: number }) => void;
  onError?: (error: Error) => void;
}

export class TripCommunicationService {
  private supabase = supabase;
  private callbacks: TripCommunicationCallbacks = {};
  private subscriptions: any[] = [];
  private currentTripId: string | null = null;
  private userId: string | null = null;

  constructor(callbacks: TripCommunicationCallbacks = {}) {
    this.callbacks = callbacks;
  }

  /**
   * Initialize the service for a specific user and trip
   */
  async initialize(userId: string, tripId: string): Promise<boolean> {
    try {
      this.userId = userId;
      this.currentTripId = tripId;
      
      console.log('TripCommunicationService: Initializing for user:', userId, 'trip:', tripId);
      
      // Subscribe to messages for this trip
      await this.subscribeToMessages(tripId);
      
      return true;
    } catch (error) {
      console.error('TripCommunicationService: Failed to initialize:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Send a text message to the driver
   */
  async sendMessage(content: string): Promise<boolean> {
    if (!this.currentTripId || !this.userId) {
      console.error('TripCommunicationService: Service not initialized');
      return false;
    }

    try {
      console.log('TripCommunicationService: Sending message:', content);
      
      const message = {
        trip_id: this.currentTripId,
        sender: 'passenger' as MessageSender,
        sender_id: this.userId,
        message_type: 'text' as MessageType,
        content,
        sent_at: new Date().toISOString(),
        is_read: false
      };

      const { error } = await this.supabase
        .from('trip_messages')
        .insert(message);

      if (error) {
        console.error('TripCommunicationService: Failed to send message:', error);
        return false;
      }

      console.log('TripCommunicationService: Message sent successfully');
      return true;
    } catch (error) {
      console.error('TripCommunicationService: Error sending message:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Send a quick action to the driver
   */
  async sendQuickAction(actionType: QuickActionType, message?: string): Promise<boolean> {
    if (!this.currentTripId || !this.userId) {
      console.error('TripCommunicationService: Service not initialized');
      return false;
    }

    try {
      console.log('TripCommunicationService: Sending quick action:', actionType);
      
      const quickActionMessage = {
        trip_id: this.currentTripId,
        sender: 'passenger' as MessageSender,
        sender_id: this.userId,
        message_type: 'quick_action' as MessageType,
        content: message || this.getQuickActionMessage(actionType),
        metadata: {
          action_type: actionType,
          timestamp: new Date().toISOString()
        },
        sent_at: new Date().toISOString(),
        is_read: false
      };

      const { error } = await this.supabase
        .from('trip_messages')
        .insert(quickActionMessage);

      if (error) {
        console.error('TripCommunicationService: Failed to send quick action:', error);
        return false;
      }

      console.log('TripCommunicationService: Quick action sent successfully');
      return true;
    } catch (error) {
      console.error('TripCommunicationService: Error sending quick action:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Get all messages for the current trip
   */
  async getMessages(): Promise<TripMessage[]> {
    if (!this.currentTripId) {
      console.error('TripCommunicationService: No active trip');
      return [];
    }

    try {
      const { data, error } = await this.supabase
        .from('trip_messages')
        .select('*')
        .eq('trip_id', this.currentTripId)
        .order('sent_at', { ascending: true });

      if (error) {
        console.error('TripCommunicationService: Failed to get messages:', error);
        return [];
      }

      return data as TripMessage[];
    } catch (error) {
      console.error('TripCommunicationService: Error getting messages:', error);
      this.callbacks.onError?.(error as Error);
      return [];
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(messageIds: string[]): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('trip_messages')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .in('id', messageIds);

      if (error) {
        console.error('TripCommunicationService: Failed to mark messages as read:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('TripCommunicationService: Error marking messages as read:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Get available quick actions
   */
  getQuickActions(): QuickAction[] {
    return [
      {
        id: 'arrival',
        text: 'I\'m here',
        icon: 'location',
        action_type: 'arrival'
      },
      {
        id: 'delay',
        text: 'Running late',
        icon: 'time',
        action_type: 'delay'
      },
      {
        id: 'location',
        text: 'Share location',
        icon: 'navigate',
        action_type: 'location'
      },
      {
        id: 'contact',
        text: 'Call me',
        icon: 'call',
        action_type: 'contact'
      },
      {
        id: 'emergency',
        text: 'Emergency',
        icon: 'warning',
        action_type: 'emergency'
      }
    ];
  }

  /**
   * Stop communication service
   */
  stopCommunication(): void {
    console.log('TripCommunicationService: Stopping communication');
    
    // Clear all subscriptions
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
    
    this.currentTripId = null;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopCommunication();
    this.userId = null;
    this.callbacks = {};
  }

  /**
   * Subscribe to messages for the trip
   */
  private async subscribeToMessages(tripId: string): Promise<void> {
    try {
      const subscription = this.supabase
        .channel(`trip-messages-${tripId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'trip_messages',
            filter: `trip_id=eq.${tripId}`
          },
          (payload) => {
            console.log('TripCommunicationService: Message received:', payload);
            this.handleMessageReceived(payload.new as TripMessage);
          }
        )
        .subscribe();

      this.subscriptions.push(subscription);
      console.log('TripCommunicationService: Subscribed to messages for trip:', tripId);
    } catch (error) {
      console.error('TripCommunicationService: Failed to subscribe to messages:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Handle received messages
   */
  private handleMessageReceived(message: TripMessage): void {
    console.log('TripCommunicationService: Handling received message:', message);
    
    // Don't process our own messages
    if (message.sender === 'passenger' && message.sender_id === this.userId) {
      return;
    }

    // Handle different message types
    if (message.message_type === 'quick_action' && message.metadata?.action_type) {
      const action: QuickAction = {
        id: message.metadata.action_type,
        text: message.content,
        icon: this.getActionIcon(message.metadata.action_type),
        action_type: message.metadata.action_type
      };
      
      this.callbacks.onQuickActionReceived?.(action, message);
      
      // Handle special actions
      if (message.metadata.action_type === 'arrival') {
        this.callbacks.onDriverArrival?.(message.metadata.location || { lat: 0, lng: 0 });
      }
    }

    this.callbacks.onMessageReceived?.(message);
  }

  /**
   * Get quick action message text
   */
  private getQuickActionMessage(actionType: QuickActionType): string {
    switch (actionType) {
      case 'arrival':
        return 'I\'m here and ready for pickup';
      case 'delay':
        return 'I\'m running a few minutes late';
      case 'location':
        return 'Here\'s my current location';
      case 'contact':
        return 'Please call me';
      case 'emergency':
        return 'Emergency assistance needed';
      default:
        return 'Quick action';
    }
  }

  /**
   * Get icon for action type
   */
  private getActionIcon(actionType: QuickActionType): string {
    switch (actionType) {
      case 'arrival':
        return 'location';
      case 'delay':
        return 'time';
      case 'location':
        return 'navigate';
      case 'contact':
        return 'call';
      case 'emergency':
        return 'warning';
      default:
        return 'chatbubble';
    }
  }
}
