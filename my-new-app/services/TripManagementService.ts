/**
 * Trip Management Service for SheMove Passenger App
 * Handles real-time trip lifecycle management, driver responses, and trip status updates
 */

import { supabase } from '../lib/supabase';
import { Trip, TripStatus, TripRequest } from '../shared/types';

export interface TripStatusUpdate {
  tripId: string;
  status: TripStatus;
  timestamp: string;
  driverLocation?: {
    lat: number;
    lng: number;
  };
  estimatedArrival?: number; // minutes
  message?: string;
}

export interface DriverResponse {
  tripId: string;
  driverId: string;
  response: 'accepted' | 'declined' | 'timeout';
  timestamp: string;
  declineReason?: string;
}

export interface TripManagementCallbacks {
  onTripStatusUpdate?: (update: TripStatusUpdate) => void;
  onDriverResponse?: (response: DriverResponse) => void;
  onDriverLocationUpdate?: (location: { lat: number; lng: number }) => void;
  onTripCompleted?: (trip: Trip) => void;
  onTripCancelled?: (trip: Trip, reason: string) => void;
  onError?: (error: Error) => void;
}

export class TripManagementService {
  private supabase = supabase;
  private callbacks: TripManagementCallbacks = {};
  private subscriptions: any[] = [];
  private currentTripId: string | null = null;
  private userId: string | null = null;
  private driverResponseTimeout: any = null;

  constructor(callbacks: TripManagementCallbacks = {}) {
    this.callbacks = callbacks;
  }

  /**
   * Initialize the service for a specific user
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      this.userId = userId;
      console.log('TripManagementService: Initializing for user:', userId);
      return true;
    } catch (error) {
      console.error('TripManagementService: Failed to initialize:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Start monitoring a trip
   */
  async startTripMonitoring(tripId: string): Promise<boolean> {
    try {
      console.log('TripManagementService: Starting monitoring for trip:', tripId);
      
      this.currentTripId = tripId;
      
      // Subscribe to trip status updates
      await this.subscribeToTripUpdates(tripId);
      
      // Subscribe to driver responses
      await this.subscribeToDriverResponses(tripId);
      
      // Subscribe to driver location updates
      await this.subscribeToDriverLocation(tripId);
      
      // Set up driver response timeout (2 minutes)
      this.setupDriverResponseTimeout(tripId);
      
      return true;
    } catch (error) {
      console.error('TripManagementService: Failed to start monitoring:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Stop monitoring current trip
   */
  stopTripMonitoring(): void {
    console.log('TripManagementService: Stopping trip monitoring');
    
    // Clear all subscriptions
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
    
    // Clear timeout
    if (this.driverResponseTimeout) {
      clearTimeout(this.driverResponseTimeout);
      this.driverResponseTimeout = null;
    }
    
    this.currentTripId = null;
  }

  /**
   * Update trip status
   */
  async updateTripStatus(tripId: string, status: TripStatus, message?: string): Promise<boolean> {
    try {
      console.log('TripManagementService: Updating trip status:', { tripId, status, message });
      
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      // Add timestamp fields based on status
      switch (status) {
        case 'accepted':
          updateData.driver_accepted_at = new Date().toISOString();
          break;
        case 'in_progress':
          updateData.trip_started_at = new Date().toISOString();
          break;
        case 'completed':
          updateData.completed_at = new Date().toISOString();
          break;
        case 'cancelled':
          updateData.cancelled_at = new Date().toISOString();
          if (message) {
            updateData.cancellation_reason = message;
            updateData.cancelled_by = 'passenger';
          }
          break;
      }

      const { error } = await this.supabase
        .from('trips')
        .update(updateData)
        .eq('id', tripId);

      if (error) {
        console.error('TripManagementService: Failed to update trip status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('TripManagementService: Error updating trip status:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Cancel current trip
   */
  async cancelTrip(reason: string): Promise<boolean> {
    if (!this.currentTripId) {
      console.error('TripManagementService: No active trip to cancel');
      return false;
    }

    try {
      console.log('TripManagementService: Cancelling trip:', this.currentTripId, 'Reason:', reason);
      
      const success = await this.updateTripStatus(this.currentTripId, 'cancelled', reason);
      
      if (success) {
        // Stop monitoring
        this.stopTripMonitoring();
        
        // Notify callback
        const trip = await this.getTripById(this.currentTripId);
        if (trip) {
          this.callbacks.onTripCancelled?.(trip, reason);
        }
      }
      
      return success;
    } catch (error) {
      console.error('TripManagementService: Error cancelling trip:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Get current trip details
   */
  async getCurrentTrip(): Promise<Trip | null> {
    if (!this.currentTripId) {
      return null;
    }
    
    return await this.getTripById(this.currentTripId);
  }

  /**
   * Get trip by ID
   */
  async getTripById(tripId: string): Promise<Trip | null> {
    try {
      const { data, error } = await this.supabase
        .from('trips')
        .select('*')
        .eq('id', tripId)
        .single();

      if (error) {
        console.error('TripManagementService: Failed to get trip:', error);
        return null;
      }

      return data as Trip;
    } catch (error) {
      console.error('TripManagementService: Error getting trip:', error);
      return null;
    }
  }

  /**
   * Subscribe to trip status updates
   */
  private async subscribeToTripUpdates(tripId: string): Promise<void> {
    try {
      const subscription = this.supabase
        .channel(`trip-updates-${tripId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'trips',
            filter: `id=eq.${tripId}`
          },
          (payload: any) => {
            console.log('TripManagementService: Trip update received:', payload);
            this.handleTripUpdate(payload.new as Trip);
          }
        )
        .subscribe();

      this.subscriptions.push(subscription);
      console.log('TripManagementService: Subscribed to trip updates for:', tripId);
    } catch (error) {
      console.error('TripManagementService: Failed to subscribe to trip updates:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Subscribe to driver responses
   */
  private async subscribeToDriverResponses(tripId: string): Promise<void> {
    try {
      const subscription = this.supabase
        .channel(`trip-requests-${tripId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'trip_requests',
            filter: `trip_id=eq.${tripId}`
          },
          (payload) => {
            console.log('TripManagementService: Driver response received:', payload);
            this.handleDriverResponse(payload.new as TripRequest);
          }
        )
        .subscribe();

      this.subscriptions.push(subscription);
      console.log('TripManagementService: Subscribed to driver responses for:', tripId);
    } catch (error) {
      console.error('TripManagementService: Failed to subscribe to driver responses:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Subscribe to driver location updates
   */
  private async subscribeToDriverLocation(tripId: string): Promise<void> {
    try {
      const subscription = this.supabase
        .channel(`driver-location-${tripId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'trip_locations',
            filter: `trip_id=eq.${tripId}`
          },
          (payload) => {
            console.log('TripManagementService: Driver location update received:', payload);
            this.handleDriverLocationUpdate(payload.new);
          }
        )
        .subscribe();

      this.subscriptions.push(subscription);
      console.log('TripManagementService: Subscribed to driver location for:', tripId);
    } catch (error) {
      console.error('TripManagementService: Failed to subscribe to driver location:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Handle trip status updates
   */
  private handleTripUpdate(trip: Trip): void {
    console.log('TripManagementService: Handling trip update:', trip.status);

    const update: TripStatusUpdate = {
      tripId: trip.id,
      status: trip.status,
      timestamp: trip.updated_at || new Date().toISOString()
    };

    // Clear timeout if driver accepted
    if (trip.status === 'accepted' && this.driverResponseTimeout) {
      clearTimeout(this.driverResponseTimeout);
      this.driverResponseTimeout = null;
    }

    // Handle trip completion
    if (trip.status === 'completed') {
      this.callbacks.onTripCompleted?.(trip);
      this.stopTripMonitoring();
    }

    // Handle trip cancellation
    if (trip.status === 'cancelled') {
      this.callbacks.onTripCancelled?.(trip, trip.cancellation_reason || 'Trip cancelled');
      this.stopTripMonitoring();
    }

    this.callbacks.onTripStatusUpdate?.(update);
  }

  /**
   * Handle driver responses
   */
  private handleDriverResponse(request: TripRequest): void {
    console.log('TripManagementService: Handling driver response:', request.driver_response);

    if (request.driver_response) {
      const response: DriverResponse = {
        tripId: request.trip_id,
        driverId: request.driver_id,
        response: request.driver_response,
        timestamp: request.response_time || new Date().toISOString(),
        declineReason: request.decline_reason
      };

      this.callbacks.onDriverResponse?.(response);

      // If driver declined, try to find another driver
      if (request.driver_response === 'declined') {
        this.handleDriverDeclined(request.trip_id);
      }
    }
  }

  /**
   * Handle driver location updates
   */
  private handleDriverLocationUpdate(locationData: any): void {
    if (locationData.location_data) {
      const location = {
        lat: locationData.location_data.lat,
        lng: locationData.location_data.lng
      };

      console.log('TripManagementService: Driver location update:', location);
      this.callbacks.onDriverLocationUpdate?.(location);
    }
  }

  /**
   * Setup driver response timeout
   */
  private setupDriverResponseTimeout(tripId: string): void {
    // Clear any existing timeout
    if (this.driverResponseTimeout) {
      clearTimeout(this.driverResponseTimeout);
    }

    // Set 2-minute timeout for driver response
    this.driverResponseTimeout = setTimeout(async () => {
      console.log('TripManagementService: Driver response timeout for trip:', tripId);
      await this.handleDriverTimeout(tripId);
    }, 2 * 60 * 1000); // 2 minutes
  }

  /**
   * Handle driver timeout
   */
  private async handleDriverTimeout(tripId: string): Promise<void> {
    try {
      console.log('TripManagementService: Handling driver timeout for trip:', tripId);

      // Mark current request as timeout
      await this.supabase
        .from('trip_requests')
        .update({
          driver_response: 'timeout',
          response_time: new Date().toISOString()
        })
        .eq('trip_id', tripId)
        .is('driver_response', null);

      // Try to find another driver
      await this.findAlternativeDriver(tripId);
    } catch (error) {
      console.error('TripManagementService: Error handling driver timeout:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Handle driver declined
   */
  private async handleDriverDeclined(tripId: string): Promise<void> {
    console.log('TripManagementService: Driver declined trip:', tripId);
    // Try to find another driver
    await this.findAlternativeDriver(tripId);
  }

  /**
   * Find alternative driver
   */
  private async findAlternativeDriver(tripId: string): Promise<void> {
    try {
      console.log('TripManagementService: Finding alternative driver for trip:', tripId);

      // Get trip details
      const trip = await this.getTripById(tripId);
      if (!trip) {
        console.error('TripManagementService: Trip not found for alternative driver search');
        return;
      }

      // Find available drivers (simplified - in production, use proper driver matching service)
      const { data: availableDrivers, error } = await this.supabase
        .from('drivers')
        .select('id, user_id')
        .eq('is_online', true)
        .eq('verification_status', 'approved')
        .not('id', 'in', `(SELECT driver_id FROM trip_requests WHERE trip_id = '${tripId}')`);

      if (error || !availableDrivers || availableDrivers.length === 0) {
        console.log('TripManagementService: No alternative drivers available');
        // Could implement trip cancellation due to no drivers
        return;
      }

      // Send request to next available driver
      const nextDriver = availableDrivers[0];
      await this.supabase
        .from('trip_requests')
        .insert({
          trip_id: tripId,
          driver_id: nextDriver.id,
          response_deadline: new Date(Date.now() + 2 * 60 * 1000).toISOString() // 2 minutes
        });

      console.log('TripManagementService: Sent request to alternative driver:', nextDriver.id);

      // Setup new timeout
      this.setupDriverResponseTimeout(tripId);
    } catch (error) {
      console.error('TripManagementService: Error finding alternative driver:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopTripMonitoring();
    this.userId = null;
    this.callbacks = {};
  }
}
