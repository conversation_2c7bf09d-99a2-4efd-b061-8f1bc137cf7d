/**
 * Tests for TripCommunicationService
 */

import { TripCommunicationService, TripMessage, QuickAction } from '../TripCommunicationService';

// Mock Supabase
jest.mock('../../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      insert: jest.fn(),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn()
        }))
      })),
      update: jest.fn(() => ({
        in: jest.fn()
      }))
    })),
    channel: jest.fn(() => ({
      on: jest.fn(() => ({
        subscribe: jest.fn()
      }))
    }))
  }
}));

describe('TripCommunicationService', () => {
  let service: TripCommunicationService;
  let mockCallbacks: any;

  beforeEach(() => {
    mockCallbacks = {
      onMessageReceived: jest.fn(),
      onQuickActionReceived: jest.fn(),
      onDriverArrival: jest.fn(),
      onError: jest.fn()
    };

    service = new TripCommunicationService(mockCallbacks);
  });

  afterEach(() => {
    service.cleanup();
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize successfully with valid user and trip ID', async () => {
      const result = await service.initialize('test-user-id', 'test-trip-id');
      expect(result).toBe(true);
    });

    it('should subscribe to messages during initialization', async () => {
      const mockChannel = {
        on: jest.fn().mockReturnThis(),
        subscribe: jest.fn()
      };
      
      require('../../lib/supabase').supabase.channel.mockReturnValue(mockChannel);

      await service.initialize('test-user-id', 'test-trip-id');

      expect(require('../../lib/supabase').supabase.channel).toHaveBeenCalledWith('trip-messages-test-trip-id');
      expect(mockChannel.on).toHaveBeenCalled();
      expect(mockChannel.subscribe).toHaveBeenCalled();
    });
  });

  describe('message sending', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id', 'test-trip-id');
    });

    it('should send text message successfully', async () => {
      const mockInsert = jest.fn().mockResolvedValue({ error: null });
      require('../../lib/supabase').supabase.from.mockReturnValue({
        insert: mockInsert
      });

      const result = await service.sendMessage('Hello driver!');

      expect(result).toBe(true);
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          trip_id: 'test-trip-id',
          sender: 'passenger',
          sender_id: 'test-user-id',
          message_type: 'text',
          content: 'Hello driver!',
          is_read: false
        })
      );
    });

    it('should handle message sending errors', async () => {
      const mockError = new Error('Database error');
      require('../../lib/supabase').supabase.from.mockReturnValue({
        insert: jest.fn().mockResolvedValue({ error: mockError })
      });

      const result = await service.sendMessage('Hello driver!');

      expect(result).toBe(false);
    });

    it('should fail to send message when not initialized', async () => {
      const uninitializedService = new TripCommunicationService();
      const result = await uninitializedService.sendMessage('Hello driver!');

      expect(result).toBe(false);
    });
  });

  describe('quick actions', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id', 'test-trip-id');
    });

    it('should send quick action successfully', async () => {
      const mockInsert = jest.fn().mockResolvedValue({ error: null });
      require('../../lib/supabase').supabase.from.mockReturnValue({
        insert: mockInsert
      });

      const result = await service.sendQuickAction('arrival');

      expect(result).toBe(true);
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          trip_id: 'test-trip-id',
          sender: 'passenger',
          sender_id: 'test-user-id',
          message_type: 'quick_action',
          content: 'I\'m here and ready for pickup',
          metadata: expect.objectContaining({
            action_type: 'arrival'
          }),
          is_read: false
        })
      );
    });

    it('should send custom message with quick action', async () => {
      const mockInsert = jest.fn().mockResolvedValue({ error: null });
      require('../../lib/supabase').supabase.from.mockReturnValue({
        insert: mockInsert
      });

      const result = await service.sendQuickAction('delay', 'Traffic is heavy, will be 10 minutes late');

      expect(result).toBe(true);
      expect(mockInsert).toHaveBeenCalledWith(
        expect.objectContaining({
          content: 'Traffic is heavy, will be 10 minutes late',
          metadata: expect.objectContaining({
            action_type: 'delay'
          })
        })
      );
    });

    it('should return available quick actions', () => {
      const actions = service.getQuickActions();

      expect(actions).toHaveLength(5);
      expect(actions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            action_type: 'arrival',
            text: 'I\'m here',
            icon: 'location'
          }),
          expect.objectContaining({
            action_type: 'delay',
            text: 'Running late',
            icon: 'time'
          }),
          expect.objectContaining({
            action_type: 'emergency',
            text: 'Emergency',
            icon: 'warning'
          })
        ])
      );
    });
  });

  describe('message retrieval', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id', 'test-trip-id');
    });

    it('should get messages for current trip', async () => {
      const mockMessages: TripMessage[] = [
        {
          id: 'msg-1',
          trip_id: 'test-trip-id',
          sender: 'driver',
          sender_id: 'driver-id',
          message_type: 'text',
          content: 'On my way!',
          sent_at: new Date().toISOString(),
          is_read: false
        }
      ];

      const mockOrder = jest.fn().mockResolvedValue({ data: mockMessages, error: null });
      const mockEq = jest.fn().mockReturnValue({ order: mockOrder });
      const mockSelect = jest.fn().mockReturnValue({ eq: mockEq });
      
      require('../../lib/supabase').supabase.from.mockReturnValue({
        select: mockSelect
      });

      const result = await service.getMessages();

      expect(result).toEqual(mockMessages);
      expect(mockSelect).toHaveBeenCalledWith('*');
      expect(mockEq).toHaveBeenCalledWith('trip_id', 'test-trip-id');
      expect(mockOrder).toHaveBeenCalledWith('sent_at', { ascending: true });
    });

    it('should handle errors when getting messages', async () => {
      const mockError = new Error('Database error');
      const mockOrder = jest.fn().mockResolvedValue({ data: null, error: mockError });
      const mockEq = jest.fn().mockReturnValue({ order: mockOrder });
      const mockSelect = jest.fn().mockReturnValue({ eq: mockEq });
      
      require('../../lib/supabase').supabase.from.mockReturnValue({
        select: mockSelect
      });

      const result = await service.getMessages();

      expect(result).toEqual([]);
    });

    it('should return empty array when no active trip', async () => {
      const uninitializedService = new TripCommunicationService();
      const result = await uninitializedService.getMessages();

      expect(result).toEqual([]);
    });
  });

  describe('message marking as read', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id', 'test-trip-id');
    });

    it('should mark messages as read successfully', async () => {
      const mockIn = jest.fn().mockResolvedValue({ error: null });
      const mockUpdate = jest.fn().mockReturnValue({ in: mockIn });
      
      require('../../lib/supabase').supabase.from.mockReturnValue({
        update: mockUpdate
      });

      const result = await service.markMessagesAsRead(['msg-1', 'msg-2']);

      expect(result).toBe(true);
      expect(mockUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          is_read: true,
          read_at: expect.any(String)
        })
      );
      expect(mockIn).toHaveBeenCalledWith('id', ['msg-1', 'msg-2']);
    });

    it('should handle errors when marking messages as read', async () => {
      const mockError = new Error('Database error');
      const mockIn = jest.fn().mockResolvedValue({ error: mockError });
      const mockUpdate = jest.fn().mockReturnValue({ in: mockIn });
      
      require('../../lib/supabase').supabase.from.mockReturnValue({
        update: mockUpdate
      });

      const result = await service.markMessagesAsRead(['msg-1']);

      expect(result).toBe(false);
    });
  });

  describe('cleanup', () => {
    it('should cleanup all resources', () => {
      service.cleanup();
      // Verify cleanup happened (no direct way to test private properties)
      expect(true).toBe(true); // Placeholder assertion
    });

    it('should stop communication and clear subscriptions', () => {
      service.stopCommunication();
      // Verify cleanup happened (no direct way to test private properties)
      expect(true).toBe(true); // Placeholder assertion
    });
  });
});
