/**
 * Distance calculation service using Haversine formula
 * Provides accurate distance calculations between geographic coordinates
 */

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface DistanceResult {
  distanceKm: number;
  distanceMiles: number;
  durationMinutes: number;
  durationText: string;
}

export interface RouteInfo {
  distance: DistanceResult;
  estimatedDuration: number; // in minutes
  averageSpeed: number; // km/h
}

class DistanceService {
  // Average speeds for different road types in South Africa (km/h)
  private readonly AVERAGE_SPEEDS = {
    CITY: 35, // Urban areas with traffic lights
    SUBURBAN: 50, // Suburban areas
    HIGHWAY: 80, // Highways and main roads
    RURAL: 60, // Rural roads
  };

  // Traffic multipliers for different times of day
  private readonly TRAFFIC_MULTIPLIERS = {
    PEAK_MORNING: 0.6, // 7-9 AM
    PEAK_EVENING: 0.65, // 5-7 PM
    NORMAL: 0.8, // Normal traffic
    LIGHT: 1.0, // Light traffic (late night/early morning)
  };

  /**
   * Calculate distance between two points using Haversine formula
   * @param point1 - Starting coordinates
   * @param point2 - Ending coordinates
   * @returns Distance result with km, miles, and estimated duration
   */
  calculateDistance(point1: Coordinates, point2: Coordinates): DistanceResult {
    const R = 6371; // Earth's radius in kilometers

    // Convert degrees to radians
    const lat1Rad = this.toRadians(point1.lat);
    const lat2Rad = this.toRadians(point2.lat);
    const deltaLatRad = this.toRadians(point2.lat - point1.lat);
    const deltaLngRad = this.toRadians(point2.lng - point1.lng);

    // Haversine formula
    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distanceKm = R * c;

    // Convert to miles
    const distanceMiles = distanceKm * 0.621371;

    // Estimate duration based on distance and average speed
    const averageSpeed = this.getAverageSpeed(distanceKm);
    const durationMinutes = Math.round((distanceKm / averageSpeed) * 60);

    return {
      distanceKm: Math.round(distanceKm * 100) / 100, // Round to 2 decimal places
      distanceMiles: Math.round(distanceMiles * 100) / 100,
      durationMinutes,
      durationText: this.formatDuration(durationMinutes),
    };
  }

  /**
   * Get route information with traffic considerations
   * @param pickup - Pickup coordinates
   * @param destination - Destination coordinates
   * @returns Route information with traffic-adjusted duration
   */
  getRouteInfo(pickup: Coordinates, destination: Coordinates): RouteInfo {
    const distance = this.calculateDistance(pickup, destination);
    const trafficMultiplier = this.getCurrentTrafficMultiplier();
    const averageSpeed = this.getAverageSpeed(distance.distanceKm);
    
    // Adjust duration for current traffic conditions
    const adjustedDuration = Math.round(distance.durationMinutes / trafficMultiplier);

    return {
      distance: {
        ...distance,
        durationMinutes: adjustedDuration,
        durationText: this.formatDuration(adjustedDuration),
      },
      estimatedDuration: adjustedDuration,
      averageSpeed: averageSpeed * trafficMultiplier,
    };
  }

  /**
   * Calculate ETA from current time
   * @param durationMinutes - Travel duration in minutes
   * @returns ETA as formatted time string
   */
  calculateETA(durationMinutes: number): string {
    const now = new Date();
    const eta = new Date(now.getTime() + durationMinutes * 60000);
    
    return eta.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  }

  /**
   * Estimate pickup time based on driver distance
   * @param driverLocation - Driver's current location
   * @param pickupLocation - Pickup location
   * @returns Estimated pickup time in minutes
   */
  estimatePickupTime(driverLocation: Coordinates, pickupLocation: Coordinates): number {
    const distance = this.calculateDistance(driverLocation, pickupLocation);
    // Drivers typically move faster than regular traffic
    const driverSpeed = this.getAverageSpeed(distance.distanceKm) * 1.2;
    const trafficMultiplier = this.getCurrentTrafficMultiplier();
    
    return Math.round((distance.distanceKm / (driverSpeed * trafficMultiplier)) * 60);
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Get appropriate average speed based on distance
   * @param distanceKm - Distance in kilometers
   * @returns Average speed in km/h
   */
  private getAverageSpeed(distanceKm: number): number {
    if (distanceKm < 5) return this.AVERAGE_SPEEDS.CITY;
    if (distanceKm < 15) return this.AVERAGE_SPEEDS.SUBURBAN;
    if (distanceKm < 50) return this.AVERAGE_SPEEDS.HIGHWAY;
    return this.AVERAGE_SPEEDS.RURAL;
  }

  /**
   * Get current traffic multiplier based on time of day
   * @returns Traffic multiplier (lower = more traffic)
   */
  private getCurrentTrafficMultiplier(): number {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 6 = Saturday

    // Weekend traffic is generally lighter
    if (day === 0 || day === 6) {
      return this.TRAFFIC_MULTIPLIERS.LIGHT;
    }

    // Weekday traffic patterns
    if ((hour >= 7 && hour <= 9)) {
      return this.TRAFFIC_MULTIPLIERS.PEAK_MORNING;
    } else if (hour >= 17 && hour <= 19) {
      return this.TRAFFIC_MULTIPLIERS.PEAK_EVENING;
    } else if (hour >= 22 || hour <= 6) {
      return this.TRAFFIC_MULTIPLIERS.LIGHT;
    } else {
      return this.TRAFFIC_MULTIPLIERS.NORMAL;
    }
  }

  /**
   * Format duration in minutes to human-readable string
   * @param minutes - Duration in minutes
   * @returns Formatted duration string
   */
  private formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours} hr`;
    }
    
    return `${hours} hr ${remainingMinutes} min`;
  }

  /**
   * Calculate bearing between two points (for driver direction indicators)
   * @param point1 - Starting point
   * @param point2 - Ending point
   * @returns Bearing in degrees (0-360)
   */
  calculateBearing(point1: Coordinates, point2: Coordinates): number {
    const lat1Rad = this.toRadians(point1.lat);
    const lat2Rad = this.toRadians(point2.lat);
    const deltaLngRad = this.toRadians(point2.lng - point1.lng);

    const y = Math.sin(deltaLngRad) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
              Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(deltaLngRad);

    const bearingRad = Math.atan2(y, x);
    const bearingDeg = (bearingRad * 180 / Math.PI + 360) % 360;

    return Math.round(bearingDeg);
  }

  /**
   * Get cardinal direction from bearing
   * @param bearing - Bearing in degrees
   * @returns Cardinal direction (N, NE, E, SE, S, SW, W, NW)
   */
  getCardinalDirection(bearing: number): string {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(bearing / 45) % 8;
    return directions[index];
  }
}

export const distanceService = new DistanceService();
