/**
 * Driver Assignment Service for SheMove
 * Implements automatic driver assignment algorithm based on industry best practices
 * Research: Uber, Lyft, Grab use closest-driver greedy algorithms with ETA optimization
 */

import { supabase } from '../lib/supabase';
import { RideType } from './fareService';
import { Coordinates } from './distanceService';

export interface AssignmentRequest {
  pickupLocation: Coordinates;
  pickupAddress: string;
  rideType: RideType;
  maxSearchRadius: number; // km
  passengerRating?: number;
}

export interface AssignedDriver {
  id: string;
  name: string;
  rating: number;
  vehicleInfo: {
    make: string;
    model: string;
    color: string;
    plate: string;
    type: RideType;
  };
  location: Coordinates;
  distanceKm: number;
  estimatedArrivalMinutes: number;
  profileImage: string;
  totalTrips: number;
  acceptanceRate: number;
}

export interface AssignmentResult {
  success: boolean;
  assignedDriver?: AssignedDriver;
  error?: string;
  searchRadius: number;
  driversFound: number;
  assignmentTime: number; // milliseconds
}

export interface AssignmentCallbacks {
  onSearchStart?: () => void;
  onDriversFound?: (count: number) => void;
  onDriverAssigned?: (driver: AssignedDriver) => void;
  onAssignmentFailed?: (reason: string) => void;
}

class DriverAssignmentService {
  private readonly MIN_DRIVER_RATING = 4.0;
  private readonly MIN_ACCEPTANCE_RATE = 70; // percentage
  private readonly MAX_ASSIGNMENT_TIME = 30000; // 30 seconds timeout
  private callbacks: AssignmentCallbacks = {};

  /**
   * Set callbacks for assignment events
   */
  setCallbacks(callbacks: AssignmentCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Automatically assign the best available driver to a passenger
   * Uses industry-standard closest-driver algorithm with ETA optimization
   */
  async assignDriver(request: AssignmentRequest): Promise<AssignmentResult> {
    const startTime = Date.now();
    
    try {
      console.log('🎯 Starting automatic driver assignment:', request);
      this.callbacks.onSearchStart?.();

      // Step 1: Find all available drivers in radius
      const availableDrivers = await this.findAvailableDrivers(request);
      
      console.log(`📍 Found ${availableDrivers.length} available drivers`);
      this.callbacks.onDriversFound?.(availableDrivers.length);

      if (availableDrivers.length === 0) {
        const error = 'No available drivers found in your area';
        this.callbacks.onAssignmentFailed?.(error);
        return {
          success: false,
          error,
          searchRadius: request.maxSearchRadius,
          driversFound: 0,
          assignmentTime: Date.now() - startTime
        };
      }

      // Step 2: Apply assignment algorithm (closest driver with quality filters)
      const bestDriver = this.selectBestDriver(availableDrivers, request);

      if (!bestDriver) {
        const error = 'No suitable drivers available (quality filters)';
        this.callbacks.onAssignmentFailed?.(error);
        return {
          success: false,
          error,
          searchRadius: request.maxSearchRadius,
          driversFound: availableDrivers.length,
          assignmentTime: Date.now() - startTime
        };
      }

      // Step 3: Reserve driver (mark as assigned)
      const reservationSuccess = await this.reserveDriver(bestDriver.id);
      
      if (!reservationSuccess) {
        const error = 'Selected driver became unavailable';
        this.callbacks.onAssignmentFailed?.(error);
        return {
          success: false,
          error,
          searchRadius: request.maxSearchRadius,
          driversFound: availableDrivers.length,
          assignmentTime: Date.now() - startTime
        };
      }

      console.log('✅ Driver assigned successfully:', bestDriver.name);
      this.callbacks.onDriverAssigned?.(bestDriver);

      return {
        success: true,
        assignedDriver: bestDriver,
        searchRadius: request.maxSearchRadius,
        driversFound: availableDrivers.length,
        assignmentTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Driver assignment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Assignment failed';
      
      this.callbacks.onAssignmentFailed?.(errorMessage);
      
      return {
        success: false,
        error: errorMessage,
        searchRadius: request.maxSearchRadius,
        driversFound: 0,
        assignmentTime: Date.now() - startTime
      };
    }
  }

  /**
   * Find all available drivers within radius
   * Uses the existing get_nearby_drivers database function
   */
  private async findAvailableDrivers(request: AssignmentRequest): Promise<AssignedDriver[]> {
    try {
      const { data, error } = await supabase.rpc('get_nearby_drivers', {
        p_pickup_lat: request.pickupLocation.lat,
        p_pickup_lng: request.pickupLocation.lng,
        p_max_distance_km: request.maxSearchRadius,
        p_ride_type_filter: request.rideType
      });

      if (error) {
        console.error('Database error finding drivers:', error);
        return [];
      }

      if (!data || data.length === 0) {
        return [];
      }

      // Transform database results to AssignedDriver format
      return data.map((dbDriver: any) => this.transformDatabaseDriver(dbDriver));
    } catch (error) {
      console.error('Error finding available drivers:', error);
      return [];
    }
  }

  /**
   * Select the best driver using industry-standard algorithm
   * Priority: 1) Distance/ETA 2) Rating 3) Acceptance Rate 4) Experience
   */
  private selectBestDriver(drivers: AssignedDriver[], request: AssignmentRequest): AssignedDriver | null {
    // Filter drivers by quality thresholds
    const qualifiedDrivers = drivers.filter(driver => 
      driver.rating >= this.MIN_DRIVER_RATING &&
      driver.acceptanceRate >= this.MIN_ACCEPTANCE_RATE
    );

    if (qualifiedDrivers.length === 0) {
      console.log('⚠️ No drivers meet quality thresholds, using all available');
      // Fallback to all drivers if none meet thresholds
      return drivers.length > 0 ? drivers[0] : null;
    }

    // Sort by multiple criteria (Uber/Lyft approach):
    // 1. Distance (primary factor)
    // 2. Rating (secondary)
    // 3. Acceptance rate (tertiary)
    const sortedDrivers = qualifiedDrivers.sort((a, b) => {
      // Primary: Distance (closest first)
      const distanceDiff = a.distanceKm - b.distanceKm;
      if (Math.abs(distanceDiff) > 0.5) { // 500m threshold
        return distanceDiff;
      }

      // Secondary: Rating (higher first)
      const ratingDiff = b.rating - a.rating;
      if (Math.abs(ratingDiff) > 0.1) {
        return ratingDiff;
      }

      // Tertiary: Acceptance rate (higher first)
      return b.acceptanceRate - a.acceptanceRate;
    });

    const selectedDriver = sortedDrivers[0];
    console.log(`🎯 Selected driver: ${selectedDriver.name} (${selectedDriver.distanceKm}km, ${selectedDriver.rating}⭐)`);
    
    return selectedDriver;
  }

  /**
   * Reserve driver by updating their availability status
   */
  private async reserveDriver(driverId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('driver_availability')
        .update({
          status: 'assigned',
          updated_at: new Date().toISOString()
        })
        .eq('driver_id', driverId)
        .eq('status', 'online'); // Only update if still online

      if (error) {
        console.error('Error reserving driver:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Driver reservation failed:', error);
      return false;
    }
  }

  /**
   * Transform database driver to AssignedDriver format
   */
  private transformDatabaseDriver(dbDriver: any): AssignedDriver {
    return {
      id: dbDriver.driver_id,
      name: dbDriver.driver_name || 'Driver',
      rating: Number(dbDriver.rating) || 5.0,
      vehicleInfo: {
        make: dbDriver.vehicle_info?.make || 'Unknown',
        model: dbDriver.vehicle_info?.model || 'Vehicle',
        color: dbDriver.vehicle_info?.color || 'Unknown',
        plate: dbDriver.vehicle_info?.plate || 'N/A',
        type: dbDriver.vehicle_info?.type || 'SheRide'
      },
      location: {
        lat: dbDriver.current_location?.y || 0,
        lng: dbDriver.current_location?.x || 0
      },
      distanceKm: Number(dbDriver.distance_km) || 0,
      estimatedArrivalMinutes: Number(dbDriver.estimated_arrival) || 5,
      profileImage: `https://ui-avatars.com/api/?name=${encodeURIComponent(dbDriver.driver_name || 'Driver')}&background=F9E6F7&color=E91E63&size=128`,
      totalTrips: 0, // Would need additional query
      acceptanceRate: 85 // Would need additional query - using default
    };
  }

  /**
   * Release driver reservation (if assignment fails)
   */
  async releaseDriver(driverId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('driver_availability')
        .update({
          status: 'online',
          updated_at: new Date().toISOString()
        })
        .eq('driver_id', driverId)
        .eq('status', 'assigned');

      return !error;
    } catch (error) {
      console.error('Error releasing driver:', error);
      return false;
    }
  }

  /**
   * Get assignment statistics for monitoring
   */
  async getAssignmentStats(timeRangeHours: number = 24): Promise<{
    totalAssignments: number;
    successRate: number;
    averageAssignmentTime: number;
    averageSearchRadius: number;
  }> {
    // This would require additional tracking tables
    // For now, return mock data
    return {
      totalAssignments: 0,
      successRate: 0,
      averageAssignmentTime: 0,
      averageSearchRadius: 0
    };
  }
}

// Export singleton instance
export const driverAssignmentService = new DriverAssignmentService();
export default driverAssignmentService;
