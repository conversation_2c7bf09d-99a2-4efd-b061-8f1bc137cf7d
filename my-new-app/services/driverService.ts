/**
 * Driver simulation service for SheMove app
 * Generates realistic nearby drivers with profiles and locations
 */

import { Coordinates } from './distanceService';
import { distanceService } from './distanceService';
import { RideType } from './fareService';

export interface Driver {
  id: string;
  name: string;
  rating: number;
  totalTrips: number;
  profileImage: string;
  vehicleInfo: {
    make: string;
    model: string;
    year: number;
    color: string;
    licensePlate: string;
    type: RideType;
  };
  location: Coordinates;
  isOnline: boolean;
  distanceFromUser: number; // in km
  estimatedArrival: number; // in minutes
  heading: number; // bearing in degrees
  isMoving: boolean;
  specialFeatures: string[];
}

export interface DriverSearchResult {
  availableDrivers: Driver[];
  totalDriversInArea: number;
  averageWaitTime: number;
  nearestDriver: Driver | null;
}

class DriverService {
  private drivers: Driver[] = [];
  private readonly MAX_SEARCH_RADIUS = 15; // km
  private readonly MIN_DRIVERS = 3;
  private readonly MAX_DRIVERS = 12;

  // South African female names for driver simulation
  private readonly FEMALE_NAMES = [
    'Nomsa', 'Than<PERSON>', '<PERSON>pho', '<PERSON><PERSON>', 'Precious', 'Bon<PERSON>', '<PERSON><PERSON>', 'Naledi',
    '<PERSON>gomotso', 'Refilwe', 'Tebogo', 'Mmabatho', 'Dineo', 'Palesa', 'Ntombi',
    'Zinhle', 'Busisiwe', 'Nomthandazo', 'Khanyisile', 'Thandiwe', 'Nompumelelo',
    'Sindisiwe', 'Nokuthula', 'Nomfundo', 'Zandile', 'Lindiwe', 'Nosipho'
  ];

  // Vehicle data for South African market
  private readonly VEHICLES = {
    SheRide: [
      { make: 'Toyota', model: 'Corolla', colors: ['White', 'Silver', 'Black', 'Blue'] },
      { make: 'Volkswagen', model: 'Polo', colors: ['White', 'Red', 'Silver', 'Black'] },
      { make: 'Hyundai', model: 'i20', colors: ['White', 'Blue', 'Silver', 'Red'] },
      { make: 'Nissan', model: 'Micra', colors: ['White', 'Silver', 'Blue', 'Black'] },
      { make: 'Ford', model: 'Fiesta', colors: ['White', 'Red', 'Silver', 'Blue'] },
    ],
    ShePool: [
      { make: 'Toyota', model: 'Yaris', colors: ['White', 'Silver', 'Blue'] },
      { make: 'Hyundai', model: 'Grand i10', colors: ['White', 'Red', 'Silver'] },
      { make: 'Suzuki', model: 'Swift', colors: ['White', 'Blue', 'Silver'] },
      { make: 'Kia', model: 'Picanto', colors: ['White', 'Red', 'Silver'] },
    ],
    SheXL: [
      { make: 'Toyota', model: 'Avanza', colors: ['White', 'Silver', 'Black'] },
      { make: 'Hyundai', model: 'H1', colors: ['White', 'Silver', 'Blue'] },
      { make: 'Volkswagen', model: 'Caravelle', colors: ['White', 'Silver', 'Black'] },
      { make: 'Ford', model: 'Tourneo', colors: ['White', 'Blue', 'Silver'] },
    ],
  };

  // Special features for women-focused service
  private readonly SPECIAL_FEATURES = [
    'Child seat available',
    'Female driver',
    'Highly rated',
    'Pet friendly',
    'Wheelchair accessible',
    'Phone charger',
    'Water bottles',
    'Air conditioning',
    'Music system',
    'Safety certified',
  ];

  /**
   * Generate nearby drivers around a location
   * @param userLocation - User's current location
   * @param rideType - Requested ride type
   * @returns Array of available drivers
   */
  generateNearbyDrivers(userLocation: Coordinates, rideType?: RideType): DriverSearchResult {
    const driverCount = Math.floor(Math.random() * (this.MAX_DRIVERS - this.MIN_DRIVERS + 1)) + this.MIN_DRIVERS;
    const availableDrivers: Driver[] = [];

    for (let i = 0; i < driverCount; i++) {
      const driver = this.createRandomDriver(userLocation, rideType);
      if (driver) {
        availableDrivers.push(driver);
      }
    }

    // Sort by distance from user
    availableDrivers.sort((a, b) => a.distanceFromUser - b.distanceFromUser);

    const averageWaitTime = availableDrivers.length > 0 
      ? Math.round(availableDrivers.reduce((sum, driver) => sum + driver.estimatedArrival, 0) / availableDrivers.length)
      : 0;

    return {
      availableDrivers,
      totalDriversInArea: driverCount,
      averageWaitTime,
      nearestDriver: availableDrivers[0] || null,
    };
  }

  /**
   * Create a random driver with realistic data
   * @param userLocation - User's location for distance calculation
   * @param preferredRideType - Preferred ride type
   * @returns Generated driver
   */
  private createRandomDriver(userLocation: Coordinates, preferredRideType?: RideType): Driver | null {
    // Generate random location within search radius
    const driverLocation = this.generateRandomLocationNearby(userLocation, this.MAX_SEARCH_RADIUS);
    if (!driverLocation) return null;

    // Calculate distance and arrival time
    const distance = distanceService.calculateDistance(userLocation, driverLocation);
    const estimatedArrival = distanceService.estimatePickupTime(driverLocation, userLocation);

    // Determine ride type
    const rideTypes: RideType[] = ['SheRide', 'ShePool', 'SheXL'];
    const rideType = preferredRideType || rideTypes[Math.floor(Math.random() * rideTypes.length)];

    // Generate vehicle info
    const vehicleOptions = this.VEHICLES[rideType];
    const selectedVehicle = vehicleOptions[Math.floor(Math.random() * vehicleOptions.length)];
    const selectedColor = selectedVehicle.colors[Math.floor(Math.random() * selectedVehicle.colors.length)];

    // Generate driver profile
    const name = this.FEMALE_NAMES[Math.floor(Math.random() * this.FEMALE_NAMES.length)];
    const rating = Math.round((4.0 + Math.random() * 1.0) * 10) / 10; // 4.0 - 5.0 rating
    const totalTrips = Math.floor(Math.random() * 2000) + 100;
    const heading = Math.floor(Math.random() * 360);
    const isMoving = Math.random() > 0.3; // 70% chance driver is moving

    // Generate special features (1-3 features per driver)
    const featureCount = Math.floor(Math.random() * 3) + 1;
    const shuffledFeatures = [...this.SPECIAL_FEATURES].sort(() => 0.5 - Math.random());
    const specialFeatures = shuffledFeatures.slice(0, featureCount);

    return {
      id: `driver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      rating,
      totalTrips,
      profileImage: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=F9E6F7&color=E91E63&size=128`,
      vehicleInfo: {
        make: selectedVehicle.make,
        model: selectedVehicle.model,
        year: 2018 + Math.floor(Math.random() * 6), // 2018-2023
        color: selectedColor,
        licensePlate: this.generateLicensePlate(),
        type: rideType,
      },
      location: driverLocation,
      isOnline: true,
      distanceFromUser: distance.distanceKm,
      estimatedArrival,
      heading,
      isMoving,
      specialFeatures,
    };
  }

  /**
   * Generate a random location within specified radius
   * @param center - Center coordinates
   * @param radiusKm - Radius in kilometers
   * @returns Random coordinates within radius
   */
  private generateRandomLocationNearby(center: Coordinates, radiusKm: number): Coordinates | null {
    // Convert radius to degrees (approximate)
    const radiusDegrees = radiusKm / 111; // 1 degree ≈ 111 km

    // Generate random angle and distance
    const angle = Math.random() * 2 * Math.PI;
    const distance = Math.random() * radiusDegrees;

    // Calculate new coordinates
    const lat = center.lat + distance * Math.cos(angle);
    const lng = center.lng + distance * Math.sin(angle);

    // Basic bounds check for South Africa
    if (lat < -35 || lat > -22 || lng < 16 || lng > 33) {
      return null;
    }

    return { lat, lng };
  }

  /**
   * Generate South African license plate format
   * @returns License plate string
   */
  private generateLicensePlate(): string {
    const provinces = ['GP', 'WC', 'KZN', 'EC', 'FS', 'LP', 'MP', 'NC', 'NW'];
    const province = provinces[Math.floor(Math.random() * provinces.length)];
    const numbers = Math.floor(Math.random() * 900) + 100; // 100-999
    const letters = String.fromCharCode(65 + Math.floor(Math.random() * 26)) + 
                   String.fromCharCode(65 + Math.floor(Math.random() * 26));
    
    return `${province} ${numbers}-${letters}`;
  }

  /**
   * Update driver locations (simulate movement)
   * @param drivers - Array of drivers to update
   * @param userLocation - User location for distance recalculation
   * @returns Updated drivers array
   */
  updateDriverLocations(drivers: Driver[], userLocation: Coordinates): Driver[] {
    return drivers.map(driver => {
      if (!driver.isMoving) return driver;

      // Simulate movement towards user (simplified)
      const moveDistance = 0.001; // Small movement in degrees
      const angleToUser = Math.atan2(
        userLocation.lat - driver.location.lat,
        userLocation.lng - driver.location.lng
      );

      const newLocation: Coordinates = {
        lat: driver.location.lat + moveDistance * Math.sin(angleToUser),
        lng: driver.location.lng + moveDistance * Math.cos(angleToUser),
      };

      // Recalculate distance and arrival time
      const distance = distanceService.calculateDistance(userLocation, newLocation);
      const estimatedArrival = distanceService.estimatePickupTime(newLocation, userLocation);

      return {
        ...driver,
        location: newLocation,
        distanceFromUser: distance.distanceKm,
        estimatedArrival,
      };
    });
  }

  /**
   * Get driver by ID
   * @param driverId - Driver ID
   * @param drivers - Array of drivers to search
   * @returns Driver or null if not found
   */
  getDriverById(driverId: string, drivers: Driver[]): Driver | null {
    return drivers.find(driver => driver.id === driverId) || null;
  }

  /**
   * Filter drivers by ride type
   * @param drivers - Array of drivers
   * @param rideType - Desired ride type
   * @returns Filtered drivers array
   */
  filterDriversByRideType(drivers: Driver[], rideType: RideType): Driver[] {
    return drivers.filter(driver => driver.vehicleInfo.type === rideType);
  }

  /**
   * Get formatted driver distance text
   * @param distanceKm - Distance in kilometers
   * @returns Formatted distance string
   */
  formatDriverDistance(distanceKm: number): string {
    if (distanceKm < 1) {
      return `${Math.round(distanceKm * 1000)}m away`;
    }
    return `${distanceKm.toFixed(1)}km away`;
  }

  /**
   * Get formatted arrival time text
   * @param minutes - Arrival time in minutes
   * @returns Formatted time string
   */
  formatArrivalTime(minutes: number): string {
    if (minutes < 1) {
      return 'Arriving now';
    } else if (minutes === 1) {
      return '1 min away';
    } else {
      return `${minutes} mins away`;
    }
  }
}

export const driverService = new DriverService();
