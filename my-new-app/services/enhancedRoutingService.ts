/**
 * Enhanced Routing Service with OSRM and GraphHopper support
 * Provides multiple routing providers with fallback mechanisms
 */

import { RouteCoordinate, RouteResponse, RouteStep, RoutingOptions } from './routingService';

export interface RoutingProvider {
  name: string;
  getRoute(start: RouteCoordinate, end: RouteCoordinate, options?: RoutingOptions): Promise<RouteResponse | null>;
}

export interface GraphHopperOptions extends RoutingOptions {
  apiKey?: string;
  vehicle?: 'car' | 'foot' | 'bike';
  locale?: string;
  optimize?: boolean;
  instructions?: boolean;
  calc_points?: boolean;
  debug?: boolean;
}

class OSRMProvider implements RoutingProvider {
  name = 'OSRM';
  private readonly baseUrl = 'https://router.project-osrm.org';

  async getRoute(start: RouteCoordinate, end: RouteCoordinate, options: RoutingOptions = {}): Promise<RouteResponse | null> {
    try {
      const opts = {
        profile: 'driving',
        alternatives: false,
        steps: true,
        geometries: 'geojson',
        overview: 'full',
        ...options
      };

      const coordinateString = `${start.lng},${start.lat};${end.lng},${end.lat}`;
      const params = new URLSearchParams({
        alternatives: opts.alternatives ? 'true' : 'false',
        steps: opts.steps ? 'true' : 'false',
        geometries: opts.geometries || 'geojson',
        overview: opts.overview || 'full'
      });

      const url = `${this.baseUrl}/route/v1/${opts.profile}/${coordinateString}?${params}`;
      
      const response = await fetch(url, {
        headers: { 'User-Agent': 'SheMove-App/1.0' }
      });

      if (!response.ok) {
        throw new Error(`OSRM HTTP error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.code !== 'Ok' || !data.routes || data.routes.length === 0) {
        throw new Error('No route found');
      }

      const route = data.routes[0];
      return {
        coordinates: this.extractCoordinates(route.geometry),
        distance: route.distance,
        duration: route.duration,
        steps: this.extractSteps(route.legs),
        geometry: route.geometry
      };
    } catch (error) {
      console.warn(`OSRM routing failed:`, error);
      return null;
    }
  }

  private extractCoordinates(geometry: any): RouteCoordinate[] {
    if (!geometry || !geometry.coordinates) return [];
    return geometry.coordinates.map((coord: number[]) => ({
      lng: coord[0],
      lat: coord[1]
    }));
  }

  private extractSteps(legs: any[]): RouteStep[] {
    if (!legs || legs.length === 0) return [];
    
    const steps: RouteStep[] = [];
    legs.forEach(leg => {
      if (leg.steps) {
        leg.steps.forEach((step: any) => {
          steps.push({
            instruction: step.maneuver?.instruction || 'Continue',
            distance: step.distance || 0,
            duration: step.duration || 0,
            coordinates: this.extractCoordinates(step.geometry)
          });
        });
      }
    });
    return steps;
  }
}

class GraphHopperProvider implements RoutingProvider {
  name = 'GraphHopper';
  private readonly baseUrl = 'https://graphhopper.com/api/1/route';
  private readonly freeApiKey = 'your-free-api-key'; // Replace with actual free API key

  async getRoute(start: RouteCoordinate, end: RouteCoordinate, options: GraphHopperOptions = {}): Promise<RouteResponse | null> {
    try {
      const opts = {
        vehicle: 'car',
        locale: 'en',
        optimize: false,
        instructions: true,
        calc_points: true,
        debug: false,
        ...options
      };

      const params = new URLSearchParams({
        point: `${start.lat},${start.lng}`,
        point: `${end.lat},${end.lng}`,
        vehicle: opts.vehicle,
        locale: opts.locale,
        optimize: opts.optimize ? 'true' : 'false',
        instructions: opts.instructions ? 'true' : 'false',
        calc_points: opts.calc_points ? 'true' : 'false',
        debug: opts.debug ? 'true' : 'false',
        key: opts.apiKey || this.freeApiKey
      });

      const response = await fetch(`${this.baseUrl}?${params}`, {
        headers: { 'User-Agent': 'SheMove-App/1.0' }
      });

      if (!response.ok) {
        throw new Error(`GraphHopper HTTP error: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data.paths || data.paths.length === 0) {
        throw new Error('No route found');
      }

      const path = data.paths[0];
      return {
        coordinates: this.decodePolyline(path.points),
        distance: path.distance,
        duration: path.time / 1000, // Convert from milliseconds to seconds
        steps: this.extractInstructions(path.instructions),
        geometry: path.points
      };
    } catch (error) {
      console.warn(`GraphHopper routing failed:`, error);
      return null;
    }
  }

  private decodePolyline(encoded: string): RouteCoordinate[] {
    // Simple polyline decoder - in production, use a proper library like @mapbox/polyline
    const coordinates: RouteCoordinate[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b: number;
      let shift = 0;
      let result = 0;
      
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      
      const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;
      
      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      
      const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      coordinates.push({
        lat: lat / 1e5,
        lng: lng / 1e5
      });
    }

    return coordinates;
  }

  private extractInstructions(instructions: any[]): RouteStep[] {
    if (!instructions) return [];
    
    return instructions.map(instruction => ({
      instruction: instruction.text || 'Continue',
      distance: instruction.distance || 0,
      duration: (instruction.time || 0) / 1000,
      coordinates: [] // GraphHopper doesn't provide per-step coordinates in free tier
    }));
  }
}

class FallbackProvider implements RoutingProvider {
  name = 'Fallback';

  async getRoute(start: RouteCoordinate, end: RouteCoordinate): Promise<RouteResponse | null> {
    // Create a curved fallback route
    const coordinates: RouteCoordinate[] = [start];
    
    const latDiff = end.lat - start.lat;
    const lngDiff = end.lng - start.lng;
    
    // Add intermediate points with slight curve for more natural appearance
    for (let i = 1; i <= 3; i++) {
      const progress = i / 4;
      const curveFactor = Math.sin(progress * Math.PI) * 0.002;
      
      coordinates.push({
        lat: start.lat + latDiff * progress + curveFactor,
        lng: start.lng + lngDiff * progress + (curveFactor * 0.5)
      });
    }
    
    coordinates.push(end);

    // Estimate distance using Haversine formula
    const distance = this.calculateDistance(start, end) * 1000;
    const estimatedDuration = (distance / 1000) * 60; // 1 km per minute estimate

    return {
      coordinates,
      distance,
      duration: estimatedDuration,
      steps: [{
        instruction: 'Head towards destination',
        distance,
        duration: estimatedDuration,
        coordinates
      }],
      geometry: 'fallback'
    };
  }

  private calculateDistance(point1: RouteCoordinate, point2: RouteCoordinate): number {
    const R = 6371; // Earth's radius in kilometers
    const lat1Rad = this.toRadians(point1.lat);
    const lat2Rad = this.toRadians(point2.lat);
    const deltaLatRad = this.toRadians(point2.lat - point1.lat);
    const deltaLngRad = this.toRadians(point2.lng - point1.lng);

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

class EnhancedRoutingService {
  private providers: RoutingProvider[] = [
    new OSRMProvider(),
    new GraphHopperProvider(),
    new FallbackProvider()
  ];

  /**
   * Get route using multiple providers with fallback
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @param options - Routing options
   * @returns Promise with route information
   */
  async getRoute(
    start: RouteCoordinate,
    end: RouteCoordinate,
    options: RoutingOptions = {}
  ): Promise<RouteResponse | null> {
    console.log(`🗺️ Getting route from [${start.lat}, ${start.lng}] to [${end.lat}, ${end.lng}]`);

    for (const provider of this.providers) {
      try {
        console.log(`Trying ${provider.name} routing provider...`);
        const route = await provider.getRoute(start, end, options);
        
        if (route) {
          console.log(`✅ ${provider.name} routing successful: ${route.distance}m, ${Math.round(route.duration/60)}min`);
          return route;
        }
      } catch (error) {
        console.warn(`❌ ${provider.name} routing failed:`, error);
        continue;
      }
    }

    console.error('All routing providers failed');
    return null;
  }

  /**
   * Get route alternatives from multiple providers
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @param options - Routing options
   * @returns Promise with array of route alternatives
   */
  async getRouteAlternatives(
    start: RouteCoordinate,
    end: RouteCoordinate,
    options: RoutingOptions = {}
  ): Promise<RouteResponse[]> {
    const routes: RouteResponse[] = [];
    
    // Try to get routes from multiple providers
    for (const provider of this.providers.slice(0, -1)) { // Exclude fallback for alternatives
      try {
        const route = await provider.getRoute(start, end, { ...options, alternatives: true });
        if (route) {
          routes.push(route);
        }
      } catch (error) {
        console.warn(`Failed to get alternatives from ${provider.name}:`, error);
      }
    }

    return routes;
  }

  /**
   * Add custom routing provider
   * @param provider - Custom routing provider
   * @param priority - Insert position (0 = highest priority)
   */
  addProvider(provider: RoutingProvider, priority: number = 0): void {
    this.providers.splice(priority, 0, provider);
  }

  /**
   * Remove routing provider
   * @param providerName - Name of provider to remove
   */
  removeProvider(providerName: string): void {
    this.providers = this.providers.filter(p => p.name !== providerName);
  }

  /**
   * Get list of available providers
   * @returns Array of provider names
   */
  getProviders(): string[] {
    return this.providers.map(p => p.name);
  }
}

export const enhancedRoutingService = new EnhancedRoutingService();
