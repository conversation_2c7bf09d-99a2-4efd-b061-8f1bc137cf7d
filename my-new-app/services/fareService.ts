/**
 * Fare calculation service for SheMove ride-sharing app
 * Handles pricing for different ride types with South African pricing structure
 */

import { DistanceResult } from './distanceService';

export type RideType = 'SheRide' | 'ShePool' | 'SheXL';

export interface FareBreakdown {
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  surgeFare: number;
  totalFare: number;
  currency: string;
  surgeMultiplier: number;
  estimatedRange: {
    min: number;
    max: number;
  };
}

export interface RidePricing {
  rideType: RideType;
  displayName: string;
  description: string;
  baseFare: number;
  perKmRate: number;
  perMinuteRate: number;
  minimumFare: number;
  icon: string;
  capacity: number;
  features: string[];
}

class FareService {
  private readonly CURRENCY = 'ZAR'; // South African Rand
  private readonly CURRENCY_SYMBOL = 'R';

  // Pricing structure for different ride types (in ZAR)
  private readonly RIDE_PRICING: Record<RideType, RidePricing> = {
    SheRide: {
      rideType: 'SheRide',
      displayName: 'SheRide',
      description: 'Affordable rides for everyday trips',
      baseFare: 8.50,
      perKmRate: 12.50,
      perMinuteRate: 1.20,
      minimumFare: 15.00,
      icon: 'car',
      capacity: 4,
      features: ['Standard vehicle', 'Female-friendly', 'Safe & reliable'],
    },
    ShePool: {
      rideType: 'ShePool',
      displayName: 'ShePool',
      description: 'Share your ride, save money',
      baseFare: 6.00,
      perKmRate: 8.50,
      perMinuteRate: 0.80,
      minimumFare: 12.00,
      icon: 'people',
      capacity: 2, // Shared with other passengers
      features: ['Shared ride', 'Eco-friendly', 'Budget option', 'Women only'],
    },
    SheXL: {
      rideType: 'SheXL',
      displayName: 'SheXL',
      description: 'Extra space for groups and luggage',
      baseFare: 12.00,
      perKmRate: 18.00,
      perMinuteRate: 1.80,
      minimumFare: 25.00,
      icon: 'car-sport',
      capacity: 6,
      features: ['Spacious vehicle', 'Group trips', 'Extra luggage space', 'Premium comfort'],
    },
  };

  // Surge pricing factors based on demand and time
  private readonly SURGE_FACTORS = {
    LOW_DEMAND: 1.0,
    NORMAL_DEMAND: 1.0,
    HIGH_DEMAND: 1.3,
    PEAK_DEMAND: 1.8,
    EXTREME_DEMAND: 2.5,
  };

  /**
   * Calculate fare for a specific ride type and distance
   * @param rideType - Type of ride (SheRide, ShePool, SheXL)
   * @param distance - Distance information
   * @returns Detailed fare breakdown
   */
  calculateFare(rideType: RideType, distance: DistanceResult): FareBreakdown {
    const pricing = this.RIDE_PRICING[rideType];
    const surgeMultiplier = this.getCurrentSurgeMultiplier();

    // Base calculations
    const baseFare = pricing.baseFare;
    const distanceFare = distance.distanceKm * pricing.perKmRate;
    const timeFare = distance.durationMinutes * pricing.perMinuteRate;

    // Subtotal before surge
    const subtotal = baseFare + distanceFare + timeFare;
    
    // Apply minimum fare
    const fareBeforeSurge = Math.max(subtotal, pricing.minimumFare);
    
    // Apply surge pricing
    const surgeFare = fareBeforeSurge * (surgeMultiplier - 1);
    const totalFare = fareBeforeSurge + surgeFare;

    // Calculate estimated range (±10% for uncertainty)
    const variance = totalFare * 0.1;

    return {
      baseFare: Math.round(baseFare * 100) / 100,
      distanceFare: Math.round(distanceFare * 100) / 100,
      timeFare: Math.round(timeFare * 100) / 100,
      surgeFare: Math.round(surgeFare * 100) / 100,
      totalFare: Math.round(totalFare * 100) / 100,
      currency: this.CURRENCY,
      surgeMultiplier,
      estimatedRange: {
        min: Math.round((totalFare - variance) * 100) / 100,
        max: Math.round((totalFare + variance) * 100) / 100,
      },
    };
  }

  /**
   * Get all available ride types with their pricing
   * @param distance - Distance information for fare calculation
   * @returns Array of ride options with fares
   */
  getAllRideOptions(distance: DistanceResult) {
    return Object.values(this.RIDE_PRICING).map(pricing => {
      const fare = this.calculateFare(pricing.rideType, distance);
      return {
        ...pricing,
        fare,
        formattedPrice: this.formatPrice(fare.totalFare),
        estimatedTime: distance.durationText,
      };
    });
  }

  /**
   * Get pricing information for a specific ride type
   * @param rideType - Type of ride
   * @returns Pricing information
   */
  getRidePricing(rideType: RideType): RidePricing {
    return this.RIDE_PRICING[rideType];
  }

  /**
   * Format price with currency symbol
   * @param amount - Price amount
   * @returns Formatted price string
   */
  formatPrice(amount: number): string {
    return `${this.CURRENCY_SYMBOL}${amount.toFixed(2)}`;
  }

  /**
   * Format price range
   * @param min - Minimum price
   * @param max - Maximum price
   * @returns Formatted price range string
   */
  formatPriceRange(min: number, max: number): string {
    return `${this.formatPrice(min)} - ${this.formatPrice(max)}`;
  }

  /**
   * Get current surge multiplier based on demand and time
   * @returns Surge multiplier (1.0 = no surge, >1.0 = surge pricing)
   */
  private getCurrentSurgeMultiplier(): number {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay(); // 0 = Sunday, 6 = Saturday
    const isWeekend = day === 0 || day === 6;

    // Simulate demand patterns
    if (isWeekend) {
      // Weekend patterns - higher demand in evenings
      if (hour >= 19 && hour <= 23) {
        return this.SURGE_FACTORS.HIGH_DEMAND;
      } else if (hour >= 12 && hour <= 18) {
        return this.SURGE_FACTORS.NORMAL_DEMAND;
      } else {
        return this.SURGE_FACTORS.LOW_DEMAND;
      }
    } else {
      // Weekday patterns
      if ((hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19)) {
        // Rush hours
        return this.SURGE_FACTORS.HIGH_DEMAND;
      } else if (hour >= 20 && hour <= 22) {
        // Evening demand
        return this.SURGE_FACTORS.NORMAL_DEMAND;
      } else if (hour >= 23 || hour <= 6) {
        // Late night/early morning
        return this.SURGE_FACTORS.LOW_DEMAND;
      } else {
        return this.SURGE_FACTORS.NORMAL_DEMAND;
      }
    }
  }

  /**
   * Get surge status information
   * @returns Surge status with user-friendly message
   */
  getSurgeStatus() {
    const multiplier = this.getCurrentSurgeMultiplier();
    
    if (multiplier === 1.0) {
      return {
        isActive: false,
        multiplier,
        message: 'Standard pricing',
        color: '#4CAF50', // Green
      };
    } else if (multiplier <= 1.3) {
      return {
        isActive: true,
        multiplier,
        message: 'Slightly higher demand',
        color: '#FF9800', // Orange
      };
    } else if (multiplier <= 1.8) {
      return {
        isActive: true,
        multiplier,
        message: 'High demand area',
        color: '#F44336', // Red
      };
    } else {
      return {
        isActive: true,
        multiplier,
        message: 'Very high demand',
        color: '#9C27B0', // Purple
      };
    }
  }

  /**
   * Calculate savings for ShePool compared to SheRide
   * @param distance - Distance information
   * @returns Savings amount and percentage
   */
  calculatePoolSavings(distance: DistanceResult) {
    const sheRideFare = this.calculateFare('SheRide', distance);
    const shePoolFare = this.calculateFare('ShePool', distance);
    
    const savings = sheRideFare.totalFare - shePoolFare.totalFare;
    const savingsPercentage = Math.round((savings / sheRideFare.totalFare) * 100);

    return {
      savings: Math.round(savings * 100) / 100,
      savingsPercentage,
      formattedSavings: this.formatPrice(savings),
    };
  }

  /**
   * Estimate fare for different distances (for UI previews)
   * @param rideType - Type of ride
   * @returns Sample fares for different distances
   */
  getSampleFares(rideType: RideType) {
    const sampleDistances = [
      { km: 2, minutes: 8, label: 'Short trip' },
      { km: 5, minutes: 15, label: 'Medium trip' },
      { km: 10, minutes: 25, label: 'Long trip' },
    ];

    return sampleDistances.map(sample => {
      const distance: DistanceResult = {
        distanceKm: sample.km,
        distanceMiles: sample.km * 0.621371,
        durationMinutes: sample.minutes,
        durationText: `${sample.minutes} min`,
      };

      const fare = this.calculateFare(rideType, distance);
      
      return {
        ...sample,
        fare: fare.totalFare,
        formattedFare: this.formatPrice(fare.totalFare),
      };
    });
  }
}

export const fareService = new FareService();
