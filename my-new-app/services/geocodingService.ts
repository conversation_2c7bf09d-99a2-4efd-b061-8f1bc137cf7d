/**
 * Enhanced Geocoding Service with Google Maps API Integration
 * Uses free services first, Google Maps API as premium fallback
 * Supports Nominatim, LocationIQ, and Google Maps APIs
 */

import { GooglePlacesService, GoogleGeocodingService, GooglePlaceResult, GoogleGeocodeResult } from './googleMapsService';
import Constants from 'expo-constants';

export interface SearchResult {
  place_id: string;
  display_name: string;
  lat: string;
  lon: string;
  type: string;
  importance: number;
  address?: {
    house_number?: string;
    road?: string;
    neighbourhood?: string;
    suburb?: string;
    city?: string;
    state?: string;
    postcode?: string;
    country?: string;
  };
}

export interface GeocodeResponse {
  results: SearchResult[];
  error?: string;
  provider?: string;
}

class GeocodingService {
  private readonly baseUrl = 'https://nominatim.openstreetmap.org';
  private readonly locationIQBaseUrl = 'https://us1.locationiq.com/v1';
  private readonly userAgent = 'SheMove-RideSharing-App/1.0';
  private userCountry: string | null = null;
  private userLocation: { lat: number; lon: number } | null = null;
  private dailyUsage: Map<string, number> = new Map();

  // Google Maps API services (premium fallback)
  private googlePlaces: GooglePlacesService | null = null;
  private googleGeocoding: GoogleGeocodingService | null = null;

  // Provider configurations
  private readonly providers = {
    nominatim: {
      name: 'nominatim',
      dailyLimit: 8640, // 1 req/sec * 86400 sec/day
      cost: 0,
      priority: 1
    },
    locationiq: {
      name: 'locationiq',
      dailyLimit: 10000,
      cost: 0,
      priority: 2
    },
    google_places: {
      name: 'google_places',
      dailyLimit: 40000, // $200 free tier / $0.005 per request
      cost: 0.017, // $0.017 per request
      priority: 3
    },
    google_geocoding: {
      name: 'google_geocoding',
      dailyLimit: 40000, // $200 free tier / $0.005 per request
      cost: 0.005, // $0.005 per request
      priority: 4
    }
  };

  constructor() {
    // Initialize Google Maps services if API key is available
    // Check multiple sources for the API key
    const googleApiKey =
      process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY ||
      process.env.GOOGLE_MAPS_API_KEY ||
      Constants.expoConfig?.extra?.googleMapsApiKey;

    if (googleApiKey && googleApiKey !== 'your_google_maps_api_key_here' && googleApiKey !== 'YOUR_GOOGLE_MAPS_API_KEY_HERE') {
      this.googlePlaces = new GooglePlacesService(googleApiKey);
      this.googleGeocoding = new GoogleGeocodingService(googleApiKey);
      console.log('✅ Google Maps API services initialized');
    } else {
      console.log('ℹ️ Google Maps API key not configured - using free services only');
    }
  }

  /**
   * Set user's current location for location-based search prioritization
   * @param lat - User's latitude
   * @param lon - User's longitude
   */
  async setUserLocation(lat: number, lon: number): Promise<void> {
    this.userLocation = { lat, lon };

    // Reverse geocode to determine user's country
    try {
      const locationInfo = await this.reverseGeocode(lat, lon);
      if (locationInfo?.address?.country) {
        this.userCountry = locationInfo.address.country.toLowerCase();
      }
    } catch (error) {
      console.warn('Could not determine user country:', error);
    }
  }

  /**
   * Manually set user country (useful when location permission is denied)
   * @param countryCode - ISO country code (e.g., 'za' for South Africa)
   */
  setUserCountry(countryCode: string): void {
    this.userCountry = countryCode.toLowerCase();
  }

  /**
   * Get the appropriate country code for search prioritization
   * @returns Country code or null
   */
  private getSearchCountryCode(): string | null {
    if (!this.userCountry) {
      // Enhanced fallback detection for South Africa
      try {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (timezone.includes('Africa/Johannesburg') ||
            timezone.includes('Africa/Cape_Town') ||
            timezone.includes('Africa/Durban') ||
            timezone.includes('Africa/Bloemfontein') ||
            timezone.includes('Africa/Maseru')) {
          console.log('🇿🇦 Detected South African timezone, setting country to ZA');
          this.userCountry = 'za';
          return 'za';
        }
      } catch (error) {
        // Ignore timezone detection errors
      }

      // Additional fallback: assume South Africa for SheMove app
      console.log('🇿🇦 No location detected, defaulting to South Africa for SheMove app');
      this.userCountry = 'za';
      return 'za';
    }

    // Map country names to ISO country codes for Nominatim
    const countryCodeMap: { [key: string]: string } = {
      'south africa': 'za',
      'united states': 'us',
      'united kingdom': 'gb',
      'canada': 'ca',
      'australia': 'au',
      'germany': 'de',
      'france': 'fr',
      'italy': 'it',
      'spain': 'es',
      'netherlands': 'nl',
      'belgium': 'be',
      'switzerland': 'ch',
      'austria': 'at',
      'sweden': 'se',
      'norway': 'no',
      'denmark': 'dk',
      'finland': 'fi',
    };

    return countryCodeMap[this.userCountry] || null;
  }

  /**
   * Enhanced search method with Google Maps API fallback for premium results
   * This is the recommended method for components to use
   * @param query - Search query (e.g., "3 Aries Road" or "Starbucks Cape Town")
   * @param limit - Maximum number of results (default: 5)
   * @param countryCode - Optional country code to limit search (e.g., 'za')
   * @returns Promise with enhanced search results
   */
  async searchLocationsEnhanced(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    return this.searchWithGoogleFallback(query, limit, countryCode);
  }

  /**
   * Search with Google Maps API as premium fallback
   * Uses free services first, then Google for better results
   */
  async searchWithGoogleFallback(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    if (!query.trim()) {
      return { results: [] };
    }

    try {
      // Step 1: Try free services first (Nominatim + LocationIQ)
      const freeResults = await this.searchWithLocationIQFallback(query, limit, countryCode);

      // Step 2: Check if results are satisfactory
      const hasHouseNumber = this.detectHouseNumber(query);
      const isResultSatisfactory = this.isResultSatisfactory(freeResults.results, hasHouseNumber);

      // Step 3: If results are poor and Google API is available, use it as fallback
      if (!isResultSatisfactory && this.googlePlaces) {
        console.log('🔄 Using Google Places API for better results');

        try {
          const googleResults = await this.searchWithGooglePlaces(query, limit, countryCode);

          if (googleResults.results.length > 0) {
            return {
              ...googleResults,
              provider: 'google_places_fallback'
            };
          }
        } catch (googleError) {
          console.warn('Google Places API fallback failed:', googleError);
          // Continue with free results if Google fails
        }
      }

      // Step 4: Return free results if Google not needed or failed
      return freeResults;

    } catch (error) {
      console.error('Enhanced search with Google fallback failed:', error);
      return {
        results: [],
        error: error instanceof Error ? error.message : 'Search failed',
        provider: 'none'
      };
    }
  }

  /**
   * Search for locations based on query string with optimized SA prioritization (Nominatim only)
   * @param query - Search query (e.g., "Starbucks Cape Town")
   * @param limit - Maximum number of results (default: 5)
   * @param countryCode - Optional country code to limit search (e.g., 'za')
   * @returns Promise with search results
   */
  async searchLocations(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    if (!query.trim()) {
      return { results: [] };
    }

    try {
      // Determine the country code to use for prioritization
      const searchCountryCode = countryCode || this.getSearchCountryCode();

      let allResults: SearchResult[] = [];

      // Optimized search strategy with parallel processing
      if (searchCountryCode === 'za') {
        console.log(`🇿🇦 Optimized SA search for: "${query}"`);
        allResults = await this.performOptimizedSouthAfricanSearch(query, limit);
      } else if (searchCountryCode) {
        console.log(`🌍 Optimized local search for ${searchCountryCode.toUpperCase()}: "${query}"`);
        allResults = await this.performOptimizedLocalSearch(query, limit, searchCountryCode);
      } else {
        // No location context, search globally
        allResults = await this.performOptimizedGlobalSearch(query, limit);
      }

      // Enhanced sorting with SA bias and clean address filtering
      const sortedResults = this.sortResultsByOptimizedRelevance(allResults, searchCountryCode);

      return { results: sortedResults.slice(0, limit) };
    } catch (error) {
      console.error('Geocoding search error:', error);
      return {
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Optimized South African search with parallel processing and strict prioritization
   * @param query - Search query
   * @param limit - Number of results
   * @returns Array of search results
   */
  private async performOptimizedSouthAfricanSearch(query: string, limit: number): Promise<SearchResult[]> {
    // Parallel search strategy for SA users
    const searchPromises = [
      // Primary: Strict SA search with enhanced query
      this.performOptimizedSearch(this.buildSmartSAQuery(query), limit * 2, 'za', true),
      // Secondary: Fallback search only if needed
      query.length > 3 ? this.performOptimizedSearch(query, Math.ceil(limit * 0.5), 'za', false) : Promise.resolve([])
    ];

    try {
      const [primaryResults, fallbackResults] = await Promise.allSettled(searchPromises);

      let allResults: SearchResult[] = [];

      // Add primary results
      if (primaryResults.status === 'fulfilled') {
        allResults.push(...primaryResults.value);
      }

      // Add fallback results only if we need more
      if (allResults.length < limit && fallbackResults.status === 'fulfilled') {
        const existingPlaceIds = new Set(allResults.map(r => r.place_id));
        const uniqueFallback = fallbackResults.value.filter(r => !existingPlaceIds.has(r.place_id));
        allResults.push(...uniqueFallback);
      }

      return allResults;
    } catch (error) {
      console.warn('Optimized SA search failed, falling back:', error);
      return this.performOptimizedSearch(query, limit, 'za', false);
    }
  }

  /**
   * Optimized local search for non-SA countries
   * @param query - Search query
   * @param limit - Number of results
   * @param countryCode - Country code
   * @returns Array of search results
   */
  private async performOptimizedLocalSearch(query: string, limit: number, countryCode: string): Promise<SearchResult[]> {
    const [localResults, globalResults] = await Promise.allSettled([
      this.performOptimizedSearch(query, Math.ceil(limit * 0.8), countryCode, true),
      this.performOptimizedSearch(query, Math.ceil(limit * 0.3))
    ]);

    let allResults: SearchResult[] = [];

    if (localResults.status === 'fulfilled') {
      allResults.push(...localResults.value);
    }

    // Only add global results if we have very few local results
    if (allResults.length < 2 && globalResults.status === 'fulfilled') {
      const existingPlaceIds = new Set(allResults.map(r => r.place_id));
      const uniqueGlobal = globalResults.value.filter(r => !existingPlaceIds.has(r.place_id));
      allResults.push(...uniqueGlobal);
    }

    return allResults;
  }

  /**
   * Optimized global search
   * @param query - Search query
   * @param limit - Number of results
   * @returns Array of search results
   */
  private async performOptimizedGlobalSearch(query: string, limit: number): Promise<SearchResult[]> {
    return this.performOptimizedSearch(query, limit);
  }

  /**
   * Build smart query for South African searches
   * @param query - Original query
   * @returns Enhanced query
   */
  private buildSmartSAQuery(query: string): string {
    const lowerQuery = query.toLowerCase();

    // Don't modify if already has SA context
    if (lowerQuery.includes('south africa') || lowerQuery.includes('gauteng') ||
        lowerQuery.includes('cape town') || lowerQuery.includes('johannesburg')) {
      return query;
    }

    // Add SA context for better results
    return `${query}, south africa`;
  }

  /**
   * Perform the actual search request to Nominatim with enhanced parameters
   * @param query - Search query
   * @param limit - Number of results
   * @param countryCode - Optional country code
   * @param strictBounds - Whether to use strict geographical bounds
   * @returns Array of search results
   */
  private async performOptimizedSearch(
    query: string,
    limit: number,
    countryCode?: string,
    strictBounds: boolean = false
  ): Promise<SearchResult[]> {
    const params = new URLSearchParams({
      q: query.trim(),
      format: 'json',
      addressdetails: '1',
      limit: (limit * 1.5).toString(), // Get more results for better filtering
      'accept-language': 'en',
      'dedupe': '1', // Always remove duplicates
    });

    if (countryCode) {
      params.append('countrycodes', countryCode);
    }

    // Optimized viewbox and bounds for better local results
    if (this.userLocation) {
      const lat = this.userLocation.lat;
      const lon = this.userLocation.lon;

      // Adaptive radius based on country and strict bounds setting
      const delta = strictBounds ? 0.3 : 0.5; // Tighter bounds for strict searches

      params.append('viewbox', `${lon - delta},${lat + delta},${lon + delta},${lat - delta}`);

      // Use strict bounds for SA searches to prioritize local results
      if (countryCode === 'za' && strictBounds) {
        params.append('bounded', '1'); // Strict bounds for SA
      } else {
        params.append('bounded', '0'); // Prefer but don't restrict
      }
    }

    const response = await fetch(`${this.baseUrl}/search?${params}`, {
      headers: {
        'User-Agent': this.userAgent,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Clean and filter results
    const results = data.map((item: any) => ({
      place_id: item.place_id?.toString() || '',
      display_name: this.cleanSouthAfricanAddress(item.display_name || ''),
      lat: item.lat || '',
      lon: item.lon || '',
      type: item.type || '',
      importance: item.importance || 0,
      address: item.address,
      extratags: item.extratags,
      namedetails: item.namedetails,
    }));

    return results.slice(0, limit); // Trim to requested limit
  }

  /**
   * Clean South African addresses by removing administrative divisions
   * @param displayName - Original display name
   * @returns Cleaned display name
   */
  private cleanSouthAfricanAddress(displayName: string): string {
    return displayName
      .split(',')
      .map(part => part.trim())
      .filter(part => !this.isAdministrativeDivision(part))
      .slice(0, 4) // Limit to 4 most relevant parts
      .join(', ');
  }

  /**
   * Check if a part is an administrative division that should be filtered
   * @param text - Text to check
   * @returns True if it's an administrative division
   */
  private isAdministrativeDivision(text: string): boolean {
    const lowerText = text.toLowerCase();
    const adminTerms = [
      'ward', 'municipality', 'metropolitan municipality',
      'local municipality', 'district municipality',
      'city of', 'greater', 'metro'
    ];

    return adminTerms.some(term => lowerText.includes(term));
  }

  /**
   * Enhanced search with multiple strategies for better address finding
   * @param query - Search query
   * @param limit - Number of results
   * @param countryCode - Optional country code
   * @returns Array of search results
   */
  private async performEnhancedSearch(
    query: string,
    limit: number,
    countryCode?: string
  ): Promise<SearchResult[]> {
    const allResults: SearchResult[] = [];

    // Strategy 1: Direct search with original query
    try {
      const directResults = await this.performOptimizedSearch(query, Math.ceil(limit * 0.6), countryCode);
      allResults.push(...directResults);
    } catch (error) {
      console.warn('Direct search failed:', error);
    }

    // Strategy 2: Enhanced query with city context for South Africa
    if (countryCode === 'za' && allResults.length < limit) {
      try {
        const enhancedQuery = this.enhanceQueryForSouthAfrica(query);
        if (enhancedQuery !== query) {
          const enhancedResults = await this.performOptimizedSearch(enhancedQuery, Math.ceil(limit * 0.4), countryCode);

          // Filter out duplicates
          const existingPlaceIds = new Set(allResults.map(r => r.place_id));
          const uniqueEnhanced = enhancedResults.filter(r => !existingPlaceIds.has(r.place_id));
          allResults.push(...uniqueEnhanced);
        }
      } catch (error) {
        console.warn('Enhanced search failed:', error);
      }
    }

    // Strategy 3: Structured search for specific address formats
    if (allResults.length < limit && this.isSpecificAddress(query)) {
      try {
        const structuredResults = await this.performStructuredSearch(query, countryCode);

        // Filter out duplicates
        const existingPlaceIds = new Set(allResults.map(r => r.place_id));
        const uniqueStructured = structuredResults.filter(r => !existingPlaceIds.has(r.place_id));
        allResults.push(...uniqueStructured);
      } catch (error) {
        console.warn('Structured search failed:', error);
      }
    }

    // Strategy 4: Fallback search with alternative formats for SA addresses
    if (allResults.length < limit && countryCode === 'za') {
      try {
        const fallbackQueries = this.generateFallbackQueries(query);

        for (const fallbackQuery of fallbackQueries) {
          if (allResults.length >= limit) break;

          const fallbackResults = await this.performOptimizedSearch(fallbackQuery, limit - allResults.length, countryCode);

          // Filter out duplicates
          const existingPlaceIds = new Set(allResults.map(r => r.place_id));
          const uniqueFallback = fallbackResults.filter(r => !existingPlaceIds.has(r.place_id));
          allResults.push(...uniqueFallback);
        }
      } catch (error) {
        console.warn('Fallback search failed:', error);
      }
    }

    return allResults.slice(0, limit);
  }

  /**
   * Generate fallback queries for South African addresses
   * @param query - Original query
   * @returns Array of fallback queries
   */
  private generateFallbackQueries(query: string): string[] {
    const fallbacks: string[] = [];
    const lowerQuery = query.toLowerCase();

    // For addresses like "3 aries road, johannesburg"
    if (this.isStreetAddress(query)) {
      // Try with different city contexts
      const cities = ['johannesburg', 'sandton', 'rosebank', 'midrand', 'randburg', 'roodepoort'];

      cities.forEach(city => {
        if (!lowerQuery.includes(city)) {
          fallbacks.push(`${query}, ${city}, gauteng`);
          fallbacks.push(`${query}, ${city}`);
        }
      });

      // Try with province context
      if (!lowerQuery.includes('gauteng')) {
        fallbacks.push(`${query}, gauteng, south africa`);
      }

      // Try without specific city but with area context
      fallbacks.push(`${query}, greater johannesburg`);
    }

    return fallbacks;
  }

  /**
   * Enhance query with South African city context
   * @param query - Original query
   * @returns Enhanced query
   */
  private enhanceQueryForSouthAfrica(query: string): string {
    const lowerQuery = query.toLowerCase();

    // If query doesn't contain major SA cities, try adding context
    const majorCities = ['johannesburg', 'cape town', 'durban', 'pretoria', 'port elizabeth', 'bloemfontein', 'sandton', 'rosebank', 'midrand'];
    const hasCity = majorCities.some(city => lowerQuery.includes(city));

    if (!hasCity) {
      // Try to detect if it's a street address and add Johannesburg context
      if (this.isStreetAddress(query)) {
        return `${query}, johannesburg, south africa`;
      }

      // For specific road names, try different city contexts
      if (lowerQuery.includes('road') || lowerQuery.includes('street') || lowerQuery.includes('avenue')) {
        // Try the most likely city first (Johannesburg for most searches)
        return `${query}, johannesburg, gauteng, south africa`;
      }
    }

    return query;
  }

  /**
   * Check if query looks like a specific address
   * @param query - Search query
   * @returns True if it looks like a specific address
   */
  private isSpecificAddress(query: string): boolean {
    const addressPatterns = [
      /^\d+\s+\w+\s+(road|street|avenue|drive|lane|way|close|crescent)/i,
      /^\d+\s+\w+\s+\w+\s+(road|street|avenue|drive|lane|way|close|crescent)/i,
    ];

    return addressPatterns.some(pattern => pattern.test(query.trim()));
  }

  /**
   * Check if query looks like a street address
   * @param query - Search query
   * @returns True if it looks like a street address
   */
  private isStreetAddress(query: string): boolean {
    return /^\d+\s+\w+/i.test(query.trim());
  }

  /**
   * Perform structured search for specific addresses
   * @param query - Search query
   * @param countryCode - Country code
   * @returns Array of search results
   */
  private async performStructuredSearch(query: string, countryCode?: string): Promise<SearchResult[]> {
    // Parse the address components
    const parts = query.trim().split(/[,\s]+/);

    if (parts.length >= 3) {
      const structuredParams = new URLSearchParams({
        format: 'json',
        addressdetails: '1',
        limit: '5',
        'accept-language': 'en',
        // Use structured search parameters
        'street': parts.slice(0, -1).join(' '), // Everything except last part as street
        'city': parts[parts.length - 1], // Last part as city
      });

      if (countryCode) {
        structuredParams.append('countrycodes', countryCode);
      }

      try {
        const response = await fetch(`${this.baseUrl}/search?${structuredParams}`, {
          headers: {
            'User-Agent': this.userAgent,
          },
        });

        if (response.ok) {
          const data = await response.json();
          return data.map((item: any) => ({
            place_id: item.place_id?.toString() || '',
            display_name: item.display_name || '',
            lat: item.lat || '',
            lon: item.lon || '',
            type: item.type || '',
            importance: item.importance || 0,
            address: item.address,
          }));
        }
      } catch (error) {
        console.warn('Structured search request failed:', error);
      }
    }

    return [];
  }

  /**
   * Sort search results by optimized relevance with SA bias
   * @param results - Array of search results
   * @param countryCode - User's country code for bias calculation
   * @returns Sorted array of search results
   */
  private sortResultsByOptimizedRelevance(results: SearchResult[], countryCode?: string): SearchResult[] {
    return results.sort((a, b) => {
      let aScore = 0;
      let bScore = 0;

      // Factor 1: Country bias (highest priority for SA users)
      if (countryCode === 'za') {
        aScore += this.calculateSouthAfricanBias(a) * 3; // Triple weight
        bScore += this.calculateSouthAfricanBias(b) * 3;
      }

      // Factor 2: Distance from user (high priority)
      if (this.userLocation) {
        aScore += this.calculateDistanceScore(a) * 2;
        bScore += this.calculateDistanceScore(b) * 2;
      }

      // Factor 3: Address specificity (medium priority)
      aScore += this.getAddressSpecificityScore(a);
      bScore += this.getAddressSpecificityScore(b);

      // Factor 4: Location type priority
      aScore += this.getLocationTypeScore(a);
      bScore += this.getLocationTypeScore(b);

      // Factor 5: Importance score (reduced weight)
      aScore += (a.importance || 0) * 30; // Reduced from 100
      bScore += (b.importance || 0) * 30;

      return bScore - aScore;
    });
  }

  /**
   * Calculate South African location bias score
   * @param result - Search result
   * @returns Bias score
   */
  private calculateSouthAfricanBias(result: SearchResult): number {
    const displayName = result.display_name.toLowerCase();

    // Strong bias for SA locations
    if (displayName.includes('south africa')) return 100;

    // Major SA cities get high priority
    const majorCities = ['johannesburg', 'cape town', 'durban', 'pretoria', 'sandton', 'rosebank'];
    for (const city of majorCities) {
      if (displayName.includes(city)) return 80;
    }

    // SA provinces get medium priority
    const provinces = ['gauteng', 'western cape', 'kwazulu-natal'];
    for (const province of provinces) {
      if (displayName.includes(province)) return 60;
    }

    return 0;
  }

  /**
   * Calculate distance-based score
   * @param result - Search result
   * @returns Distance score
   */
  private calculateDistanceScore(result: SearchResult): number {
    if (!this.userLocation) return 0;

    const lat = parseFloat(result.lat);
    const lon = parseFloat(result.lon);

    if (isNaN(lat) || isNaN(lon)) return 0;

    const distance = Math.sqrt(
      Math.pow(lat - this.userLocation.lat, 2) +
      Math.pow(lon - this.userLocation.lon, 2)
    );

    // Closer locations get higher scores (inverse distance)
    return Math.max(0, 100 - distance * 50);
  }

  /**
   * Sort search results by relevance considering user location and address specificity
   * @param results - Array of search results
   * @returns Sorted array of search results
   */
  private sortResultsByRelevance(results: SearchResult[]): SearchResult[] {
    return results.sort((a, b) => {
      let aScore = 0;
      let bScore = 0;

      // Factor 1: Importance score from Nominatim
      aScore += (a.importance || 0) * 100;
      bScore += (b.importance || 0) * 100;

      // Factor 2: Address specificity (more specific addresses get higher scores)
      aScore += this.getAddressSpecificityScore(a);
      bScore += this.getAddressSpecificityScore(b);

      // Factor 3: Location type priority (addresses > POIs > areas)
      aScore += this.getLocationTypeScore(a);
      bScore += this.getLocationTypeScore(b);

      // Factor 4: Distance from user (if available)
      if (this.userLocation) {
        const aLat = parseFloat(a.lat);
        const aLon = parseFloat(a.lon);
        const bLat = parseFloat(b.lat);
        const bLon = parseFloat(b.lon);

        const aDistance = Math.sqrt(Math.pow(aLat - this.userLocation.lat, 2) + Math.pow(aLon - this.userLocation.lon, 2));
        const bDistance = Math.sqrt(Math.pow(bLat - this.userLocation.lat, 2) + Math.pow(bLon - this.userLocation.lon, 2));

        // Closer locations get higher scores (inverse distance)
        aScore += Math.max(0, 50 - aDistance * 10);
        bScore += Math.max(0, 50 - bDistance * 10);
      }

      // Factor 5: South African city priority
      if (this.userCountry === 'south africa') {
        aScore += this.getSouthAfricanCityScore(a);
        bScore += this.getSouthAfricanCityScore(b);
      }

      return bScore - aScore;
    });
  }

  /**
   * Get address specificity score
   * @param result - Search result
   * @returns Specificity score
   */
  private getAddressSpecificityScore(result: SearchResult): number {
    const address = result.address;
    if (!address) return 0;

    let score = 0;

    // House number adds specificity
    if (address.house_number) score += 30;

    // Street name adds specificity
    if (address.road) score += 20;

    // Suburb/neighbourhood adds context
    if (address.suburb || address.neighbourhood) score += 15;

    // City adds context
    if (address.city) score += 10;

    // Postcode adds precision
    if (address.postcode) score += 5;

    return score;
  }

  /**
   * Get location type score based on OSM type
   * @param result - Search result
   * @returns Type score
   */
  private getLocationTypeScore(result: SearchResult): number {
    const type = result.type?.toLowerCase() || '';

    // Prioritize specific addresses
    if (type === 'house' || type === 'building') return 40;
    if (type === 'residential') return 35;

    // Then commercial/amenity locations
    if (type === 'commercial' || type === 'retail') return 30;
    if (type === 'amenity') return 25;

    // Then roads and streets
    if (type === 'highway' || type === 'primary' || type === 'secondary') return 20;

    // Then areas and regions
    if (type === 'suburb' || type === 'neighbourhood') return 15;
    if (type === 'city' || type === 'town') return 10;

    return 0;
  }

  /**
   * Get South African city priority score
   * @param result - Search result
   * @returns City priority score
   */
  private getSouthAfricanCityScore(result: SearchResult): number {
    const displayName = result.display_name.toLowerCase();
    const majorCities = [
      'johannesburg', 'cape town', 'durban', 'pretoria', 'port elizabeth',
      'bloemfontein', 'east london', 'pietermaritzburg', 'kimberley', 'polokwane'
    ];

    for (const city of majorCities) {
      if (displayName.includes(city)) {
        return 25; // Boost for major SA cities
      }
    }

    if (displayName.includes('south africa')) {
      return 15; // Boost for SA locations
    }

    return 0;
  }

  /**
   * Reverse geocoding - get address from coordinates
   * @param lat - Latitude
   * @param lon - Longitude
   * @returns Promise with location details
   */
  async reverseGeocode(lat: number, lon: number): Promise<SearchResult | null> {
    try {
      const params = new URLSearchParams({
        lat: lat.toString(),
        lon: lon.toString(),
        format: 'json',
        addressdetails: '1',
        'accept-language': 'en',
      });

      const response = await fetch(`${this.baseUrl}/reverse?${params}`, {
        headers: {
          'User-Agent': this.userAgent,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (!data || data.error) {
        return null;
      }

      return {
        place_id: data.place_id?.toString() || '',
        display_name: data.display_name || '',
        lat: data.lat || lat.toString(),
        lon: data.lon || lon.toString(),
        type: data.type || '',
        importance: data.importance || 0,
        address: data.address,
      };
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }

  /**
   * Get formatted address from search result
   * @param result - Search result object
   * @returns Formatted address string
   */
  formatAddress(result: SearchResult): string {
    if (!result.address) {
      return result.display_name;
    }

    const { address } = result;
    const parts: string[] = [];

    // Add house number and road
    if (address.house_number && address.road) {
      parts.push(`${address.house_number} ${address.road}`);
    } else if (address.road) {
      parts.push(address.road);
    }

    // Add neighborhood or suburb
    if (address.neighbourhood) {
      parts.push(address.neighbourhood);
    } else if (address.suburb) {
      parts.push(address.suburb);
    }

    // Add city
    if (address.city) {
      parts.push(address.city);
    }

    // Add state and postcode
    if (address.state) {
      let statePart = address.state;
      if (address.postcode) {
        statePart += ` ${address.postcode}`;
      }
      parts.push(statePart);
    }

    return parts.length > 0 ? parts.join(', ') : result.display_name;
  }

  /**
   * Get short address for display in search results (Google-style formatting)
   * @param result - Search result object
   * @returns Short formatted address following industry best practices
   */
  getShortAddress(result: SearchResult): string {
    const address = result.address;
    const displayParts = result.display_name.split(',').map(part => part.trim());

    // Use structured address data for smart formatting
    if (address) {
      return this.formatStructuredShortAddress(address, displayParts);
    }

    // Fallback to display_name parsing
    return this.formatDisplayNameShortAddress(displayParts);
  }

  /**
   * Format short address from structured address data
   */
  private formatStructuredShortAddress(address: any, displayParts: string[]): string {
    // Strategy 1: Specific Address (House + Street)
    if (address.house_number && address.road) {
      return `${address.house_number} ${address.road}`;
    }

    // Strategy 2: Street/Road Only
    if (address.road) {
      return address.road;
    }

    // Strategy 3: Business/POI Name (from display_name first part)
    if (displayParts.length > 0 && !this.isGenericLocationName(displayParts[0])) {
      return displayParts[0];
    }

    // Strategy 4: Neighborhood/Suburb
    if (address.neighbourhood) {
      return address.neighbourhood;
    }
    if (address.suburb) {
      return address.suburb;
    }

    // Strategy 5: City/Town
    if (address.city) {
      return address.city;
    }
    if (address.town) {
      return address.town;
    }

    // Fallback to first part of display name
    return displayParts[0] || 'Unknown location';
  }

  /**
   * Format short address from display name parts
   */
  private formatDisplayNameShortAddress(displayParts: string[]): string {
    if (displayParts.length === 0) {
      return 'Unknown location';
    }

    // Return the most specific part (first part)
    return displayParts[0];
  }

  /**
   * Check if a location name is too generic to be useful as primary text
   */
  private isGenericLocationName(text: string): boolean {
    const genericTerms = [
      'city', 'town', 'village', 'municipality', 'ward', 'district',
      'province', 'region', 'area', 'zone', 'sector'
    ];

    const lowerText = text.toLowerCase();
    return genericTerms.some(term => lowerText.includes(term));
  }

  // ============================================================================
  // LOCATIONIQ INTEGRATION METHODS
  // ============================================================================

  /**
   * Enhanced search with LocationIQ fallback for better house number coverage
   * @param query - Search query
   * @param limit - Maximum number of results
   * @param countryCode - Optional country code
   * @returns Promise with enhanced search results
   */
  async searchWithLocationIQFallback(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    if (!query.trim()) {
      return { results: [] };
    }

    const hasHouseNumber = this.detectHouseNumber(query);

    try {
      // Try Nominatim first (free)
      const nominatimResults = await this.searchLocations(query, limit, countryCode);

      // Check if results are satisfactory
      if (this.isResultSatisfactory(nominatimResults.results, hasHouseNumber)) {
        return {
          ...nominatimResults,
          provider: 'nominatim'
        };
      }

      // Fallback to LocationIQ for house number queries or poor results
      if (this.canUseLocationIQ()) {
        console.log('🔄 Falling back to LocationIQ for better results');
        const locationIQResults = await this.searchWithLocationIQ(query, limit, countryCode);

        if (locationIQResults.results.length > 0) {
          this.trackUsage('locationiq');
          return {
            ...locationIQResults,
            provider: 'locationiq'
          };
        }
      }

      // Return original results if fallback fails
      return {
        ...nominatimResults,
        provider: 'nominatim'
      };

    } catch (error) {
      console.error('Enhanced search failed:', error);
      return {
        results: [],
        error: error instanceof Error ? error.message : 'Search failed',
        provider: 'none'
      };
    }
  }

  /**
   * Search using LocationIQ API
   * @param query - Search query
   * @param limit - Maximum number of results
   * @param countryCode - Optional country code
   * @returns Promise with LocationIQ search results
   */
  private async searchWithLocationIQ(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    // Get API key from Expo Constants with fallback to process.env
    const apiKey = Constants.expoConfig?.extra?.LOCATIONIQ_API_KEY || process.env.LOCATIONIQ_API_KEY;

    if (!apiKey || apiKey === 'your_locationiq_api_key_here') {
      console.warn('LocationIQ API key not configured');
      return { results: [] };
    }

    try {
      const params = new URLSearchParams({
        key: apiKey,
        q: query.trim(),
        format: 'json',
        addressdetails: '1',
        limit: limit.toString(),
        'accept-language': 'en',
        dedupe: '1'
      });

      // Add country code if specified
      if (countryCode) {
        params.append('countrycodes', countryCode);
      }

      const url = `${this.locationIQBaseUrl}/search.php?${params}`;

      const response = await fetch(url, {
        headers: {
          'User-Agent': this.userAgent,
        },
      });

      if (!response.ok) {
        throw new Error(`LocationIQ API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Handle LocationIQ error responses
      if (data.error) {
        throw new Error(`LocationIQ error: ${data.error}`);
      }

      // Convert LocationIQ results to our format
      const results = this.normalizeLocationIQResults(data);

      return { results };

    } catch (error) {
      console.error('LocationIQ search failed:', error);
      return {
        results: [],
        error: error instanceof Error ? error.message : 'LocationIQ search failed'
      };
    }
  }

  /**
   * Normalize LocationIQ results to match our SearchResult interface
   * @param locationIQResults - Raw LocationIQ API results
   * @returns Normalized SearchResult array
   */
  private normalizeLocationIQResults(locationIQResults: any[]): SearchResult[] {
    return locationIQResults.map((item: any) => ({
      place_id: item.place_id?.toString() || item.osm_id?.toString() || Math.random().toString(),
      display_name: item.display_name || '',
      lat: item.lat || '0',
      lon: item.lon || '0',
      type: item.type || 'unknown',
      importance: parseFloat(item.importance) || 0,
      address: item.address ? {
        house_number: item.address.house_number,
        road: item.address.road,
        neighbourhood: item.address.neighbourhood,
        suburb: item.address.suburb,
        city: item.address.city || item.address.town,
        state: item.address.state,
        postcode: item.address.postcode,
        country: item.address.country
      } : undefined
    }));
  }

  /**
   * Detect if query contains a house number
   * @param query - Search query
   * @returns True if house number detected
   */
  private detectHouseNumber(query: string): boolean {
    // Match patterns like "123 Street Name" or "Unit 5A Street Name"
    return /^(\d+[A-Za-z]?|\d+\/\d+|Unit\s+\d+[A-Za-z]?)\s+/.test(query.trim());
  }

  /**
   * Check if search results are satisfactory
   * @param results - Search results
   * @param hasHouseNumber - Whether query had house number
   * @returns True if results are satisfactory
   */
  private isResultSatisfactory(results: SearchResult[], hasHouseNumber: boolean): boolean {
    if (!results.length) return false;

    // If user searched for house number, prefer results with house numbers
    if (hasHouseNumber) {
      return results.some(r => r.address?.house_number);
    }

    // For non-house-number queries, any results are fine
    return true;
  }

  /**
   * Search using Google Places API
   * @param query - Search query
   * @param limit - Maximum number of results
   * @param countryCode - Optional country code
   * @returns Promise with Google Places search results
   */
  private async searchWithGooglePlaces(
    query: string,
    limit: number = 5,
    countryCode?: string
  ): Promise<GeocodeResponse> {
    if (!this.googlePlaces) {
      throw new Error('Google Places API not configured');
    }

    try {
      // Get user location for better results
      const location = this.userLocation;

      // Search for places
      const places = await this.googlePlaces.searchPlaces(query, location || undefined);

      // Get detailed information for each place
      const results: SearchResult[] = [];

      for (let i = 0; i < Math.min(places.length, limit); i++) {
        const place = places[i];

        try {
          const details = await this.googlePlaces.getPlaceDetails(place.placeId);

          // Convert Google result to our SearchResult format
          results.push({
            place_id: details.placeId,
            display_name: details.formattedAddress,
            lat: details.coordinates.lat.toString(),
            lon: details.coordinates.lng.toString(),
            type: details.types[0] || 'address',
            importance: 0.9 - (i * 0.1), // High importance for Google results
            address: this.parseGoogleAddress(details.formattedAddress)
          });

        } catch (detailError) {
          console.warn(`Failed to get details for place ${place.placeId}:`, detailError);
          // Continue with next place
        }
      }

      this.trackUsage('google_places', results.length);

      return {
        results,
        provider: 'google_places'
      };

    } catch (error) {
      console.error('Google Places search failed:', error);
      throw error;
    }
  }

  /**
   * Parse Google formatted address into components
   * @param formattedAddress - Google's formatted address
   * @returns Parsed address components
   */
  private parseGoogleAddress(formattedAddress: string): SearchResult['address'] {
    // Simple parsing - could be enhanced with more sophisticated logic
    const parts = formattedAddress.split(', ');

    return {
      display_name: formattedAddress,
      road: parts[0] || undefined,
      city: parts.find(part => part.includes('Johannesburg') || part.includes('Cape Town') || part.includes('Durban')) || undefined,
      country: parts.find(part => part.includes('South Africa')) || undefined
    };
  }

  /**
   * Check if LocationIQ can be used (within daily limits)
   * @returns True if LocationIQ can be used
   */
  private canUseLocationIQ(): boolean {
    const today = new Date().toDateString();
    const usage = this.dailyUsage.get(`locationiq-${today}`) || 0;
    return usage < this.providers.locationiq.dailyLimit;
  }

  /**
   * Track API usage for rate limiting
   * @param provider - Provider name
   * @param count - Number of requests (default: 1)
   */
  private trackUsage(provider: string, count: number = 1): void {
    const today = new Date().toDateString();
    const key = `${provider}-${today}`;
    const current = this.dailyUsage.get(key) || 0;
    this.dailyUsage.set(key, current + count);
  }

  /**
   * Get remaining quota for a provider
   * @param provider - Provider name
   * @returns Remaining requests for today
   */
  getRemainingQuota(provider: 'nominatim' | 'locationiq' | 'google_places' | 'google_geocoding'): number {
    const today = new Date().toDateString();
    const usage = this.dailyUsage.get(`${provider}-${today}`) || 0;
    return Math.max(0, this.providers[provider].dailyLimit - usage);
  }

  /**
   * Get Google API usage statistics
   * @returns Google API usage and cost information
   */
  getGoogleUsageStats() {
    if (!this.googlePlaces) {
      return { available: false, message: 'Google Maps API not configured' };
    }

    const placesStats = this.googlePlaces.getUsageStats();

    return {
      available: true,
      dailyUsage: placesStats.dailyUsage,
      monthlyUsage: placesStats.monthlyUsage,
      remainingCredit: placesStats.remainingCredit,
      requestsToday: placesStats.requestsToday,
      costToday: placesStats.dailyUsage,
      freeCreditsRemaining: placesStats.remainingCredit > 0
    };
  }

  /**
   * Get usage statistics
   * @returns Usage statistics for all providers
   */
  getUsageStats(): { [key: string]: { used: number; limit: number; remaining: number } } {
    const stats: { [key: string]: { used: number; limit: number; remaining: number } } = {};

    Object.entries(this.providers).forEach(([name, config]) => {
      const today = new Date().toDateString();
      const used = this.dailyUsage.get(`${name}-${today}`) || 0;
      stats[name] = {
        used,
        limit: config.dailyLimit,
        remaining: Math.max(0, config.dailyLimit - used)
      };
    });

    return stats;
  }
}

export const geocodingService = new GeocodingService();
