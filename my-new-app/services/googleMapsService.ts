/**
 * Google Maps API Service for SheMove App
 * Legal, free-tier usage of Google's mapping services
 * 
 * Free Tier Limits (Monthly $200 credit):
 * - Places API: ~40,000 requests
 * - Geocoding API: ~40,000 requests  
 * - Directions API: ~40,000 requests
 */

export interface GooglePlaceResult {
  placeId: string;
  description: string;
  mainText: string;
  secondaryText: string;
  types: string[];
}

export interface GooglePlaceDetails {
  formattedAddress: string;
  coordinates: { lat: number; lng: number };
  name: string;
  types: string[];
  placeId: string;
}

export interface GoogleGeocodeResult {
  formattedAddress: string;
  coordinates: { lat: number; lng: number };
  types: string[];
  addressComponents: any[];
  placeId: string;
  confidence: number;
}

export interface GoogleRouteResult {
  distance: { text: string; value: number };
  duration: { text: string; value: number };
  startAddress: string;
  endAddress: string;
  polyline: string;
  steps: any[];
}

export interface APIUsageStats {
  dailyUsage: number;
  monthlyUsage: number;
  remainingCredit: number;
  requestsToday: number;
}

/**
 * Google Places API Service
 * Best for: Address autocomplete and place search
 */
export class GooglePlacesService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/place';
  private usageTracker: Map<string, number> = new Map();

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Search for places with autocomplete
   * Cost: $0.017 per request (2.83 per $1)
   */
  async searchPlaces(
    query: string, 
    location?: { lat: number; lng: number }, 
    radius: number = 50000
  ): Promise<GooglePlaceResult[]> {
    this.trackUsage('places_autocomplete', 0.017);

    const params = new URLSearchParams({
      input: query.trim(),
      key: this.apiKey,
      types: 'address',
      components: 'country:za', // Restrict to South Africa
      language: 'en'
    });

    // Add location bias for better local results
    if (location) {
      params.append('location', `${location.lat},${location.lng}`);
      params.append('radius', radius.toString());
    }

    try {
      const response = await fetch(`${this.baseUrl}/autocomplete/json?${params}`, {
        headers: {
          'User-Agent': 'SheMove-RideSharing-App/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`Places API HTTP error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.status === 'REQUEST_DENIED') {
        throw new Error(`Places API access denied: ${data.error_message}`);
      }

      if (data.status === 'OVER_QUERY_LIMIT') {
        throw new Error('Places API quota exceeded');
      }

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        console.warn(`Places API warning: ${data.status}`);
      }

      return (data.predictions || []).map((prediction: any) => ({
        placeId: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting?.main_text || prediction.description,
        secondaryText: prediction.structured_formatting?.secondary_text || '',
        types: prediction.types || []
      }));

    } catch (error) {
      console.error('Places API error:', error);
      throw error;
    }
  }

  /**
   * Get detailed information about a place
   * Cost: $0.017 per request
   */
  async getPlaceDetails(placeId: string): Promise<GooglePlaceDetails> {
    this.trackUsage('place_details', 0.017);

    const params = new URLSearchParams({
      place_id: placeId,
      key: this.apiKey,
      fields: 'formatted_address,geometry,name,types,place_id',
      language: 'en'
    });

    try {
      const response = await fetch(`${this.baseUrl}/details/json?${params}`);
      
      if (!response.ok) {
        throw new Error(`Place Details HTTP error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.status !== 'OK') {
        throw new Error(`Place Details error: ${data.status}`);
      }

      const place = data.result;
      return {
        formattedAddress: place.formatted_address,
        coordinates: {
          lat: place.geometry.location.lat,
          lng: place.geometry.location.lng
        },
        name: place.name || '',
        types: place.types || [],
        placeId: place.place_id
      };

    } catch (error) {
      console.error('Place Details error:', error);
      throw error;
    }
  }

  /**
   * Get current API usage statistics
   */
  getUsageStats(): APIUsageStats {
    const today = new Date().toDateString();
    const month = new Date().toISOString().slice(0, 7);
    
    const dailyUsage = this.usageTracker.get(`daily_${today}`) || 0;
    const monthlyUsage = this.usageTracker.get(`monthly_${month}`) || 0;
    
    return {
      dailyUsage,
      monthlyUsage,
      remainingCredit: Math.max(0, 200 - monthlyUsage),
      requestsToday: Math.floor(dailyUsage / 0.017)
    };
  }

  private trackUsage(operation: string, cost: number): void {
    const today = new Date().toDateString();
    const month = new Date().toISOString().slice(0, 7);
    
    const dailyKey = `daily_${today}`;
    const monthlyKey = `monthly_${month}`;
    
    this.usageTracker.set(dailyKey, (this.usageTracker.get(dailyKey) || 0) + cost);
    this.usageTracker.set(monthlyKey, (this.usageTracker.get(monthlyKey) || 0) + cost);
    
    // Warn if approaching limits
    const monthlyUsage = this.usageTracker.get(monthlyKey) || 0;
    if (monthlyUsage > 150) { // 75% of free tier
      console.warn(`⚠️ Google API usage: $${monthlyUsage.toFixed(2)} of $200 free tier`);
    }
  }
}

/**
 * Google Geocoding API Service  
 * Best for: Converting addresses to coordinates
 */
export class GoogleGeocodingService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/geocode';
  private usageTracker: Map<string, number> = new Map();

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Geocode an address to coordinates
   * Cost: $0.005 per request (200 per $1)
   */
  async geocodeAddress(address: string): Promise<GoogleGeocodeResult[]> {
    this.trackUsage('geocoding', 0.005);

    const params = new URLSearchParams({
      address: address.trim(),
      key: this.apiKey,
      components: 'country:ZA', // Restrict to South Africa
      region: 'za', // Bias results to South Africa
      language: 'en'
    });

    try {
      const response = await fetch(`${this.baseUrl}/json?${params}`);
      
      if (!response.ok) {
        throw new Error(`Geocoding HTTP error: ${response.status}`);
      }

      const data = await response.json();

      if (data.status === 'REQUEST_DENIED') {
        throw new Error(`Geocoding API access denied: ${data.error_message}`);
      }

      if (data.status === 'OVER_QUERY_LIMIT') {
        throw new Error('Geocoding API quota exceeded');
      }

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        console.warn(`Geocoding API warning: ${data.status}`);
      }

      return (data.results || []).map((result: any, index: number) => ({
        formattedAddress: result.formatted_address,
        coordinates: {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        },
        types: result.types || [],
        addressComponents: result.address_components || [],
        placeId: result.place_id,
        confidence: this.calculateConfidence(result, index)
      }));

    } catch (error) {
      console.error('Geocoding error:', error);
      throw error;
    }
  }

  /**
   * Reverse geocode coordinates to address
   * Cost: $0.005 per request
   */
  async reverseGeocode(lat: number, lng: number): Promise<GoogleGeocodeResult | null> {
    this.trackUsage('reverse_geocoding', 0.005);

    const params = new URLSearchParams({
      latlng: `${lat},${lng}`,
      key: this.apiKey,
      result_type: 'street_address|route|neighborhood|locality',
      language: 'en'
    });

    try {
      const response = await fetch(`${this.baseUrl}/json?${params}`);
      const data = await response.json();

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        throw new Error(`Reverse geocoding error: ${data.status}`);
      }

      const result = data.results?.[0];
      if (!result) return null;

      return {
        formattedAddress: result.formatted_address,
        coordinates: { lat, lng },
        types: result.types || [],
        addressComponents: result.address_components || [],
        placeId: result.place_id,
        confidence: 0.9 // High confidence for reverse geocoding
      };

    } catch (error) {
      console.error('Reverse geocoding error:', error);
      throw error;
    }
  }

  private calculateConfidence(result: any, index: number): number {
    // Higher confidence for first results and exact matches
    let confidence = Math.max(0.5, 1.0 - (index * 0.1));
    
    // Boost confidence for specific address types
    if (result.types?.includes('street_address')) confidence += 0.2;
    if (result.types?.includes('premise')) confidence += 0.1;
    
    return Math.min(1.0, confidence);
  }

  private trackUsage(operation: string, cost: number): void {
    const today = new Date().toDateString();
    const month = new Date().toISOString().slice(0, 7);
    
    const dailyKey = `daily_${today}`;
    const monthlyKey = `monthly_${month}`;
    
    this.usageTracker.set(dailyKey, (this.usageTracker.get(dailyKey) || 0) + cost);
    this.usageTracker.set(monthlyKey, (this.usageTracker.get(monthlyKey) || 0) + cost);
  }
}

/**
 * Google Directions API Service
 * Best for: Route calculation and navigation
 */
export class GoogleDirectionsService {
  private readonly apiKey: string;
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/directions';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Get route between two points
   * Cost: $0.005 per request
   */
  async getRoute(
    origin: { lat: number; lng: number },
    destination: { lat: number; lng: number },
    mode: 'driving' | 'walking' | 'transit' = 'driving'
  ): Promise<GoogleRouteResult> {
    const params = new URLSearchParams({
      origin: `${origin.lat},${origin.lng}`,
      destination: `${destination.lat},${destination.lng}`,
      mode: mode,
      key: this.apiKey,
      region: 'za',
      language: 'en'
    });

    try {
      const response = await fetch(`${this.baseUrl}/json?${params}`);
      const data = await response.json();

      if (data.status !== 'OK') {
        throw new Error(`Directions error: ${data.status}`);
      }

      const route = data.routes[0];
      const leg = route.legs[0];

      return {
        distance: leg.distance,
        duration: leg.duration,
        startAddress: leg.start_address,
        endAddress: leg.end_address,
        polyline: route.overview_polyline.points,
        steps: leg.steps
      };

    } catch (error) {
      console.error('Directions error:', error);
      throw error;
    }
  }
}
