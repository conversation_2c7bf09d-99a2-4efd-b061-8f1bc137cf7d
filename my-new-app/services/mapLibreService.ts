/**
 * MapLibre GL JS Service for SheMove App
 * Replaces Leaflet with vector tile rendering and enhanced performance
 */

import { Map, NavigationControl, GeolocateControl, Marker, LngLatBounds } from 'maplibre-gl';
import { RouteCoordinate } from './routingService';

export interface MapLibreConfig {
  container: string | HTMLElement;
  center: [number, number];
  zoom: number;
  style?: string;
  attributionControl?: boolean;
}

export interface MapMarker {
  id: string;
  coordinates: [number, number];
  title: string;
  description?: string;
  color?: string;
}

export interface RouteLayer {
  id: string;
  coordinates: RouteCoordinate[];
  color: string;
  width: number;
  opacity: number;
  animated?: boolean;
}

class MapLibreService {
  private map: Map | null = null;
  private markers: Map<string, Marker> = new Map();
  private routeLayers: Map<string, string> = new Map(); // layerId -> sourceId mapping
  
  // South African default configuration
  private readonly DEFAULT_CONFIG: MapLibreConfig = {
    container: 'map',
    center: [28.0473, -26.2041], // Johannesburg
    zoom: 10,
    style: 'https://demotiles.maplibre.org/style.json',
    attributionControl: false
  };

  // SheMove color scheme
  private readonly COLORS = {
    PRIMARY: '#E91E63',
    LIGHT: '#FFF0FF',
    ACCENT: '#F9E6F7'
  };

  /**
   * Initialize MapLibre GL JS map
   * @param config - Map configuration
   * @returns Promise that resolves when map is loaded
   */
  async initializeMap(config: Partial<MapLibreConfig> = {}): Promise<Map> {
    const mapConfig = { ...this.DEFAULT_CONFIG, ...config };
    
    this.map = new Map(mapConfig);
    
    return new Promise((resolve, reject) => {
      if (!this.map) {
        reject(new Error('Failed to create map instance'));
        return;
      }

      this.map.on('load', () => {
        this.setupMapControls();
        this.setupMapSources();
        resolve(this.map!);
      });

      this.map.on('error', (error) => {
        console.error('MapLibre error:', error);
        reject(error);
      });
    });
  }

  /**
   * Setup map controls (navigation, geolocation)
   */
  private setupMapControls(): void {
    if (!this.map) return;

    // Add navigation controls
    this.map.addControl(new NavigationControl({
      visualizePitch: true,
      showZoom: true,
      showCompass: true
    }), 'top-right');

    // Add geolocation control
    this.map.addControl(new GeolocateControl({
      positionOptions: {
        enableHighAccuracy: true
      },
      trackUserLocation: true,
      showUserHeading: true
    }), 'top-right');
  }

  /**
   * Setup map data sources for routes and markers
   */
  private setupMapSources(): void {
    if (!this.map) return;

    // Add source for route lines
    this.map.addSource('routes', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: []
      }
    });

    // Add source for animated routes
    this.map.addSource('animated-routes', {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: []
      }
    });
  }

  /**
   * Add marker to map
   * @param marker - Marker configuration
   */
  addMarker(marker: MapMarker): void {
    if (!this.map) return;

    const mapMarker = new Marker({
      color: marker.color || this.COLORS.PRIMARY
    })
      .setLngLat(marker.coordinates)
      .addTo(this.map);

    // Add popup if description provided
    if (marker.description) {
      const popup = new maplibregl.Popup({ offset: 25 })
        .setHTML(`<h3>${marker.title}</h3><p>${marker.description}</p>`);
      mapMarker.setPopup(popup);
    }

    this.markers.set(marker.id, mapMarker);
  }

  /**
   * Remove marker from map
   * @param markerId - Marker ID to remove
   */
  removeMarker(markerId: string): void {
    const marker = this.markers.get(markerId);
    if (marker) {
      marker.remove();
      this.markers.delete(markerId);
    }
  }

  /**
   * Clear all markers
   */
  clearMarkers(): void {
    this.markers.forEach(marker => marker.remove());
    this.markers.clear();
  }

  /**
   * Add route layer with two-tone animation effect
   * @param route - Route configuration
   */
  addRouteLayer(route: RouteLayer): void {
    if (!this.map) return;

    const coordinates = route.coordinates.map(coord => [coord.lng, coord.lat]);
    
    // Create GeoJSON feature for the route
    const routeFeature = {
      type: 'Feature' as const,
      properties: {
        id: route.id,
        color: route.color,
        width: route.width,
        opacity: route.opacity
      },
      geometry: {
        type: 'LineString' as const,
        coordinates
      }
    };

    // Add base route layer (light pink)
    const baseLayerId = `${route.id}-base`;
    this.map.addLayer({
      id: baseLayerId,
      type: 'line',
      source: 'routes',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': this.COLORS.LIGHT,
        'line-width': route.width + 2,
        'line-opacity': route.opacity * 0.8
      }
    });

    // Add animated route layer (deep pink)
    const animatedLayerId = `${route.id}-animated`;
    this.map.addLayer({
      id: animatedLayerId,
      type: 'line',
      source: 'animated-routes',
      layout: {
        'line-join': 'round',
        'line-cap': 'round'
      },
      paint: {
        'line-color': route.color,
        'line-width': route.width,
        'line-opacity': route.opacity
      }
    });

    // Update route source with new feature
    const routeSource = this.map.getSource('routes') as maplibregl.GeoJSONSource;
    const currentData = routeSource._data as GeoJSON.FeatureCollection;
    currentData.features.push(routeFeature);
    routeSource.setData(currentData);

    // Animate route if requested
    if (route.animated) {
      this.animateRoute(route.id, coordinates);
    }

    this.routeLayers.set(route.id, baseLayerId);
  }

  /**
   * Animate route drawing with progressive reveal
   * @param routeId - Route identifier
   * @param coordinates - Route coordinates
   */
  private animateRoute(routeId: string, coordinates: number[][]): void {
    if (!this.map) return;

    let currentIndex = 0;
    const animationSpeed = Math.max(50, Math.min(200, 3000 / coordinates.length));

    const animate = () => {
      if (currentIndex >= coordinates.length) return;

      const currentCoordinates = coordinates.slice(0, currentIndex + 1);
      
      const animatedFeature = {
        type: 'Feature' as const,
        properties: { id: routeId },
        geometry: {
          type: 'LineString' as const,
          coordinates: currentCoordinates
        }
      };

      const animatedSource = this.map!.getSource('animated-routes') as maplibregl.GeoJSONSource;
      const currentData = animatedSource._data as GeoJSON.FeatureCollection;
      
      // Update or add the animated feature
      const existingIndex = currentData.features.findIndex(
        f => f.properties?.id === routeId
      );
      
      if (existingIndex >= 0) {
        currentData.features[existingIndex] = animatedFeature;
      } else {
        currentData.features.push(animatedFeature);
      }
      
      animatedSource.setData(currentData);

      currentIndex++;
      setTimeout(animate, animationSpeed);
    };

    animate();
  }

  /**
   * Remove route layer
   * @param routeId - Route ID to remove
   */
  removeRouteLayer(routeId: string): void {
    if (!this.map) return;

    const baseLayerId = `${routeId}-base`;
    const animatedLayerId = `${routeId}-animated`;

    // Remove layers
    if (this.map.getLayer(baseLayerId)) {
      this.map.removeLayer(baseLayerId);
    }
    if (this.map.getLayer(animatedLayerId)) {
      this.map.removeLayer(animatedLayerId);
    }

    // Remove from route sources
    const routeSource = this.map.getSource('routes') as maplibregl.GeoJSONSource;
    const animatedSource = this.map.getSource('animated-routes') as maplibregl.GeoJSONSource;
    
    if (routeSource) {
      const routeData = routeSource._data as GeoJSON.FeatureCollection;
      routeData.features = routeData.features.filter(f => f.properties?.id !== routeId);
      routeSource.setData(routeData);
    }
    
    if (animatedSource) {
      const animatedData = animatedSource._data as GeoJSON.FeatureCollection;
      animatedData.features = animatedData.features.filter(f => f.properties?.id !== routeId);
      animatedSource.setData(animatedData);
    }

    this.routeLayers.delete(routeId);
  }

  /**
   * Fit map to show all markers and routes
   * @param padding - Padding around bounds
   */
  fitToContent(padding: number = 50): void {
    if (!this.map) return;

    const bounds = new LngLatBounds();
    let hasContent = false;

    // Include markers in bounds
    this.markers.forEach(marker => {
      bounds.extend(marker.getLngLat());
      hasContent = true;
    });

    // Include routes in bounds
    const routeSource = this.map.getSource('routes') as maplibregl.GeoJSONSource;
    if (routeSource) {
      const data = routeSource._data as GeoJSON.FeatureCollection;
      data.features.forEach(feature => {
        if (feature.geometry.type === 'LineString') {
          feature.geometry.coordinates.forEach(coord => {
            bounds.extend(coord as [number, number]);
            hasContent = true;
          });
        }
      });
    }

    if (hasContent) {
      this.map.fitBounds(bounds, { padding });
    }
  }

  /**
   * Get map instance
   * @returns MapLibre map instance
   */
  getMap(): Map | null {
    return this.map;
  }

  /**
   * Destroy map instance
   */
  destroy(): void {
    if (this.map) {
      this.clearMarkers();
      this.map.remove();
      this.map = null;
    }
    this.markers.clear();
    this.routeLayers.clear();
  }
}

export const mapLibreService = new MapLibreService();
