/**
 * Points of Interest (POI) Search Service
 * Integrates Overpass API for OSM data and Foursquare API for business discovery
 */

export interface POIResult {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  lat: number;
  lng: number;
  address?: string;
  phone?: string;
  website?: string;
  rating?: number;
  distance?: number; // in meters
  source: 'osm' | 'foursquare';
  amenity?: string;
  opening_hours?: string;
}

export interface POISearchOptions {
  radius?: number; // in meters, default 5000
  limit?: number; // max results, default 20
  categories?: string[]; // filter by categories
  source?: 'osm' | 'foursquare' | 'both'; // data source preference
}

export interface POISearchResponse {
  results: POIResult[];
  error?: string;
  source: string;
}

class OverpassProvider {
  private readonly baseUrl = 'https://overpass-api.de/api/interpreter';
  
  async searchPOIs(
    lat: number,
    lng: number,
    query: string,
    options: POISearchOptions = {}
  ): Promise<POIResult[]> {
    const radius = options.radius || 5000;
    const limit = options.limit || 20;
    
    try {
      // Build Overpass QL query for common amenities
      const overpassQuery = this.buildOverpassQuery(lat, lng, radius, query, limit);
      
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'SheMove-App/1.0'
        },
        body: `data=${encodeURIComponent(overpassQuery)}`
      });

      if (!response.ok) {
        throw new Error(`Overpass API error: ${response.status}`);
      }

      const data = await response.json();
      return this.parseOverpassResponse(data, lat, lng);
    } catch (error) {
      console.warn('Overpass API search failed:', error);
      return [];
    }
  }

  private buildOverpassQuery(lat: number, lng: number, radius: number, query: string, limit: number): string {
    const bbox = this.calculateBoundingBox(lat, lng, radius);
    
    // Common amenity types for ride-sharing destinations
    const amenities = [
      'restaurant', 'cafe', 'bar', 'pub', 'fast_food',
      'hospital', 'clinic', 'pharmacy',
      'bank', 'atm',
      'fuel', 'charging_station',
      'school', 'university', 'library',
      'cinema', 'theatre', 'nightclub',
      'shopping_mall', 'supermarket', 'marketplace',
      'hotel', 'guest_house',
      'place_of_worship',
      'police', 'fire_station',
      'post_office'
    ];

    const amenityFilter = amenities.map(a => `["amenity"="${a}"]`).join('');
    
    return `
      [out:json][timeout:25];
      (
        node["name"~"${query}",i]${amenityFilter}(${bbox});
        way["name"~"${query}",i]${amenityFilter}(${bbox});
        relation["name"~"${query}",i]${amenityFilter}(${bbox});
      );
      out center ${limit};
    `.trim();
  }

  private calculateBoundingBox(lat: number, lng: number, radius: number): string {
    // Approximate conversion: 1 degree ≈ 111km
    const latDelta = radius / 111000;
    const lngDelta = radius / (111000 * Math.cos(lat * Math.PI / 180));
    
    const south = lat - latDelta;
    const north = lat + latDelta;
    const west = lng - lngDelta;
    const east = lng + lngDelta;
    
    return `${south},${west},${north},${east}`;
  }

  private parseOverpassResponse(data: any, userLat: number, userLng: number): POIResult[] {
    if (!data.elements) return [];

    return data.elements.map((element: any) => {
      const lat = element.lat || element.center?.lat;
      const lng = element.lon || element.center?.lon;
      
      if (!lat || !lng) return null;

      const distance = this.calculateDistance(userLat, userLng, lat, lng);

      return {
        id: `osm-${element.id}`,
        name: element.tags?.name || 'Unknown',
        category: this.mapAmenityToCategory(element.tags?.amenity),
        subcategory: element.tags?.amenity,
        lat,
        lng,
        address: this.buildAddress(element.tags),
        phone: element.tags?.phone,
        website: element.tags?.website,
        distance,
        source: 'osm' as const,
        amenity: element.tags?.amenity,
        opening_hours: element.tags?.opening_hours
      };
    }).filter(Boolean);
  }

  private mapAmenityToCategory(amenity: string): string {
    const categoryMap: { [key: string]: string } = {
      'restaurant': 'Food & Drink',
      'cafe': 'Food & Drink',
      'bar': 'Food & Drink',
      'pub': 'Food & Drink',
      'fast_food': 'Food & Drink',
      'hospital': 'Healthcare',
      'clinic': 'Healthcare',
      'pharmacy': 'Healthcare',
      'bank': 'Finance',
      'atm': 'Finance',
      'fuel': 'Transportation',
      'charging_station': 'Transportation',
      'school': 'Education',
      'university': 'Education',
      'library': 'Education',
      'cinema': 'Entertainment',
      'theatre': 'Entertainment',
      'nightclub': 'Entertainment',
      'shopping_mall': 'Shopping',
      'supermarket': 'Shopping',
      'marketplace': 'Shopping',
      'hotel': 'Accommodation',
      'guest_house': 'Accommodation',
      'place_of_worship': 'Religious',
      'police': 'Emergency',
      'fire_station': 'Emergency',
      'post_office': 'Services'
    };

    return categoryMap[amenity] || 'Other';
  }

  private buildAddress(tags: any): string {
    const parts = [];
    if (tags['addr:housenumber']) parts.push(tags['addr:housenumber']);
    if (tags['addr:street']) parts.push(tags['addr:street']);
    if (tags['addr:suburb']) parts.push(tags['addr:suburb']);
    if (tags['addr:city']) parts.push(tags['addr:city']);
    return parts.join(', ') || undefined;
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    const deltaLatRad = (lat2 - lat1) * Math.PI / 180;
    const deltaLngRad = (lng2 - lng1) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
}

class FoursquareProvider {
  private readonly baseUrl = 'https://api.foursquare.com/v3/places/search';
  private readonly apiKey = 'your-foursquare-api-key'; // Replace with actual API key

  async searchPOIs(
    lat: number,
    lng: number,
    query: string,
    options: POISearchOptions = {}
  ): Promise<POIResult[]> {
    const radius = options.radius || 5000;
    const limit = Math.min(options.limit || 20, 50); // Foursquare limit is 50

    try {
      const params = new URLSearchParams({
        ll: `${lat},${lng}`,
        radius: radius.toString(),
        query,
        limit: limit.toString(),
        fields: 'name,location,categories,rating,tel,website,hours'
      });

      const response = await fetch(`${this.baseUrl}?${params}`, {
        headers: {
          'Authorization': this.apiKey,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Foursquare API error: ${response.status}`);
      }

      const data = await response.json();
      return this.parseFoursquareResponse(data, lat, lng);
    } catch (error) {
      console.warn('Foursquare API search failed:', error);
      return [];
    }
  }

  private parseFoursquareResponse(data: any, userLat: number, userLng: number): POIResult[] {
    if (!data.results) return [];

    return data.results.map((place: any) => {
      const lat = place.geocodes?.main?.latitude;
      const lng = place.geocodes?.main?.longitude;
      
      if (!lat || !lng) return null;

      const distance = this.calculateDistance(userLat, userLng, lat, lng);
      const category = place.categories?.[0];

      return {
        id: `fsq-${place.fsq_id}`,
        name: place.name,
        category: category?.name || 'Other',
        subcategory: category?.short_name,
        lat,
        lng,
        address: this.buildFoursquareAddress(place.location),
        phone: place.tel,
        website: place.website,
        rating: place.rating,
        distance,
        source: 'foursquare' as const,
        opening_hours: place.hours?.display
      };
    }).filter(Boolean);
  }

  private buildFoursquareAddress(location: any): string {
    if (!location) return undefined;
    
    const parts = [];
    if (location.address) parts.push(location.address);
    if (location.locality) parts.push(location.locality);
    if (location.region) parts.push(location.region);
    return parts.join(', ') || undefined;
  }

  private calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371000; // Earth's radius in meters
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    const deltaLatRad = (lat2 - lat1) * Math.PI / 180;
    const deltaLngRad = (lng2 - lng1) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }
}

class POISearchService {
  private overpassProvider = new OverpassProvider();
  private foursquareProvider = new FoursquareProvider();

  /**
   * Search for Points of Interest near a location
   * @param lat - Latitude
   * @param lng - Longitude
   * @param query - Search query
   * @param options - Search options
   * @returns Promise with POI search results
   */
  async searchPOIs(
    lat: number,
    lng: number,
    query: string,
    options: POISearchOptions = {}
  ): Promise<POISearchResponse> {
    const source = options.source || 'both';
    let allResults: POIResult[] = [];
    let sources: string[] = [];

    try {
      // Search OpenStreetMap data via Overpass API
      if (source === 'osm' || source === 'both') {
        console.log('🗺️ Searching OSM POIs via Overpass API...');
        const osmResults = await this.overpassProvider.searchPOIs(lat, lng, query, options);
        allResults.push(...osmResults);
        if (osmResults.length > 0) sources.push('OpenStreetMap');
      }

      // Search Foursquare data (if API key is configured)
      if ((source === 'foursquare' || source === 'both') && this.foursquareProvider) {
        console.log('🏢 Searching business POIs via Foursquare API...');
        const foursquareResults = await this.foursquareProvider.searchPOIs(lat, lng, query, options);
        allResults.push(...foursquareResults);
        if (foursquareResults.length > 0) sources.push('Foursquare');
      }

      // Remove duplicates and sort by distance
      const uniqueResults = this.removeDuplicates(allResults);
      const sortedResults = uniqueResults
        .sort((a, b) => (a.distance || 0) - (b.distance || 0))
        .slice(0, options.limit || 20);

      return {
        results: sortedResults,
        source: sources.join(' + ') || 'No sources available'
      };
    } catch (error) {
      console.error('POI search error:', error);
      return {
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        source: 'Error'
      };
    }
  }

  /**
   * Remove duplicate POIs based on name and proximity
   * @param results - Array of POI results
   * @returns Deduplicated results
   */
  private removeDuplicates(results: POIResult[]): POIResult[] {
    const unique: POIResult[] = [];
    const seen = new Set<string>();

    for (const result of results) {
      // Create a key based on name and approximate location
      const locationKey = `${Math.round(result.lat * 1000)},${Math.round(result.lng * 1000)}`;
      const key = `${result.name.toLowerCase()}-${locationKey}`;

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(result);
      }
    }

    return unique;
  }

  /**
   * Get popular POI categories for South Africa
   * @returns Array of popular categories
   */
  getPopularCategories(): string[] {
    return [
      'Food & Drink',
      'Shopping',
      'Healthcare',
      'Transportation',
      'Entertainment',
      'Education',
      'Finance',
      'Accommodation',
      'Services',
      'Emergency'
    ];
  }
}

export const poiSearchService = new POISearchService();
