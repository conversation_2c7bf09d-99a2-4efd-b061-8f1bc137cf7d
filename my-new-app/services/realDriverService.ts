/**
 * Real Driver Service for SheMove Passenger App
 * Fetches actual drivers from Supabase database instead of generating mock data
 */

import { Coordinates } from './distanceService';
import { distanceService } from './distanceService';
import { RideType } from './fareService';
import { RealtimeService } from '../shared/services/RealtimeService';
import { supabase } from '../lib/supabase';

// Re-export the Driver interface from the mock service for compatibility
export interface Driver {
  id: string;
  name: string;
  rating: number;
  totalTrips: number;
  profileImage: string;
  vehicleInfo: {
    make: string;
    model: string;
    year: number;
    color: string;
    licensePlate: string;
    type: RideType;
  };
  location: Coordinates;
  isOnline: boolean;
  distanceFromUser: number; // in km
  estimatedArrival: number; // in minutes
  heading: number; // bearing in degrees
  isMoving: boolean;
  specialFeatures: string[];
}

export interface DriverSearchResult {
  availableDrivers: Driver[];
  totalDriversInArea: number;
  averageWaitTime: number;
  nearestDriver: Driver | null;
}

// Database driver response interface (matches get_nearby_drivers function)
interface DatabaseDriver {
  id: string;
  full_name: string;
  vehicle_info: {
    make: string;
    model: string;
    color: string;
    plate: string;
    type: RideType;
  };
  distance_km: number;
  rating: number;
  estimated_arrival_minutes: number;
  current_location: {
    x: number;
    y: number;
  };
}

class RealDriverService {
  private realtimeService: RealtimeService | null = null;
  private readonly MAX_SEARCH_RADIUS = 15; // km
  private readonly FALLBACK_FEATURES = [
    'Air conditioning',
    'Phone charger',
    'Water bottles',
    'Safety verified',
    'Highly rated',
    'Experienced driver'
  ];

  /**
   * Initialize the service with user context
   */
  async initialize(userId: string): Promise<void> {
    if (!supabase) {
      throw new Error('Supabase client not available');
    }

    try {
      this.realtimeService = new RealtimeService({
        supabase,
        userId,
        userType: 'passenger'
      });

      // Use startListening() instead of connect()
      await this.realtimeService.startListening();
      console.log('RealDriverService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize RealDriverService:', error);
      throw error;
    }
  }

  /**
   * Get nearby drivers from the database
   */
  async getNearbyDrivers(
    userLocation: Coordinates, 
    rideType?: RideType
  ): Promise<DriverSearchResult> {
    if (!this.realtimeService) {
      throw new Error('RealDriverService not initialized. Call initialize() first.');
    }

    try {
      console.log('🔍 Searching for real drivers near:', userLocation, 'for ride type:', rideType);

      // Call the database function directly
      const databaseDrivers = await this.getNearbyDriversFromDatabase(
        userLocation.lat,
        userLocation.lng,
        this.MAX_SEARCH_RADIUS,
        rideType
      );

      console.log('📍 Found', databaseDrivers.length, 'drivers in database');

      // Transform database drivers to app Driver format
      const availableDrivers = databaseDrivers.map(dbDriver =>
        this.transformDatabaseDriver(dbDriver, userLocation)
      );

      // Calculate search result metrics
      const totalDriversInArea = availableDrivers.length;
      const averageWaitTime = this.calculateAverageWaitTime(availableDrivers);
      const nearestDriver = availableDrivers.length > 0 ? availableDrivers[0] : null;

      const result: DriverSearchResult = {
        availableDrivers,
        totalDriversInArea,
        averageWaitTime,
        nearestDriver
      };

      console.log('✅ Driver search completed:', {
        found: totalDriversInArea,
        nearest: nearestDriver?.name,
        avgWait: averageWaitTime
      });

      return result;
    } catch (error) {
      console.error('❌ Error fetching nearby drivers:', error);
      
      // Return empty result instead of throwing to maintain app stability
      return {
        availableDrivers: [],
        totalDriversInArea: 0,
        averageWaitTime: 0,
        nearestDriver: null
      };
    }
  }

  /**
   * Get nearby drivers from database
   */
  private async getNearbyDriversFromDatabase(
    lat: number,
    lng: number,
    radiusKm: number,
    rideType: RideType
  ): Promise<any[]> {
    try {
      const { data, error } = await supabase.rpc('get_nearby_drivers', {
        p_pickup_lat: lat,
        p_pickup_lng: lng,
        p_max_distance_km: radiusKm,
        p_ride_type_filter: rideType
      });

      if (error) {
        console.error('Database error getting nearby drivers:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error calling get_nearby_drivers:', error);
      return [];
    }
  }

  /**
   * Transform database driver data to app Driver interface
   */
  private transformDatabaseDriver(dbDriver: DatabaseDriver, userLocation: Coordinates): Driver {
    // Extract location coordinates
    const driverLocation: Coordinates = {
      lat: dbDriver.current_location.y,
      lng: dbDriver.current_location.x
    };

    // Generate realistic heading and movement status
    const heading = Math.floor(Math.random() * 360);
    const isMoving = Math.random() > 0.3; // 70% chance driver is moving

    // Generate special features based on driver rating and vehicle type
    const specialFeatures = this.generateSpecialFeatures(dbDriver.rating, dbDriver.vehicle_info.type);

    return {
      id: dbDriver.id,
      name: dbDriver.full_name,
      rating: Number(dbDriver.rating),
      totalTrips: Math.floor(dbDriver.rating * 100 + Math.random() * 200), // Estimate based on rating
      profileImage: `https://ui-avatars.com/api/?name=${encodeURIComponent(dbDriver.full_name)}&background=F9E6F7&color=E91E63&size=128`,
      vehicleInfo: {
        make: dbDriver.vehicle_info.make,
        model: dbDriver.vehicle_info.model,
        year: 2018 + Math.floor(Math.random() * 6), // 2018-2023 (we don't store year in DB yet)
        color: dbDriver.vehicle_info.color,
        licensePlate: dbDriver.vehicle_info.plate,
        type: dbDriver.vehicle_info.type
      },
      location: driverLocation,
      isOnline: true, // Only online drivers are returned from database
      distanceFromUser: dbDriver.distance_km,
      estimatedArrival: dbDriver.estimated_arrival_minutes,
      heading,
      isMoving,
      specialFeatures
    };
  }

  /**
   * Generate special features based on driver rating and vehicle type
   */
  private generateSpecialFeatures(rating: number, vehicleType: RideType): string[] {
    const features: string[] = [];
    
    // Base features for all drivers
    features.push('Safety verified', 'Women-only service');
    
    // Rating-based features
    if (rating >= 4.8) {
      features.push('Top rated driver', 'Excellent service');
    } else if (rating >= 4.5) {
      features.push('Highly rated', 'Reliable service');
    }
    
    // Vehicle type specific features
    switch (vehicleType) {
      case 'SheXL':
        features.push('Extra space', 'Luggage friendly');
        break;
      case 'ShePool':
        features.push('Eco-friendly', 'Shared ride');
        break;
      default: // SheRide
        features.push('Comfortable ride', 'Direct route');
    }
    
    // Add random additional features
    const additionalFeatures = this.FALLBACK_FEATURES.filter(f => !features.includes(f));
    const randomCount = Math.floor(Math.random() * 2) + 1; // 1-2 additional features
    
    for (let i = 0; i < randomCount && i < additionalFeatures.length; i++) {
      const randomIndex = Math.floor(Math.random() * additionalFeatures.length);
      features.push(additionalFeatures[randomIndex]);
      additionalFeatures.splice(randomIndex, 1);
    }
    
    return features.slice(0, 4); // Limit to 4 features max
  }

  /**
   * Calculate average wait time based on driver distances
   */
  private calculateAverageWaitTime(drivers: Driver[]): number {
    if (drivers.length === 0) return 0;
    
    const totalWaitTime = drivers.reduce((sum, driver) => sum + driver.estimatedArrival, 0);
    return Math.round(totalWaitTime / drivers.length);
  }

  /**
   * Check if the service is properly initialized
   */
  isInitialized(): boolean {
    return this.realtimeService !== null;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.realtimeService) {
      this.realtimeService.stopListening(); // Use stopListening() instead of disconnect()
      this.realtimeService = null;
    }
  }
}

// Export singleton instance
export const realDriverService = new RealDriverService();
export default realDriverService;
