/**
 * Recent Trips Service
 * Manages user trip history, frequent destinations, and trip statistics
 * Provides efficient querying and caching for better UX
 */

import { supabase, RecentTrip, FrequentDestination, TripStatistics } from '../lib/supabase';

export interface TripDisplayData {
  id: string;
  destination: string;
  date: string;
  price: string;
  status: 'completed' | 'cancelled';
  rideType: 'SheRide' | 'ShePool' | 'SheXL';
  distance?: string;
  duration?: string;
  rating?: number;
}

export class RecentTripsService {
  private static instance: RecentTripsService;
  private userId: string | null = null;
  private cachedTrips: TripDisplayData[] = [];
  private lastFetchTime: number = 0;
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): RecentTripsService {
    if (!RecentTripsService.instance) {
      RecentTripsService.instance = new RecentTripsService();
    }
    return RecentTripsService.instance;
  }

  /**
   * Set the current user ID for all operations
   */
  public setUserId(userId: string | null) {
    if (this.userId !== userId) {
      this.userId = userId;
      this.clearCache();
    }
  }

  /**
   * Clear cached data
   */
  private clearCache() {
    this.cachedTrips = [];
    this.lastFetchTime = 0;
  }

  /**
   * Check if cache is valid
   */
  private isCacheValid(): boolean {
    return Date.now() - this.lastFetchTime < this.cacheTimeout;
  }

  /**
   * Format trip data for display
   */
  private formatTripForDisplay(trip: RecentTrip): TripDisplayData {
    const tripDate = new Date(trip.trip_date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - tripDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let dateDisplay: string;
    if (diffDays === 1) {
      dateDisplay = 'Yesterday';
    } else if (diffDays <= 7) {
      dateDisplay = `${diffDays} days ago`;
    } else if (diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7);
      dateDisplay = weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
    } else {
      dateDisplay = tripDate.toLocaleDateString('en-ZA', { 
        month: 'short', 
        day: 'numeric' 
      });
    }

    return {
      id: trip.trip_id,
      destination: trip.destination_address_short || trip.destination_location,
      date: dateDisplay,
      price: `R${trip.fare_amount.toFixed(0)}`,
      status: trip.trip_status as 'completed' | 'cancelled',
      rideType: trip.ride_type,
      distance: `${trip.distance_km.toFixed(1)}km`,
      duration: `${trip.duration_minutes}min`,
      rating: trip.driver_rating || undefined,
    };
  }

  /**
   * Get recent trips for the user
   */
  public async getRecentTrips(limit: number = 10, forceRefresh: boolean = false): Promise<TripDisplayData[]> {
    if (!this.userId || !supabase) {
      return [];
    }

    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && this.isCacheValid() && this.cachedTrips.length > 0) {
      return this.cachedTrips.slice(0, limit);
    }

    try {
      const { data, error } = await supabase
        .rpc('get_recent_trips', {
          p_user_id: this.userId,
          p_limit: limit,
        });

      if (error) {
        // Check if function doesn't exist
        if (error.code === 'PGRST202') {
          console.warn('Recent trips function not yet created. Please run database migrations.');
          return [];
        }
        console.error('Error fetching recent trips:', error);
        return [];
      }

      const formattedTrips = (data || []).map(trip => this.formatTripForDisplay(trip));
      
      // Update cache
      this.cachedTrips = formattedTrips;
      this.lastFetchTime = Date.now();

      return formattedTrips;
    } catch (error) {
      console.error('Error in getRecentTrips:', error);
      return [];
    }
  }

  /**
   * Get frequent destinations for the user
   */
  public async getFrequentDestinations(limit: number = 5): Promise<FrequentDestination[]> {
    if (!this.userId || !supabase) {
      return [];
    }

    try {
      const { data, error } = await supabase
        .rpc('get_frequent_destinations', {
          p_user_id: this.userId,
          p_limit: limit,
        });

      if (error) {
        console.error('Error fetching frequent destinations:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getFrequentDestinations:', error);
      return [];
    }
  }

  /**
   * Get trip statistics for the user
   */
  public async getTripStatistics(): Promise<TripStatistics | null> {
    if (!this.userId || !supabase) {
      return null;
    }

    try {
      const { data, error } = await supabase
        .rpc('get_trip_statistics', {
          p_user_id: this.userId,
        });

      if (error) {
        console.error('Error fetching trip statistics:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Error in getTripStatistics:', error);
      return null;
    }
  }

  /**
   * Get a specific trip by ID
   */
  public async getTripById(tripId: string): Promise<RecentTrip | null> {
    if (!this.userId || !supabase) {
      return null;
    }

    try {
      const { data, error } = await supabase
        .from('trips')
        .select(`
          *,
          trip_ratings(passenger_rating, driver_rating, passenger_comment),
          drivers(vehicle_make, vehicle_model, vehicle_color, vehicle_plate),
          profiles!drivers(full_name)
        `)
        .eq('id', tripId)
        .eq('passenger_id', this.userId)
        .single();

      if (error) {
        console.error('Error fetching trip by ID:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getTripById:', error);
      return null;
    }
  }

  /**
   * Create a new trip (for booking)
   */
  public async createTrip(tripData: {
    pickupLocation: string;
    pickupCoordinates: [number, number];
    destinationLocation: string;
    destinationCoordinates: [number, number];
    rideType: 'SheRide' | 'ShePool' | 'SheXL';
    fareAmount: number;
    distanceKm: number;
    durationMinutes: number;
    pickupAddressShort?: string;
    destinationAddressShort?: string;
  }): Promise<string | null> {
    if (!this.userId || !supabase) {
      return null;
    }

    try {
      const { data, error } = await supabase
        .from('trips')
        .insert({
          passenger_id: this.userId,
          pickup_location: tripData.pickupLocation,
          pickup_coordinates: `(${tripData.pickupCoordinates[0]},${tripData.pickupCoordinates[1]})`,
          destination_location: tripData.destinationLocation,
          destination_coordinates: `(${tripData.destinationCoordinates[0]},${tripData.destinationCoordinates[1]})`,
          ride_type: tripData.rideType,
          fare_amount: tripData.fareAmount,
          distance_km: tripData.distanceKm,
          duration_minutes: tripData.durationMinutes,
          pickup_address_short: tripData.pickupAddressShort,
          destination_address_short: tripData.destinationAddressShort,
          status: 'requested',
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating trip:', error);
        return null;
      }

      // Clear cache to force refresh on next fetch
      this.clearCache();

      return data.id;
    } catch (error) {
      console.error('Error in createTrip:', error);
      return null;
    }
  }

  /**
   * Update trip status
   */
  public async updateTripStatus(
    tripId: string, 
    status: 'accepted' | 'in_progress' | 'completed' | 'cancelled',
    cancellationReason?: string
  ): Promise<boolean> {
    if (!this.userId || !supabase) {
      return false;
    }

    try {
      const updateData: any = { status };
      
      if (status === 'cancelled' && cancellationReason) {
        updateData.cancellation_reason = cancellationReason;
      }

      const { error } = await supabase
        .from('trips')
        .update(updateData)
        .eq('id', tripId)
        .eq('passenger_id', this.userId);

      if (error) {
        console.error('Error updating trip status:', error);
        return false;
      }

      // Clear cache to force refresh on next fetch
      this.clearCache();

      return true;
    } catch (error) {
      console.error('Error in updateTripStatus:', error);
      return false;
    }
  }

  /**
   * Check if user has any trips (for onboarding/empty state)
   */
  public async hasTrips(): Promise<boolean> {
    if (!this.userId || !supabase) {
      return false;
    }

    try {
      const { count, error } = await supabase
        .from('trips')
        .select('id', { count: 'exact' })
        .eq('passenger_id', this.userId)
        .limit(1);

      if (error) {
        console.error('Error checking if user has trips:', error);
        return false;
      }

      return (count || 0) > 0;
    } catch (error) {
      console.error('Error in hasTrips:', error);
      return false;
    }
  }
}

// Export singleton instance
export const recentTripsService = RecentTripsService.getInstance();
