/**
 * Routing service using OpenStreetMap Routing Service (OSRM)
 * Provides real road-based routing for accurate path visualization
 */

export interface RouteCoordinate {
  lat: number;
  lng: number;
}

export interface RouteStep {
  instruction: string;
  distance: number;
  duration: number;
  coordinates: RouteCoordinate[];
}

export interface RouteResponse {
  coordinates: RouteCoordinate[];
  distance: number; // in meters
  duration: number; // in seconds
  steps: RouteStep[];
  geometry: string; // encoded polyline
}

export interface RoutingOptions {
  profile?: 'driving' | 'walking' | 'cycling';
  alternatives?: boolean;
  steps?: boolean;
  geometries?: 'polyline' | 'geojson';
  overview?: 'full' | 'simplified' | 'false';
}

class RoutingService {
  // Multiple OSRM endpoints for redundancy
  private readonly OSRM_ENDPOINTS = [
    'https://routing.openstreetmap.de/routed-car',  // German server (more reliable)
    'https://router.project-osrm.org',              // Demo server (rate limited)
    'https://osrm.mapzen.com'                       // Alternative endpoint
  ];

  private readonly DEFAULT_OPTIONS: RoutingOptions = {
    profile: 'driving',
    alternatives: false,
    steps: true,
    geometries: 'geojson',
    overview: 'full'
  };

  private currentEndpointIndex = 0;
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1 second

  /**
   * Get route between two points using OSRM with multiple endpoints and retry logic
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @param options - Routing options
   * @returns Promise with route information
   */
  async getRoute(
    start: RouteCoordinate,
    end: RouteCoordinate,
    options: RoutingOptions = {}
  ): Promise<RouteResponse | null> {
    console.log(`🗺️ Getting route from [${start.lat}, ${start.lng}] to [${end.lat}, ${end.lng}]`);

    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const coordinateString = `${start.lng},${start.lat};${end.lng},${end.lat}`;

    const params = new URLSearchParams({
      alternatives: opts.alternatives ? 'true' : 'false',
      steps: opts.steps ? 'true' : 'false',
      geometries: opts.geometries || 'geojson',
      overview: opts.overview || 'full'
    });

    // Try multiple endpoints with retry logic
    for (let attempt = 0; attempt < this.MAX_RETRIES; attempt++) {
      for (let endpointIndex = 0; endpointIndex < this.OSRM_ENDPOINTS.length; endpointIndex++) {
        const currentIndex = (this.currentEndpointIndex + endpointIndex) % this.OSRM_ENDPOINTS.length;
        const baseUrl = this.OSRM_ENDPOINTS[currentIndex];

        try {
          const url = `${baseUrl}/route/v1/${opts.profile}/${coordinateString}?${params}`;
          console.log(`🔄 Trying OSRM endpoint ${currentIndex + 1}/${this.OSRM_ENDPOINTS.length}: ${baseUrl}`);

          const response = await this.fetchWithTimeout(url, {
            headers: {
              'User-Agent': 'SheMove-App/1.0',
              'Accept': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }, 10000); // 10 second timeout

          if (!response.ok) {
            console.warn(`❌ OSRM endpoint ${currentIndex + 1} failed with status ${response.status}: ${response.statusText}`);
            continue;
          }

          const data = await response.json();

          if (data.code !== 'Ok' || !data.routes || data.routes.length === 0) {
            console.warn(`❌ OSRM endpoint ${currentIndex + 1} returned no routes: ${data.message || 'Unknown error'}`);
            continue;
          }

          // Success! Update current endpoint for next request
          this.currentEndpointIndex = currentIndex;

          const route = data.routes[0];
          const routeCoordinates = this.extractCoordinates(route.geometry);

          console.log(`✅ OSRM routing successful: ${route.distance}m, ${Math.round(route.duration/60)}min`);

          return {
            coordinates: routeCoordinates,
            distance: route.distance,
            duration: route.duration,
            steps: this.extractSteps(route.legs),
            geometry: route.geometry
          };
        } catch (error) {
          console.warn(`❌ OSRM endpoint ${currentIndex + 1} error:`, error);
          continue;
        }
      }

      // Wait before retrying all endpoints
      if (attempt < this.MAX_RETRIES - 1) {
        console.log(`⏳ Waiting ${this.RETRY_DELAY}ms before retry attempt ${attempt + 2}...`);
        await this.delay(this.RETRY_DELAY);
      }
    }

    console.warn('❌ All OSRM endpoints failed, trying GraphHopper...');

    // Try GraphHopper as fallback
    const graphHopperRoute = await this.tryGraphHopperRouting(start, end, opts);
    if (graphHopperRoute) {
      return graphHopperRoute;
    }

    console.error('❌ All routing services failed, falling back to straight line route');
    return this.createFallbackRoute(start, end);
  }

  /**
   * Try GraphHopper as alternative routing service
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @param options - Routing options
   * @returns Promise with route information
   */
  private async tryGraphHopperRouting(
    start: RouteCoordinate,
    end: RouteCoordinate,
    options: RoutingOptions = {}
  ): Promise<RouteResponse | null> {
    try {
      console.log('🔄 Trying GraphHopper routing as fallback...');

      // GraphHopper free API endpoint (requires API key for production)
      const baseUrl = 'https://graphhopper.com/api/1/route';
      const params = new URLSearchParams({
        point: `${start.lat},${start.lng}`,
        point: `${end.lat},${end.lng}`,
        vehicle: 'car',
        locale: 'en',
        instructions: 'true',
        calc_points: 'true',
        key: 'your-free-api-key' // Replace with actual free API key
      });

      const response = await this.fetchWithTimeout(`${baseUrl}?${params}`, {
        headers: {
          'User-Agent': 'SheMove-App/1.0',
          'Accept': 'application/json'
        }
      }, 10000);

      if (!response.ok) {
        console.warn(`❌ GraphHopper failed with status ${response.status}`);
        return null;
      }

      const data = await response.json();

      if (!data.paths || data.paths.length === 0) {
        console.warn('❌ GraphHopper returned no paths');
        return null;
      }

      const path = data.paths[0];
      console.log(`✅ GraphHopper routing successful: ${path.distance}m, ${Math.round(path.time/60000)}min`);

      return {
        coordinates: this.decodePolyline(path.points),
        distance: path.distance,
        duration: path.time / 1000, // Convert from milliseconds
        steps: this.extractGraphHopperInstructions(path.instructions),
        geometry: path.points
      };
    } catch (error) {
      console.warn('❌ GraphHopper routing error:', error);
      return null;
    }
  }

  /**
   * Simple polyline decoder for GraphHopper
   * @param encoded - Encoded polyline string
   * @returns Array of coordinates
   */
  private decodePolyline(encoded: string): RouteCoordinate[] {
    const coordinates: RouteCoordinate[] = [];
    let index = 0;
    let lat = 0;
    let lng = 0;

    while (index < encoded.length) {
      let b: number;
      let shift = 0;
      let result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const deltaLat = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lat += deltaLat;

      shift = 0;
      result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const deltaLng = ((result & 1) !== 0 ? ~(result >> 1) : (result >> 1));
      lng += deltaLng;

      coordinates.push({
        lat: lat / 1e5,
        lng: lng / 1e5
      });
    }

    return coordinates;
  }

  /**
   * Extract instructions from GraphHopper response
   * @param instructions - GraphHopper instructions array
   * @returns Array of route steps
   */
  private extractGraphHopperInstructions(instructions: any[]): RouteStep[] {
    if (!instructions) return [];

    return instructions.map(instruction => ({
      instruction: instruction.text || 'Continue',
      distance: instruction.distance || 0,
      duration: (instruction.time || 0) / 1000,
      coordinates: []
    }));
  }

  /**
   * Get multiple route alternatives using different providers
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @returns Promise with array of route alternatives
   */
  async getRouteAlternatives(
    start: RouteCoordinate,
    end: RouteCoordinate
  ): Promise<RouteResponse[]> {
    const routes: RouteResponse[] = [];

    try {
      // Try OSRM first
      const osrmRoute = await this.getRoute(start, end, { alternatives: true });
      if (osrmRoute) routes.push(osrmRoute);

      // Try GraphHopper as alternative
      const graphHopperRoute = await this.tryGraphHopperRouting(start, end);
      if (graphHopperRoute) routes.push(graphHopperRoute);

      // Add fallback if no routes found
      if (routes.length === 0) {
        const fallback = this.createFallbackRoute(start, end);
        if (fallback) routes.push(fallback);
      }

      return routes;
    } catch (error) {
      console.error('Error getting route alternatives:', error);
      const fallback = this.createFallbackRoute(start, end);
      return fallback ? [fallback] : [];
    }
  }

  /**
   * Extract coordinates from OSRM geometry
   * @param geometry - OSRM geometry object
   * @returns Array of route coordinates
   */
  private extractCoordinates(geometry: any): RouteCoordinate[] {
    if (!geometry || !geometry.coordinates) {
      return [];
    }

    return geometry.coordinates.map((coord: number[]) => ({
      lng: coord[0],
      lat: coord[1]
    }));
  }

  /**
   * Extract route steps from OSRM legs
   * @param legs - OSRM route legs
   * @returns Array of route steps
   */
  private extractSteps(legs: any[]): RouteStep[] {
    if (!legs || legs.length === 0) {
      return [];
    }

    const steps: RouteStep[] = [];
    
    legs.forEach(leg => {
      if (leg.steps) {
        leg.steps.forEach((step: any) => {
          steps.push({
            instruction: step.maneuver?.instruction || 'Continue',
            distance: step.distance || 0,
            duration: step.duration || 0,
            coordinates: this.extractCoordinates(step.geometry)
          });
        });
      }
    });

    return steps;
  }

  /**
   * Create fallback straight-line route when OSRM fails
   * @param start - Starting coordinates
   * @param end - Ending coordinates
   * @returns Fallback route response
   */
  private createFallbackRoute(start: RouteCoordinate, end: RouteCoordinate): RouteResponse {
    // Create intermediate points for a more natural curve
    const fallbackCoordinates: RouteCoordinate[] = [start];

    const latDiff = end.lat - start.lat;
    const lngDiff = end.lng - start.lng;

    // Add 3-4 intermediate points with slight curve for more natural appearance
    for (let i = 1; i <= 3; i++) {
      const progress = i / 4;
      const curveFactor = Math.sin(progress * Math.PI) * 0.002;

      fallbackCoordinates.push({
        lat: start.lat + latDiff * progress + curveFactor,
        lng: start.lng + lngDiff * progress + (curveFactor * 0.5)
      });
    }

    fallbackCoordinates.push(end);

    // Estimate distance using Haversine formula
    const distance = this.calculateDistance(start, end) * 1000; // Convert to meters
    const estimatedDuration = (distance / 1000) * 60; // Rough estimate: 1 km per minute in city

    return {
      coordinates: fallbackCoordinates,
      distance,
      duration: estimatedDuration,
      steps: [{
        instruction: 'Head towards destination',
        distance,
        duration: estimatedDuration,
        coordinates: fallbackCoordinates
      }],
      geometry: 'fallback'
    };
  }

  /**
   * Fetch with timeout to prevent hanging requests
   * @param url - URL to fetch
   * @param options - Fetch options
   * @param timeout - Timeout in milliseconds
   * @returns Promise with response
   */
  private async fetchWithTimeout(url: string, options: RequestInit, timeout: number): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Delay execution for specified milliseconds
   * @param ms - Milliseconds to delay
   * @returns Promise that resolves after delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Calculate distance between two points using Haversine formula
   * @param point1 - Starting point
   * @param point2 - Ending point
   * @returns Distance in kilometers
   */
  private calculateDistance(point1: RouteCoordinate, point2: RouteCoordinate): number {
    const R = 6371; // Earth's radius in kilometers
    const lat1Rad = this.toRadians(point1.lat);
    const lat2Rad = this.toRadians(point2.lat);
    const deltaLatRad = this.toRadians(point2.lat - point1.lat);
    const deltaLngRad = this.toRadians(point2.lng - point1.lng);

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Simplify route coordinates to reduce complexity while maintaining shape
   * @param coordinates - Original coordinates
   * @param tolerance - Simplification tolerance
   * @returns Simplified coordinates
   */
  simplifyRoute(coordinates: RouteCoordinate[], tolerance: number = 0.0001): RouteCoordinate[] {
    if (coordinates.length <= 2) return coordinates;
    
    // Simple Douglas-Peucker-like simplification
    const simplified: RouteCoordinate[] = [coordinates[0]];
    
    for (let i = 1; i < coordinates.length - 1; i++) {
      const prev = coordinates[i - 1];
      const current = coordinates[i];
      const next = coordinates[i + 1];
      
      // Calculate perpendicular distance from current point to line between prev and next
      const distance = this.perpendicularDistance(current, prev, next);
      
      if (distance > tolerance) {
        simplified.push(current);
      }
    }
    
    simplified.push(coordinates[coordinates.length - 1]);
    return simplified;
  }

  /**
   * Calculate perpendicular distance from point to line
   */
  private perpendicularDistance(point: RouteCoordinate, lineStart: RouteCoordinate, lineEnd: RouteCoordinate): number {
    const A = point.lat - lineStart.lat;
    const B = point.lng - lineStart.lng;
    const C = lineEnd.lat - lineStart.lat;
    const D = lineEnd.lng - lineStart.lng;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return Math.sqrt(A * A + B * B);
    
    const param = dot / lenSq;
    
    let xx: number, yy: number;
    
    if (param < 0) {
      xx = lineStart.lat;
      yy = lineStart.lng;
    } else if (param > 1) {
      xx = lineEnd.lat;
      yy = lineEnd.lng;
    } else {
      xx = lineStart.lat + param * C;
      yy = lineStart.lng + param * D;
    }
    
    const dx = point.lat - xx;
    const dy = point.lng - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
  }
}

export const routingService = new RoutingService();
