/**
 * Search History Service
 * Manages user search patterns, suggestions, and favorite locations
 * Provides privacy controls and automatic cleanup
 */

import { supabase, SearchHistory, FavoriteLocation, RecentDestination, SearchSuggestion } from '../lib/supabase';
import { SearchResult } from './geocodingService';

export interface SearchHistoryEntry {
  id?: string;
  searchQuery: string;
  selectedResult?: SearchResult;
  resultAddress?: string;
  resultCoordinates?: [number, number];
  searchContext?: 'pickup' | 'destination';
  locationContext?: [number, number];
  resultClicked?: boolean;
}

export interface FavoriteLocationEntry {
  id?: string;
  label: string;
  address: string;
  coordinates: [number, number];
  isPrimary?: boolean;
  iconName?: string;
}

export class SearchHistoryService {
  private static instance: SearchHistoryService;
  private userId: string | null = null;

  private constructor() {}

  public static getInstance(): SearchHistoryService {
    if (!SearchHistoryService.instance) {
      SearchHistoryService.instance = new SearchHistoryService();
    }
    return SearchHistoryService.instance;
  }

  /**
   * Set the current user ID for all operations
   */
  public setUserId(userId: string | null) {
    this.userId = userId;
  }

  /**
   * Store a search query in history
   */
  public async storeSearch(entry: SearchHistoryEntry): Promise<void> {
    if (!this.userId || !supabase) {
      console.warn('Search history: User not authenticated or Supabase not configured');
      return;
    }

    try {
      const { error } = await supabase
        .from('search_history')
        .insert({
          user_id: this.userId,
          search_query: entry.searchQuery,
          selected_result: entry.selectedResult || null,
          result_address: entry.resultAddress || null,
          result_coordinates: entry.resultCoordinates ? `(${entry.resultCoordinates[0]},${entry.resultCoordinates[1]})` : null,
          search_context: entry.searchContext || null,
          location_context: entry.locationContext ? `(${entry.locationContext[0]},${entry.locationContext[1]})` : null,
          result_clicked: entry.resultClicked || false,
        });

      if (error) {
        console.error('Error storing search history:', error);
      }
    } catch (error) {
      console.error('Error in storeSearch:', error);
    }
  }

  /**
   * Mark a search result as clicked/selected
   */
  public async markResultClicked(searchQuery: string, result: SearchResult): Promise<void> {
    if (!this.userId || !supabase) return;

    try {
      // Update the most recent search with this query
      const { error } = await supabase
        .from('search_history')
        .update({
          result_clicked: true,
          selected_result: result,
          result_address: result.display_name,
          result_coordinates: `(${parseFloat(result.lat)},${parseFloat(result.lon)})`,
        })
        .eq('user_id', this.userId)
        .eq('search_query', searchQuery)
        .order('search_timestamp', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error marking result as clicked:', error);
      }
    } catch (error) {
      console.error('Error in markResultClicked:', error);
    }
  }

  /**
   * Get recent search history for the user
   */
  public async getRecentSearches(limit: number = 10): Promise<SearchHistory[]> {
    if (!this.userId || !supabase) return [];

    try {
      const { data, error } = await supabase
        .from('search_history')
        .select('*')
        .eq('user_id', this.userId)
        .eq('result_clicked', true)
        .order('search_timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        // Check if table doesn't exist
        if (error.code === '42P01') {
          console.warn('Search history table not yet created. Please run database migrations.');
          return [];
        }
        console.error('Error fetching recent searches:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getRecentSearches:', error);
      return [];
    }
  }

  /**
   * Get search suggestions based on query
   */
  public async getSearchSuggestions(query: string, limit: number = 5): Promise<SearchSuggestion[]> {
    if (!this.userId || !supabase || query.length < 2) return [];

    try {
      const { data, error } = await supabase
        .rpc('get_search_suggestions', {
          p_user_id: this.userId,
          p_query: query,
          p_limit: limit,
        });

      if (error) {
        console.error('Error fetching search suggestions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getSearchSuggestions:', error);
      return [];
    }
  }

  /**
   * Add or update a favorite location
   */
  public async addFavoriteLocation(location: FavoriteLocationEntry): Promise<void> {
    if (!this.userId || !supabase) return;

    try {
      const { error } = await supabase
        .from('favorite_locations')
        .upsert({
          user_id: this.userId,
          label: location.label,
          address: location.address,
          coordinates: location.coordinates,
          is_primary: location.isPrimary || false,
          icon_name: location.iconName || 'location',
        }, {
          onConflict: 'user_id,label'
        });

      if (error) {
        console.error('Error adding favorite location:', error);
      }
    } catch (error) {
      console.error('Error in addFavoriteLocation:', error);
    }
  }

  /**
   * Get user's favorite locations
   */
  public async getFavoriteLocations(): Promise<FavoriteLocation[]> {
    if (!this.userId || !supabase) return [];

    try {
      const { data, error } = await supabase
        .from('favorite_locations')
        .select('*')
        .eq('user_id', this.userId)
        .order('is_primary', { ascending: false })
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching favorite locations:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getFavoriteLocations:', error);
      return [];
    }
  }

  /**
   * Remove a favorite location
   */
  public async removeFavoriteLocation(locationId: string): Promise<void> {
    if (!this.userId || !supabase) return;

    try {
      const { error } = await supabase
        .from('favorite_locations')
        .delete()
        .eq('id', locationId)
        .eq('user_id', this.userId);

      if (error) {
        console.error('Error removing favorite location:', error);
      }
    } catch (error) {
      console.error('Error in removeFavoriteLocation:', error);
    }
  }

  /**
   * Update or create recent destination
   */
  public async updateRecentDestination(address: string, coordinates: [number, number]): Promise<void> {
    if (!this.userId || !supabase) return;

    try {
      // Check if destination already exists
      const { data: existing } = await supabase
        .from('recent_destinations')
        .select('*')
        .eq('user_id', this.userId)
        .eq('address', address)
        .single();

      if (existing) {
        // Update existing destination
        const { error } = await supabase
          .from('recent_destinations')
          .update({
            visit_count: existing.visit_count + 1,
            last_visited: new Date().toISOString(),
          })
          .eq('id', existing.id);

        if (error) {
          console.error('Error updating recent destination:', error);
        }
      } else {
        // Create new destination
        const { error } = await supabase
          .from('recent_destinations')
          .insert({
            user_id: this.userId,
            address,
            coordinates: `(${coordinates[0]},${coordinates[1]})`,
            visit_count: 1,
            last_visited: new Date().toISOString(),
            first_visited: new Date().toISOString(),
          });

        if (error) {
          console.error('Error creating recent destination:', error);
        }
      }
    } catch (error) {
      console.error('Error in updateRecentDestination:', error);
    }
  }

  /**
   * Get recent destinations
   */
  public async getRecentDestinations(limit: number = 5): Promise<RecentDestination[]> {
    if (!this.userId || !supabase) return [];

    try {
      const { data, error } = await supabase
        .from('recent_destinations')
        .select('*')
        .eq('user_id', this.userId)
        .order('visit_count', { ascending: false })
        .order('last_visited', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recent destinations:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getRecentDestinations:', error);
      return [];
    }
  }

  /**
   * Clear all search history for the user
   */
  public async clearSearchHistory(): Promise<void> {
    if (!this.userId || !supabase) return;

    try {
      const { error } = await supabase
        .from('search_history')
        .delete()
        .eq('user_id', this.userId);

      if (error) {
        console.error('Error clearing search history:', error);
      }
    } catch (error) {
      console.error('Error in clearSearchHistory:', error);
    }
  }

  /**
   * Get search analytics for the user (optional)
   */
  public async getSearchAnalytics(): Promise<{
    totalSearches: number;
    clickedResults: number;
    favoriteLocations: number;
    recentDestinations: number;
  }> {
    if (!this.userId || !supabase) {
      return { totalSearches: 0, clickedResults: 0, favoriteLocations: 0, recentDestinations: 0 };
    }

    try {
      const [searchCount, clickedCount, favCount, recentCount] = await Promise.all([
        supabase.from('search_history').select('id', { count: 'exact' }).eq('user_id', this.userId),
        supabase.from('search_history').select('id', { count: 'exact' }).eq('user_id', this.userId).eq('result_clicked', true),
        supabase.from('favorite_locations').select('id', { count: 'exact' }).eq('user_id', this.userId),
        supabase.from('recent_destinations').select('id', { count: 'exact' }).eq('user_id', this.userId),
      ]);

      return {
        totalSearches: searchCount.count || 0,
        clickedResults: clickedCount.count || 0,
        favoriteLocations: favCount.count || 0,
        recentDestinations: recentCount.count || 0,
      };
    } catch (error) {
      console.error('Error in getSearchAnalytics:', error);
      return { totalSearches: 0, clickedResults: 0, favoriteLocations: 0, recentDestinations: 0 };
    }
  }
}

// Export singleton instance
export const searchHistoryService = SearchHistoryService.getInstance();
