/**
 * Smart Address Service - Improves UX for house number searches
 * Provides intelligent suggestions and address completion for South African addresses
 */

import { geocodingService, SearchResult } from './geocodingService';

export interface SmartSuggestion {
  id: string;
  displayText: string;
  secondaryText: string;
  fullAddress: string;
  coordinates: { lat: number; lon: number };
  confidence: 'high' | 'medium' | 'low';
  type: 'exact' | 'interpolated' | 'nearby';
  icon: 'house' | 'building' | 'street' | 'area';
}

export interface AddressContext {
  userLocation?: { lat: number; lon: number };
  recentSearches: string[];
  commonAreas: string[];
}

class SmartAddressService {
  private recentSearches: Map<string, SearchResult[]> = new Map();
  private popularAddresses: Map<string, number> = new Map();
  
  // Common South African address patterns
  private readonly commonStreetTypes = [
    'road', 'street', 'avenue', 'drive', 'lane', 'way', 'close', 'crescent',
    'place', 'square', 'circle', 'court', 'gardens', 'park', 'ridge'
  ];

  private readonly majorSACities = [
    'johannesburg', 'cape town', 'durban', 'pretoria', 'port elizabeth',
    'bloemfontein', 'sandton', 'rosebank', 'midrand', 'randburg', 'roodepoort'
  ];

  /**
   * Enhanced search with smart suggestions for house numbers
   */
  async getSmartSuggestions(
    query: string, 
    context: AddressContext = { recentSearches: [], commonAreas: [] }
  ): Promise<SmartSuggestion[]> {
    if (!query.trim()) return [];

    const suggestions: SmartSuggestion[] = [];
    
    // 1. Try exact search first
    const exactResults = await this.getExactMatches(query);
    suggestions.push(...exactResults);

    // 2. If house number query with poor results, add smart suggestions
    if (this.isHouseNumberQuery(query) && exactResults.length < 2) {
      const smartResults = await this.generateHouseNumberSuggestions(query, context);
      suggestions.push(...smartResults);
    }

    // 3. Add contextual suggestions
    const contextualResults = await this.getContextualSuggestions(query, context);
    suggestions.push(...contextualResults);

    // 4. Remove duplicates and rank by relevance
    return this.rankAndDeduplicate(suggestions, query);
  }

  /**
   * Get exact matches from geocoding service
   */
  private async getExactMatches(query: string): Promise<SmartSuggestion[]> {
    try {
      const response = await geocodingService.searchLocationsEnhanced(query, 5);
      
      return response.results.map(result => ({
        id: `exact_${result.place_id}`,
        displayText: this.formatPrimaryText(result),
        secondaryText: this.formatSecondaryText(result),
        fullAddress: result.display_name,
        coordinates: { lat: parseFloat(result.lat), lon: parseFloat(result.lon) },
        confidence: result.address?.house_number ? 'high' : 'medium',
        type: result.address?.house_number ? 'exact' : 'nearby',
        icon: this.getAddressIcon(result)
      }));
    } catch (error) {
      console.error('Exact match search failed:', error);
      return [];
    }
  }

  /**
   * Generate smart suggestions for house number queries
   */
  private async generateHouseNumberSuggestions(
    query: string, 
    context: AddressContext
  ): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];
    const houseNumberMatch = query.match(/^(\d+)\s+(.+)/);
    
    if (!houseNumberMatch) return suggestions;
    
    const [, houseNumber, streetPart] = houseNumberMatch;
    
    // Strategy 1: Search for the street without house number
    const streetResults = await this.searchStreetOnly(streetPart);
    
    for (const streetResult of streetResults) {
      // Create interpolated suggestions
      suggestions.push({
        id: `interpolated_${streetResult.place_id}_${houseNumber}`,
        displayText: `${houseNumber} ${this.extractStreetName(streetResult)}`,
        secondaryText: `${this.extractArea(streetResult)} (approximate location)`,
        fullAddress: `${houseNumber} ${streetResult.display_name}`,
        coordinates: { 
          lat: parseFloat(streetResult.lat), 
          lon: parseFloat(streetResult.lon) 
        },
        confidence: 'medium',
        type: 'interpolated',
        icon: 'house'
      });
    }

    // Strategy 2: Suggest nearby known addresses
    const nearbyKnown = await this.findNearbyKnownAddresses(streetPart, context);
    suggestions.push(...nearbyKnown);

    return suggestions;
  }

  /**
   * Search for street without house number
   */
  private async searchStreetOnly(streetPart: string): Promise<SearchResult[]> {
    try {
      const response = await geocodingService.searchLocationsEnhanced(streetPart, 3);
      return response.results.filter(result => 
        result.type === 'highway' || 
        result.type === 'residential' ||
        result.display_name.toLowerCase().includes('road') ||
        result.display_name.toLowerCase().includes('street') ||
        result.display_name.toLowerCase().includes('avenue')
      );
    } catch (error) {
      console.error('Street search failed:', error);
      return [];
    }
  }

  /**
   * Get contextual suggestions based on user context
   */
  private async getContextualSuggestions(
    query: string, 
    context: AddressContext
  ): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];
    
    // Add city context if missing
    if (!this.hasCity(query)) {
      const cityEnhanced = await this.addCityContext(query, context);
      suggestions.push(...cityEnhanced);
    }

    // Add recent searches context
    const recentMatches = this.findRecentMatches(query, context.recentSearches);
    suggestions.push(...recentMatches);

    return suggestions;
  }

  /**
   * Add city context to queries
   */
  private async addCityContext(query: string, context: AddressContext): Promise<SmartSuggestion[]> {
    const suggestions: SmartSuggestion[] = [];
    
    // Try with major SA cities
    const citiesToTry = context.userLocation ? 
      this.getNearestCities(context.userLocation) : 
      ['johannesburg', 'cape town', 'durban'];

    for (const city of citiesToTry.slice(0, 2)) {
      try {
        const cityQuery = `${query}, ${city}`;
        const response = await geocodingService.searchLocationsEnhanced(cityQuery, 2);
        
        response.results.forEach(result => {
          suggestions.push({
            id: `city_${result.place_id}`,
            displayText: this.formatPrimaryText(result),
            secondaryText: `${city.charAt(0).toUpperCase() + city.slice(1)} area`,
            fullAddress: result.display_name,
            coordinates: { lat: parseFloat(result.lat), lon: parseFloat(result.lon) },
            confidence: 'medium',
            type: 'nearby',
            icon: this.getAddressIcon(result)
          });
        });
      } catch (error) {
        // Continue with next city
      }
    }

    return suggestions;
  }

  /**
   * Find nearby known addresses with house numbers
   */
  private async findNearbyKnownAddresses(
    streetPart: string, 
    context: AddressContext
  ): Promise<SmartSuggestion[]> {
    // This would ideally connect to a database of known addresses
    // For now, we'll use common business addresses as examples
    const knownAddresses = [
      { number: '1', street: 'sandton drive', area: 'sandton' },
      { number: '44', street: 'stanley avenue', area: 'johannesburg' },
      { number: '88', street: 'strand street', area: 'cape town' }
    ];

    const suggestions: SmartSuggestion[] = [];
    const lowerStreet = streetPart.toLowerCase();

    for (const known of knownAddresses) {
      if (known.street.includes(lowerStreet) || lowerStreet.includes(known.street)) {
        suggestions.push({
          id: `known_${known.number}_${known.street}`,
          displayText: `${known.number} ${known.street.split(' ').map(w => 
            w.charAt(0).toUpperCase() + w.slice(1)).join(' ')}`,
          secondaryText: `${known.area} (known address)`,
          fullAddress: `${known.number} ${known.street}, ${known.area}`,
          coordinates: { lat: -26.1076, lon: 28.0567 }, // Placeholder
          confidence: 'high',
          type: 'exact',
          icon: 'building'
        });
      }
    }

    return suggestions;
  }

  /**
   * Utility methods
   */
  private isHouseNumberQuery(query: string): boolean {
    return /^\d+\s+/.test(query.trim());
  }

  private hasCity(query: string): boolean {
    const lowerQuery = query.toLowerCase();
    return this.majorSACities.some(city => lowerQuery.includes(city));
  }

  private formatPrimaryText(result: SearchResult): string {
    if (result.address?.house_number && result.address?.road) {
      return `${result.address.house_number} ${result.address.road}`;
    }
    
    const parts = result.display_name.split(',');
    return parts[0].trim();
  }

  private formatSecondaryText(result: SearchResult): string {
    const parts = result.display_name.split(',');
    if (parts.length > 1) {
      return parts.slice(1, 3).join(',').trim();
    }
    return result.address?.city || result.address?.suburb || 'South Africa';
  }

  private extractStreetName(result: SearchResult): string {
    return result.address?.road || result.display_name.split(',')[0];
  }

  private extractArea(result: SearchResult): string {
    return result.address?.suburb || result.address?.city || 'Unknown area';
  }

  private getAddressIcon(result: SearchResult): 'house' | 'building' | 'street' | 'area' {
    if (result.address?.house_number) return 'house';
    if (result.type === 'amenity' || result.type === 'shop') return 'building';
    if (result.type === 'highway' || result.type === 'residential') return 'street';
    return 'area';
  }

  private getNearestCities(userLocation: { lat: number; lon: number }): string[] {
    // Simple distance-based city selection (can be enhanced)
    if (userLocation.lat < -30) return ['cape town', 'stellenbosch'];
    if (userLocation.lat > -25) return ['johannesburg', 'pretoria', 'sandton'];
    return ['durban', 'pietermaritzburg'];
  }

  private findRecentMatches(query: string, recentSearches: string[]): SmartSuggestion[] {
    // Implementation for recent search matching
    return [];
  }

  private rankAndDeduplicate(suggestions: SmartSuggestion[], query: string): SmartSuggestion[] {
    // Remove duplicates based on coordinates proximity
    const unique = suggestions.filter((suggestion, index, self) => 
      index === self.findIndex(s => 
        Math.abs(s.coordinates.lat - suggestion.coordinates.lat) < 0.001 &&
        Math.abs(s.coordinates.lon - suggestion.coordinates.lon) < 0.001
      )
    );

    // Sort by confidence and relevance
    return unique.sort((a, b) => {
      const confidenceOrder = { high: 3, medium: 2, low: 1 };
      const typeOrder = { exact: 3, interpolated: 2, nearby: 1 };
      
      const aScore = confidenceOrder[a.confidence] + typeOrder[a.type];
      const bScore = confidenceOrder[b.confidence] + typeOrder[b.type];
      
      return bScore - aScore;
    }).slice(0, 8); // Limit to 8 suggestions
  }
}

export const smartAddressService = new SmartAddressService();
