/**
 * Trip Booking Service for SheMove
 * Handles trip creation, booking logic, and database operations
 */

import { supabase } from '../lib/supabase';
import { Trip, TripStatus, RideType } from '../shared/types';
import { Driver } from './realDriverService';
import { FareBreakdown } from './fareService';
import { Coordinates } from './distanceService';

export interface BookingRequest {
  pickupLocation: {
    lat: number;
    lng: number;
    address: string;
    shortAddress?: string;
  };
  destination: {
    lat: number;
    lng: number;
    address: string;
    shortAddress?: string;
  };
  rideType: RideType;
  driver: Driver;
  fare: FareBreakdown;
  scheduledTime?: string; // ISO string for future bookings
  passengerNotes?: string;
}

export interface BookingResult {
  success: boolean;
  trip?: Trip;
  error?: string;
  errorCode?: 'NETWORK_ERROR' | 'DATABASE_ERROR' | 'VALIDATION_ERROR' | 'DRIVER_UNAVAILABLE';
}

export interface TripBookingCallbacks {
  onBookingStart?: () => void;
  onBookingSuccess?: (trip: Trip) => void;
  onBookingError?: (error: string, errorCode?: string) => void;
  onDriverUnavailable?: () => void;
}

class TripBookingService {
  private readonly BOOKING_TIMEOUT = 30000; // 30 seconds
  private callbacks: TripBookingCallbacks = {};

  /**
   * Set callbacks for booking events
   */
  setCallbacks(callbacks: TripBookingCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Create a new trip booking
   */
  async createTrip(request: BookingRequest, userId: string): Promise<BookingResult> {
    try {
      // Validate request
      const validationError = this.validateBookingRequest(request);
      if (validationError) {
        return {
          success: false,
          error: validationError,
          errorCode: 'VALIDATION_ERROR'
        };
      }

      // Notify booking start
      this.callbacks.onBookingStart?.();

      // Check if driver is still available
      const isDriverAvailable = await this.checkDriverAvailability(request.driver.id);
      if (!isDriverAvailable) {
        this.callbacks.onDriverUnavailable?.();
        return {
          success: false,
          error: 'Selected driver is no longer available. Please select another driver.',
          errorCode: 'DRIVER_UNAVAILABLE'
        };
      }

      // Create trip in database
      const trip = await this.insertTripToDatabase(request, userId);
      
      if (!trip) {
        const error = 'Failed to create trip in database';
        this.callbacks.onBookingError?.(error, 'DATABASE_ERROR');
        return {
          success: false,
          error,
          errorCode: 'DATABASE_ERROR'
        };
      }

      // Send trip request to driver (async - don't wait for response)
      this.sendTripRequestToDriver(trip.id, request.driver.id).catch(error => {
        console.warn('Failed to send trip request to driver:', error);
        // Trip is still created, just driver notification failed
      });

      // Notify success
      this.callbacks.onBookingSuccess?.(trip);

      return {
        success: true,
        trip
      };

    } catch (error) {
      console.error('Trip booking error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      this.callbacks.onBookingError?.(errorMessage, 'NETWORK_ERROR');
      
      return {
        success: false,
        error: errorMessage,
        errorCode: 'NETWORK_ERROR'
      };
    }
  }

  /**
   * Validate booking request data
   */
  private validateBookingRequest(request: BookingRequest): string | null {
    if (!request.pickupLocation?.lat || !request.pickupLocation?.lng) {
      return 'Invalid pickup location coordinates';
    }

    if (!request.destination?.lat || !request.destination?.lng) {
      return 'Invalid destination coordinates';
    }

    if (!request.pickupLocation?.address?.trim()) {
      return 'Pickup address is required';
    }

    if (!request.destination?.address?.trim()) {
      return 'Destination address is required';
    }

    if (!['SheRide', 'ShePool', 'SheXL'].includes(request.rideType)) {
      return 'Invalid ride type';
    }

    if (!request.driver?.id) {
      return 'Driver selection is required';
    }

    if (!request.fare?.totalFare || request.fare.totalFare <= 0) {
      return 'Invalid fare amount';
    }

    // Validate scheduled time if provided
    if (request.scheduledTime) {
      const scheduledDate = new Date(request.scheduledTime);
      const now = new Date();
      
      if (scheduledDate <= now) {
        return 'Scheduled time must be in the future';
      }

      // Don't allow scheduling more than 7 days in advance
      const maxAdvanceTime = new Date(now.getTime() + (7 * 24 * 60 * 60 * 1000));
      if (scheduledDate > maxAdvanceTime) {
        return 'Cannot schedule rides more than 7 days in advance';
      }
    }

    return null;
  }

  /**
   * Check if driver is still available for booking
   */
  private async checkDriverAvailability(driverId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('driver_availability')
        .select('status, is_online')
        .eq('driver_id', driverId)
        .single();

      if (error) {
        console.warn('Could not check driver availability:', error);
        // Assume available if we can't check (fail gracefully)
        return true;
      }

      return data?.is_online === true && data?.status === 'available';
    } catch (error) {
      console.warn('Driver availability check failed:', error);
      // Assume available if check fails
      return true;
    }
  }

  /**
   * Insert trip into database
   */
  private async insertTripToDatabase(request: BookingRequest, userId: string): Promise<Trip | null> {
    try {
      const now = new Date().toISOString();

      // Prepare trip data
      const tripData = {
        passenger_id: userId,
        pickup_location: request.pickupLocation.address,
        pickup_coordinates: `POINT(${request.pickupLocation.lng} ${request.pickupLocation.lat})`,
        destination_location: request.destination.address,
        destination_coordinates: `POINT(${request.destination.lng} ${request.destination.lat})`,
        ride_type: request.rideType,
        status: 'requested' as TripStatus,
        fare_amount: request.fare.totalFare,
        distance_km: request.fare.distanceFare / this.getPerKmRate(request.rideType), // Reverse calculate
        duration_minutes: Math.round(request.fare.timeFare / this.getPerMinuteRate(request.rideType)), // Reverse calculate
        surge_multiplier: request.fare.surgeMultiplier,
        scheduled_time: request.scheduledTime || null,
        pickup_address_short: request.pickupLocation.shortAddress || this.shortenAddress(request.pickupLocation.address),
        destination_address_short: request.destination.shortAddress || this.shortenAddress(request.destination.address),
        passenger_notes: request.passengerNotes || null,
        created_at: now,
        updated_at: now
      };

      const { data, error } = await supabase
        .from('trips')
        .insert(tripData)
        .select('*')
        .single();

      if (error) {
        console.error('Database insert error:', error);
        return null;
      }

      // Transform database result to Trip interface
      return this.transformDatabaseTrip(data);
    } catch (error) {
      console.error('Failed to insert trip:', error);
      return null;
    }
  }

  /**
   * Send trip request to driver
   */
  private async sendTripRequestToDriver(tripId: string, driverId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('send_trip_request_to_driver', {
        trip_id_param: tripId,
        driver_id_param: driverId,
        timeout_minutes: 2
      });

      if (error) {
        console.error('Failed to send trip request:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Trip request sending error:', error);
      return false;
    }
  }

  /**
   * Transform database trip to Trip interface
   */
  private transformDatabaseTrip(dbTrip: any): Trip {
    return {
      id: dbTrip.id,
      passenger_id: dbTrip.passenger_id,
      driver_id: dbTrip.driver_id,
      pickup_location: dbTrip.pickup_location,
      pickup_coordinates: this.parseCoordinates(dbTrip.pickup_coordinates),
      destination_location: dbTrip.destination_location,
      destination_coordinates: this.parseCoordinates(dbTrip.destination_coordinates),
      ride_type: dbTrip.ride_type,
      status: dbTrip.status,
      fare_amount: Number(dbTrip.fare_amount),
      distance_km: Number(dbTrip.distance_km),
      duration_minutes: dbTrip.duration_minutes,
      scheduled_time: dbTrip.scheduled_time,
      driver_accepted_at: dbTrip.driver_accepted_at,
      driver_arrived_at: dbTrip.driver_arrived_at,
      trip_started_at: dbTrip.trip_started_at,
      completed_at: dbTrip.completed_at,
      cancelled_at: dbTrip.cancelled_at,
      cancellation_reason: dbTrip.cancellation_reason,
      cancelled_by: dbTrip.cancelled_by,
      pickup_address_short: dbTrip.pickup_address_short,
      destination_address_short: dbTrip.destination_address_short,
      driver_notes: dbTrip.driver_notes,
      passenger_notes: dbTrip.passenger_notes,
      surge_multiplier: Number(dbTrip.surge_multiplier),
      estimated_arrival_time: dbTrip.estimated_arrival_time,
      actual_pickup_time: dbTrip.actual_pickup_time,
      route_data: dbTrip.route_data,
      created_at: dbTrip.created_at,
      updated_at: dbTrip.updated_at
    };
  }

  /**
   * Parse coordinates from database POINT format
   */
  private parseCoordinates(pointString: string): Coordinates {
    // Handle POINT(lng lat) format from PostGIS
    const match = pointString.match(/POINT\(([^)]+)\)/);
    if (match) {
      const [lng, lat] = match[1].split(' ').map(Number);
      return { lat, lng };
    }

    // Fallback - assume it's already in correct format
    return { lat: 0, lng: 0 };
  }

  /**
   * Shorten address for display
   */
  private shortenAddress(fullAddress: string): string {
    // Extract main parts of address (first part before comma)
    const parts = fullAddress.split(',');
    return parts[0]?.trim() || fullAddress;
  }

  /**
   * Get per-km rate for ride type (for reverse calculation)
   */
  private getPerKmRate(rideType: RideType): number {
    const rates = {
      'SheRide': 12.50,
      'ShePool': 8.50,
      'SheXL': 18.00
    };
    return rates[rideType] || rates['SheRide'];
  }

  /**
   * Get per-minute rate for ride type (for reverse calculation)
   */
  private getPerMinuteRate(rideType: RideType): number {
    const rates = {
      'SheRide': 1.20,
      'ShePool': 0.80,
      'SheXL': 1.80
    };
    return rates[rideType] || rates['SheRide'];
  }

  /**
   * Cancel a trip
   */
  async cancelTrip(tripId: string, reason?: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('trips')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancelled_by: 'passenger',
          cancellation_reason: reason,
          updated_at: new Date().toISOString()
        })
        .eq('id', tripId);

      return !error;
    } catch (error) {
      console.error('Failed to cancel trip:', error);
      return false;
    }
  }

  /**
   * Get trip by ID
   */
  async getTripById(tripId: string): Promise<Trip | null> {
    try {
      const { data, error } = await supabase
        .from('trips')
        .select('*')
        .eq('id', tripId)
        .single();

      if (error || !data) {
        return null;
      }

      return this.transformDatabaseTrip(data);
    } catch (error) {
      console.error('Failed to get trip:', error);
      return null;
    }
  }
}

// Export singleton instance
export const tripBookingService = new TripBookingService();
export default tripBookingService;
