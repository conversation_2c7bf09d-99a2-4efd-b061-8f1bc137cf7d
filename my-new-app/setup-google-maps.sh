#!/bin/bash

# Google Maps API Setup Script for SheMove App
# Helps configure Google Maps API for legal, free-tier usage

echo "🗺️  Google Maps API Setup for SheMove"
echo "====================================="
echo ""
echo "This script will help you set up Google Maps API for legal, free usage"
echo "within the $200 monthly free tier (approximately 40,000 requests)."
echo ""

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Creating from template..."
    cp .env.example .env 2>/dev/null || echo "GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here" > .env
fi

echo "📋 Setup Steps:"
echo ""
echo "1. 🌐 Go to Google Cloud Console:"
echo "   https://console.cloud.google.com/"
echo ""
echo "2. 📁 Create or select a project:"
echo "   - Click 'Select a project' → 'New Project'"
echo "   - Name: 'SheMove-Maps' (or similar)"
echo "   - Click 'Create'"
echo ""
echo "3. 💳 Enable billing (required for free tier):"
echo "   - Go to 'Billing' in the menu"
echo "   - Link a credit card (won't be charged within free limits)"
echo "   - Set up billing alerts at $50, $100, $150"
echo ""
echo "4. 🔌 Enable required APIs:"
echo "   - Go to 'APIs & Services' → 'Library'"
echo "   - Search and enable:"
echo "     ✓ Places API"
echo "     ✓ Geocoding API"
echo "     ✓ Maps JavaScript API"
echo "     ✓ Directions API"
echo ""
echo "5. 🔑 Create API Key:"
echo "   - Go to 'APIs & Services' → 'Credentials'"
echo "   - Click 'Create Credentials' → 'API Key'"
echo "   - Copy the generated key"
echo ""
echo "6. 🔒 Restrict API Key (IMPORTANT for security):"
echo "   - Click 'Restrict Key' next to your new key"
echo "   - Application restrictions:"
echo "     • For mobile app: Select 'Android apps' or 'iOS apps'"
echo "     • For web app: Select 'HTTP referrers'"
echo "   - API restrictions:"
echo "     • Select 'Restrict key'"
echo "     • Choose only the APIs you enabled above"
echo ""

read -p "Have you completed steps 1-6? (y/n): " setup_complete

if [ "$setup_complete" != "y" ]; then
    echo ""
    echo "⏸️  Please complete the setup steps above first."
    echo "   Run this script again when you have your API key."
    exit 0
fi

echo ""
read -p "🔑 Enter your Google Maps API key: " api_key

if [ -z "$api_key" ]; then
    echo "❌ No API key provided. Exiting."
    exit 1
fi

# Validate API key format (basic check)
if [[ ! $api_key =~ ^AIza[0-9A-Za-z_-]{35}$ ]]; then
    echo "⚠️  Warning: API key format doesn't look correct."
    echo "   Google API keys usually start with 'AIza' and are 39 characters long."
    read -p "   Continue anyway? (y/n): " continue_anyway
    
    if [ "$continue_anyway" != "y" ]; then
        echo "Setup cancelled."
        exit 1
    fi
fi

# Update .env file
echo "📝 Updating .env file..."

# Create backup
cp .env .env.backup

# Update the API key
if grep -q "GOOGLE_MAPS_API_KEY=" .env; then
    # Replace existing key
    sed -i.bak "s/GOOGLE_MAPS_API_KEY=.*/GOOGLE_MAPS_API_KEY=$api_key/" .env
else
    # Add new key
    echo "GOOGLE_MAPS_API_KEY=$api_key" >> .env
fi

echo "✅ API key saved to .env file"
echo ""

# Test the API key
echo "🧪 Testing API key..."

# Create a simple test script
cat > test-google-api.js << 'EOF'
const https = require('https');
const fs = require('fs');

// Read API key from .env
const envContent = fs.readFileSync('.env', 'utf8');
const apiKeyMatch = envContent.match(/GOOGLE_MAPS_API_KEY=(.+)/);

if (!apiKeyMatch) {
    console.log('❌ API key not found in .env file');
    process.exit(1);
}

const apiKey = apiKeyMatch[1].trim();

// Test with a simple geocoding request
const testAddress = 'Sandton, Johannesburg, South Africa';
const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(testAddress)}&key=${apiKey}`;

https.get(url, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
        data += chunk;
    });
    
    res.on('end', () => {
        try {
            const result = JSON.parse(data);
            
            if (result.status === 'OK') {
                console.log('✅ API key is working!');
                console.log(`📍 Test result: ${result.results[0].formatted_address}`);
                console.log(`🌍 Coordinates: ${result.results[0].geometry.location.lat}, ${result.results[0].geometry.location.lng}`);
            } else if (result.status === 'REQUEST_DENIED') {
                console.log('❌ API key denied. Check your restrictions and billing setup.');
                console.log(`   Error: ${result.error_message || 'Unknown error'}`);
            } else if (result.status === 'OVER_QUERY_LIMIT') {
                console.log('⚠️  Query limit exceeded. Check your billing and quotas.');
            } else {
                console.log(`⚠️  API returned status: ${result.status}`);
                console.log(`   Message: ${result.error_message || 'No additional info'}`);
            }
        } catch (error) {
            console.log('❌ Failed to parse API response:', error.message);
        }
    });
}).on('error', (error) => {
    console.log('❌ Network error:', error.message);
});
EOF

# Run the test
node test-google-api.js

# Clean up test file
rm test-google-api.js

echo ""
echo "🎉 Google Maps API Setup Complete!"
echo ""
echo "📊 Free Tier Limits:"
echo "   • Monthly credit: $200"
echo "   • Places API: ~40,000 requests ($0.017 each)"
echo "   • Geocoding API: ~40,000 requests ($0.005 each)"
echo "   • Directions API: ~40,000 requests ($0.005 each)"
echo ""
echo "💡 Usage Tips:"
echo "   • Monitor usage in Google Cloud Console"
echo "   • Set up billing alerts to avoid surprises"
echo "   • Use free services (Nominatim/LocationIQ) first"
echo "   • Google API will be used as premium fallback"
echo ""
echo "🔄 Next Steps:"
echo "   1. Test your SheMove app with the new API key"
echo "   2. Monitor usage in Google Cloud Console"
echo "   3. Set up billing alerts if not done already"
echo "   4. Consider upgrading to paid tier as your app scales"
echo ""
echo "📱 Your SheMove app will now use:"
echo "   • Nominatim (free) → LocationIQ (free) → Google Maps (premium)"
echo "   • This provides the best balance of cost and quality!"
echo ""

# Check if backup was created
if [ -f ".env.backup" ]; then
    echo "💾 Backup of original .env saved as .env.backup"
fi

echo "✅ Setup complete! Your SheMove app now has access to Google Maps API."
