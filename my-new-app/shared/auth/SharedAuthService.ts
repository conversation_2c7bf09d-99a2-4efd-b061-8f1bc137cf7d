/**
 * Shared Authentication Service for SheMove Apps
 * This service can be used by both passenger and driver apps
 * Provides unified authentication with Supabase
 */

import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// Shared types
export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  phone_number?: string;
  avatar_url?: string;
  user_type: 'passenger' | 'driver' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface DriverProfile {
  id: string;
  user_id: string;
  license_number: string;
  license_expiry?: string;
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number;
  vehicle_color: string;
  vehicle_plate: string;
  vehicle_type: 'SheRide' | 'ShePool' | 'SheXL';
  verification_status: 'pending' | 'approved' | 'rejected';
  is_online: boolean;
  rating: number;
  total_trips: number;
  onboarding_completed: boolean;
}

export interface AuthUser {
  id: string;
  email: string;
  createdAt: string;
  profile?: UserProfile;
  driverProfile?: DriverProfile;
}

export interface AuthConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  appType: 'passenger' | 'driver';
}

export class SharedAuthService {
  private supabase: SupabaseClient | null = null;
  private appType: 'passenger' | 'driver';
  private initialized = false;

  constructor(config: AuthConfig) {
    this.appType = config.appType;
    this.initializeSupabase(config);
  }

  private initializeSupabase(config: AuthConfig) {
    if (!config.supabaseUrl || !config.supabaseAnonKey) {
      console.warn('Supabase configuration missing');
      return;
    }

    if (config.supabaseUrl === 'your_supabase_project_url_here' || 
        config.supabaseAnonKey === 'your_supabase_anon_key_here') {
      console.warn('Supabase configuration not properly set');
      return;
    }

    const authConfig: any = {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: Platform.OS === 'web',
    };

    // Only use AsyncStorage on native platforms
    if (Platform.OS !== 'web') {
      authConfig.storage = AsyncStorage;
    }

    this.supabase = createClient(config.supabaseUrl, config.supabaseAnonKey, {
      auth: authConfig,
      global: {
        headers: {
          'X-Client-Info': `shemove-${this.appType}-app`,
        },
      },
    });

    this.initialized = true;
  }

  // Check if service is properly configured
  isConfigured(): boolean {
    return this.initialized && this.supabase !== null;
  }

  // Get current session
  async getSession(): Promise<{ user: User | null; session: Session | null }> {
    if (!this.supabase) {
      return { user: null, session: null };
    }

    const { data, error } = await this.supabase.auth.getSession();
    if (error) {
      console.error('Error getting session:', error);
      return { user: null, session: null };
    }

    return { user: data.session?.user || null, session: data.session };
  }

  // Sign up new user
  async signUp(email: string, password: string, fullName?: string, userType: 'passenger' | 'driver' = 'passenger'): Promise<AuthUser> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { data, error } = await this.supabase.auth.signUp({
      email: email.toLowerCase().trim(),
      password: password,
      options: {
        data: {
          full_name: fullName || '',
          user_type: userType,
        },
      },
    });

    if (error) {
      throw error;
    }

    if (!data.user) {
      throw new Error('Failed to create user account');
    }

    return {
      id: data.user.id,
      email: data.user.email || '',
      createdAt: data.user.created_at,
    };
  }

  // Sign in existing user
  async signIn(email: string, password: string): Promise<AuthUser> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { data, error } = await this.supabase.auth.signInWithPassword({
      email: email.toLowerCase().trim(),
      password: password,
    });

    if (error) {
      throw error;
    }

    if (!data.user) {
      throw new Error('Failed to sign in');
    }

    // Get user profile
    const profile = await this.getUserProfile(data.user.id);
    let driverProfile: DriverProfile | undefined;

    // Get driver profile if user is a driver
    if (profile?.user_type === 'driver') {
      driverProfile = await this.getDriverProfile(data.user.id);
    }

    return {
      id: data.user.id,
      email: data.user.email || '',
      createdAt: data.user.created_at,
      profile,
      driverProfile,
    };
  }

  // Sign out
  async signOut(): Promise<void> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { error } = await this.supabase.auth.signOut();
    if (error) {
      throw error;
    }
  }

  // Forgot password
  async forgotPassword(email: string): Promise<void> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { error } = await this.supabase.auth.resetPasswordForEmail(
      email.toLowerCase().trim(),
      {
        redirectTo: `${this.appType}://reset-password`,
      }
    );

    if (error) {
      throw error;
    }
  }

  // Reset password
  async resetPassword(accessToken: string, newPassword: string): Promise<void> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { error } = await this.supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      throw error;
    }
  }

  // Get user profile
  async getUserProfile(userId: string): Promise<UserProfile | null> {
    if (!this.supabase) {
      return null;
    }

    const { data, error } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error fetching user profile:', error);
      return null;
    }

    return data;
  }

  // Get driver profile
  async getDriverProfile(userId: string): Promise<DriverProfile | null> {
    if (!this.supabase) {
      return null;
    }

    const { data, error } = await this.supabase
      .from('drivers')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching driver profile:', error);
      return null;
    }

    return data;
  }

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { data, error } = await this.supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  // Update driver profile
  async updateDriverProfile(userId: string, updates: Partial<DriverProfile>): Promise<DriverProfile> {
    if (!this.supabase) {
      throw new Error('Authentication service not configured');
    }

    const { data, error } = await this.supabase
      .from('drivers')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (user: AuthUser | null) => void) {
    if (!this.supabase) {
      return { unsubscribe: () => {} };
    }

    const { data: { subscription } } = this.supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`Auth state changed (${this.appType}):`, event, session?.user?.email);

        if (session?.user) {
          const profile = await this.getUserProfile(session.user.id);
          let driverProfile: DriverProfile | undefined;

          if (profile?.user_type === 'driver') {
            driverProfile = await this.getDriverProfile(session.user.id);
          }

          const userData: AuthUser = {
            id: session.user.id,
            email: session.user.email || '',
            createdAt: session.user.created_at,
            profile,
            driverProfile,
          };

          callback(userData);
        } else {
          callback(null);
        }
      }
    );

    return {
      unsubscribe: () => subscription.unsubscribe(),
    };
  }

  // Get Supabase client for direct access (use carefully)
  getSupabaseClient(): SupabaseClient | null {
    return this.supabase;
  }
}
