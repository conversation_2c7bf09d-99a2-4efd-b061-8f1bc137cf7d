/**
 * Shared Realtime Service for SheMove Apps
 * Handles real-time communication between passenger and driver apps
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { RealtimeMessage, Trip, DriverLocation, TripRequest } from '../types';

export interface RealtimeConfig {
  supabase: SupabaseClient;
  userId: string;
  userType: 'passenger' | 'driver';
}

export interface RealtimeCallbacks {
  onTripRequest?: (request: TripRequest) => void;
  onTripUpdate?: (trip: Trip) => void;
  onLocationUpdate?: (location: DriverLocation) => void;
  onDriverResponse?: (response: { tripId: string; driverId: string; response: string }) => void;
  onMessage?: (message: RealtimeMessage) => void;
  onError?: (error: Error) => void;
}

export class RealtimeService {
  private supabase: SupabaseClient;
  private userId: string;
  private userType: 'passenger' | 'driver';
  private subscriptions: any[] = [];
  private callbacks: RealtimeCallbacks = {};

  constructor(config: RealtimeConfig) {
    this.supabase = config.supabase;
    this.userId = config.userId;
    this.userType = config.userType;
  }

  // Set callbacks for real-time events
  setCallbacks(callbacks: RealtimeCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  // Start real-time subscriptions
  async startListening() {
    try {
      if (this.userType === 'driver') {
        await this.subscribeToDriverEvents();
      } else {
        await this.subscribeToPassengerEvents();
      }
      console.log(`Real-time listening started for ${this.userType}`);
    } catch (error) {
      console.error('Error starting real-time listening:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  // Stop all real-time subscriptions
  stopListening() {
    this.subscriptions.forEach(subscription => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    });
    this.subscriptions = [];
    console.log('Real-time listening stopped');
  }

  // Subscribe to driver-specific events
  private async subscribeToDriverEvents() {
    // Get driver ID
    const { data: driverData } = await this.supabase
      .from('drivers')
      .select('id')
      .eq('user_id', this.userId)
      .single();

    if (!driverData) {
      throw new Error('Driver profile not found');
    }

    const driverId = driverData.id;

    // Subscribe to trip requests
    const tripRequestsSubscription = this.supabase
      .channel('trip_requests')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'trip_requests',
          filter: `driver_id=eq.${driverId}`,
        },
        (payload) => {
          console.log('New trip request:', payload);
          this.callbacks.onTripRequest?.(payload.new as TripRequest);
        }
      )
      .subscribe();

    // Subscribe to trip updates for assigned trips
    const tripUpdatesSubscription = this.supabase
      .channel('trip_updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'trips',
          filter: `driver_id=eq.${driverId}`,
        },
        (payload) => {
          console.log('Trip updated:', payload);
          this.callbacks.onTripUpdate?.(payload.new as Trip);
        }
      )
      .subscribe();

    this.subscriptions.push(tripRequestsSubscription, tripUpdatesSubscription);
  }

  // Subscribe to passenger-specific events
  private async subscribeToPassengerEvents() {
    // Subscribe to trip updates for passenger's trips
    const tripUpdatesSubscription = this.supabase
      .channel('passenger_trip_updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'trips',
          filter: `passenger_id=eq.${this.userId}`,
        },
        (payload) => {
          console.log('Trip updated for passenger:', payload);
          this.callbacks.onTripUpdate?.(payload.new as Trip);
        }
      )
      .subscribe();

    // Subscribe to driver location updates for active trips
    const locationUpdatesSubscription = this.supabase
      .channel('driver_locations')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'driver_locations',
        },
        async (payload) => {
          const location = payload.new as DriverLocation;
          
          // Check if this location update is for passenger's active trip
          if (location.trip_id) {
            const { data: trip } = await this.supabase
              .from('trips')
              .select('passenger_id')
              .eq('id', location.trip_id)
              .eq('passenger_id', this.userId)
              .single();

            if (trip) {
              console.log('Driver location updated:', location);
              this.callbacks.onLocationUpdate?.(location);
            }
          }
        }
      )
      .subscribe();

    this.subscriptions.push(tripUpdatesSubscription, locationUpdatesSubscription);
  }

  // Send trip request to driver
  async sendTripRequest(tripId: string, driverId: string, timeoutMinutes: number = 2): Promise<boolean> {
    try {
      const { data, error } = await this.supabase.rpc('send_trip_request_to_driver', {
        trip_id_param: tripId,
        driver_id_param: driverId,
        timeout_minutes: timeoutMinutes,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error sending trip request:', error);
      return false;
    }
  }

  // Respond to trip request (driver only)
  async respondToTripRequest(
    tripId: string,
    response: 'accepted' | 'declined',
    declineReason?: string
  ): Promise<boolean> {
    if (this.userType !== 'driver') {
      throw new Error('Only drivers can respond to trip requests');
    }

    try {
      // Get driver ID
      const { data: driverData } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', this.userId)
        .single();

      if (!driverData) {
        throw new Error('Driver profile not found');
      }

      const { data, error } = await this.supabase.rpc('respond_to_trip_request', {
        trip_id_param: tripId,
        driver_id_param: driverData.id,
        response_param: response,
        decline_reason_param: declineReason,
      });

      if (error) {
        throw error;
      }

      // Notify callback
      this.callbacks.onDriverResponse?.({
        tripId,
        driverId: driverData.id,
        response,
      });

      return data;
    } catch (error) {
      console.error('Error responding to trip request:', error);
      return false;
    }
  }

  // Update driver location (driver only)
  async updateDriverLocation(
    lat: number,
    lng: number,
    heading?: number,
    speed?: number,
    accuracy?: number
  ): Promise<boolean> {
    if (this.userType !== 'driver') {
      throw new Error('Only drivers can update location');
    }

    try {
      // Get driver ID
      const { data: driverData } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', this.userId)
        .single();

      if (!driverData) {
        throw new Error('Driver profile not found');
      }

      const { data, error } = await this.supabase.rpc('update_driver_location', {
        driver_id_param: driverData.id,
        lat,
        lng,
        heading_param: heading,
        speed_param: speed,
        accuracy_param: accuracy,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error updating driver location:', error);
      return false;
    }
  }

  // Update trip status
  async updateTripStatus(
    tripId: string,
    status: 'accepted' | 'in_progress' | 'completed' | 'cancelled',
    notes?: string
  ): Promise<boolean> {
    try {
      const updates: any = {
        status,
        updated_at: new Date().toISOString(),
      };

      // Add timestamp for specific status changes
      switch (status) {
        case 'accepted':
          updates.driver_accepted_at = new Date().toISOString();
          break;
        case 'in_progress':
          updates.trip_started_at = new Date().toISOString();
          break;
        case 'completed':
          updates.completed_at = new Date().toISOString();
          break;
        case 'cancelled':
          updates.cancelled_at = new Date().toISOString();
          updates.cancelled_by = this.userType;
          if (notes) {
            updates.cancellation_reason = notes;
          }
          break;
      }

      // Add notes based on user type
      if (notes) {
        if (this.userType === 'driver') {
          updates.driver_notes = notes;
        } else {
          updates.passenger_notes = notes;
        }
      }

      const { error } = await this.supabase
        .from('trips')
        .update(updates)
        .eq('id', tripId);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error updating trip status:', error);
      return false;
    }
  }

  // Get nearby drivers (passenger only)
  async getNearbyDrivers(
    pickupLat: number,
    pickupLng: number,
    maxDistance: number = 15,
    rideType?: 'SheRide' | 'ShePool' | 'SheXL'
  ) {
    if (this.userType !== 'passenger') {
      throw new Error('Only passengers can search for drivers');
    }

    try {
      const { data, error } = await this.supabase.rpc('get_nearby_drivers', {
        p_pickup_lat: pickupLat,
        p_pickup_lng: pickupLng,
        p_max_distance_km: maxDistance,
        p_ride_type_filter: rideType,
      });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting nearby drivers:', error);
      return [];
    }
  }

  // Send custom message
  async sendMessage(message: RealtimeMessage): Promise<boolean> {
    try {
      // Use Supabase's broadcast feature for custom messages
      const channel = this.supabase.channel('custom_messages');
      
      await channel.send({
        type: 'broadcast',
        event: message.type,
        payload: {
          ...message,
          sender_id: this.userId,
          timestamp: new Date().toISOString(),
        },
      });

      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  }
}
