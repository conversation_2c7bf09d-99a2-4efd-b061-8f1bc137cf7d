/**
 * Shared Types for SheMove Apps
 * Common interfaces and types used by both passenger and driver apps
 */

// =====================================================
// USER & AUTHENTICATION TYPES
// =====================================================

export type UserType = 'passenger' | 'driver' | 'admin';
export type VerificationStatus = 'pending' | 'approved' | 'rejected';
export type TripStatus = 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
export type RideType = 'SheRide' | 'ShePool' | 'SheXL';
export type DriverStatus = 'offline' | 'online' | 'busy' | 'break';
export type DocumentType = 'drivers_license' | 'vehicle_registration' | 'insurance' | 'profile_photo' | 'vehicle_photo';
export type DocumentStatus = 'pending' | 'approved' | 'rejected' | 'expired';

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface UserProfile {
  id: string;
  email: string;
  full_name?: string;
  phone_number?: string;
  avatar_url?: string;
  user_type: UserType;
  created_at: string;
  updated_at: string;
}

export interface DriverProfile {
  id: string;
  user_id: string;
  license_number: string;
  license_expiry?: string;
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number;
  vehicle_color: string;
  vehicle_plate: string;
  vehicle_type: RideType;
  insurance_policy?: string;
  insurance_expiry?: string;
  verification_status: VerificationStatus;
  is_online: boolean;
  current_location?: Coordinates;
  rating: number;
  total_trips: number;
  total_earnings: number;
  acceptance_rate: number;
  cancellation_rate: number;
  average_rating: number;
  onboarding_completed: boolean;
  background_check_status: VerificationStatus;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  preferred_language: string;
  created_at: string;
  updated_at: string;
}

// =====================================================
// TRIP TYPES
// =====================================================

export interface Trip {
  id: string;
  passenger_id: string;
  driver_id?: string;
  pickup_location: string;
  pickup_coordinates: Coordinates;
  destination_location: string;
  destination_coordinates: Coordinates;
  ride_type: RideType;
  status: TripStatus;
  fare_amount: number;
  distance_km: number;
  duration_minutes: number;
  scheduled_time?: string;
  driver_accepted_at?: string;
  driver_arrived_at?: string;
  trip_started_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  cancelled_by?: 'passenger' | 'driver' | 'system';
  pickup_address_short?: string;
  destination_address_short?: string;
  driver_notes?: string;
  passenger_notes?: string;
  surge_multiplier: number;
  estimated_arrival_time?: number;
  actual_pickup_time?: string;
  route_data?: any; // JSONB data
  created_at: string;
  updated_at: string;
}

export interface TripRequest {
  id: string;
  trip_id: string;
  driver_id: string;
  request_sent_at: string;
  response_deadline: string;
  driver_response?: 'accepted' | 'declined' | 'timeout';
  response_time?: string;
  decline_reason?: string;
  created_at: string;
}

export interface TripRating {
  id: string;
  trip_id: string;
  passenger_rating?: number;
  driver_rating?: number;
  passenger_comment?: string;
  driver_comment?: string;
  created_at: string;
}

// =====================================================
// DRIVER-SPECIFIC TYPES
// =====================================================

export interface DriverDocument {
  id: string;
  driver_id: string;
  document_type: DocumentType;
  document_url: string;
  document_status: DocumentStatus;
  uploaded_at: string;
  reviewed_at?: string;
  reviewed_by?: string;
  rejection_reason?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DriverAvailability {
  id: string;
  driver_id: string;
  status: DriverStatus;
  available_from?: string;
  available_until?: string;
  preferred_areas?: any; // JSONB array
  max_distance_km: number;
  last_location_update: string;
  created_at: string;
  updated_at: string;
}

export interface DriverEarnings {
  id: string;
  driver_id: string;
  trip_id: string;
  base_fare: number;
  distance_fare: number;
  time_fare: number;
  surge_multiplier: number;
  total_fare: number;
  commission_rate: number;
  commission_amount: number;
  driver_payout: number;
  tips: number;
  bonuses: number;
  payout_status: 'pending' | 'processed' | 'paid';
  payout_date?: string;
  created_at: string;
}

export interface DriverLocation {
  id: string;
  driver_id: string;
  location: Coordinates;
  heading?: number;
  speed_kmh?: number;
  accuracy_meters?: number;
  is_moving: boolean;
  trip_id?: string;
  recorded_at: string;
  created_at: string;
}

export interface IncidentReport {
  id: string;
  reporter_id: string;
  trip_id?: string;
  incident_type: 'safety' | 'vehicle' | 'route' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location?: Coordinates;
  photos?: string[]; // Array of photo URLs
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  assigned_to?: string;
  resolution_notes?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
}

// =====================================================
// SEARCH & LOCATION TYPES
// =====================================================

export interface SearchResult {
  display_name: string;
  lat: string;
  lon: string;
  address?: {
    house_number?: string;
    road?: string;
    suburb?: string;
    city?: string;
    state?: string;
    postcode?: string;
    country?: string;
  };
  importance?: number;
  place_id?: string;
}

export interface SearchHistory {
  id: string;
  user_id: string;
  search_query: string;
  selected_result?: any; // JSONB
  result_address?: string;
  result_coordinates?: Coordinates;
  search_context?: string;
  search_timestamp: string;
  location_context?: Coordinates;
  result_clicked: boolean;
  created_at: string;
  updated_at: string;
}

export interface FavoriteLocation {
  id: string;
  user_id: string;
  label: string;
  address: string;
  coordinates: Coordinates;
  is_primary: boolean;
  icon_name: string;
  created_at: string;
  updated_at: string;
}

// =====================================================
// REAL-TIME & COMMUNICATION TYPES
// =====================================================

export interface RealtimeMessage {
  type: 'trip_request' | 'trip_update' | 'location_update' | 'chat_message' | 'system_notification';
  data: any;
  timestamp: string;
  sender_id?: string;
  recipient_id?: string;
}

export interface ChatMessage {
  id: string;
  trip_id: string;
  sender_id: string;
  sender_type: 'passenger' | 'driver';
  message: string;
  message_type: 'text' | 'location' | 'system';
  read_at?: string;
  created_at: string;
}

// =====================================================
// ANALYTICS & REPORTING TYPES
// =====================================================

export interface DriverPerformanceMetrics {
  acceptance_rate: number;
  cancellation_rate: number;
  average_rating: number;
  total_trips: number;
  online_hours: number;
  trips_per_hour: number;
  earnings_per_hour: number;
}

export interface EarningsSummary {
  total_trips: number;
  total_earnings: number;
  total_tips: number;
  total_bonuses: number;
  commission_paid: number;
  net_earnings: number;
  average_trip_earnings: number;
  period_start: string;
  period_end: string;
}

export interface TripStatistics {
  total_trips: number;
  completed_trips: number;
  cancelled_trips: number;
  total_distance: number;
  total_earnings: number;
  average_rating: number;
  favorite_destinations: Array<{
    address: string;
    count: number;
  }>;
}

// =====================================================
// API RESPONSE TYPES
// =====================================================

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// =====================================================
// NOTIFICATION TYPES
// =====================================================

export interface PushNotification {
  id: string;
  user_id: string;
  title: string;
  body: string;
  data?: any;
  type: 'trip_request' | 'trip_update' | 'earnings' | 'system' | 'safety';
  priority: 'low' | 'normal' | 'high';
  sent_at: string;
  read_at?: string;
}

// =====================================================
// UTILITY TYPES
// =====================================================

export interface DistanceResult {
  distanceKm: number;
  distanceMiles: number;
  durationMinutes: number;
  durationText: string;
}

export interface RouteCoordinate {
  lat: number;
  lng: number;
  timestamp?: number;
}

export interface FareBreakdown {
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  surgeFare: number;
  totalFare: number;
  currency: string;
}

// =====================================================
// FORM & VALIDATION TYPES
// =====================================================

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isValid: boolean;
  isSubmitting: boolean;
}
