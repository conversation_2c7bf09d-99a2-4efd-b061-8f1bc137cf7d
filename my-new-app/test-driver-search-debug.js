#!/usr/bin/env node

/**
 * Debug script to test driver search functionality
 * This will help identify why the passenger app finds 0 drivers
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function debugDriverSearch() {
  console.log('🔍 Starting driver search debug...\n');

  try {
    // 1. Check if drivers table exists and has data
    console.log('1. Checking drivers table...');
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*')
      .limit(10);

    if (driversError) {
      console.error('❌ Error querying drivers table:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} drivers in total`);
    if (drivers.length > 0) {
      console.log('📋 Sample driver data:');
      drivers.forEach((driver, index) => {
        console.log(`   ${index + 1}. ID: ${driver.id}`);
        console.log(`      User ID: ${driver.user_id}`);
        console.log(`      Online: ${driver.is_online}`);
        console.log(`      Verification: ${driver.verification_status}`);
        console.log(`      Location: ${driver.current_location}`);
        console.log(`      Vehicle Type: ${driver.vehicle_type}`);
        console.log('');
      });
    }

    // 2. Check driver_availability table
    console.log('2. Checking driver_availability table...');
    const { data: availability, error: availabilityError } = await supabase
      .from('driver_availability')
      .select('*')
      .limit(10);

    if (availabilityError) {
      console.error('❌ Error querying driver_availability table:', availabilityError);
    } else {
      console.log(`📊 Found ${availability.length} availability records`);
      if (availability.length > 0) {
        console.log('📋 Sample availability data:');
        availability.forEach((avail, index) => {
          console.log(`   ${index + 1}. Driver ID: ${avail.driver_id}`);
          console.log(`      Status: ${avail.status}`);
          console.log(`      Created: ${avail.created_at}`);
          console.log('');
        });
      }
    }

    // 3. Check profiles table for driver users
    console.log('3. Checking profiles table for drivers...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_type', 'driver')
      .limit(10);

    if (profilesError) {
      console.error('❌ Error querying profiles table:', profilesError);
    } else {
      console.log(`📊 Found ${profiles.length} driver profiles`);
      if (profiles.length > 0) {
        console.log('📋 Sample profile data:');
        profiles.forEach((profile, index) => {
          console.log(`   ${index + 1}. ID: ${profile.id}`);
          console.log(`      Email: ${profile.email}`);
          console.log(`      Name: ${profile.full_name}`);
          console.log(`      Type: ${profile.user_type}`);
          console.log('');
        });
      }
    }

    // 4. Test the get_nearby_drivers function directly
    console.log('4. Testing get_nearby_drivers function...');
    
    // Use coordinates from the passenger app logs
    const testLat = -26.3018625;
    const testLng = 27.8769306;
    const maxDistance = 15;
    
    console.log(`   Testing with coordinates: ${testLat}, ${testLng}`);
    console.log(`   Max distance: ${maxDistance}km`);
    
    const { data: nearbyDrivers, error: functionError } = await supabase.rpc('get_nearby_drivers', {
      p_pickup_lat: testLat,
      p_pickup_lng: testLng,
      p_max_distance_km: maxDistance,
      p_ride_type_filter: 'SheRide'
    });

    if (functionError) {
      console.error('❌ Error calling get_nearby_drivers function:', functionError);
      console.error('   Error details:', JSON.stringify(functionError, null, 2));
    } else {
      console.log(`✅ Function executed successfully`);
      console.log(`📊 Found ${nearbyDrivers ? nearbyDrivers.length : 0} nearby drivers`);
      
      if (nearbyDrivers && nearbyDrivers.length > 0) {
        console.log('📋 Nearby drivers:');
        nearbyDrivers.forEach((driver, index) => {
          console.log(`   ${index + 1}. ${driver.driver_name}`);
          console.log(`      Distance: ${driver.distance_km}km`);
          console.log(`      Vehicle: ${JSON.stringify(driver.vehicle_info)}`);
          console.log(`      Rating: ${driver.rating}`);
          console.log('');
        });
      }
    }

    // 5. Check if the function exists
    console.log('5. Checking if get_nearby_drivers function exists...');
    const { data: functions, error: functionsError } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_type')
      .eq('routine_name', 'get_nearby_drivers')
      .eq('routine_schema', 'public');

    if (functionsError) {
      console.error('❌ Error checking functions:', functionsError);
    } else {
      console.log(`📊 Found ${functions.length} matching functions`);
      if (functions.length > 0) {
        console.log('✅ get_nearby_drivers function exists');
      } else {
        console.log('❌ get_nearby_drivers function NOT found');
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the debug
debugDriverSearch().then(() => {
  console.log('🏁 Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Debug failed:', error);
  process.exit(1);
});
