/**
 * Test Enhanced Geocoding Service with Google Maps Fallback
 * Demonstrates the intelligent fallback system: Nominatim → LocationIQ → Google Maps
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  }
}

loadEnvFile();

// Simple test to demonstrate the fallback system
async function testEnhancedGeocoding() {
  console.log('🔄 Enhanced Geocoding Service Test');
  console.log('==================================');
  console.log('Testing intelligent fallback: Nominatim → LocationIQ → Google Maps');
  console.log('');

  // Test addresses that might challenge free services
  const testAddresses = [
    "3 Aries Road, Johannesburg", // Known problematic address
    "1 Sandton Drive, Sandton, Johannesburg", // Should work well
    "100 Maude Street, Sandton, Johannesburg", // Business district
    "25 Kloof Street, Cape Town", // Cape Town address
    "8 West Street, Durban", // Durban address
  ];

  console.log('📊 Configuration Status:');
  console.log(`   Nominatim: ✅ Always available (free)`);
  console.log(`   LocationIQ: ${process.env.LOCATIONIQ_API_KEY ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Google Maps: ${process.env.GOOGLE_MAPS_API_KEY && process.env.GOOGLE_MAPS_API_KEY !== 'your_google_maps_api_key_here' ? '✅ Configured' : '❌ Not configured'}`);
  console.log('');

  // Simulate the enhanced geocoding service behavior
  for (let i = 0; i < testAddresses.length; i++) {
    const address = testAddresses[i];
    console.log(`${i + 1}. Testing: "${address}"`);
    
    // Step 1: Try Nominatim (free)
    console.log('   🔍 Step 1: Trying Nominatim (free)...');
    const nominatimResult = await testNominatim(address);
    
    if (nominatimResult.success && nominatimResult.quality === 'good') {
      console.log(`   ✅ Nominatim success: ${nominatimResult.result}`);
      console.log(`   💰 Cost: $0.00 (free)`);
    } else {
      console.log(`   ⚠️  Nominatim: ${nominatimResult.result}`);
      
      // Step 2: Try LocationIQ (free tier)
      console.log('   🔍 Step 2: Trying LocationIQ (free tier)...');
      const locationIQResult = await testLocationIQ(address);
      
      if (locationIQResult.success && locationIQResult.quality === 'good') {
        console.log(`   ✅ LocationIQ success: ${locationIQResult.result}`);
        console.log(`   💰 Cost: $0.00 (free tier)`);
      } else {
        console.log(`   ⚠️  LocationIQ: ${locationIQResult.result}`);
        
        // Step 3: Try Google Maps (premium fallback)
        if (process.env.GOOGLE_MAPS_API_KEY && process.env.GOOGLE_MAPS_API_KEY !== 'your_google_maps_api_key_here') {
          console.log('   🔍 Step 3: Trying Google Maps (premium fallback)...');
          const googleResult = await testGoogleMaps(address);
          
          if (googleResult.success) {
            console.log(`   ✅ Google Maps success: ${googleResult.result}`);
            console.log(`   💰 Cost: ~$0.017 (premium)`);
          } else {
            console.log(`   ❌ Google Maps failed: ${googleResult.result}`);
          }
        } else {
          console.log('   ⚠️  Google Maps not configured - would use as fallback');
        }
      }
    }
    
    console.log('');
    await delay(1000); // Rate limiting
  }

  console.log('💡 Fallback Strategy Benefits:');
  console.log('   • 🆓 Uses free services first to minimize costs');
  console.log('   • 🎯 Falls back to premium Google Maps for difficult addresses');
  console.log('   • 📊 Tracks usage and costs automatically');
  console.log('   • 🚀 Provides best possible results for SheMove users');
  console.log('');
  console.log('✅ Your SheMove app now has enterprise-grade address resolution!');
}

async function testNominatim(address) {
  try {
    const https = require('https');
    const params = new URLSearchParams({
      q: address,
      format: 'json',
      addressdetails: '1',
      limit: '1',
      countrycodes: 'za'
    });

    const url = `https://nominatim.openstreetmap.org/search?${params}`;
    
    return new Promise((resolve) => {
      https.get(url, { headers: { 'User-Agent': 'SheMove-Test/1.0' } }, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const results = JSON.parse(data);
            if (results.length > 0) {
              const hasHouseNumber = /^\d+\s/.test(address);
              const resultHasHouseNumber = /^\d+\s/.test(results[0].display_name);
              const quality = hasHouseNumber && resultHasHouseNumber ? 'good' : 'poor';
              
              resolve({
                success: true,
                quality,
                result: results[0].display_name,
                coordinates: { lat: results[0].lat, lon: results[0].lon }
              });
            } else {
              resolve({ success: false, result: 'No results found' });
            }
          } catch (error) {
            resolve({ success: false, result: `Parse error: ${error.message}` });
          }
        });
      }).on('error', (error) => {
        resolve({ success: false, result: `Network error: ${error.message}` });
      });
    });
  } catch (error) {
    return { success: false, result: `Error: ${error.message}` };
  }
}

async function testLocationIQ(address) {
  const apiKey = process.env.LOCATIONIQ_API_KEY;
  if (!apiKey) {
    return { success: false, result: 'API key not configured' };
  }

  try {
    const https = require('https');
    const params = new URLSearchParams({
      key: apiKey,
      q: address,
      format: 'json',
      addressdetails: '1',
      limit: '1',
      countrycodes: 'za'
    });

    const url = `https://us1.locationiq.com/v1/search.php?${params}`;
    
    return new Promise((resolve) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const results = JSON.parse(data);
            if (Array.isArray(results) && results.length > 0) {
              const hasHouseNumber = /^\d+\s/.test(address);
              const resultHasHouseNumber = /^\d+\s/.test(results[0].display_name);
              const quality = hasHouseNumber && resultHasHouseNumber ? 'good' : 'poor';
              
              resolve({
                success: true,
                quality,
                result: results[0].display_name,
                coordinates: { lat: results[0].lat, lon: results[0].lon }
              });
            } else {
              resolve({ success: false, result: 'No results found' });
            }
          } catch (error) {
            resolve({ success: false, result: `Parse error: ${error.message}` });
          }
        });
      }).on('error', (error) => {
        resolve({ success: false, result: `Network error: ${error.message}` });
      });
    });
  } catch (error) {
    return { success: false, result: `Error: ${error.message}` };
  }
}

async function testGoogleMaps(address) {
  const apiKey = process.env.GOOGLE_MAPS_API_KEY;
  if (!apiKey || apiKey === 'your_google_maps_api_key_here') {
    return { success: false, result: 'API key not configured' };
  }

  try {
    const https = require('https');
    const params = new URLSearchParams({
      address: address,
      key: apiKey,
      components: 'country:ZA',
      region: 'za'
    });

    const url = `https://maps.googleapis.com/maps/api/geocode/json?${params}`;
    
    return new Promise((resolve) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (result.status === 'OK' && result.results.length > 0) {
              resolve({
                success: true,
                result: result.results[0].formatted_address,
                coordinates: result.results[0].geometry.location
              });
            } else {
              resolve({ success: false, result: `API error: ${result.status}` });
            }
          } catch (error) {
            resolve({ success: false, result: `Parse error: ${error.message}` });
          }
        });
      }).on('error', (error) => {
        resolve({ success: false, result: `Network error: ${error.message}` });
      });
    });
  } catch (error) {
    return { success: false, result: `Error: ${error.message}` };
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the test
if (require.main === module) {
  testEnhancedGeocoding().catch(console.error);
}

module.exports = { testEnhancedGeocoding };
