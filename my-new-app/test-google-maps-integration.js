/**
 * Test Google Maps API Integration with SheMove Geocoding Service
 * Verifies that Google Maps API works as a premium fallback
 */

const fs = require('fs');
const path = require('path');

// Mock environment for testing
process.env.NODE_ENV = 'test';

// Load environment variables
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');
  
  lines.forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      process.env[key.trim()] = valueParts.join('=').trim();
    }
  });
}

// Import services (would normally be TypeScript)
const { GooglePlacesService, GoogleGeocodingService } = require('./services/googleMapsService.ts');

class GoogleMapsIntegrationTest {
  constructor() {
    this.results = [];
    this.errors = [];
    
    // Test addresses for South Africa
    this.testAddresses = [
      "1 Sandton Drive, Sandton, Johannesburg",
      "3 Aries Road, Johannesburg", // Known problematic address
      "Nelson Mandela Square, Sandton",
      "V&A Waterfront, Cape Town",
      "123 Main Road, Durban",
      "University of Cape Town",
      "OR Tambo International Airport",
      "Table Mountain, Cape Town",
      "Kruger National Park",
      "Gold Reef City, Johannesburg"
    ];
  }

  async runTests() {
    console.log('🧪 Google Maps API Integration Test');
    console.log('=====================================');
    console.log('');

    // Check API key configuration
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    if (!apiKey || apiKey === 'your_google_maps_api_key_here') {
      console.log('❌ Google Maps API key not configured');
      console.log('   Run: ./setup-google-maps.sh to configure');
      return;
    }

    console.log('✅ Google Maps API key found');
    console.log(`🔑 Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}`);
    console.log('');

    // Initialize services
    const placesService = new GooglePlacesService(apiKey);
    const geocodingService = new GoogleGeocodingService(apiKey);

    // Test 1: Places API Search
    console.log('📍 Test 1: Google Places API Search');
    console.log('-----------------------------------');
    
    for (let i = 0; i < Math.min(5, this.testAddresses.length); i++) {
      const address = this.testAddresses[i];
      await this.testPlacesSearch(placesService, address, i + 1);
    }

    // Test 2: Geocoding API
    console.log('\n🌍 Test 2: Google Geocoding API');
    console.log('-------------------------------');
    
    for (let i = 0; i < Math.min(3, this.testAddresses.length); i++) {
      const address = this.testAddresses[i];
      await this.testGeocoding(geocodingService, address, i + 1);
    }

    // Test 3: Usage Statistics
    console.log('\n📊 Test 3: Usage Statistics');
    console.log('---------------------------');
    this.testUsageStats(placesService);

    // Test 4: Error Handling
    console.log('\n⚠️  Test 4: Error Handling');
    console.log('-------------------------');
    await this.testErrorHandling(placesService, geocodingService);

    // Generate Report
    this.generateReport();
  }

  async testPlacesSearch(placesService, address, testNum) {
    try {
      console.log(`${testNum}. Searching: "${address}"`);
      
      const startTime = Date.now();
      const results = await placesService.searchPlaces(address);
      const duration = Date.now() - startTime;
      
      if (results.length > 0) {
        console.log(`   ✅ Found ${results.length} results (${duration}ms)`);
        console.log(`   📍 Top result: ${results[0].description}`);
        
        // Get details for first result
        const details = await placesService.getPlaceDetails(results[0].placeId);
        console.log(`   🌍 Coordinates: ${details.coordinates.lat}, ${details.coordinates.lng}`);
        
        this.results.push({
          test: 'places_search',
          address,
          success: true,
          resultCount: results.length,
          duration,
          topResult: results[0].description,
          coordinates: details.coordinates
        });
      } else {
        console.log(`   ⚠️  No results found (${duration}ms)`);
        this.results.push({
          test: 'places_search',
          address,
          success: false,
          duration,
          error: 'No results'
        });
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      this.errors.push({
        test: 'places_search',
        address,
        error: error.message
      });
    }
    
    // Rate limiting delay
    await this.delay(1000);
  }

  async testGeocoding(geocodingService, address, testNum) {
    try {
      console.log(`${testNum}. Geocoding: "${address}"`);
      
      const startTime = Date.now();
      const results = await geocodingService.geocodeAddress(address);
      const duration = Date.now() - startTime;
      
      if (results.length > 0) {
        console.log(`   ✅ Found ${results.length} results (${duration}ms)`);
        console.log(`   📍 Address: ${results[0].formattedAddress}`);
        console.log(`   🌍 Coordinates: ${results[0].coordinates.lat}, ${results[0].coordinates.lng}`);
        console.log(`   🎯 Confidence: ${results[0].confidence.toFixed(2)}`);
        
        this.results.push({
          test: 'geocoding',
          address,
          success: true,
          resultCount: results.length,
          duration,
          formattedAddress: results[0].formattedAddress,
          coordinates: results[0].coordinates,
          confidence: results[0].confidence
        });
      } else {
        console.log(`   ⚠️  No results found (${duration}ms)`);
        this.results.push({
          test: 'geocoding',
          address,
          success: false,
          duration,
          error: 'No results'
        });
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      this.errors.push({
        test: 'geocoding',
        address,
        error: error.message
      });
    }
    
    // Rate limiting delay
    await this.delay(1000);
  }

  testUsageStats(placesService) {
    try {
      const stats = placesService.getUsageStats();
      
      console.log(`📈 Daily Usage: $${stats.dailyUsage.toFixed(3)}`);
      console.log(`📅 Monthly Usage: $${stats.monthlyUsage.toFixed(2)}`);
      console.log(`💰 Remaining Credit: $${stats.remainingCredit.toFixed(2)}`);
      console.log(`🔢 Requests Today: ${stats.requestsToday}`);
      
      if (stats.remainingCredit < 50) {
        console.log('⚠️  Warning: Low remaining credit!');
      }
      
    } catch (error) {
      console.log(`❌ Error getting usage stats: ${error.message}`);
    }
  }

  async testErrorHandling(placesService, geocodingService) {
    // Test invalid place ID
    try {
      console.log('1. Testing invalid place ID...');
      await placesService.getPlaceDetails('invalid_place_id');
      console.log('   ⚠️  Expected error but got success');
    } catch (error) {
      console.log(`   ✅ Correctly handled error: ${error.message}`);
    }

    // Test empty query
    try {
      console.log('2. Testing empty query...');
      const results = await placesService.searchPlaces('');
      console.log(`   ✅ Empty query handled: ${results.length} results`);
    } catch (error) {
      console.log(`   ✅ Empty query error handled: ${error.message}`);
    }

    // Test very long query
    try {
      console.log('3. Testing very long query...');
      const longQuery = 'a'.repeat(1000);
      const results = await geocodingService.geocodeAddress(longQuery);
      console.log(`   ✅ Long query handled: ${results.length} results`);
    } catch (error) {
      console.log(`   ✅ Long query error handled: ${error.message}`);
    }
  }

  generateReport() {
    console.log('\n📋 Test Report');
    console.log('==============');
    
    const successfulTests = this.results.filter(r => r.success);
    const failedTests = this.results.filter(r => !r.success);
    
    console.log(`✅ Successful tests: ${successfulTests.length}`);
    console.log(`❌ Failed tests: ${failedTests.length}`);
    console.log(`⚠️  Errors encountered: ${this.errors.length}`);
    
    if (successfulTests.length > 0) {
      const avgDuration = successfulTests.reduce((sum, r) => sum + r.duration, 0) / successfulTests.length;
      console.log(`⏱️  Average response time: ${avgDuration.toFixed(0)}ms`);
    }
    
    console.log('\n💡 Recommendations:');
    
    if (successfulTests.length === 0) {
      console.log('   ❌ Google Maps API is not working - check your setup');
      console.log('   🔧 Run: ./setup-google-maps.sh to reconfigure');
    } else if (failedTests.length > successfulTests.length) {
      console.log('   ⚠️  High failure rate - check API restrictions and billing');
    } else {
      console.log('   ✅ Google Maps API is working well!');
      console.log('   💰 Monitor usage to stay within free tier');
      console.log('   🚀 Ready to use as premium fallback in SheMove app');
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.length,
        successful: successfulTests.length,
        failed: failedTests.length,
        errors: this.errors.length
      },
      results: this.results,
      errors: this.errors
    };
    
    fs.writeFileSync('google-maps-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved: google-maps-test-report.json');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new GoogleMapsIntegrationTest();
  tester.runTests().catch(console.error);
}

module.exports = GoogleMapsIntegrationTest;
