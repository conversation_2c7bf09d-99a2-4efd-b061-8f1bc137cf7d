/**
 * Test Google Maps API Implementation with House Number Addresses
 * Tests 15 South African addresses with house numbers to verify geocoding accuracy
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env file
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  }
}

loadEnvFile();

// Mock the TypeScript imports for Node.js testing
const https = require('https');

class GooglePlacesService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://maps.googleapis.com/maps/api/place';
    this.usageTracker = new Map();
  }

  async searchPlaces(query, location = null, radius = 50000) {
    const params = new URLSearchParams({
      input: query.trim(),
      key: this.apiKey,
      types: 'address',
      components: 'country:za',
      language: 'en'
    });

    if (location) {
      params.append('location', `${location.lat},${location.lng}`);
      params.append('radius', radius.toString());
    }

    const url = `${this.baseUrl}/autocomplete/json?${params}`;
    
    return new Promise((resolve, reject) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (result.status === 'OK' || result.status === 'ZERO_RESULTS') {
              resolve((result.predictions || []).map(prediction => ({
                placeId: prediction.place_id,
                description: prediction.description,
                mainText: prediction.structured_formatting?.main_text || prediction.description,
                secondaryText: prediction.structured_formatting?.secondary_text || '',
                types: prediction.types || []
              })));
            } else {
              reject(new Error(`Places API error: ${result.status} - ${result.error_message || 'Unknown error'}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      }).on('error', reject);
    });
  }

  async getPlaceDetails(placeId) {
    const params = new URLSearchParams({
      place_id: placeId,
      key: this.apiKey,
      fields: 'formatted_address,geometry,name,types,place_id',
      language: 'en'
    });

    const url = `${this.baseUrl}/details/json?${params}`;
    
    return new Promise((resolve, reject) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (result.status === 'OK') {
              const place = result.result;
              resolve({
                formattedAddress: place.formatted_address,
                coordinates: {
                  lat: place.geometry.location.lat,
                  lng: place.geometry.location.lng
                },
                name: place.name || '',
                types: place.types || [],
                placeId: place.place_id
              });
            } else {
              reject(new Error(`Place Details error: ${result.status} - ${result.error_message || 'Unknown error'}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      }).on('error', reject);
    });
  }
}

class GoogleGeocodingService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://maps.googleapis.com/maps/api/geocode';
  }

  async geocodeAddress(address) {
    const params = new URLSearchParams({
      address: address.trim(),
      key: this.apiKey,
      components: 'country:ZA',
      region: 'za',
      language: 'en'
    });

    const url = `${this.baseUrl}/json?${params}`;
    
    return new Promise((resolve, reject) => {
      https.get(url, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            if (result.status === 'OK' || result.status === 'ZERO_RESULTS') {
              resolve((result.results || []).map((res, index) => ({
                formattedAddress: res.formatted_address,
                coordinates: {
                  lat: res.geometry.location.lat,
                  lng: res.geometry.location.lng
                },
                types: res.types || [],
                addressComponents: res.address_components || [],
                placeId: res.place_id,
                confidence: Math.max(0.5, 1.0 - (index * 0.1))
              })));
            } else {
              reject(new Error(`Geocoding error: ${result.status} - ${result.error_message || 'Unknown error'}`));
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${error.message}`));
          }
        });
      }).on('error', reject);
    });
  }
}

class HouseNumberAddressTest {
  constructor() {
    this.testAddresses = [
      // Johannesburg addresses with house numbers
      "1 Sandton Drive, Sandton, Johannesburg",
      "2 Sandton Drive, Sandton, Johannesburg", 
      "3 Aries Road, Johannesburg",
      "15 Rivonia Road, Sandton, Johannesburg",
      "25 West Street, Sandton, Johannesburg",
      "100 Maude Street, Sandton, Johannesburg",
      "45 Jan Smuts Avenue, Rosebank, Johannesburg",
      "12 Oxford Road, Rosebank, Johannesburg",
      
      // Cape Town addresses with house numbers
      "10 Long Street, Cape Town",
      "25 Kloof Street, Cape Town",
      "5 Bree Street, Cape Town",
      "33 Loop Street, Cape Town",
      
      // Durban addresses with house numbers
      "8 West Street, Durban",
      "20 Smith Street, Durban",
      "7 Pine Street, Durban"
    ];
    
    this.results = [];
    this.errors = [];
  }

  async runTest() {
    console.log('🏠 House Number Address Test for Google Maps API');
    console.log('===============================================');
    console.log(`📍 Testing ${this.testAddresses.length} South African addresses with house numbers`);
    console.log('');

    // Check API key
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    if (!apiKey || apiKey === 'your_google_maps_api_key_here') {
      console.log('❌ Google Maps API key not configured in .env file');
      console.log('   Please run: ./setup-google-maps.sh to configure');
      return;
    }

    console.log('✅ Google Maps API key found');
    console.log(`🔑 Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}`);
    console.log('');

    // Initialize services
    const placesService = new GooglePlacesService(apiKey);
    const geocodingService = new GoogleGeocodingService(apiKey);

    console.log('🔍 Testing Places API (Address Autocomplete)');
    console.log('--------------------------------------------');
    
    for (let i = 0; i < this.testAddresses.length; i++) {
      const address = this.testAddresses[i];
      await this.testPlacesAPI(placesService, address, i + 1);
      
      // Rate limiting delay
      await this.delay(1000);
    }

    console.log('\n🌍 Testing Geocoding API (Direct Address Lookup)');
    console.log('------------------------------------------------');
    
    for (let i = 0; i < this.testAddresses.length; i++) {
      const address = this.testAddresses[i];
      await this.testGeocodingAPI(geocodingService, address, i + 1);
      
      // Rate limiting delay
      await this.delay(1000);
    }

    // Generate comprehensive report
    this.generateReport();
  }

  async testPlacesAPI(placesService, address, testNum) {
    try {
      console.log(`${testNum.toString().padStart(2)}. Places API: "${address}"`);
      
      const startTime = Date.now();
      const results = await placesService.searchPlaces(address);
      const duration = Date.now() - startTime;
      
      if (results.length > 0) {
        // Get details for the first result
        const details = await placesService.getPlaceDetails(results[0].placeId);
        
        console.log(`    ✅ Found ${results.length} results (${duration}ms)`);
        console.log(`    📍 Best match: ${details.formattedAddress}`);
        console.log(`    🌍 Coordinates: ${details.coordinates.lat}, ${details.coordinates.lng}`);
        
        // Check if house number is preserved
        const originalHouseNumber = this.extractHouseNumber(address);
        const resultHouseNumber = this.extractHouseNumber(details.formattedAddress);
        const houseNumberMatch = originalHouseNumber && resultHouseNumber && 
                                originalHouseNumber === resultHouseNumber;
        
        if (houseNumberMatch) {
          console.log(`    🏠 House number preserved: ${originalHouseNumber}`);
        } else {
          console.log(`    ⚠️  House number issue: ${originalHouseNumber} → ${resultHouseNumber || 'not found'}`);
        }
        
        this.results.push({
          testNum,
          api: 'places',
          address,
          success: true,
          resultCount: results.length,
          duration,
          bestMatch: details.formattedAddress,
          coordinates: details.coordinates,
          originalHouseNumber,
          resultHouseNumber,
          houseNumberMatch,
          confidence: 'high'
        });
        
      } else {
        console.log(`    ❌ No results found (${duration}ms)`);
        this.results.push({
          testNum,
          api: 'places',
          address,
          success: false,
          duration,
          error: 'No results found'
        });
      }
      
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
      this.errors.push({
        testNum,
        api: 'places',
        address,
        error: error.message
      });
    }
  }

  async testGeocodingAPI(geocodingService, address, testNum) {
    try {
      console.log(`${testNum.toString().padStart(2)}. Geocoding API: "${address}"`);
      
      const startTime = Date.now();
      const results = await geocodingService.geocodeAddress(address);
      const duration = Date.now() - startTime;
      
      if (results.length > 0) {
        const bestResult = results[0];
        
        console.log(`    ✅ Found ${results.length} results (${duration}ms)`);
        console.log(`    📍 Best match: ${bestResult.formattedAddress}`);
        console.log(`    🌍 Coordinates: ${bestResult.coordinates.lat}, ${bestResult.coordinates.lng}`);
        console.log(`    🎯 Confidence: ${bestResult.confidence.toFixed(2)}`);
        
        // Check house number preservation
        const originalHouseNumber = this.extractHouseNumber(address);
        const resultHouseNumber = this.extractHouseNumber(bestResult.formattedAddress);
        const houseNumberMatch = originalHouseNumber && resultHouseNumber && 
                                originalHouseNumber === resultHouseNumber;
        
        if (houseNumberMatch) {
          console.log(`    🏠 House number preserved: ${originalHouseNumber}`);
        } else {
          console.log(`    ⚠️  House number issue: ${originalHouseNumber} → ${resultHouseNumber || 'not found'}`);
        }
        
        this.results.push({
          testNum,
          api: 'geocoding',
          address,
          success: true,
          resultCount: results.length,
          duration,
          bestMatch: bestResult.formattedAddress,
          coordinates: bestResult.coordinates,
          originalHouseNumber,
          resultHouseNumber,
          houseNumberMatch,
          confidence: bestResult.confidence
        });
        
      } else {
        console.log(`    ❌ No results found (${duration}ms)`);
        this.results.push({
          testNum,
          api: 'geocoding',
          address,
          success: false,
          duration,
          error: 'No results found'
        });
      }
      
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
      this.errors.push({
        testNum,
        api: 'geocoding',
        address,
        error: error.message
      });
    }
  }

  extractHouseNumber(address) {
    const match = address.match(/^(\d+)\s/);
    return match ? match[1] : null;
  }

  generateReport() {
    console.log('\n📊 Test Results Summary');
    console.log('=======================');
    
    const placesResults = this.results.filter(r => r.api === 'places');
    const geocodingResults = this.results.filter(r => r.api === 'geocoding');
    
    const placesSuccess = placesResults.filter(r => r.success);
    const geocodingSuccess = geocodingResults.filter(r => r.success);
    
    const placesHouseNumberSuccess = placesSuccess.filter(r => r.houseNumberMatch);
    const geocodingHouseNumberSuccess = geocodingSuccess.filter(r => r.houseNumberMatch);
    
    console.log(`\n🔍 Places API Results:`);
    console.log(`   ✅ Successful: ${placesSuccess.length}/${placesResults.length} (${(placesSuccess.length/placesResults.length*100).toFixed(1)}%)`);
    console.log(`   🏠 House numbers preserved: ${placesHouseNumberSuccess.length}/${placesSuccess.length} (${placesSuccess.length > 0 ? (placesHouseNumberSuccess.length/placesSuccess.length*100).toFixed(1) : 0}%)`);
    
    console.log(`\n🌍 Geocoding API Results:`);
    console.log(`   ✅ Successful: ${geocodingSuccess.length}/${geocodingResults.length} (${(geocodingSuccess.length/geocodingResults.length*100).toFixed(1)}%)`);
    console.log(`   🏠 House numbers preserved: ${geocodingHouseNumberSuccess.length}/${geocodingSuccess.length} (${geocodingSuccess.length > 0 ? (geocodingHouseNumberSuccess.length/geocodingSuccess.length*100).toFixed(1) : 0}%)`);
    
    if (this.errors.length > 0) {
      console.log(`\n❌ Errors encountered: ${this.errors.length}`);
      this.errors.forEach(error => {
        console.log(`   ${error.testNum}. ${error.api}: ${error.error}`);
      });
    }
    
    // Calculate average response times
    const successfulResults = this.results.filter(r => r.success);
    if (successfulResults.length > 0) {
      const avgDuration = successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length;
      console.log(`\n⏱️  Average response time: ${avgDuration.toFixed(0)}ms`);
    }
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    
    const overallSuccessRate = successfulResults.length / this.results.length;
    const houseNumberSuccessRate = successfulResults.filter(r => r.houseNumberMatch).length / successfulResults.length;
    
    if (overallSuccessRate > 0.8) {
      console.log('   ✅ Excellent success rate - Google Maps API is working well!');
    } else if (overallSuccessRate > 0.6) {
      console.log('   ⚠️  Good success rate but room for improvement');
    } else {
      console.log('   ❌ Low success rate - check API configuration and billing');
    }
    
    if (houseNumberSuccessRate > 0.8) {
      console.log('   🏠 Excellent house number preservation - perfect for SheMove!');
    } else if (houseNumberSuccessRate > 0.6) {
      console.log('   🏠 Good house number preservation - suitable for most use cases');
    } else {
      console.log('   ⚠️  House number preservation needs improvement');
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      testAddresses: this.testAddresses,
      summary: {
        totalTests: this.results.length,
        successful: successfulResults.length,
        successRate: overallSuccessRate,
        houseNumberPreservation: houseNumberSuccessRate,
        averageResponseTime: successfulResults.length > 0 ? 
          successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length : 0,
        errors: this.errors.length
      },
      results: this.results,
      errors: this.errors
    };
    
    fs.writeFileSync('house-number-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved: house-number-test-report.json');
    
    console.log('\n🚀 Ready for SheMove Integration!');
    console.log('   Your Google Maps API is configured and working well with house numbers.');
    console.log('   The enhanced geocoding service will use this as a premium fallback.');
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
if (require.main === module) {
  const tester = new HouseNumberAddressTest();
  tester.runTest().catch(console.error);
}

module.exports = HouseNumberAddressTest;
