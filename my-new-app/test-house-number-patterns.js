/**
 * House Number Pattern Testing Script
 * Tests different house number formats to understand data availability patterns
 */

const fetch = require('node-fetch');

class HouseNumberPatternTester {
  constructor() {
    this.baseUrl = 'https://nominatim.openstreetmap.org';
    this.locationIQBaseUrl = 'https://us1.locationiq.com/v1';
    this.userAgent = 'SheMove-RideSharing-App/1.0';
  }

  async runPatternTests() {
    console.log('🏠 HOUSE NUMBER PATTERN ANALYSIS');
    console.log('=================================');
    
    // Test different house number patterns and known addresses
    const testPatterns = [
      // Different number formats
      { query: '1 Sandton Drive', expected: 'Should work - known commercial address' },
      { query: '44 Stanley Avenue', expected: 'Should work - known business address' },
      { query: '88 Strand Street Cape Town', expected: 'Should work - known address' },
      { query: '33 Bree Street Cape Town', expected: 'Should work - known business' },
      { query: '66 Paul <PERSON> Street Pretoria', expected: 'Should work - known address' },
      
      // Residential patterns that typically don't work
      { query: '3 Aries Road', expected: 'Likely no house number - residential' },
      { query: '10 Kilmore Avenue West', expected: 'Likely no house number - residential' },
      { query: '15 Rivonia Road', expected: 'Likely no house number - residential' },
      { query: '25 Pritchard Street', expected: 'Likely no house number - residential' },
      
      // Different formats
      { query: 'Unit 5 Sandton Drive', expected: 'Test unit format' },
      { query: '123A Main Street Johannesburg', expected: 'Test letter suffix' },
      { query: '12/34 Long Street Cape Town', expected: 'Test fraction format' },
      
      // Well-known commercial areas
      { query: '1 Rosebank Mall', expected: 'Commercial - might work' },
      { query: '2 Nelson Mandela Square', expected: 'Commercial - might work' },
      { query: '5 V&A Waterfront Cape Town', expected: 'Commercial - might work' },
    ];

    console.log(`Testing ${testPatterns.length} different house number patterns...\n`);

    for (let i = 0; i < testPatterns.length; i++) {
      const test = testPatterns[i];
      console.log(`[${i + 1}/${testPatterns.length}] "${test.query}"`);
      console.log(`Expected: ${test.expected}`);
      
      await this.analyzePattern(test.query);
      console.log('─'.repeat(60));
      
      // Rate limiting
      await this.delay(800);
    }
  }

  async analyzePattern(query) {
    try {
      // Test both providers
      const [nominatimResults, locationiqResults] = await Promise.all([
        this.searchWithNominatim(query),
        this.searchWithLocationIQ(query)
      ]);

      // Analyze results
      const nominatimHouseNumbers = nominatimResults.filter(r => r.address?.house_number);
      const locationiqHouseNumbers = locationiqResults.filter(r => r.address?.house_number);

      console.log(`📊 Results Analysis:`);
      console.log(`  Nominatim: ${nominatimResults.length} results, ${nominatimHouseNumbers.length} with house numbers`);
      console.log(`  LocationIQ: ${locationiqResults.length} results, ${locationiqHouseNumbers.length} with house numbers`);

      // Show house number results if any
      if (nominatimHouseNumbers.length > 0) {
        console.log(`  🏠 Nominatim house numbers found:`);
        nominatimHouseNumbers.forEach(r => {
          console.log(`    • ${r.address.house_number} ${r.address.road || 'Unknown Road'} - ${r.display_name.split(',')[0]}`);
        });
      }

      if (locationiqHouseNumbers.length > 0) {
        console.log(`  🏠 LocationIQ house numbers found:`);
        locationiqHouseNumbers.forEach(r => {
          console.log(`    • ${r.address.house_number} ${r.address.road || 'Unknown Road'} - ${r.display_name.split(',')[0]}`);
        });
      }

      // Show top results for comparison
      if (nominatimResults.length > 0) {
        console.log(`  📍 Nominatim top result: ${nominatimResults[0].display_name}`);
      }
      if (locationiqResults.length > 0) {
        console.log(`  📍 LocationIQ top result: ${locationiqResults[0].display_name}`);
      }

      // Analysis
      if (nominatimHouseNumbers.length > 0 || locationiqHouseNumbers.length > 0) {
        console.log(`  ✅ SUCCESS: House number data available`);
      } else {
        console.log(`  ❌ NO HOUSE NUMBERS: Falls back to street-level results`);
      }

    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
    }
  }

  async searchWithNominatim(query) {
    const params = new URLSearchParams({
      q: query.trim(),
      format: 'json',
      addressdetails: '1',
      limit: '3',
      'accept-language': 'en',
      'dedupe': '1',
      'countrycodes': 'za'
    });

    const response = await fetch(`${this.baseUrl}/search?${params}`, {
      headers: { 'User-Agent': this.userAgent }
    });

    if (!response.ok) {
      throw new Error(`Nominatim HTTP ${response.status}`);
    }

    return await response.json();
  }

  async searchWithLocationIQ(query) {
    const apiKey = process.env.LOCATIONIQ_API_KEY;
    
    const params = new URLSearchParams({
      key: apiKey,
      q: query.trim(),
      format: 'json',
      addressdetails: '1',
      limit: '3',
      'accept-language': 'en',
      dedupe: '1',
      countrycodes: 'za'
    });

    const response = await fetch(`${this.locationIQBaseUrl}/search.php?${params}`, {
      headers: { 'User-Agent': this.userAgent }
    });

    if (!response.ok) {
      throw new Error(`LocationIQ HTTP ${response.status}`);
    }

    const data = await response.json();
    if (data.error) {
      throw new Error(`LocationIQ: ${data.error}`);
    }

    return data;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
async function runTest() {
  const tester = new HouseNumberPatternTester();
  
  try {
    await tester.runPatternTests();
    
    console.log('\n🎯 KEY INSIGHTS:');
    console.log('================');
    console.log('1. House numbers work well for COMMERCIAL addresses (businesses, offices)');
    console.log('2. House numbers rarely work for RESIDENTIAL addresses in South Africa');
    console.log('3. Both Nominatim and LocationIQ have similar data limitations');
    console.log('4. The fallback system provides more search results, improving overall UX');
    console.log('5. Success depends on OpenStreetMap data quality, not the API provider');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

if (require.main === module) {
  runTest();
}
