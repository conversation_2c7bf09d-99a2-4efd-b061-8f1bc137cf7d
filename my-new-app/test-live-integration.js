/**
 * Test Live Google Maps Integration in SheMove App
 * Verifies that your app components are using Google Maps fallback
 */

const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          process.env[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  }
}

loadEnvFile();

async function testLiveIntegration() {
  console.log('🔍 Testing Live Google Maps Integration in SheMove App');
  console.log('====================================================');
  console.log('');

  // Check configuration
  console.log('📋 Configuration Status:');
  const googleConfigured = process.env.GOOGLE_MAPS_API_KEY && 
                          process.env.GOOGLE_MAPS_API_KEY !== 'your_google_maps_api_key_here';
  const locationIQConfigured = process.env.LOCATIONIQ_API_KEY && 
                              process.env.LOCATIONIQ_API_KEY !== 'your_locationiq_api_key_here';

  console.log(`   Google Maps API: ${googleConfigured ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   LocationIQ API: ${locationIQConfigured ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Nominatim: ✅ Always available (free)`);
  console.log('');

  // Check service files
  console.log('📁 Service Files Status:');
  const files = [
    'services/geocodingService.ts',
    'services/googleMapsService.ts', 
    'services/smartAddressService.ts',
    'components/SmartAddressInput.tsx'
  ];

  let allFilesExist = true;
  for (const file of files) {
    const exists = fs.existsSync(path.join(__dirname, file));
    console.log(`   ${file}: ${exists ? '✅ Exists' : '❌ Missing'}`);
    if (!exists) allFilesExist = false;
  }
  console.log('');

  // Check integration points
  console.log('🔗 Integration Points:');
  
  // Check if SmartAddressInput uses enhanced geocoding
  const smartAddressContent = fs.readFileSync(path.join(__dirname, 'services/smartAddressService.ts'), 'utf8');
  const usesEnhanced = smartAddressContent.includes('searchLocationsEnhanced');
  console.log(`   SmartAddressService → Enhanced Geocoding: ${usesEnhanced ? '✅ Connected' : '❌ Not connected'}`);

  // Check if enhanced geocoding has Google fallback
  const geocodingContent = fs.readFileSync(path.join(__dirname, 'services/geocodingService.ts'), 'utf8');
  const hasGoogleFallback = geocodingContent.includes('searchWithGoogleFallback');
  console.log(`   Enhanced Geocoding → Google Fallback: ${hasGoogleFallback ? '✅ Connected' : '❌ Not connected'}`);

  // Check if Google services are imported
  const hasGoogleImport = geocodingContent.includes('GooglePlacesService');
  console.log(`   Geocoding Service → Google Maps API: ${hasGoogleImport ? '✅ Connected' : '❌ Not connected'}`);

  console.log('');

  // Integration status
  console.log('🚀 Integration Status:');
  
  if (googleConfigured && allFilesExist && usesEnhanced && hasGoogleFallback && hasGoogleImport) {
    console.log('   ✅ FULLY INTEGRATED AND LIVE!');
    console.log('');
    console.log('🎯 Your SheMove app is now using:');
    console.log('   1. User types address in SmartAddressInput');
    console.log('   2. SmartAddressService calls geocodingService.searchLocationsEnhanced()');
    console.log('   3. Enhanced geocoding tries: Nominatim → LocationIQ → Google Maps');
    console.log('   4. Google Maps provides perfect house number results as fallback');
    console.log('');
    console.log('🏠 Test this in your app:');
    console.log('   • Open your SheMove app');
    console.log('   • Type "3 Aries Road, Johannesburg" in address search');
    console.log('   • You should now get accurate results with house number!');
    console.log('');
    console.log('💰 Cost tracking:');
    console.log('   • Free services used first (Nominatim, LocationIQ)');
    console.log('   • Google Maps only used when needed (~$0.017 per request)');
    console.log('   • Usage automatically tracked and monitored');
    
  } else {
    console.log('   ⚠️  PARTIALLY INTEGRATED - Some issues detected:');
    
    if (!googleConfigured) {
      console.log('   ❌ Google Maps API key not configured');
      console.log('      → Run: ./setup-google-maps.sh');
    }
    
    if (!allFilesExist) {
      console.log('   ❌ Some service files are missing');
    }
    
    if (!usesEnhanced || !hasGoogleFallback || !hasGoogleImport) {
      console.log('   ❌ Integration code not properly connected');
    }
  }

  console.log('');
  console.log('📊 To monitor usage:');
  console.log('   • Check Google Cloud Console for API usage');
  console.log('   • Monitor app logs for fallback usage');
  console.log('   • Set up billing alerts in Google Cloud');
  console.log('');
  console.log('🔧 To test manually:');
  console.log('   • Run: node test-house-number-addresses.js');
  console.log('   • Run: node test-enhanced-geocoding.js');
}

// Run the test
if (require.main === module) {
  testLiveIntegration().catch(console.error);
}

module.exports = { testLiveIntegration };
