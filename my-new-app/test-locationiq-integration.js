/**
 * LocationIQ Integration Test Script
 * Tests the enhanced search functionality with LocationIQ fallback
 */

// Import the geocoding service
const { geocodingService } = require('./services/geocodingService');

class LocationIQTester {
  constructor() {
    this.testResults = [];
  }

  async runComprehensiveTest() {
    console.log('🧪 LOCATIONIQ INTEGRATION TEST');
    console.log('==============================');
    
    // Check environment setup
    await this.checkEnvironmentSetup();
    
    // Test house number searches (should improve with LocationIQ)
    await this.testHouseNumberSearches();
    
    // Test general searches (should work with both providers)
    await this.testGeneralSearches();
    
    // Test usage tracking
    await this.testUsageTracking();
    
    // Generate report
    this.generateReport();
  }

  async checkEnvironmentSetup() {
    console.log('\n📋 ENVIRONMENT SETUP CHECK');
    console.log('---------------------------');
    
    const locationIQKey = process.env.LOCATIONIQ_API_KEY;
    
    if (!locationIQKey) {
      console.log('❌ LOCATIONIQ_API_KEY not found in environment variables');
      console.log('   Please add your LocationIQ API key to .env file');
      console.log('   LOCATIONIQ_API_KEY=your_api_key_here');
      return false;
    }
    
    if (locationIQKey.startsWith('pk.')) {
      console.log('✅ LocationIQ API key found and properly formatted');
    } else {
      console.log('⚠️  LocationIQ API key found but may be incorrectly formatted');
      console.log('   LocationIQ keys should start with "pk."');
    }
    
    return true;
  }

  async testHouseNumberSearches() {
    console.log('\n🏠 HOUSE NUMBER SEARCH TESTS');
    console.log('-----------------------------');
    
    const houseNumberQueries = [
      '3 Aries Road',
      '3 Aries Road Johannesburg',
      '123 Main Street',
      '123 Main Street Johannesburg',
      '1 Sandton Drive',
      '1 Sandton Drive Sandton',
      '44 Stanley Avenue',
      '44 Stanley Avenue Johannesburg'
    ];

    for (const query of houseNumberQueries) {
      await this.testSingleQuery(query, 'house_number');
      await this.delay(500); // Rate limiting
    }
  }

  async testGeneralSearches() {
    console.log('\n🌍 GENERAL SEARCH TESTS');
    console.log('------------------------');
    
    const generalQueries = [
      'Aries Road',
      'Aries Road Johannesburg',
      'Main Street',
      'Sandton Drive',
      'Stanley Avenue',
      'Starbucks Sandton',
      'McDonald\'s Johannesburg'
    ];

    for (const query of generalQueries) {
      await this.testSingleQuery(query, 'general');
      await this.delay(500); // Rate limiting
    }
  }

  async testSingleQuery(query, type) {
    console.log(`\nTesting: "${query}"`);
    
    try {
      // Test enhanced search
      const enhancedResult = await geocodingService.searchLocationsEnhanced(query, 5);
      
      const hasHouseNumbers = enhancedResult.results.some(r => r.address?.house_number);
      const resultCount = enhancedResult.results.length;
      const provider = enhancedResult.provider || 'unknown';
      
      console.log(`  📊 Results: ${resultCount} | Provider: ${provider} | House numbers: ${hasHouseNumbers ? '✅' : '❌'}`);
      
      if (resultCount > 0) {
        console.log(`  📍 Top result: ${enhancedResult.results[0].display_name}`);
        if (enhancedResult.results[0].address?.house_number) {
          console.log(`  🏠 House number: ${enhancedResult.results[0].address.house_number}`);
        }
      }
      
      // Store test result
      this.testResults.push({
        query,
        type,
        resultCount,
        provider,
        hasHouseNumbers,
        success: resultCount > 0,
        topResult: resultCount > 0 ? enhancedResult.results[0].display_name : null
      });
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      this.testResults.push({
        query,
        type,
        resultCount: 0,
        provider: 'error',
        hasHouseNumbers: false,
        success: false,
        error: error.message
      });
    }
  }

  async testUsageTracking() {
    console.log('\n📊 USAGE TRACKING TEST');
    console.log('-----------------------');
    
    const stats = geocodingService.getUsageStats();
    
    console.log('Daily usage statistics:');
    Object.entries(stats).forEach(([provider, data]) => {
      console.log(`  ${provider}: ${data.used}/${data.limit} (${data.remaining} remaining)`);
    });
    
    // Test individual quota check
    const locationIQRemaining = geocodingService.getRemainingQuota('locationiq');
    const nominatimRemaining = geocodingService.getRemainingQuota('nominatim');
    
    console.log(`\nRemaining quotas:`);
    console.log(`  LocationIQ: ${locationIQRemaining}`);
    console.log(`  Nominatim: ${nominatimRemaining}`);
  }

  generateReport() {
    console.log('\n📈 TEST REPORT');
    console.log('==============');
    
    const totalTests = this.testResults.length;
    const successfulTests = this.testResults.filter(r => r.success).length;
    const houseNumberTests = this.testResults.filter(r => r.type === 'house_number');
    const houseNumberSuccesses = houseNumberTests.filter(r => r.success).length;
    const houseNumberWithNumbers = houseNumberTests.filter(r => r.hasHouseNumbers).length;
    
    const locationIQUsage = this.testResults.filter(r => r.provider === 'locationiq').length;
    const nominatimUsage = this.testResults.filter(r => r.provider === 'nominatim').length;
    
    console.log(`\n📊 Overall Statistics:`);
    console.log(`  Total tests: ${totalTests}`);
    console.log(`  Successful: ${successfulTests}/${totalTests} (${Math.round(successfulTests/totalTests*100)}%)`);
    
    console.log(`\n🏠 House Number Search Performance:`);
    console.log(`  House number queries: ${houseNumberTests.length}`);
    console.log(`  Successful: ${houseNumberSuccesses}/${houseNumberTests.length} (${Math.round(houseNumberSuccesses/houseNumberTests.length*100)}%)`);
    console.log(`  With actual house numbers: ${houseNumberWithNumbers}/${houseNumberTests.length} (${Math.round(houseNumberWithNumbers/houseNumberTests.length*100)}%)`);
    
    console.log(`\n🔄 Provider Usage:`);
    console.log(`  LocationIQ: ${locationIQUsage} requests`);
    console.log(`  Nominatim: ${nominatimUsage} requests`);
    
    console.log(`\n🎯 Recommendations:`);
    if (houseNumberWithNumbers / houseNumberTests.length > 0.5) {
      console.log(`  ✅ LocationIQ integration is working well for house numbers!`);
    } else {
      console.log(`  ⚠️  House number results could be improved. Check API key configuration.`);
    }
    
    if (locationIQUsage > 0) {
      console.log(`  ✅ LocationIQ fallback is being used successfully`);
    } else {
      console.log(`  ⚠️  LocationIQ fallback not triggered. May indicate good Nominatim results or API issues.`);
    }
    
    console.log(`\n🔍 Failed Queries:`);
    const failedQueries = this.testResults.filter(r => !r.success);
    if (failedQueries.length === 0) {
      console.log(`  ✅ All queries returned results!`);
    } else {
      failedQueries.forEach(result => {
        console.log(`  ❌ "${result.query}" - ${result.error || 'No results'}`);
      });
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
async function runTest() {
  const tester = new LocationIQTester();
  
  try {
    await tester.runComprehensiveTest();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Export for use in other scripts
module.exports = { LocationIQTester };

// Run if called directly
if (require.main === module) {
  runTest();
}
