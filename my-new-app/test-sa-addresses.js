/**
 * South African Address Testing Script
 * Tests LocationIQ integration with specific SA house number addresses
 */

const fetch = require('node-fetch');

class SouthAfricanAddressTester {
  constructor() {
    this.baseUrl = 'https://nominatim.openstreetmap.org';
    this.locationIQBaseUrl = 'https://us1.locationiq.com/v1';
    this.userAgent = 'SheMove-RideSharing-App/1.0';
    this.testResults = [];
  }

  async runSAAddressTests() {
    console.log('🇿🇦 SOUTH AFRICAN ADDRESS TESTING WITH LOCATIONIQ');
    console.log('==================================================');
    
    // Check API key
    const apiKey = process.env.LOCATIONIQ_API_KEY;
    if (!apiKey) {
      console.log('❌ LOCATIONIQ_API_KEY not found in environment');
      console.log('Please set your LocationIQ API key in .env file');
      return;
    }
    
    console.log('✅ LocationIQ API key found');
    console.log(`🔑 Key format: ${apiKey.substring(0, 8)}...`);
    
    // Test addresses - mix of residential and commercial SA addresses
    const testAddresses = [
      // User's specific examples
      '10 Kilmore Avenue West',
      '3 Aries Road',
      
      // Johannesburg addresses
      '1 Sandton Drive, Sandton',
      '44 Stanley Avenue, Johannesburg',
      '15 Rivonia Road, Sandton',
      '25 Pritchard Street, Johannesburg',
      '100 Grayston Drive, Sandton',
      
      // Cape Town addresses
      '12 Long Street, Cape Town',
      '88 Strand Street, Cape Town',
      '5 Kloof Street, Cape Town',
      '33 Bree Street, Cape Town',
      
      // Durban addresses
      '7 West Street, Durban',
      '22 Smith Street, Durban',
      '45 Florida Road, Durban',
      
      // Pretoria addresses
      '18 Church Street, Pretoria',
      '66 Paul Kruger Street, Pretoria'
    ];

    console.log(`\n📋 Testing ${testAddresses.length} South African addresses`);
    console.log('=' .repeat(60));

    for (let i = 0; i < testAddresses.length; i++) {
      const address = testAddresses[i];
      console.log(`\n[${i + 1}/${testAddresses.length}] Testing: "${address}"`);
      
      await this.testSingleAddress(address);
      
      // Rate limiting - be respectful to APIs
      await this.delay(1000);
    }

    this.generateComprehensiveReport();
  }

  async testSingleAddress(address) {
    const results = {
      address,
      nominatim: { success: false, hasHouseNumber: false, results: [], error: null },
      locationiq: { success: false, hasHouseNumber: false, results: [], error: null },
      comparison: { winner: null, improvement: false }
    };

    // Test Nominatim first
    console.log('  🔍 Testing with Nominatim...');
    try {
      const nominatimResults = await this.searchWithNominatim(address);
      results.nominatim.results = nominatimResults;
      results.nominatim.success = nominatimResults.length > 0;
      results.nominatim.hasHouseNumber = nominatimResults.some(r => r.address?.house_number);
      
      console.log(`    📊 Nominatim: ${nominatimResults.length} results, House numbers: ${results.nominatim.hasHouseNumber ? '✅' : '❌'}`);
      
      if (nominatimResults.length > 0) {
        console.log(`    📍 Top result: ${nominatimResults[0].display_name}`);
      }
    } catch (error) {
      results.nominatim.error = error.message;
      console.log(`    ❌ Nominatim error: ${error.message}`);
    }

    // Test LocationIQ
    console.log('  🔍 Testing with LocationIQ...');
    try {
      const locationIQResults = await this.searchWithLocationIQ(address);
      results.locationiq.results = locationIQResults;
      results.locationiq.success = locationIQResults.length > 0;
      results.locationiq.hasHouseNumber = locationIQResults.some(r => r.address?.house_number);
      
      console.log(`    📊 LocationIQ: ${locationIQResults.length} results, House numbers: ${results.locationiq.hasHouseNumber ? '✅' : '❌'}`);
      
      if (locationIQResults.length > 0) {
        console.log(`    📍 Top result: ${locationIQResults[0].display_name}`);
      }
    } catch (error) {
      results.locationiq.error = error.message;
      console.log(`    ❌ LocationIQ error: ${error.message}`);
    }

    // Compare results
    this.compareResults(results);
    this.testResults.push(results);
  }

  async searchWithNominatim(query) {
    const params = new URLSearchParams({
      q: query.trim(),
      format: 'json',
      addressdetails: '1',
      limit: '5',
      'accept-language': 'en',
      'dedupe': '1',
      'countrycodes': 'za'
    });

    const url = `${this.baseUrl}/search?${params}`;
    
    const response = await fetch(url, {
      headers: { 'User-Agent': this.userAgent }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  async searchWithLocationIQ(query) {
    const apiKey = process.env.LOCATIONIQ_API_KEY;
    
    const params = new URLSearchParams({
      key: apiKey,
      q: query.trim(),
      format: 'json',
      addressdetails: '1',
      limit: '5',
      'accept-language': 'en',
      dedupe: '1',
      countrycodes: 'za'
    });

    const url = `${this.locationIQBaseUrl}/search.php?${params}`;
    
    const response = await fetch(url, {
      headers: { 'User-Agent': this.userAgent }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    
    // Handle LocationIQ error responses
    if (data.error) {
      throw new Error(`LocationIQ API error: ${data.error}`);
    }

    return data;
  }

  compareResults(results) {
    const { nominatim, locationiq } = results;
    
    // Determine winner based on house number availability and result quality
    if (locationiq.hasHouseNumber && !nominatim.hasHouseNumber) {
      results.comparison.winner = 'locationiq';
      results.comparison.improvement = true;
      console.log('    🏆 Winner: LocationIQ (has house numbers)');
    } else if (nominatim.hasHouseNumber && !locationiq.hasHouseNumber) {
      results.comparison.winner = 'nominatim';
      results.comparison.improvement = false;
      console.log('    🏆 Winner: Nominatim (has house numbers)');
    } else if (locationiq.results.length > nominatim.results.length) {
      results.comparison.winner = 'locationiq';
      results.comparison.improvement = true;
      console.log('    🏆 Winner: LocationIQ (more results)');
    } else if (nominatim.results.length > locationiq.results.length) {
      results.comparison.winner = 'nominatim';
      results.comparison.improvement = false;
      console.log('    🏆 Winner: Nominatim (more results)');
    } else if (nominatim.success && locationiq.success) {
      results.comparison.winner = 'tie';
      results.comparison.improvement = false;
      console.log('    🤝 Tie: Both providers returned similar results');
    } else if (locationiq.success && !nominatim.success) {
      results.comparison.winner = 'locationiq';
      results.comparison.improvement = true;
      console.log('    🏆 Winner: LocationIQ (only provider with results)');
    } else if (nominatim.success && !locationiq.success) {
      results.comparison.winner = 'nominatim';
      results.comparison.improvement = false;
      console.log('    🏆 Winner: Nominatim (only provider with results)');
    } else {
      results.comparison.winner = 'none';
      results.comparison.improvement = false;
      console.log('    ❌ No winner: Both providers failed');
    }
  }

  generateComprehensiveReport() {
    console.log('\n\n📈 COMPREHENSIVE TEST REPORT');
    console.log('============================');
    
    const totalTests = this.testResults.length;
    const nominatimSuccesses = this.testResults.filter(r => r.nominatim.success).length;
    const locationiqSuccesses = this.testResults.filter(r => r.locationiq.success).length;
    
    const nominatimHouseNumbers = this.testResults.filter(r => r.nominatim.hasHouseNumber).length;
    const locationiqHouseNumbers = this.testResults.filter(r => r.locationiq.hasHouseNumber).length;
    
    const locationiqWins = this.testResults.filter(r => r.comparison.winner === 'locationiq').length;
    const nominatimWins = this.testResults.filter(r => r.comparison.winner === 'nominatim').length;
    const ties = this.testResults.filter(r => r.comparison.winner === 'tie').length;
    const improvements = this.testResults.filter(r => r.comparison.improvement).length;

    console.log(`\n📊 Overall Statistics:`);
    console.log(`  Total addresses tested: ${totalTests}`);
    console.log(`  Nominatim successes: ${nominatimSuccesses}/${totalTests} (${Math.round(nominatimSuccesses/totalTests*100)}%)`);
    console.log(`  LocationIQ successes: ${locationiqSuccesses}/${totalTests} (${Math.round(locationiqSuccesses/totalTests*100)}%)`);
    
    console.log(`\n🏠 House Number Performance:`);
    console.log(`  Nominatim with house numbers: ${nominatimHouseNumbers}/${totalTests} (${Math.round(nominatimHouseNumbers/totalTests*100)}%)`);
    console.log(`  LocationIQ with house numbers: ${locationiqHouseNumbers}/${totalTests} (${Math.round(locationiqHouseNumbers/totalTests*100)}%)`);
    console.log(`  House number improvement: ${locationiqHouseNumbers > nominatimHouseNumbers ? '+' : ''}${locationiqHouseNumbers - nominatimHouseNumbers} addresses`);
    
    console.log(`\n🏆 Provider Comparison:`);
    console.log(`  LocationIQ wins: ${locationiqWins}/${totalTests} (${Math.round(locationiqWins/totalTests*100)}%)`);
    console.log(`  Nominatim wins: ${nominatimWins}/${totalTests} (${Math.round(nominatimWins/totalTests*100)}%)`);
    console.log(`  Ties: ${ties}/${totalTests} (${Math.round(ties/totalTests*100)}%)`);
    console.log(`  Overall improvements: ${improvements}/${totalTests} (${Math.round(improvements/totalTests*100)}%)`);

    console.log(`\n🎯 Detailed Results:`);
    this.testResults.forEach((result, index) => {
      const status = result.comparison.improvement ? '✅ IMPROVED' : 
                    result.comparison.winner === 'tie' ? '🤝 TIE' :
                    result.comparison.winner === 'none' ? '❌ FAILED' : '➖ NO CHANGE';
      
      console.log(`  ${index + 1}. "${result.address}" - ${status}`);
      console.log(`     Nominatim: ${result.nominatim.results.length} results${result.nominatim.hasHouseNumber ? ' (with house #)' : ''}`);
      console.log(`     LocationIQ: ${result.locationiq.results.length} results${result.locationiq.hasHouseNumber ? ' (with house #)' : ''}`);
    });

    console.log(`\n🔍 Failed Searches:`);
    const failedSearches = this.testResults.filter(r => !r.nominatim.success && !r.locationiq.success);
    if (failedSearches.length === 0) {
      console.log(`  ✅ All addresses returned results from at least one provider!`);
    } else {
      failedSearches.forEach(result => {
        console.log(`  ❌ "${result.address}"`);
        if (result.nominatim.error) console.log(`     Nominatim error: ${result.nominatim.error}`);
        if (result.locationiq.error) console.log(`     LocationIQ error: ${result.locationiq.error}`);
      });
    }

    console.log(`\n💡 Recommendations:`);
    if (improvements / totalTests > 0.3) {
      console.log(`  ✅ LocationIQ integration provides significant value (${Math.round(improvements/totalTests*100)}% improvement rate)`);
    } else {
      console.log(`  ⚠️  LocationIQ provides limited improvement (${Math.round(improvements/totalTests*100)}% improvement rate)`);
    }
    
    if (locationiqHouseNumbers > nominatimHouseNumbers) {
      console.log(`  ✅ LocationIQ significantly improves house number coverage (+${locationiqHouseNumbers - nominatimHouseNumbers} addresses)`);
    } else {
      console.log(`  ⚠️  LocationIQ doesn't improve house number coverage significantly`);
    }

    console.log(`\n🚀 Integration Impact:`);
    console.log(`  With fallback system, success rate would be: ${Math.max(nominatimSuccesses, locationiqSuccesses)}/${totalTests} (${Math.round(Math.max(nominatimSuccesses, locationiqSuccesses)/totalTests*100)}%)`);
    console.log(`  House number coverage would be: ${Math.max(nominatimHouseNumbers, locationiqHouseNumbers)}/${totalTests} (${Math.round(Math.max(nominatimHouseNumbers, locationiqHouseNumbers)/totalTests*100)}%)`);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
async function runTest() {
  const tester = new SouthAfricanAddressTester();
  
  try {
    await tester.runSAAddressTests();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Export for use in other scripts
module.exports = { SouthAfricanAddressTester };

// Run if called directly
if (require.main === module) {
  runTest();
}
