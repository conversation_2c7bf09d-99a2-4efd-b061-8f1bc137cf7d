/**
 * Search Analysis Test Script
 * Tests different search patterns to identify house number search issues
 */

const fetch = require('node-fetch');

class SearchAnalyzer {
  constructor() {
    this.baseUrl = 'https://nominatim.openstreetmap.org';
    this.userAgent = 'SheMove-RideSharing-App/1.0';
  }

  async testSearch(query, description, useStructured = false) {
    console.log(`\n=== ${description} ===`);
    console.log(`Query: "${query}"`);
    
    try {
      let params;
      
      if (useStructured && query.includes(' ')) {
        // Try structured search for house number queries
        const parts = query.split(' ');
        const houseNumber = parts[0];
        const streetName = parts.slice(1).join(' ');
        
        params = new URLSearchParams({
          format: 'json',
          addressdetails: '1',
          limit: '5',
          'accept-language': 'en',
          'dedupe': '1',
          'countrycodes': 'za',
          'street': `${houseNumber} ${streetName}`,
          'city': 'johannesburg'
        });
        
        console.log(`Structured search - street: "${houseNumber} ${streetName}", city: "johannesburg"`);
      } else {
        // Regular free-form search
        params = new URLSearchParams({
          q: query.trim(),
          format: 'json',
          addressdetails: '1',
          limit: '5',
          'accept-language': 'en',
          'dedupe': '1',
          'countrycodes': 'za'
        });
      }

      const url = `${this.baseUrl}/search?${params}`;
      console.log(`API URL: ${url}`);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': this.userAgent,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      console.log(`Results found: ${data.length}`);
      
      if (data.length > 0) {
        data.forEach((result, index) => {
          console.log(`\n  Result ${index + 1}:`);
          console.log(`    Display: ${result.display_name}`);
          console.log(`    Type: ${result.type}`);
          console.log(`    Class: ${result.class}`);
          console.log(`    Importance: ${result.importance}`);
          
          if (result.address) {
            console.log(`    Address breakdown:`);
            if (result.address.house_number) console.log(`      House number: ${result.address.house_number}`);
            if (result.address.road) console.log(`      Road: ${result.address.road}`);
            if (result.address.suburb) console.log(`      Suburb: ${result.address.suburb}`);
            if (result.address.city) console.log(`      City: ${result.address.city}`);
            if (result.address.state) console.log(`      State: ${result.address.state}`);
            if (result.address.country) console.log(`      Country: ${result.address.country}`);
          }
        });
      } else {
        console.log('  No results found');
      }
      
      return data;
    } catch (error) {
      console.error(`Error: ${error.message}`);
      return [];
    }
  }

  async runAnalysis() {
    console.log('🔍 SEARCH ANALYSIS FOR HOUSE NUMBER ISSUE');
    console.log('==========================================');

    // Test cases to identify the issue
    const testCases = [
      // Working cases (broad searches)
      { query: 'Aries Road', description: 'Broad street search (should work)' },
      { query: 'Aries Road Johannesburg', description: 'Street with city (should work)' },
      { query: 'Main Street', description: 'Generic street search (should work)' },

      // Failing cases (house number searches)
      { query: '3 Aries Road', description: 'House number search (likely failing)' },
      { query: '3 Aries Road Johannesburg', description: 'House number with city (likely failing)' },
      { query: '123 Main Street', description: 'Generic house number search (likely failing)' },
      { query: '123 Main Street Johannesburg', description: 'Generic house number with city (likely failing)' },

      // Test with known addresses that should have house numbers
      { query: '1 Sandton Drive', description: 'Known commercial address' },
      { query: '1 Sandton Drive Sandton', description: 'Known commercial address with city' },
      { query: '44 Stanley Avenue', description: 'Residential address format' },
      { query: '44 Stanley Avenue Johannesburg', description: 'Residential address with city' },

      // Alternative formats
      { query: 'Aries Road 3', description: 'Reversed format (number after street)' },
      { query: 'Number 3 Aries Road', description: 'With "Number" prefix' },
    ];
    
    // Test regular searches
    for (const testCase of testCases) {
      await this.testSearch(testCase.query, testCase.description);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Rate limiting
    }
    
    console.log('\n\n🏗️ STRUCTURED SEARCH TESTS');
    console.log('============================');
    
    // Test structured searches for house numbers
    const structuredTests = [
      { query: '3 Aries Road', description: 'Structured search for house number' },
      { query: '123 Main Street', description: 'Structured search for generic address' },
    ];
    
    for (const testCase of structuredTests) {
      await this.testSearch(testCase.query, testCase.description, true);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Rate limiting
    }
    
    console.log('\n\n📊 ANALYSIS COMPLETE');
    console.log('====================');
  }
}

// Run the analysis
const analyzer = new SearchAnalyzer();
analyzer.runAnalysis().catch(console.error);
