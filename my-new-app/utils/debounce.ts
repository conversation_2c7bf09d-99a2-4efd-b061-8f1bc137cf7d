/**
 * Debounce utility for optimizing search performance
 * Reduces API calls by delaying execution until user stops typing
 */

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Advanced debounce with immediate execution option
 * Useful for search where first character should trigger immediately
 */
export function debounceAdvanced<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate: boolean = false
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    const callNow = immediate && !timeoutId;
    
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      timeoutId = null as any;
      if (!immediate) func(...args);
    }, delay);
    
    if (callNow) func(...args);
  };
}

/**
 * Smart debounce for search queries
 * - Immediate execution for first 2 characters
 * - Debounced execution for subsequent characters
 * - Minimum query length validation
 */
export function debounceSearch<T extends (query: string, ...args: any[]) => any>(
  func: T,
  delay: number = 300,
  minLength: number = 2
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  let lastQuery = '';
  
  return (query: string, ...args: any[]) => {
    // Clear previous timeout
    clearTimeout(timeoutId);
    
    // Skip if query is too short
    if (query.length < minLength) {
      return;
    }
    
    // Immediate execution for significant changes
    const isSignificantChange = 
      query.length <= 2 || // First few characters
      query.length - lastQuery.length > 2 || // Paste operation
      Math.abs(query.length - lastQuery.length) > 3; // Major edit
    
    lastQuery = query;
    
    if (isSignificantChange) {
      func(query, ...args);
    } else {
      // Debounced execution for incremental typing
      timeoutId = setTimeout(() => {
        func(query, ...args);
      }, delay);
    }
  };
}

/**
 * Throttle utility for rate limiting
 * Ensures function is called at most once per interval
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
