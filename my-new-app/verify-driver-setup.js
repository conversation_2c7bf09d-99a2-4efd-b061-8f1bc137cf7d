#!/usr/bin/env node

/**
 * Verify complete driver setup for passenger app integration
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function verifyDriverSetup() {
  console.log('🔍 Verifying complete driver setup...\n');

  try {
    // 1. Check profiles table
    console.log('1. Checking profiles table...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_type', 'driver');

    if (profilesError) {
      console.error('❌ Error querying profiles:', profilesError);
      return;
    }

    console.log(`📊 Found ${profiles.length} driver profiles`);
    if (profiles.length === 0) {
      console.log('❌ NO DRIVER PROFILES FOUND');
      console.log('   → Driver signup did not create profile properly');
      return;
    }

    // 2. Check drivers table
    console.log('\n2. Checking drivers table...');
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*');

    if (driversError) {
      console.error('❌ Error querying drivers:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} driver records`);
    if (drivers.length === 0) {
      console.log('❌ NO DRIVER RECORDS FOUND');
      console.log('   → Driver onboarding not completed');
      return;
    }

    // 3. Check driver_availability table
    console.log('\n3. Checking driver_availability table...');
    const { data: availability, error: availError } = await supabase
      .from('driver_availability')
      .select('*')
      .order('created_at', { ascending: false });

    if (availError) {
      console.error('❌ Error querying availability:', availError);
      return;
    }

    console.log(`📊 Found ${availability.length} availability records`);

    // 4. Detailed analysis for each driver
    console.log('\n4. Detailed driver analysis...');
    
    for (const profile of profiles) {
      console.log(`\n🔍 Analyzing driver profile: ${profile.id}`);
      console.log(`   Email: ${profile.email}`);
      console.log(`   Name: ${profile.full_name}`);
      console.log(`   Type: ${profile.user_type}`);

      // Find corresponding driver record
      const driver = drivers.find(d => d.user_id === profile.id);
      if (!driver) {
        console.log('   ❌ NO DRIVER RECORD FOUND');
        console.log('   → Profile exists but no driver record');
        continue;
      }

      console.log(`   ✅ Driver record found: ${driver.id}`);
      console.log(`   📍 Online: ${driver.is_online}`);
      console.log(`   ✅ Verification: ${driver.verification_status}`);
      console.log(`   📍 Location: ${driver.current_location}`);
      console.log(`   🚗 Vehicle: ${driver.vehicle_type} ${driver.vehicle_make} ${driver.vehicle_model}`);

      // Check requirements for get_nearby_drivers
      const requirements = {
        'Profile exists': !!profile,
        'Driver record exists': !!driver,
        'Is online': driver.is_online,
        'Is approved': driver.verification_status === 'approved',
        'Has location': !!driver.current_location,
        'Has vehicle type': !!driver.vehicle_type
      };

      console.log('\n   📋 Requirements check:');
      Object.entries(requirements).forEach(([req, met]) => {
        console.log(`   ${met ? '✅' : '❌'} ${req}`);
      });

      // Check latest availability
      const latestAvail = availability.find(a => a.driver_id === driver.id);
      if (latestAvail) {
        console.log(`   📊 Latest availability: ${latestAvail.status} (${latestAvail.created_at})`);
        console.log(`   ${latestAvail.status === 'online' ? '✅' : '❌'} Availability is online`);
      } else {
        console.log('   ❌ NO AVAILABILITY RECORD');
        console.log('   → Driver never went online');
      }

      // Overall status
      const allRequirementsMet = Object.values(requirements).every(Boolean) && 
                                latestAvail?.status === 'online';
      
      console.log(`\n   🎯 OVERALL STATUS: ${allRequirementsMet ? '✅ READY FOR PASSENGER APP' : '❌ NOT READY'}`);
      
      if (allRequirementsMet) {
        console.log('   → This driver should be found by passenger app');
      } else {
        console.log('   → This driver will NOT be found by passenger app');
      }
    }

    // 5. Test the get_nearby_drivers function
    console.log('\n5. Testing get_nearby_drivers function...');
    
    const testLat = -26.3018625;
    const testLng = 27.8769306;
    const maxDistance = 15;
    
    const { data: nearbyDrivers, error: functionError } = await supabase.rpc('get_nearby_drivers', {
      p_pickup_lat: testLat,
      p_pickup_lng: testLng,
      p_max_distance_km: maxDistance,
      p_ride_type_filter: 'SheRide'
    });

    if (functionError) {
      console.error('❌ Function error:', functionError);
    } else {
      console.log(`📊 Function result: ${nearbyDrivers ? nearbyDrivers.length : 0} drivers found`);
      
      if (nearbyDrivers && nearbyDrivers.length > 0) {
        console.log('🎉 SUCCESS! Drivers found by function:');
        nearbyDrivers.forEach((driver, index) => {
          console.log(`   ${index + 1}. ${driver.driver_name}`);
          console.log(`      Distance: ${driver.distance_km}km`);
          console.log(`      Vehicle: ${JSON.stringify(driver.vehicle_info)}`);
          console.log(`      Rating: ${driver.rating}`);
        });
      } else {
        console.log('❌ No drivers found by function');
      }
    }

    // 6. Summary and recommendations
    console.log('\n6. Summary and Recommendations...');
    
    const readyDrivers = profiles.filter(profile => {
      const driver = drivers.find(d => d.user_id === profile.id);
      const latestAvail = availability.find(a => a.driver_id === driver?.id);
      
      return driver && 
             driver.is_online && 
             driver.verification_status === 'approved' && 
             driver.current_location && 
             latestAvail?.status === 'online';
    });

    console.log(`📊 Ready drivers: ${readyDrivers.length}/${profiles.length}`);
    
    if (readyDrivers.length === 0) {
      console.log('\n❌ NO DRIVERS READY FOR PASSENGER APP');
      console.log('\n💡 TO FIX:');
      console.log('1. Ensure driver completes signup (creates profile)');
      console.log('2. Ensure driver completes onboarding (creates driver record)');
      console.log('3. Admin approves driver (verification_status = approved)');
      console.log('4. Driver goes online (is_online = true, availability = online)');
      console.log('5. Driver has location permissions enabled');
    } else {
      console.log('\n✅ DRIVERS ARE READY!');
      console.log('   → Passenger app should find these drivers');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the verification
verifyDriverSetup().then(() => {
  console.log('\n🏁 Verification complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Verification failed:', error);
  process.exit(1);
});
