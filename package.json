{"name": "google-maps-scraper-poc", "version": "1.0.0", "description": "Proof of Concept for Google Maps address scraping - Educational purposes only", "main": "google-maps-scraper-poc.js", "scripts": {"test": "node google-maps-scraper-poc.js", "install-deps": "npm install", "clean": "rm -f google-maps-poc-report.json"}, "keywords": ["google-maps", "scraping", "proof-of-concept", "educational"], "author": "SheMove Development Team", "license": "MIT", "dependencies": {"puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer": "^21.0.0"}, "devDependencies": {"eslint": "^8.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "private"}, "warnings": ["⚠️  This package is for educational/testing purposes only", "⚠️  Using this code may violate Google's Terms of Service", "⚠️  Do not use for commercial or production purposes", "⚠️  Legal risks apply - consult legal counsel before use"]}