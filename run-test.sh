#!/bin/bash

# Google Maps Scraping PoC Test Runner
# Educational purposes only - high legal risk!

echo "🧪 Google Maps Scraping Proof of Concept"
echo "========================================"
echo ""
echo "⚠️  LEGAL WARNING:"
echo "⚠️  This test may violate Google's Terms of Service"
echo "⚠️  Educational/feasibility testing only"
echo "⚠️  Do NOT use for commercial purposes"
echo "⚠️  High legal risk - consult counsel first"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo ""
echo "🚀 Starting proof of concept test..."
echo "📍 Testing 10 addresses in Johannesburg Sandton"
echo "⏱️  Expected duration: 2-5 minutes"
echo ""

# Run the test
node google-maps-scraper-poc.js

echo ""
echo "✅ Test completed!"
echo ""

# Check if report was generated
if [ -f "google-maps-poc-report.json" ]; then
    echo "📊 Report generated: google-maps-poc-report.json"
    echo ""
    echo "📈 Quick Summary:"
    
    # Extract key metrics from report (requires jq, but fallback if not available)
    if command -v jq &> /dev/null; then
        SUCCESS_RATE=$(jq -r '.testInfo.successRate' google-maps-poc-report.json 2>/dev/null || echo "N/A")
        DETECTION_FLAGS=$(jq -r '.testInfo.detectionFlags' google-maps-poc-report.json 2>/dev/null || echo "N/A")
        AVG_CONFIDENCE=$(jq -r '.analysis.averageConfidence' google-maps-poc-report.json 2>/dev/null || echo "N/A")
        
        echo "   Success Rate: $SUCCESS_RATE"
        echo "   Detection Flags: $DETECTION_FLAGS"
        echo "   Avg Confidence: $AVG_CONFIDENCE"
    else
        echo "   (Install 'jq' for detailed summary)"
    fi
    
    echo ""
    echo "📄 View full report: cat google-maps-poc-report.json | jq"
else
    echo "⚠️  No report generated - test may have failed"
fi

echo ""
echo "🧹 Cleanup options:"
echo "   Remove report: rm google-maps-poc-report.json"
echo "   Remove all: rm google-maps-poc-report.json && npm run clean"
echo ""
echo "💡 Next Steps:"
echo "   1. Review the report for feasibility assessment"
echo "   2. Consider legal implications before proceeding"
echo "   3. Explore legitimate alternatives (APIs, open data)"
echo "   4. Consult legal counsel if considering implementation"
echo ""
echo "⚠️  REMINDER: This was educational testing only!"
