-- =====================================================
-- SAFE FIX FOR DOCUMENT TRIGGER ONLY
-- Does not interfere with other table triggers
-- =====================================================

-- Step 1: Remove only document_uploads triggers (keep others intact)
DROP TRIGGER IF EXISTS handle_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;
DROP TRIGGER IF EXISTS admin_document_update_trigger ON document_uploads;
DROP TRIGGER IF EXISTS bulletproof_document_trigger ON document_uploads;

-- Step 2: Remove only document-specific functions (keep shared ones)
DROP FUNCTION IF EXISTS handle_document_update();
DROP FUNCTION IF EXISTS safe_update_document_status();
DROP FUNCTION IF EXISTS admin_safe_document_update();
DROP FUNCTION IF EXISTS bulletproof_document_trigger();

-- NOTE: We keep update_updated_at_column() because other tables use it

-- Step 3: Create a document-specific trigger function with detailed logging
CREATE OR REPLACE FUNCTION document_approval_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- This function handles ONLY document_uploads table operations
    -- and is designed to NEVER reach the end without RETURN

    -- DETAILED LOGGING: Log entry into trigger
    RAISE NOTICE 'TRIGGER START: Operation=%, Table=%, TriggerName=%', TG_OP, TG_TABLE_NAME, TG_NAME;

    -- Handle UPDATE operations (admin dashboard approval/rejection)
    IF TG_OP = 'UPDATE' THEN
        RAISE NOTICE 'TRIGGER UPDATE: Processing document ID=%', NEW.id;
        RAISE NOTICE 'TRIGGER UPDATE: Status change % -> %', OLD.status, NEW.status;
        RAISE NOTICE 'TRIGGER UPDATE: OLD record exists=%', (OLD IS NOT NULL);
        RAISE NOTICE 'TRIGGER UPDATE: NEW record exists=%', (NEW IS NOT NULL);

        -- Always update the timestamp
        NEW.updated_at = NOW();
        RAISE NOTICE 'TRIGGER UPDATE: Set updated_at=%', NEW.updated_at;

        -- Set reviewed_at when status changes to approved or rejected
        IF NEW.status IN ('approved', 'rejected') THEN
            NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
            RAISE NOTICE 'TRIGGER UPDATE: Set reviewed_at=%', NEW.reviewed_at;
        END IF;

        -- Log the operation for debugging
        RAISE NOTICE 'TRIGGER UPDATE: About to return NEW for document %', NEW.id;

        -- ALWAYS return NEW for UPDATE operations
        RETURN NEW;
    END IF;
    
    -- Handle INSERT operations (document upload)
    IF TG_OP = 'INSERT' THEN
        RAISE NOTICE 'TRIGGER INSERT: Processing new document ID=%', NEW.id;

        -- Set default timestamps
        NEW.created_at = COALESCE(NEW.created_at, NOW());
        NEW.updated_at = COALESCE(NEW.updated_at, NOW());
        RAISE NOTICE 'TRIGGER INSERT: Set timestamps created_at=%, updated_at=%', NEW.created_at, NEW.updated_at;

        -- Set default status if not provided
        IF NEW.status IS NULL THEN
            NEW.status = 'uploaded';
            RAISE NOTICE 'TRIGGER INSERT: Set default status=%', NEW.status;
        END IF;

        RAISE NOTICE 'TRIGGER INSERT: About to return NEW for document %', NEW.id;

        -- ALWAYS return NEW for INSERT operations
        RETURN NEW;
    END IF;

    -- Handle DELETE operations (if needed)
    IF TG_OP = 'DELETE' THEN
        RAISE NOTICE 'TRIGGER DELETE: Processing document ID=%', OLD.id;
        RAISE NOTICE 'TRIGGER DELETE: About to return OLD';

        -- ALWAYS return OLD for DELETE operations
        RETURN OLD;
    END IF;

    -- Safety net: This should never be reached
    -- But we ensure we always return something
    RAISE WARNING 'TRIGGER SAFETY NET: Unexpected operation %, returning fallback', TG_OP;

    -- Return appropriate value based on operation
    IF TG_OP IN ('INSERT', 'UPDATE') THEN
        RAISE NOTICE 'TRIGGER SAFETY NET: Returning NEW';
        RETURN NEW;
    ELSE
        RAISE NOTICE 'TRIGGER SAFETY NET: Returning OLD';
        RETURN OLD;
    END IF;
    
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create the trigger for document_uploads table only
CREATE TRIGGER document_approval_trigger
    BEFORE INSERT OR UPDATE OR DELETE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION document_approval_trigger();

-- Step 5: Test the trigger with a real document
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    test_successful BOOLEAN := false;
BEGIN
    -- Find a document to test with
    SELECT id, status INTO test_doc_id, original_status
    FROM document_uploads 
    WHERE status IN ('under_review', 'uploaded')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing document approval trigger...';
        RAISE NOTICE 'Document ID: %', test_doc_id;
        RAISE NOTICE 'Original status: %', original_status;
        
        -- Test approval (exactly what admin dashboard does)
        UPDATE document_uploads 
        SET 
            status = 'approved',
            reviewed_at = NOW(),
            admin_notes = 'Test approval from trigger fix'
        WHERE id = test_doc_id;
        
        -- If we reach here, the trigger worked without error
        test_successful := true;
        RAISE NOTICE '✅ SUCCESS: Document approval test passed!';
        
        -- Test rejection too
        UPDATE document_uploads 
        SET 
            status = 'rejected',
            reviewed_at = NOW(),
            rejection_reason = 'Test rejection',
            admin_notes = 'Test rejection from trigger fix'
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ SUCCESS: Document rejection test passed!';
        
        -- Restore original status
        UPDATE document_uploads 
        SET 
            status = original_status::document_status,
            reviewed_at = NULL,
            rejection_reason = NULL,
            admin_notes = NULL
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ Document restored to original status: %', original_status;
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents found for testing';
        RAISE NOTICE 'Upload a document in the driver app first';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ TEST FAILED: %', SQLERRM;
        RAISE NOTICE 'Error code: %', SQLSTATE;
        
        -- Try to restore document state
        IF test_doc_id IS NOT NULL THEN
            BEGIN
                UPDATE document_uploads 
                SET 
                    status = original_status::document_status,
                    reviewed_at = NULL,
                    rejection_reason = NULL,
                    admin_notes = NULL
                WHERE id = test_doc_id;
                RAISE NOTICE 'Document restored after error';
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Could not restore document state';
            END;
        END IF;
END $$;

-- Step 6: Verify the new trigger is active
SELECT 
    '=== DOCUMENT TRIGGER STATUS ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    '✅ Active' as status
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
AND trigger_name = 'document_approval_trigger';

-- Step 7: Verify other triggers are still intact
SELECT 
    '=== OTHER TRIGGERS STILL ACTIVE ===' as section,
    event_object_table as table_name,
    trigger_name,
    '✅ Preserved' as status
FROM information_schema.triggers 
WHERE event_object_table IN ('profiles', 'drivers', 'trips')
AND trigger_name LIKE '%updated_at%'
ORDER BY event_object_table, trigger_name;

-- Step 8: Show current documents for testing
SELECT 
    '=== AVAILABLE DOCUMENTS FOR TESTING ===' as section,
    id,
    document_type,
    status,
    created_at
FROM document_uploads 
ORDER BY created_at DESC
LIMIT 3;

SELECT '🎉 SAFE FIX COMPLETE!' as result;
SELECT 'Admin dashboard document approval should now work without affecting other tables' as message;
