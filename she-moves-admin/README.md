# SheMove Admin Dashboard

A Next.js 14 admin dashboard for managing SheMove driver verification and operations.

## 🚀 Features

### ✅ **COMPLETED**
- **Authentication System**: Admin login with Supabase Auth
- **Dashboard Layout**: Responsive sidebar navigation and header
- **Dashboard Overview**: Stats cards and activity feeds
- **Database Integration**: Supabase client configuration
- **Role-based Access**: Admin user verification
- **Modern UI**: Tailwind CSS with pink/purple branding

### 🔄 **IN PROGRESS**
- Document review interface
- Driver management pages
- Real-time notifications

### 📋 **PLANNED**
- Analytics and reporting
- Support ticket system
- Admin user management
- Settings and configuration

## 🛠️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **State Management**: <PERSON>ustand (ready)
- **Forms**: React Hook Form + Zod (ready)
- **Tables**: TanStack Table (ready)

## 📁 Project Structure

```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/           # Admin login page
│   │   └── layout.tsx       # Auth layout
│   ├── (dashboard)/
│   │   ├── dashboard/       # Main dashboard
│   │   └── layout.tsx       # Dashboard layout with sidebar
│   ├── unauthorized/        # Access denied page
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx            # Redirects to login
├── components/
│   ├── dashboard/
│   │   ├── stats-cards.tsx
│   │   ├── verification-queue.tsx
│   │   └── recent-activity.tsx
│   └── layout/
│       ├── sidebar.tsx
│       └── header.tsx
└── lib/
    ├── supabase.ts         # Database client & types
    ├── auth.ts             # Authentication utilities
    └── utils.ts            # Helper functions
```

## 🔧 Setup Instructions

### 1. Environment Configuration

Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

### 2. Database Setup

The admin dashboard uses the same Supabase database as the driver app. Ensure these tables exist:

- `drivers` - Driver information and verification status
- `document_uploads` - Uploaded documents
- `document_reviews` - Document review workflow
- `admin_users` - Admin user management

### 3. Install Dependencies

```bash
npm install
```

### 4. Run Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` - it will redirect to `/login`

## 🔐 Authentication Flow

1. **Login Page** (`/login`): Admin email/password authentication
2. **Admin Verification**: Checks if user exists in `admin_users` table
3. **Dashboard Access**: Redirects to `/dashboard` on successful auth
4. **Protected Routes**: All dashboard routes require admin authentication

## 📊 Dashboard Features

### Stats Cards
- Total Drivers
- Pending Verification
- Approved Drivers
- Pending Documents

### Verification Queue
- Prioritized document review list
- Quick action buttons
- Status indicators

### Recent Activity
- Real-time activity feed
- Admin action history
- Timestamp tracking

## 🎨 Design System

### Colors
- **Primary**: Pink (#E91E63, #F9E6F7)
- **Secondary**: Purple (#9C27B0)
- **Success**: Green (#4CAF50)
- **Warning**: Yellow (#FF9800)
- **Error**: Red (#F44336)

### Typography
- **Font**: Inter (Google Fonts)
- **Headings**: Bold, various sizes
- **Body**: Regular weight, readable sizes

## 🔗 Integration Points

### Shared Database
- Uses same Supabase instance as driver mobile app
- Real-time sync between admin actions and driver app
- Consistent data models and relationships

### API Endpoints
- Server-side API routes for admin operations
- Client-side API calls for real-time updates
- Secure service role access for admin functions

## 🚀 Deployment

### Vercel (Recommended)
1. Connect GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables for Production
```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
NODE_ENV=production
```

## 📈 Next Steps

1. **Complete Document Review Interface**
   - Document viewer with zoom/pan
   - Approval/rejection workflow
   - Batch processing

2. **Build Driver Management**
   - Driver profile pages
   - Verification status management
   - Communication tools

3. **Add Real-time Features**
   - Live notifications
   - Real-time dashboard updates
   - WebSocket connections

4. **Implement Analytics**
   - Performance metrics
   - Operational reports
   - Data visualization

## 🤝 Contributing

1. Follow the existing code structure
2. Use TypeScript for all new files
3. Maintain the pink/purple branding
4. Test authentication flows thoroughly
5. Ensure mobile responsiveness

## 📞 Support

For technical issues or questions:
- Check the Supabase dashboard for database issues
- Review authentication flow for access problems
- Verify environment variables are set correctly
