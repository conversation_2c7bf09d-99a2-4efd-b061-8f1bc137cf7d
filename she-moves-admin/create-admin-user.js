#!/usr/bin/env node

/**
 * Create admin user for SheMove Admin Dashboard
 * This script creates both the auth user and profile record
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// We need the service role key to create users programmatically
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.log('   Please add your Supabase service role key to .env file');
  console.log('   You can find it in your Supabase project settings > API');
  console.log('   Add this line to your .env file:');
  console.log('   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here');
  process.exit(1);
}

const supabase = createClient(
  process.env.SUPABASE_URL,
  SUPABASE_SERVICE_ROLE_KEY
);

async function createAdminUser() {
  console.log('🔧 Creating admin user...\n');

  const adminEmail = '<EMAIL>';
  const adminPassword = '12345678';

  try {
    // 1. Check if user already exists
    console.log('1. Checking if admin user already exists...');
    
    const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.error('❌ Error checking existing users:', listError);
      return;
    }

    const existingUser = existingUsers.users.find(user => user.email === adminEmail);
    
    if (existingUser) {
      console.log('⚠️  Admin user already exists in auth.users');
      console.log(`   User ID: ${existingUser.id}`);
      console.log(`   Email: ${existingUser.email}`);
      console.log(`   Created: ${existingUser.created_at}`);
      
      // Check if profile exists
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', existingUser.id)
        .single();
        
      if (profileError && profileError.code !== 'PGRST116') {
        console.error('❌ Error checking profile:', profileError);
        return;
      }
      
      if (!profile) {
        console.log('❌ Profile missing - creating profile...');
        await createProfile(existingUser.id, adminEmail);
      } else {
        console.log('✅ Profile already exists');
        console.log(`   User Type: ${profile.user_type}`);
        
        if (profile.user_type !== 'admin') {
          console.log('🔧 Updating user type to admin...');
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ user_type: 'admin' })
            .eq('id', profile.id);
            
          if (updateError) {
            console.error('❌ Error updating user type:', updateError);
          } else {
            console.log('✅ User type updated to admin');
          }
        }
      }
      
      console.log('\n✅ Admin user is ready!');
      return;
    }

    // 2. Create new admin user
    console.log('2. Creating new admin user...');
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: adminEmail,
      password: adminPassword,
      email_confirm: true,
      user_metadata: {
        full_name: 'Admin User',
        user_type: 'admin'
      }
    });

    if (authError) {
      console.error('❌ Error creating auth user:', authError);
      return;
    }

    if (!authData.user) {
      console.error('❌ No user data returned from auth creation');
      return;
    }

    console.log('✅ Auth user created successfully');
    console.log(`   User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);

    // 3. Create profile
    console.log('\n3. Creating admin profile...');
    await createProfile(authData.user.id, adminEmail);

    console.log('\n🎉 Admin user created successfully!');
    console.log('\n📋 Login Details:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('\n🔗 You can now login to the admin dashboard');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function createProfile(userId, email) {
  const profileData = {
    id: userId,
    email: email,
    full_name: 'Admin User',
    user_type: 'admin',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .insert(profileData)
    .select()
    .single();

  if (profileError) {
    console.error('❌ Error creating profile:', profileError);
    throw profileError;
  }

  console.log('✅ Profile created successfully');
  console.log(`   Profile ID: ${profile.id}`);
  console.log(`   User Type: ${profile.user_type}`);
  
  return profile;
}

// Run the creation
createAdminUser().then(() => {
  console.log('\n🏁 Admin user creation complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Admin user creation failed:', error);
  process.exit(1);
});
