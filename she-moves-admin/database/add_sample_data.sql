-- =====================================================
-- ADD SAMPLE DATA FOR TESTING
-- =====================================================
-- This adds sample document uploads and reviews using your existing driver

-- =====================================================
-- 1. INSERT SAMPLE DOCUMENT UPLOADS
-- =====================================================

-- Using your existing driver ID: f2391652-176b-4920-bb7a-11f6f15188a7
INSERT INTO document_uploads (
    id,
    driver_id,
    document_type,
    file_name,
    file_size,
    file_url,
    mime_type,
    status,
    created_at
) VALUES 
    (
        gen_random_uuid(),
        'f2391652-176b-4920-bb7a-11f6f15188a7',
        'drivers_license',
        'license_yaya.jpg',
        1024000,
        '/uploads/license_yaya.jpg',
        'image/jpeg',
        'pending',
        NOW() - INTERVAL '2 days'
    ),
    (
        gen_random_uuid(),
        'f2391652-176b-4920-bb7a-11f6f15188a7',
        'vehicle_registration',
        'registration_yaya.pdf',
        2048000,
        '/uploads/registration_yaya.pdf',
        'application/pdf',
        'pending',
        NOW() - INTERVAL '1 day'
    ),
    (
        gen_random_uuid(),
        'f2391652-176b-4920-bb7a-11f6f15188a7',
        'insurance',
        'insurance_yaya.pdf',
        1536000,
        '/uploads/insurance_yaya.pdf',
        'application/pdf',
        'pending',
        NOW() - INTERVAL '3 hours'
    )
ON CONFLICT (driver_id, document_type) DO NOTHING;

-- =====================================================
-- 2. INSERT SAMPLE DOCUMENT REVIEWS
-- =====================================================

-- Create document reviews for the uploaded documents
INSERT INTO document_reviews (
    id,
    document_id,
    reviewer_id,
    status,
    review_notes,
    created_at
)
SELECT 
    gen_random_uuid(),
    du.id,
    au.id,
    'pending',
    'Awaiting review',
    du.created_at
FROM document_uploads du
CROSS JOIN admin_users au
WHERE du.driver_id = 'f2391652-176b-4920-bb7a-11f6f15188a7'
  AND au.email = '<EMAIL>'
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. ADD MORE SAMPLE ACTIVITY LOGS
-- =====================================================

-- Insert sample activity logs related to the driver
INSERT INTO admin_activity_logs (admin_id, action, target_type, target_id, details, created_at)
SELECT 
    au.id,
    'Document uploaded',
    'document',
    du.id,
    jsonb_build_object(
        'document_type', du.document_type,
        'driver_license', 'YAYA',
        'file_name', du.file_name
    ),
    du.created_at + INTERVAL '5 minutes'
FROM admin_users au
CROSS JOIN document_uploads du
WHERE au.email = '<EMAIL>'
  AND du.driver_id = 'f2391652-176b-4920-bb7a-11f6f15188a7'
ON CONFLICT DO NOTHING;

-- Add review started activity
INSERT INTO admin_activity_logs (admin_id, action, target_type, target_id, details, created_at)
SELECT 
    au.id,
    'Document review started',
    'document',
    du.id,
    jsonb_build_object(
        'document_type', du.document_type,
        'driver_license', 'YAYA',
        'status', 'pending'
    ),
    NOW() - INTERVAL '1 hour'
FROM admin_users au
CROSS JOIN document_uploads du
WHERE au.email = '<EMAIL>'
  AND du.driver_id = 'f2391652-176b-4920-bb7a-11f6f15188a7'
  AND du.document_type = 'drivers_license'
LIMIT 1
ON CONFLICT DO NOTHING;

-- Add driver verification activity
INSERT INTO admin_activity_logs (admin_id, action, target_type, target_id, details, created_at)
SELECT 
    au.id,
    'Driver verification in progress',
    'driver',
    d.id,
    jsonb_build_object(
        'driver_license', d.license_number,
        'vehicle', d.vehicle_make || ' ' || d.vehicle_model,
        'status', d.verification_status
    ),
    NOW() - INTERVAL '30 minutes'
FROM admin_users au
CROSS JOIN drivers d
WHERE au.email = '<EMAIL>'
  AND d.id = 'f2391652-176b-4920-bb7a-11f6f15188a7'
ON CONFLICT DO NOTHING;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'SAMPLE DATA ADDED SUCCESSFULLY!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Added sample document uploads for driver YAYA';
    RAISE NOTICE 'Added sample document reviews (pending status)';
    RAISE NOTICE 'Added sample admin activity logs';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Your dashboard should now show:';
    RAISE NOTICE '- Pending documents in verification queue';
    RAISE NOTICE '- Recent admin activities';
    RAISE NOTICE '- Updated stats counts';
    RAISE NOTICE '=================================================';
END $$;
