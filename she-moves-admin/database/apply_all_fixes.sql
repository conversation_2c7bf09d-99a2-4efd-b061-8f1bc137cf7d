-- =====================================================
-- APPLY ALL DOCUMENT MANAGEMENT FIXES
-- Run this script to fix all three critical issues
-- =====================================================

-- Fix 1: Ensure document_uploads table has all required columns
ALTER TABLE document_uploads 
ADD COLUMN IF NOT EXISTS admin_notes TEXT,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS reviewed_by UUID REFERENCES profiles(id) ON DELETE SET NULL;

-- Fix 2: Add 'under_review' status to enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Fix 3: Update storage bucket to public
UPDATE storage.buckets 
SET public = true 
WHERE id = 'driver-documents';

-- Fix 4: Update storage policies for public bucket
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;

CREATE POLICY "Allow authenticated uploads to driver-documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Allow public read access to driver-documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'driver-documents');

CREATE POLICY "Allow authenticated updates to driver-documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Allow authenticated deletes from driver-documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Verification queries
SELECT 'FIXES APPLIED SUCCESSFULLY' as result;

SELECT 'Document table columns:' as check_type, column_name
FROM information_schema.columns 
WHERE table_name = 'document_uploads' 
AND column_name IN ('admin_notes', 'rejection_reason', 'reviewed_at')
ORDER BY column_name;

SELECT 'Storage bucket status:' as check_type, 
       CASE WHEN public THEN 'Public ✅' ELSE 'Private ⚠️' END as status
FROM storage.buckets 
WHERE id = 'driver-documents';

SELECT 'Document status values:' as check_type, enumlabel as status_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;
