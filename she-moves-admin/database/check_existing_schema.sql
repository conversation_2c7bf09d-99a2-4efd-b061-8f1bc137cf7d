-- =====================================================
-- CHECK EXISTING DATABASE SCHEMA
-- =====================================================
-- Run this first to see what tables and columns already exist

-- Check existing tables
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check drivers table structure (if it exists)
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'drivers' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check profiles table structure (if it exists)
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check existing enums
SELECT t.typname, e.enumlabel
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN ('admin_role', 'review_status', 'document_type', 'document_status', 'verification_status')
ORDER BY t.typname, e.enumsortorder;
