-- =====================================================
-- FIXED ADMIN DASHBOARD SETUP
-- =====================================================
-- This creates the admin tables compatible with your existing database

-- =====================================================
-- 1. CREATE ADMIN USERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role admin_role NOT NULL DEFAULT 'reviewer',
    department TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL, -- Will reference drivers table
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    reviewed_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, document_type)
);

-- =====================================================
-- 3. CREATE DOCUMENT REVIEWS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES admin_users(id),
    status review_status DEFAULT 'pending',
    review_notes TEXT,
    rejection_reason TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    review_started_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CREATE ADMIN ACTIVITY LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admin_users(id),
    action TEXT NOT NULL,
    target_type TEXT, -- 'driver', 'document', 'ticket', 'user'
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE BASIC INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);

CREATE INDEX IF NOT EXISTS idx_document_reviews_status ON document_reviews(status);
CREATE INDEX IF NOT EXISTS idx_document_reviews_created_at ON document_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);

-- =====================================================
-- 6. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 7. CREATE RLS POLICIES (drop existing first)
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Service role can manage admin_users" ON admin_users;
DROP POLICY IF EXISTS "Service role can manage document_uploads" ON document_uploads;
DROP POLICY IF EXISTS "Service role can manage document_reviews" ON document_reviews;
DROP POLICY IF EXISTS "Service role can manage admin_activity_logs" ON admin_activity_logs;

-- Create new policies
CREATE POLICY "Service role can manage admin_users" ON admin_users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage document_uploads" ON document_uploads
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage document_reviews" ON document_reviews
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage admin_activity_logs" ON admin_activity_logs
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 8. CREATE SAMPLE ADMIN USER
-- =====================================================

-- Insert a sample admin user (you can modify the email)
INSERT INTO admin_users (email, full_name, role, permissions, is_active)
VALUES (
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true}',
    true
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- 9. ADD MISSING COLUMNS TO DRIVERS TABLE (if needed)
-- =====================================================

-- Add full_name column to drivers table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drivers' AND column_name = 'full_name') THEN
        ALTER TABLE drivers ADD COLUMN full_name TEXT;
    END IF;
END $$;

-- Add phone_number column to drivers table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'drivers' AND column_name = 'phone_number') THEN
        ALTER TABLE drivers ADD COLUMN phone_number TEXT;
    END IF;
END $$;

-- =====================================================
-- 10. CREATE SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample drivers (using existing drivers table structure with required fields)
INSERT INTO drivers (
    id,
    user_id,
    license_number,
    vehicle_make,
    vehicle_model,
    vehicle_year,
    vehicle_color,
    vehicle_plate,
    full_name,
    phone_number,
    verification_status,
    created_at
)
VALUES
    (
        gen_random_uuid(),
        gen_random_uuid(),
        'DL001234567',
        'Toyota',
        'Corolla',
        2020,
        'White',
        'CA123GP',
        'Sarah Johnson',
        '+27123456789',
        'pending',
        NOW() - INTERVAL '2 days'
    ),
    (
        gen_random_uuid(),
        gen_random_uuid(),
        'DL002345678',
        'Honda',
        'Civic',
        2019,
        'Silver',
        'CA456GP',
        'Maria Garcia',
        '+27987654321',
        'pending',
        NOW() - INTERVAL '1 day'
    ),
    (
        gen_random_uuid(),
        gen_random_uuid(),
        'DL003456789',
        'Nissan',
        'Micra',
        2021,
        'Blue',
        'CA789GP',
        'Aisha Patel',
        '+27555123456',
        'approved',
        NOW() - INTERVAL '3 days'
    )
ON CONFLICT (license_number) DO NOTHING;

-- Insert sample document uploads and reviews
DO $$
DECLARE
    driver_id_1 UUID;
    driver_id_2 UUID;
    doc_id_1 UUID;
    doc_id_2 UUID;
BEGIN
    -- Get sample driver IDs
    SELECT id INTO driver_id_1 FROM drivers WHERE full_name = 'Sarah Johnson' LIMIT 1;
    SELECT id INTO driver_id_2 FROM drivers WHERE full_name = 'Maria Garcia' LIMIT 1;
    
    IF driver_id_1 IS NOT NULL THEN
        -- Insert sample documents
        INSERT INTO document_uploads (id, driver_id, document_type, file_name, file_size, file_url, mime_type, status, created_at)
        VALUES 
            (gen_random_uuid(), driver_id_1, 'drivers_license', 'license_sarah.jpg', 1024000, '/uploads/license_sarah.jpg', 'image/jpeg', 'pending', NOW() - INTERVAL '2 days'),
            (gen_random_uuid(), driver_id_2, 'vehicle_registration', 'registration_maria.pdf', 2048000, '/uploads/registration_maria.pdf', 'application/pdf', 'pending', NOW() - INTERVAL '1 day')
        ON CONFLICT DO NOTHING;
        
        -- Insert sample document reviews
        SELECT id INTO doc_id_1 FROM document_uploads WHERE driver_id = driver_id_1 LIMIT 1;
        SELECT id INTO doc_id_2 FROM document_uploads WHERE driver_id = driver_id_2 LIMIT 1;
        
        IF doc_id_1 IS NOT NULL THEN
            INSERT INTO document_reviews (document_id, status, created_at)
            VALUES 
                (doc_id_1, 'pending', NOW() - INTERVAL '2 days'),
                (doc_id_2, 'pending', NOW() - INTERVAL '1 day')
            ON CONFLICT DO NOTHING;
        END IF;
    END IF;
END $$;

-- Insert sample activity logs
INSERT INTO admin_activity_logs (admin_id, action, target_type, details, created_at)
SELECT 
    au.id,
    'Document review started',
    'document',
    '{"document_type": "drivers_license", "driver_name": "Sarah Johnson"}',
    NOW() - INTERVAL '1 hour'
FROM admin_users au 
WHERE au.email = '<EMAIL>'
LIMIT 1
ON CONFLICT DO NOTHING;

-- Add more sample activity logs
INSERT INTO admin_activity_logs (admin_id, action, target_type, details, created_at)
SELECT 
    au.id,
    'Driver license approved',
    'document',
    '{"document_type": "drivers_license", "driver_name": "Aisha Patel"}',
    NOW() - INTERVAL '2 hours'
FROM admin_users au 
WHERE au.email = '<EMAIL>'
LIMIT 1
ON CONFLICT DO NOTHING;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'ADMIN DASHBOARD SETUP COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created tables: admin_users, document_uploads, document_reviews, admin_activity_logs';
    RAISE NOTICE 'Created sample admin user: <EMAIL>';
    RAISE NOTICE 'Created sample data for testing';
    RAISE NOTICE 'Updated drivers table with missing columns';
    RAISE NOTICE '=================================================';
END $$;
