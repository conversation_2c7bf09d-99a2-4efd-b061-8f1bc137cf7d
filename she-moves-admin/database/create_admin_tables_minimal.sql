-- =====================================================
-- MINIMAL ADMIN DASHBOARD SETUP
-- =====================================================
-- This creates only the admin tables without touching existing drivers table

-- =====================================================
-- 1. CREATE ADMIN USERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role admin_role NOT NULL DEFAULT 'reviewer',
    department TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL, -- Will reference drivers table
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    reviewed_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, document_type)
);

-- =====================================================
-- 3. CREATE DOCUMENT REVIEWS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES admin_users(id),
    status review_status DEFAULT 'pending',
    review_notes TEXT,
    rejection_reason TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    review_started_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CREATE ADMIN ACTIVITY LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admin_users(id),
    action TEXT NOT NULL,
    target_type TEXT, -- 'driver', 'document', 'ticket', 'user'
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE BASIC INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);

CREATE INDEX IF NOT EXISTS idx_document_reviews_status ON document_reviews(status);
CREATE INDEX IF NOT EXISTS idx_document_reviews_created_at ON document_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);

-- =====================================================
-- 6. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 7. CREATE RLS POLICIES (drop existing first)
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Service role can manage admin_users" ON admin_users;
DROP POLICY IF EXISTS "Service role can manage document_uploads" ON document_uploads;
DROP POLICY IF EXISTS "Service role can manage document_reviews" ON document_reviews;
DROP POLICY IF EXISTS "Service role can manage admin_activity_logs" ON admin_activity_logs;

-- Create new policies
CREATE POLICY "Service role can manage admin_users" ON admin_users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage document_uploads" ON document_uploads
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage document_reviews" ON document_reviews
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage admin_activity_logs" ON admin_activity_logs
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 8. CREATE SAMPLE ADMIN USER
-- =====================================================

-- Insert a sample admin user (you can modify the email)
INSERT INTO admin_users (email, full_name, role, permissions, is_active)
VALUES (
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true}',
    true
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- 9. CREATE SAMPLE ACTIVITY LOGS (without drivers)
-- =====================================================

-- Insert sample activity logs
INSERT INTO admin_activity_logs (admin_id, action, target_type, details, created_at)
SELECT 
    au.id,
    'System initialized',
    'system',
    '{"message": "Admin dashboard setup completed"}',
    NOW() - INTERVAL '1 hour'
FROM admin_users au 
WHERE au.email = '<EMAIL>'
LIMIT 1
ON CONFLICT DO NOTHING;

INSERT INTO admin_activity_logs (admin_id, action, target_type, details, created_at)
SELECT 
    au.id,
    'Admin user created',
    'user',
    '{"user_email": "<EMAIL>", "role": "super_admin"}',
    NOW() - INTERVAL '30 minutes'
FROM admin_users au 
WHERE au.email = '<EMAIL>'
LIMIT 1
ON CONFLICT DO NOTHING;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'MINIMAL ADMIN DASHBOARD SETUP COMPLETED!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created tables: admin_users, document_uploads, document_reviews, admin_activity_logs';
    RAISE NOTICE 'Created sample admin user: <EMAIL>';
    RAISE NOTICE 'Created basic activity logs';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'NOTE: No sample drivers or documents created.';
    RAISE NOTICE 'The dashboard will show empty states until real data is added.';
    RAISE NOTICE '=================================================';
END $$;
