-- =====================================================
-- SIMPLE ADMIN DASHBOARD SETUP
-- =====================================================
-- This creates the minimum required tables for the admin dashboard

-- =====================================================
-- 1. CREATE ENUMS (with error handling)
-- =====================================================

DO $$ BEGIN
    CREATE TYPE admin_role AS ENUM ('super_admin', 'admin', 'reviewer', 'support');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE review_status AS ENUM ('pending', 'in_review', 'approved', 'rejected', 'requires_resubmission');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_type AS ENUM ('drivers_license', 'vehicle_registration', 'insurance', 'vehicle_photo', 'profile_photo');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_status AS ENUM ('uploaded', 'pending', 'approved', 'rejected', 'expired');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- 2. CREATE ADMIN USERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role admin_role NOT NULL DEFAULT 'reviewer',
    department TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL, -- Will reference drivers table when it exists
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    reviewed_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, document_type)
);

-- =====================================================
-- 4. CREATE DOCUMENT REVIEWS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES admin_users(id),
    status review_status DEFAULT 'pending',
    review_notes TEXT,
    rejection_reason TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    review_started_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE ADMIN ACTIVITY LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admin_users(id),
    action TEXT NOT NULL,
    target_type TEXT, -- 'driver', 'document', 'ticket', 'user'
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. CREATE BASIC INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);

CREATE INDEX IF NOT EXISTS idx_document_reviews_status ON document_reviews(status);
CREATE INDEX IF NOT EXISTS idx_document_reviews_created_at ON document_reviews(created_at);

CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);

-- =====================================================
-- 7. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 8. CREATE BASIC RLS POLICIES
-- =====================================================

-- Allow service role to bypass RLS for admin operations
CREATE POLICY IF NOT EXISTS "Service role can manage admin_users" ON admin_users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY IF NOT EXISTS "Service role can manage document_uploads" ON document_uploads
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY IF NOT EXISTS "Service role can manage document_reviews" ON document_reviews
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY IF NOT EXISTS "Service role can manage admin_activity_logs" ON admin_activity_logs
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- 9. CREATE SAMPLE ADMIN USER
-- =====================================================

-- Insert a sample admin user (you can modify the email)
INSERT INTO admin_users (email, full_name, role, permissions, is_active)
VALUES (
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true}',
    true
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- 10. CREATE SAMPLE DATA FOR TESTING
-- =====================================================

-- Create a simple drivers table if it doesn't exist (for testing)
CREATE TABLE IF NOT EXISTS drivers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    full_name TEXT,
    phone_number TEXT,
    verification_status TEXT DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample drivers (basic structure)
INSERT INTO drivers (id, user_id, full_name, phone_number, verification_status, created_at)
VALUES
    (gen_random_uuid(), gen_random_uuid(), 'Sarah Johnson', '+27123456789', 'pending', NOW() - INTERVAL '2 days'),
    (gen_random_uuid(), gen_random_uuid(), 'Maria Garcia', '+27987654321', 'pending', NOW() - INTERVAL '1 day'),
    (gen_random_uuid(), gen_random_uuid(), 'Aisha Patel', '+27555123456', 'approved', NOW() - INTERVAL '3 days')
ON CONFLICT DO NOTHING;

-- Insert sample document uploads
DO $$
DECLARE
    driver_id_1 UUID;
    driver_id_2 UUID;
    doc_id_1 UUID;
    doc_id_2 UUID;
BEGIN
    -- Get sample driver IDs
    SELECT id INTO driver_id_1 FROM drivers WHERE full_name = 'Sarah Johnson' LIMIT 1;
    SELECT id INTO driver_id_2 FROM drivers WHERE full_name = 'Maria Garcia' LIMIT 1;
    
    IF driver_id_1 IS NOT NULL THEN
        -- Insert sample documents
        INSERT INTO document_uploads (id, driver_id, document_type, file_name, file_size, file_url, mime_type, status, created_at)
        VALUES 
            (gen_random_uuid(), driver_id_1, 'drivers_license', 'license_sarah.jpg', 1024000, '/uploads/license_sarah.jpg', 'image/jpeg', 'pending', NOW() - INTERVAL '2 days'),
            (gen_random_uuid(), driver_id_2, 'vehicle_registration', 'registration_maria.pdf', 2048000, '/uploads/registration_maria.pdf', 'application/pdf', 'pending', NOW() - INTERVAL '1 day')
        ON CONFLICT DO NOTHING;
        
        -- Insert sample document reviews
        SELECT id INTO doc_id_1 FROM document_uploads WHERE driver_id = driver_id_1 LIMIT 1;
        SELECT id INTO doc_id_2 FROM document_uploads WHERE driver_id = driver_id_2 LIMIT 1;
        
        IF doc_id_1 IS NOT NULL THEN
            INSERT INTO document_reviews (document_id, status, created_at)
            VALUES 
                (doc_id_1, 'pending', NOW() - INTERVAL '2 days'),
                (doc_id_2, 'pending', NOW() - INTERVAL '1 day')
            ON CONFLICT DO NOTHING;
        END IF;
    END IF;
END $$;

-- Insert sample activity logs
INSERT INTO admin_activity_logs (admin_id, action, target_type, details, created_at)
SELECT 
    au.id,
    'Document review started',
    'document',
    '{"document_type": "drivers_license", "driver_name": "Sarah Johnson"}',
    NOW() - INTERVAL '1 hour'
FROM admin_users au 
WHERE au.email = '<EMAIL>'
LIMIT 1
ON CONFLICT DO NOTHING;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'ADMIN DASHBOARD SETUP COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created tables: admin_users, document_uploads, document_reviews, admin_activity_logs';
    RAISE NOTICE 'Created sample admin user: <EMAIL>';
    RAISE NOTICE 'Created sample data for testing';
    RAISE NOTICE '=================================================';
END $$;
