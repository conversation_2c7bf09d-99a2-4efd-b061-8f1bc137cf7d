-- =====================================================
-- CREATE ADMIN USER FOR DOCUMENT APPROVAL
-- =====================================================
-- This creates an admin user to enable activity logging

-- Create admin user if none exists
INSERT INTO admin_users (email, full_name, role, permissions, is_active)
VALUES (
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true}',
    true
) ON CONFLICT (email) DO NOTHING;

-- Verify admin user was created
SELECT 'admin_user_created' as status, id, email, full_name, role 
FROM admin_users 
WHERE email = '<EMAIL>';

-- Show all admin users
SELECT 'all_admin_users' as status, id, email, full_name, role, is_active, created_at
FROM admin_users
ORDER BY created_at DESC;
