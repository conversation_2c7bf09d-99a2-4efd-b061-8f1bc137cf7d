-- =====================================================
-- DEBUG DATA RELATIONSHIPS
-- =====================================================
-- This helps identify why driver lookups are failing

-- =====================================================
-- 1. CHECK DOCUMENT UPLOADS TABLE
-- =====================================================

SELECT 
    'document_uploads' as table_name,
    id,
    driver_id,
    document_type,
    status,
    created_at
FROM document_uploads
ORDER BY created_at DESC
LIMIT 10;

-- =====================================================
-- 2. CHECK DRIVERS TABLE
-- =====================================================

SELECT 
    'drivers' as table_name,
    id,
    license_number,
    vehicle_make,
    vehicle_model,
    verification_status,
    created_at
FROM drivers
ORDER BY created_at DESC
LIMIT 10;

-- =====================================================
-- 3. CHECK DOCUMENT REVIEWS TABLE
-- =====================================================

SELECT 
    'document_reviews' as table_name,
    id,
    document_id,
    status,
    created_at
FROM document_reviews
WHERE status = 'pending'
ORDER BY created_at DESC
LIMIT 10;

-- =====================================================
-- 4. CHECK RELATIONSHIPS - JOIN TABLES
-- =====================================================

SELECT 
    'relationship_check' as check_type,
    dr.id as review_id,
    dr.status as review_status,
    du.id as document_id,
    du.document_type,
    du.driver_id,
    d.id as actual_driver_id,
    d.license_number,
    CASE 
        WHEN d.id IS NULL THEN 'DRIVER NOT FOUND'
        ELSE 'DRIVER FOUND'
    END as driver_status
FROM document_reviews dr
LEFT JOIN document_uploads du ON dr.document_id = du.id
LEFT JOIN drivers d ON du.driver_id = d.id
WHERE dr.status = 'pending'
ORDER BY dr.created_at DESC;

-- =====================================================
-- 5. CHECK FOR ORPHANED RECORDS
-- =====================================================

-- Documents without matching drivers
SELECT 
    'orphaned_documents' as issue_type,
    du.id as document_id,
    du.driver_id as missing_driver_id,
    du.document_type,
    du.created_at
FROM document_uploads du
LEFT JOIN drivers d ON du.driver_id = d.id
WHERE d.id IS NULL;

-- Reviews without matching documents
SELECT 
    'orphaned_reviews' as issue_type,
    dr.id as review_id,
    dr.document_id as missing_document_id,
    dr.status,
    dr.created_at
FROM document_reviews dr
LEFT JOIN document_uploads du ON dr.document_id = du.id
WHERE du.id IS NULL;
