-- =====================================================
-- FIX ACTIVITY LOGS PERMISSIONS - RESOLVE 403 ERROR
-- =====================================================
-- This fixes the 403 error when inserting into admin_activity_logs

-- =====================================================
-- 1. CHECK CURRENT PERMISSIONS ON ADMIN_ACTIVITY_LOGS
-- =====================================================

-- Check if table exists and current permissions
SELECT 'admin_activity_logs_check' as check_type,
       schemaname, tablename, tableowner, hasinserts, hasselects, hasupdates, hasdeletes
FROM pg_tables 
WHERE tablename = 'admin_activity_logs';

-- Check current RLS status
SELECT 'rls_status' as check_type,
       schemaname, tablename, rowsecurity, forcerowsecurity
FROM pg_tables 
WHERE tablename = 'admin_activity_logs';

-- =====================================================
-- 2. DISABLE RLS AND GRANT PERMISSIONS
-- =====================================================

-- Disable RLS on admin_activity_logs
ALTER TABLE admin_activity_logs DISABLE ROW LEVEL SECURITY;

-- Grant all permissions to authenticated users
GRANT ALL ON admin_activity_logs TO authenticated;
GRANT ALL ON admin_activity_logs TO anon;
GRANT ALL ON admin_activity_logs TO service_role;

-- Grant usage on the sequence if it exists
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- =====================================================
-- 3. VERIFY ADMIN USER EXISTS
-- =====================================================

-- Check if admin user exists
SELECT 'admin_user_check' as check_type,
       id, email, full_name, role, is_active, created_at
FROM admin_users
WHERE email = '<EMAIL>';

-- Create admin user if it doesn't exist
INSERT INTO admin_users (email, full_name, role, permissions, is_active)
VALUES (
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true}',
    true
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- 4. TEST ACTIVITY LOG INSERT
-- =====================================================

-- Test inserting into admin_activity_logs
DO $$
DECLARE
    admin_user_id UUID;
    test_log_id UUID;
BEGIN
    -- Get admin user ID
    SELECT id INTO admin_user_id 
    FROM admin_users 
    WHERE email = '<EMAIL>' 
    LIMIT 1;
    
    IF admin_user_id IS NOT NULL THEN
        -- Test insert
        INSERT INTO admin_activity_logs (
            admin_id,
            action,
            target_type,
            details,
            created_at
        ) VALUES (
            admin_user_id,
            'Test activity log insert',
            'system',
            '{"test": "permissions working"}',
            NOW()
        ) RETURNING id INTO test_log_id;
        
        RAISE NOTICE 'Successfully inserted test activity log with ID: %', test_log_id;
        
        -- Clean up test record
        DELETE FROM admin_activity_logs WHERE id = test_log_id;
        RAISE NOTICE 'Cleaned up test activity log';
    ELSE
        RAISE NOTICE 'No admin user found - cannot test activity log insert';
    END IF;
END $$;

-- =====================================================
-- 5. CHECK DOCUMENT_UPLOADS COLUMNS
-- =====================================================

-- Check what columns exist in document_uploads
SELECT 'document_uploads_columns' as check_type,
       column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'document_uploads' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 6. ADD MISSING COLUMNS IF NEEDED
-- =====================================================

-- Add reviewed_at column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_uploads' 
          AND column_name = 'reviewed_at' 
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE document_uploads ADD COLUMN reviewed_at TIMESTAMP WITH TIME ZONE;
        RAISE NOTICE 'Added reviewed_at column to document_uploads';
    ELSE
        RAISE NOTICE 'reviewed_at column already exists in document_uploads';
    END IF;
END $$;

-- Add rejection_reason column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'document_uploads' 
          AND column_name = 'rejection_reason' 
          AND table_schema = 'public'
    ) THEN
        ALTER TABLE document_uploads ADD COLUMN rejection_reason TEXT;
        RAISE NOTICE 'Added rejection_reason column to document_uploads';
    ELSE
        RAISE NOTICE 'rejection_reason column already exists in document_uploads';
    END IF;
END $$;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'ACTIVITY LOGS PERMISSIONS FIXED!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Disabled RLS on admin_activity_logs table';
    RAISE NOTICE 'Granted permissions to authenticated/anon users';
    RAISE NOTICE 'Verified admin user exists';
    RAISE NOTICE 'Added missing columns to document_uploads';
    RAISE NOTICE 'Tested activity log insert operation';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Document approval should now work completely';
    RAISE NOTICE '=================================================';
END $$;
