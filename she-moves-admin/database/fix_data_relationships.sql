-- =====================================================
-- FIX DATA RELATIONSHIPS - FOCUSED SOLUTION
-- =====================================================
-- This fixes the specific data relationship issues identified

-- =====================================================
-- 1. CHECK CURRENT DATA STATE
-- =====================================================

-- Check what's in the drivers table
SELECT 'drivers_table_check' as check_type, 
       id, user_id, license_number, verification_status, created_at
FROM drivers 
WHERE id = 'f2391652-176b-4920-bb7a-11f6f15188a7';

-- Check what's in auth.users
SELECT 'auth_users_check' as check_type,
       id, email, created_at, email_confirmed_at
FROM auth.users 
WHERE id = '36fff86c-99b6-4729-914c-3685f2752942';

-- Check what's in profiles table
SELECT 'profiles_check' as check_type,
       id, email, full_name, user_type, created_at
FROM profiles 
WHERE id = '36fff86c-99b6-4729-914c-3685f2752942';

-- =====================================================
-- 2. CREATE MISSING PROFILE RECORD
-- =====================================================

-- Insert profile record for the authenticated user
INSERT INTO profiles (id, email, full_name, user_type, created_at, updated_at)
SELECT 
    au.id,
    au.email,
    COALESCE(au.raw_user_meta_data->>'full_name', au.email) as full_name,
    'driver' as user_type,
    au.created_at,
    NOW()
FROM auth.users au
WHERE au.id = '36fff86c-99b6-4729-914c-3685f2752942'
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    updated_at = NOW();

-- =====================================================
-- 3. UPDATE DRIVERS TABLE TO LINK TO PROFILE
-- =====================================================

-- Update the driver record to ensure it has the correct user_id
UPDATE drivers 
SET user_id = '36fff86c-99b6-4729-914c-3685f2752942',
    updated_at = NOW()
WHERE id = 'f2391652-176b-4920-bb7a-11f6f15188a7';

-- =====================================================
-- 4. VERIFY THE RELATIONSHIPS
-- =====================================================

-- Check the complete relationship chain
SELECT 
    'relationship_verification' as check_type,
    d.id as driver_id,
    d.user_id as driver_user_id,
    d.license_number,
    p.id as profile_id,
    p.email as profile_email,
    p.full_name as profile_name,
    au.email as auth_email
FROM drivers d
LEFT JOIN profiles p ON d.user_id = p.id
LEFT JOIN auth.users au ON d.user_id = au.id
WHERE d.id = 'f2391652-176b-4920-bb7a-11f6f15188a7';

-- =====================================================
-- 5. CREATE RPC FUNCTION TO ACCESS AUTH.USERS
-- =====================================================

-- Create function to get user data from auth.users
CREATE OR REPLACE FUNCTION get_user_by_id(user_id UUID)
RETURNS TABLE(id UUID, email TEXT, created_at TIMESTAMPTZ)
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT au.id, au.email, au.created_at
  FROM auth.users au
  WHERE au.id = user_id;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_user_by_id(UUID) TO anon, authenticated, service_role;

-- =====================================================
-- 6. GRANT PERMISSIONS FOR PROFILES TABLE
-- =====================================================

-- Ensure the profiles table has proper permissions
GRANT SELECT ON profiles TO anon, authenticated;
GRANT ALL ON profiles TO service_role;

-- Create RLS policy for profiles if it doesn't exist
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Allow authenticated read access" ON profiles;
DROP POLICY IF EXISTS "Allow service role full access" ON profiles;

-- Create new policies
CREATE POLICY "Allow authenticated read access" ON profiles
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow service role full access" ON profiles
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DATA RELATIONSHIPS FIXED!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created profile record for authenticated user';
    RAISE NOTICE 'Updated driver record with correct user_id';
    RAISE NOTICE 'Set up proper RLS policies for profiles table';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Dashboard should now show driver names correctly';
    RAISE NOTICE '=================================================';
END $$;
