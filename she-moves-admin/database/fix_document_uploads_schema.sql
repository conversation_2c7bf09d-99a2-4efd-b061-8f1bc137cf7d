-- =====================================================
-- FIX DOCUMENT UPLOADS TABLE SCHEMA
-- Ensures all required columns exist for admin dashboard
-- =====================================================

-- Add missing columns if they don't exist
ALTER TABLE document_uploads 
ADD COLUMN IF NOT EXISTS admin_notes TEXT,
ADD COLUMN IF NOT EXISTS rejection_reason TEXT,
ADD COLUMN IF NOT EXISTS reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS reviewed_by UUID REFERENCES profiles(id) ON DELETE SET NULL;

-- Update status enum to include all required values
DO $$ 
BEGIN
    -- Check if 'under_review' status exists, if not add it
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Verify the table structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'document_uploads' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Show current document statuses
SELECT DISTINCT status, COUNT(*) as count
FROM document_uploads 
GROUP BY status;

-- Success message
SELECT 'SUCCESS: document_uploads table schema updated' as result;
