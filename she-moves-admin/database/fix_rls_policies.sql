-- =====================================================
-- FIX RLS POLICIES - REMOVE INFINITE RECURSION
-- =====================================================
-- This fixes the infinite recursion issue in RLS policies

-- =====================================================
-- 1. DROP ALL EXISTING POLICIES
-- =====================================================

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Service role can manage admin_users" ON admin_users;
DROP POLICY IF EXISTS "Service role can manage document_uploads" ON document_uploads;
DROP POLICY IF EXISTS "Service role can manage document_reviews" ON document_reviews;
DROP POLICY IF EXISTS "Service role can manage admin_activity_logs" ON admin_activity_logs;

DROP POLICY IF EXISTS "Admins can view admin users" ON admin_users;
DROP POLICY IF EXISTS "Admins can manage document reviews" ON document_reviews;
DROP POLICY IF EXISTS "Admins can view document uploads" ON document_uploads;
DROP POLICY IF EXISTS "Admins can manage drivers" ON drivers;
DROP POLICY IF EXISTS "Admins can view activity logs" ON admin_activity_logs;

-- =====================================================
-- 2. TEMPORARILY DISABLE RLS FOR TESTING
-- =====================================================

-- Disable RLS temporarily to allow the dashboard to work
ALTER TABLE admin_users DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads DISABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- 3. CREATE SIMPLE NON-RECURSIVE POLICIES
-- =====================================================

-- Re-enable RLS with simple policies
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- Simple policy: Allow all operations for service role
CREATE POLICY "Allow service role full access" ON admin_users
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role full access" ON document_uploads
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role full access" ON document_reviews
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Allow service role full access" ON admin_activity_logs
    FOR ALL USING (auth.role() = 'service_role');

-- Simple policy: Allow authenticated users to read (for now)
CREATE POLICY "Allow authenticated read access" ON admin_users
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated read access" ON document_uploads
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated read access" ON document_reviews
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated read access" ON admin_activity_logs
    FOR SELECT USING (auth.role() = 'authenticated');

-- =====================================================
-- 4. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Grant select permissions on tables
GRANT SELECT ON admin_users TO anon, authenticated;
GRANT SELECT ON document_uploads TO anon, authenticated;
GRANT SELECT ON document_reviews TO anon, authenticated;
GRANT SELECT ON admin_activity_logs TO anon, authenticated;
GRANT SELECT ON drivers TO anon, authenticated;

-- Grant all permissions to service role
GRANT ALL ON admin_users TO service_role;
GRANT ALL ON document_uploads TO service_role;
GRANT ALL ON document_reviews TO service_role;
GRANT ALL ON admin_activity_logs TO service_role;
GRANT ALL ON drivers TO service_role;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'RLS POLICIES FIXED SUCCESSFULLY!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Removed infinite recursion policies';
    RAISE NOTICE 'Created simple non-recursive policies';
    RAISE NOTICE 'Granted necessary permissions';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Dashboard should now work without RLS errors';
    RAISE NOTICE '=================================================';
END $$;
