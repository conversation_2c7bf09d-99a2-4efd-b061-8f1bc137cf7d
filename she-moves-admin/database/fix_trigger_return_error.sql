-- =====================================================
-- FIX DATABASE TRIGGER RETURN ERROR
-- Resolves "control reached end of trigger procedure without RETURN"
-- =====================================================

-- Step 1: Check existing triggers on document_uploads table
SELECT 
    'Existing Triggers on document_uploads' as info,
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 2: Fix the update_updated_at_column function
-- This function must ALWAYS return a value
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure we always have a NEW record to work with
    IF NEW IS NULL THEN
        RAISE EXCEPTION 'NEW record is null in update_updated_at_column trigger';
    END IF;
    
    -- Update the updated_at timestamp
    NEW.updated_at = NOW();
    
    -- ALWAYS return NEW for BEFORE UPDATE triggers
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but still return NEW to prevent transaction failure
        RAISE WARNING 'Error in update_updated_at_column: %', SQLERRM;
        -- Ensure updated_at is set even if there's an error
        NEW.updated_at = NOW();
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Recreate the trigger with proper error handling
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
CREATE TRIGGER update_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 4: Check for any other problematic triggers
-- Look for triggers that might not be returning values properly
SELECT 
    'Potential Problem Triggers' as info,
    t.trigger_name,
    t.event_object_table,
    p.prosrc as function_body
FROM information_schema.triggers t
JOIN pg_proc p ON p.proname = SPLIT_PART(t.action_statement, '()', 1)
WHERE t.event_object_table IN ('document_uploads', 'document_reviews')
AND p.prosrc NOT LIKE '%RETURN%'
ORDER BY t.trigger_name;

-- Step 5: Create a safer document status update function
-- This handles the document approval workflow more safely
CREATE OR REPLACE FUNCTION safe_update_document_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Always ensure we have required fields
    IF NEW.status IS NULL THEN
        NEW.status = OLD.status;
    END IF;
    
    -- Set reviewed_at when status changes to approved or rejected
    IF NEW.status IN ('approved', 'rejected') AND OLD.status != NEW.status THEN
        NEW.reviewed_at = COALESCE(NEW.reviewed_at, NOW());
    END IF;
    
    -- Update updated_at timestamp
    NEW.updated_at = NOW();
    
    -- ALWAYS return NEW
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the transaction
        RAISE WARNING 'Error in safe_update_document_status: %', SQLERRM;
        -- Ensure basic fields are set
        NEW.updated_at = NOW();
        RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Replace the problematic trigger with the safer version
DROP TRIGGER IF EXISTS safe_document_status_update ON document_uploads;
CREATE TRIGGER safe_document_status_update
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION safe_update_document_status();

-- Step 7: Verification queries
SELECT 'Trigger Functions Check' as info;

-- Check that all trigger functions return values
SELECT 
    proname as function_name,
    CASE 
        WHEN prosrc LIKE '%RETURN NEW%' OR prosrc LIKE '%RETURN OLD%' THEN 'Has RETURN ✅'
        ELSE 'Missing RETURN ❌'
    END as return_status
FROM pg_proc 
WHERE proname IN ('update_updated_at_column', 'safe_update_document_status');

-- Test the trigger by doing a safe update
SELECT 'Testing trigger with safe update' as info;

-- Success message
SELECT 'SUCCESS: Database triggers fixed and tested' as result;
