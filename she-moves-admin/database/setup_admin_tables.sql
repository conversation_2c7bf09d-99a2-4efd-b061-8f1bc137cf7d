-- =====================================================
-- SHEMOVE ADMIN DASHBOARD DATABASE SETUP
-- =====================================================
-- This script creates all necessary tables for the admin dashboard
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. CREATE ENUMS
-- =====================================================

-- Admin role enum
DO $$ BEGIN
    CREATE TYPE admin_role AS ENUM (
        'super_admin',
        'admin', 
        'reviewer',
        'support'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Review status enum
DO $$ BEGIN
    CREATE TYPE review_status AS ENUM (
        'pending',
        'in_review',
        'approved',
        'rejected',
        'requires_resubmission'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Document type enum
DO $$ BEGIN
    CREATE TYPE document_type AS ENUM (
        'drivers_license',
        'vehicle_registration',
        'insurance',
        'vehicle_photo',
        'profile_photo'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Document status enum
DO $$ BEGIN
    CREATE TYPE document_status AS ENUM (
        'uploaded',
        'pending',
        'approved',
        'rejected',
        'expired'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Verification status enum
DO $$ BEGIN
    CREATE TYPE verification_status AS ENUM (
        'pending',
        'approved',
        'rejected',
        'suspended'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- 2. CREATE ADMIN USERS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role admin_role NOT NULL DEFAULT 'reviewer',
    department TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    driver_id UUID NOT NULL, -- References drivers table
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    
    -- Verification fields
    reviewed_by UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    
    -- Expiry tracking
    expires_at TIMESTAMP WITH TIME ZONE,
    expiry_reminder_sent BOOLEAN DEFAULT FALSE,
    
    -- Upload metadata
    upload_ip_address INET,
    upload_user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(driver_id, document_type),
    CHECK (file_size > 0),
    CHECK (file_size <= 10485760) -- Max 10MB
);

-- =====================================================
-- 4. CREATE DOCUMENT REVIEWS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES admin_users(id),
    status review_status DEFAULT 'pending',
    review_notes TEXT,
    rejection_reason TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    review_started_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE/UPDATE DRIVERS TABLE
-- =====================================================

-- First, let's check if drivers table exists and add missing columns if needed
DO $$
BEGIN
    -- Create drivers table if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'drivers' AND table_schema = 'public') THEN
        CREATE TABLE drivers (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID UNIQUE NOT NULL,
            full_name TEXT NOT NULL,
            phone_number TEXT,
            license_number TEXT UNIQUE,
            license_expiry DATE,
            vehicle_make TEXT,
            vehicle_model TEXT,
            vehicle_year INTEGER,
            vehicle_color TEXT,
            vehicle_plate TEXT UNIQUE,
            verification_status verification_status DEFAULT 'pending',
            is_online BOOLEAN DEFAULT FALSE,
            rating DECIMAL(3,2) DEFAULT 5.0,
            total_trips INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;

    -- Add full_name column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'drivers' AND column_name = 'full_name' AND table_schema = 'public') THEN
        ALTER TABLE drivers ADD COLUMN full_name TEXT;
    END IF;

    -- Add phone_number column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'drivers' AND column_name = 'phone_number' AND table_schema = 'public') THEN
        ALTER TABLE drivers ADD COLUMN phone_number TEXT;
    END IF;
END $$;

-- =====================================================
-- 6. CREATE ADMIN ACTIVITY LOGS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id UUID REFERENCES admin_users(id),
    action TEXT NOT NULL,
    target_type TEXT, -- 'driver', 'document', 'ticket', 'user'
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Admin users indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_role ON admin_users(role);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);
CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);

-- Document uploads indexes
CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);
CREATE INDEX IF NOT EXISTS idx_document_uploads_type ON document_uploads(document_type);

-- Document reviews indexes
CREATE INDEX IF NOT EXISTS idx_document_reviews_document_id ON document_reviews(document_id);
CREATE INDEX IF NOT EXISTS idx_document_reviews_reviewer_id ON document_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_document_reviews_status ON document_reviews(status);
CREATE INDEX IF NOT EXISTS idx_document_reviews_created_at ON document_reviews(created_at);

-- Drivers indexes
CREATE INDEX IF NOT EXISTS idx_drivers_user_id ON drivers(user_id);
CREATE INDEX IF NOT EXISTS idx_drivers_verification_status ON drivers(verification_status);
CREATE INDEX IF NOT EXISTS idx_drivers_email ON drivers(email);

-- Admin activity logs indexes
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_target ON admin_activity_logs(target_type, target_id);

-- =====================================================
-- 8. ENABLE ROW LEVEL SECURITY
-- =====================================================

ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 9. CREATE RLS POLICIES
-- =====================================================

-- Admin users policies
DROP POLICY IF EXISTS "Admins can view admin users" ON admin_users;
CREATE POLICY "Admins can view admin users" ON admin_users
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Document reviews policies
DROP POLICY IF EXISTS "Admins can manage document reviews" ON document_reviews;
CREATE POLICY "Admins can manage document reviews" ON document_reviews
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Document uploads policies
DROP POLICY IF EXISTS "Admins can view document uploads" ON document_uploads;
CREATE POLICY "Admins can view document uploads" ON document_uploads
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Drivers policies
DROP POLICY IF EXISTS "Admins can manage drivers" ON drivers;
CREATE POLICY "Admins can manage drivers" ON drivers
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Admin activity logs policies
DROP POLICY IF EXISTS "Admins can view activity logs" ON admin_activity_logs;
CREATE POLICY "Admins can view activity logs" ON admin_activity_logs
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'ADMIN DASHBOARD TABLES CREATED SUCCESSFULLY';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created tables:';
    RAISE NOTICE '- admin_users (admin authentication)';
    RAISE NOTICE '- document_uploads (document storage)';
    RAISE NOTICE '- document_reviews (document verification)';
    RAISE NOTICE '- drivers (driver information)';
    RAISE NOTICE '- admin_activity_logs (audit trail)';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next: Create admin user and sample data';
    RAISE NOTICE '=================================================';
END $$;
