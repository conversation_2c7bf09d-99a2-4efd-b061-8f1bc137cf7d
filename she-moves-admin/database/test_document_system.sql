-- =====================================================
-- TEST DOCUMENT MANAGEMENT SYSTEM
-- Comprehensive test to verify all fixes are working
-- =====================================================

-- 1. Test document_uploads table structure
SELECT 'Testing document_uploads table structure...' as test_step;

SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'document_uploads' 
AND table_schema = 'public'
AND column_name IN ('admin_notes', 'rejection_reason', 'reviewed_at', 'reviewed_by', 'status')
ORDER BY column_name;

-- 2. Test document status enum values
SELECT 'Testing document_status enum values...' as test_step;

SELECT enumlabel as status_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- 3. Test storage bucket configuration
SELECT 'Testing storage bucket configuration...' as test_step;

SELECT 
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- 4. Test storage policies
SELECT 'Testing storage policies...' as test_step;

SELECT 
    policyname,
    cmd as operation,
    CASE 
        WHEN cmd = 'INSERT' THEN 'Upload'
        WHEN cmd = 'SELECT' THEN 'Download/View'
        WHEN cmd = 'UPDATE' THEN 'Update'
        WHEN cmd = 'DELETE' THEN 'Delete'
        ELSE cmd
    END as description
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver-documents%'
ORDER BY cmd;

-- 5. Test sample document operations
SELECT 'Testing sample document operations...' as test_step;

-- Count existing documents by status
SELECT 
    status,
    COUNT(*) as count
FROM document_uploads 
GROUP BY status
ORDER BY status;

-- Show recent documents
SELECT 
    id,
    document_type,
    status,
    created_at,
    CASE 
        WHEN file_url LIKE 'https://example.com%' THEN 'Mock URL'
        WHEN file_url LIKE '%supabase.co%' THEN 'Real Supabase URL'
        ELSE 'Other URL'
    END as url_type
FROM document_uploads 
ORDER BY created_at DESC 
LIMIT 5;

-- 6. Test admin dashboard queries
SELECT 'Testing admin dashboard queries...' as test_step;

-- Test the query used by admin dashboard for pending documents
SELECT COUNT(*) as pending_documents_count
FROM document_uploads 
WHERE status = 'under_review';

-- Test the query used by verification queue
SELECT 
    du.id,
    du.document_type,
    du.status,
    du.created_at,
    d.id as driver_id,
    p.full_name as driver_name
FROM document_uploads du
JOIN drivers d ON du.driver_id = d.id
JOIN profiles p ON d.user_id = p.id
WHERE du.status = 'under_review'
ORDER BY du.created_at ASC
LIMIT 3;

-- 7. Test update operations (simulate admin approval)
SELECT 'Testing document approval simulation...' as test_step;

-- Show what would happen during approval (without actually updating)
SELECT 
    id,
    document_type,
    status,
    'Would update to: approved' as simulation,
    'Would set reviewed_at to: ' || NOW() as timestamp_simulation,
    'Would set admin_notes to: Approved by test' as notes_simulation
FROM document_uploads 
WHERE status = 'under_review'
LIMIT 1;

-- 8. Summary report
SELECT 'SYSTEM STATUS SUMMARY' as report_section;

SELECT 
    'Document Uploads Table' as component,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'document_uploads' 
            AND column_name = 'admin_notes'
        ) THEN '✅ Ready'
        ELSE '❌ Missing admin_notes column'
    END as status;

SELECT 
    'Storage Bucket' as component,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM storage.buckets 
            WHERE id = 'driver-documents' AND public = true
        ) THEN '✅ Public bucket configured'
        WHEN EXISTS (
            SELECT 1 FROM storage.buckets 
            WHERE id = 'driver-documents' AND public = false
        ) THEN '⚠️ Private bucket (may cause upload issues)'
        ELSE '❌ Bucket not found'
    END as status;

SELECT 
    'Document Status Enum' as component,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_enum 
            WHERE enumlabel = 'under_review' 
            AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
        ) THEN '✅ under_review status available'
        ELSE '❌ Missing under_review status'
    END as status;

SELECT 'TEST COMPLETED - Check results above' as final_message;
