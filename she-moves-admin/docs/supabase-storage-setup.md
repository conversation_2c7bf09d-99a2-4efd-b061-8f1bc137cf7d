# Supabase Storage Setup for Document Management

## Overview
Currently, your document URLs are placeholders (`https://example.com/...`). To enable real document viewing and storage, you need to set up Supabase Storage.

## Current Status
✅ **Database Structure** - Document tables are properly set up
✅ **Admin Dashboard** - Review interface is working
❌ **File Storage** - Using placeholder URLs instead of real files

## Setup Steps

### 1. Create Storage Bucket in Supabase

1. **Go to your Supabase Dashboard**
   - Navigate to Storage → Buckets
   - Click "Create Bucket"

2. **Create Documents Bucket**
   ```
   Bucket Name: documents
   Public: Yes (for easy access)
   File Size Limit: 10MB
   Allowed MIME Types: 
   - application/pdf
   - image/jpeg
   - image/png
   - image/jpg
   ```

### 2. Set Up Storage Policies

Run this SQL in your Supabase SQL Editor:

```sql
-- Create storage policies for documents bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'documents',
  'documents',
  true,
  10485760, -- 10MB
  ARRAY['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']
) ON CONFLICT (id) DO NOTHING;

-- Allow authenticated users to upload documents
CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'documents' AND 
    auth.role() = 'authenticated'
  );

-- Allow public read access to documents
CREATE POLICY "Allow public read access" ON storage.objects
  FOR SELECT USING (bucket_id = 'documents');

-- Allow service role full access
CREATE POLICY "Allow service role full access" ON storage.objects
  FOR ALL USING (
    bucket_id = 'documents' AND 
    auth.role() = 'service_role'
  );
```

### 3. Update Document URLs Format

Real Supabase Storage URLs follow this format:
```
https://[your-project-id].supabase.co/storage/v1/object/public/driver-documents/[driver-id]/[document-type]_[timestamp].[extension]
```

Example (using your driver-documents bucket):
```
https://pcacyfyhxvzbjcouxzub.supabase.co/storage/v1/object/public/driver-documents/f2391652-176b-4920-bb7a-11f6f15188a7/drivers_license_1752175540627.pdf
```

### 4. Upload Test Documents

You can upload test documents via:

**Option A: Supabase Dashboard**
1. Go to Storage → documents bucket
2. Create folder: `f2391652-176b-4920-bb7a-11f6f15188a7`
3. Upload test PDF/image files

**Option B: Update Database with Real URLs**
```sql
-- Update existing records with real Supabase Storage URLs (using your driver-documents bucket)
UPDATE document_uploads
SET file_url = 'https://pcacyfyhxvzbjcouxzub.supabase.co/storage/v1/object/public/driver-documents/' || driver_id || '/' || document_type || '_' || extract(epoch from created_at)::bigint || '.pdf'
WHERE file_url LIKE 'https://example.com%' AND document_type IN ('drivers_license', 'vehicle_registration', 'insurance');

UPDATE document_uploads
SET file_url = 'https://pcacyfyhxvzbjcouxzub.supabase.co/storage/v1/object/public/driver-documents/' || driver_id || '/' || document_type || '_' || extract(epoch from created_at)::bigint || '.jpg'
WHERE file_url LIKE 'https://example.com%' AND document_type = 'profile_photo';
```

### 5. Integration with Driver App

For the driver mobile app to upload documents:

```javascript
// Example upload function
const uploadDocument = async (file, driverId, documentType) => {
  const fileName = `${documentType}_${Date.now()}.${file.type.split('/')[1]}`
  const filePath = `${driverId}/${fileName}`
  
  const { data, error } = await supabase.storage
    .from('documents')
    .upload(filePath, file)
  
  if (error) throw error
  
  const publicUrl = supabase.storage
    .from('documents')
    .getPublicUrl(filePath).data.publicUrl
  
  // Save to document_uploads table
  await supabase
    .from('document_uploads')
    .insert({
      driver_id: driverId,
      document_type: documentType,
      file_name: file.name,
      file_size: file.size,
      file_url: publicUrl,
      mime_type: file.type,
      status: 'uploaded'
    })
}
```

## Current Dashboard Behavior

✅ **Placeholder Documents** - Shows document details and metadata
✅ **Real Documents** - Will display PDF/image viewer when URLs are updated
✅ **Approval Process** - Works regardless of document type
✅ **Error Handling** - Gracefully handles missing or invalid documents

## Next Steps

1. **Immediate**: Dashboard works with placeholder documents for testing approval workflow
2. **Short-term**: Set up Supabase Storage bucket and policies
3. **Long-term**: Integrate file upload in driver mobile app

The admin dashboard is fully functional for document review and approval, even with placeholder documents!
