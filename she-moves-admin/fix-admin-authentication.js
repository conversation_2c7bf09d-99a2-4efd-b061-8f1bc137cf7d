#!/usr/bin/env node

/**
 * Fix admin authentication by creating admin_users table and record
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixAdminAuthentication() {
  console.log('🔧 Fixing admin authentication...\n');

  try {
    // 1. Check if admin_users table exists
    console.log('1. Checking admin_users table...');
    
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    if (adminError) {
      if (adminError.code === '42P01') {
        console.log('❌ admin_users table does not exist');
        console.log('   Creating admin_users table...');
        await createAdminUsersTable();
      } else {
        console.error('❌ Error querying admin_users:', adminError);
        return;
      }
    } else {
      console.log(`✅ admin_users table exists with ${adminUsers.length} records`);
    }

    // 2. Find the admin user we created in profiles
    console.log('\n2. Finding admin user in profiles table...');
    
    const { data: adminProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .eq('user_type', 'admin')
      .single();

    if (profileError) {
      console.error('❌ Error finding admin profile:', profileError);
      console.log('   Please make sure the admin user was created properly');
      return;
    }

    console.log('✅ Found admin profile:');
    console.log(`   ID: ${adminProfile.id}`);
    console.log(`   Email: ${adminProfile.email}`);
    console.log(`   Name: ${adminProfile.full_name}`);

    // 3. Check if admin_users record already exists
    console.log('\n3. Checking if admin_users record exists...');
    
    const { data: existingAdmin, error: existingError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', adminProfile.id)
      .single();

    if (existingError && existingError.code !== 'PGRST116') {
      console.error('❌ Error checking existing admin:', existingError);
      return;
    }

    if (existingAdmin) {
      console.log('✅ Admin user record already exists');
      console.log(`   Role: ${existingAdmin.role}`);
      console.log(`   Active: ${existingAdmin.is_active}`);
    } else {
      console.log('❌ Admin user record does not exist');
      console.log('   Creating admin_users record...');
      
      const adminUserData = {
        user_id: adminProfile.id,
        email: adminProfile.email,
        full_name: adminProfile.full_name || 'Admin User',
        role: 'super_admin',
        department: 'Administration',
        permissions: {
          drivers: { view: true, approve: true, reject: true, suspend: true },
          documents: { view: true, review: true, approve: true, reject: true },
          trips: { view: true, manage: true },
          analytics: { view: true },
          users: { view: true, manage: true },
          system: { view: true, manage: true }
        },
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: newAdmin, error: insertError } = await supabase
        .from('admin_users')
        .insert(adminUserData)
        .select()
        .single();

      if (insertError) {
        console.error('❌ Error creating admin user record:', insertError);
        return;
      }

      console.log('✅ Admin user record created successfully');
      console.log(`   ID: ${newAdmin.id}`);
      console.log(`   Role: ${newAdmin.role}`);
    }

    // 4. Test the admin check API
    console.log('\n4. Testing admin authentication...');
    
    // Get a session token for the admin user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: '12345678'
    });

    if (authError) {
      console.error('❌ Error signing in admin user:', authError);
      return;
    }

    console.log('✅ Admin user signed in successfully');
    console.log(`   Access Token: ${authData.session.access_token.substring(0, 20)}...`);

    // Test the admin check
    const { data: adminCheck, error: checkError } = await supabase
      .from('admin_users')
      .select('id, role, is_active, full_name, email')
      .eq('user_id', authData.user.id)
      .eq('is_active', true)
      .single();

    if (checkError) {
      console.error('❌ Admin check failed:', checkError);
    } else {
      console.log('✅ Admin check successful:');
      console.log(`   Role: ${adminCheck.role}`);
      console.log(`   Active: ${adminCheck.is_active}`);
      console.log(`   Name: ${adminCheck.full_name}`);
    }

    console.log('\n🎉 Admin authentication should now work!');
    console.log('\n📋 Next steps:');
    console.log('1. Restart the admin dashboard (npm run dev)');
    console.log('2. Go to http://localhost:3000/login');
    console.log('3. Login with: <EMAIL> / 12345678');
    console.log('4. You should now have access to the dashboard');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function createAdminUsersTable() {
  console.log('⚠️  Creating admin_users table via SQL...');
  
  const sql = `
-- Create admin_users table
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'reviewer' CHECK (role IN ('super_admin', 'reviewer', 'support', 'analyst', 'manager')),
    department TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Admin users can view their own record" ON admin_users
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Super admins can manage all admin users" ON admin_users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM admin_users 
            WHERE admin_users.user_id = auth.uid() 
            AND admin_users.role = 'super_admin' 
            AND admin_users.is_active = true
        )
    );

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);
`;

  try {
    // Execute the SQL using the service role
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error('❌ Error creating table via RPC:', error);
      console.log('\n📋 Please run this SQL manually in Supabase Dashboard → SQL Editor:');
      console.log(sql);
    } else {
      console.log('✅ admin_users table created successfully');
    }
  } catch (error) {
    console.log('\n📋 Please run this SQL manually in Supabase Dashboard → SQL Editor:');
    console.log(sql);
  }
}

// Run the fix
fixAdminAuthentication().then(() => {
  console.log('\n🏁 Fix complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fix failed:', error);
  process.exit(1);
});
