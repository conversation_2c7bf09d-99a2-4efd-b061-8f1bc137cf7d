import { requireAdminAuth } from '@/lib/auth'
import { createServerSupabase, getAdminUser } from '@/lib/supabase'
import { AdminUsersList } from '@/components/admin/admin-users-list'
import { CreateAdminUserForm } from '@/components/admin/create-admin-user-form'

export default async function AdminUsersPage() {
  const session = await requireAdminAuth()
  const supabase = createServerSupabase()
  
  // Get current admin user to check permissions
  const currentAdmin = await getAdminUser(session.user.id)
  const canCreateUsers = currentAdmin.role === 'super_admin'

  // Fetch all admin users
  const { data: adminUsers, error } = await supabase
    .from('admin_users')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    throw new Error('Failed to fetch admin users')
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Users</h1>
          <p className="text-gray-600">Manage admin user accounts and permissions</p>
        </div>
        
        {canCreateUsers && (
          <CreateAdminUserForm />
        )}
      </div>

      {/* Security notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Security Notice</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <ul className="list-disc pl-5 space-y-1">
                <li>Only Super Admins can create new admin users</li>
                <li>All admin users must use company email addresses</li>
                <li>New users receive temporary passwords via email</li>
                <li>Users must change password on first login</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Admin users list */}
      <AdminUsersList 
        adminUsers={adminUsers} 
        currentUserId={session.user.id}
        canManageUsers={canCreateUsers}
      />
    </div>
  )
}
