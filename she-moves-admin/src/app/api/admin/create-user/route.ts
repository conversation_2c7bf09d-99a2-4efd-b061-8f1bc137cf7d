import { NextRequest, NextResponse } from 'next/server'
import { createAdminSupabase, getAdminUser } from '@/lib/supabase'

// Only super admins can create new admin users
export async function POST(request: NextRequest) {
  try {
    const supabase = createAdminSupabase()
    
    // Get the current user from the request
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the requesting user is a super admin
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Check if requesting user is super admin
    const requestingAdmin = await getAdminUser(user.id)
    if (requestingAdmin.role !== 'super_admin') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get request data
    const { email, full_name, role, department } = await request.json()

    // Validate input
    if (!email || !full_name || !role) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Validate email domain (optional - add your company domains)
    const allowedDomains = ['pss-group.co.za', 'shemove.com'] // Add your domains
    const emailDomain = email.split('@')[1]
    if (!allowedDomains.includes(emailDomain)) {
      return NextResponse.json({
        error: 'Email domain not allowed. Use company email (@pss-group.co.za or @shemove.com).'
      }, { status: 400 })
    }

    // Create auth user first
    const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
      email,
      password: generateTemporaryPassword(), // Generate secure temp password
      email_confirm: true, // Auto-confirm for admin users
      user_metadata: {
        full_name,
        role: 'admin'
      }
    })

    if (createError || !authUser.user) {
      return NextResponse.json({ 
        error: 'Failed to create auth user: ' + createError?.message 
      }, { status: 500 })
    }

    // Create admin user record
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .insert({
        user_id: authUser.user.id,
        email,
        full_name,
        role,
        department: department || null,
        is_active: true,
        permissions: getDefaultPermissions(role)
      })
      .select()
      .single()

    if (adminError) {
      // Cleanup: delete auth user if admin user creation fails
      await supabase.auth.admin.deleteUser(authUser.user.id)
      return NextResponse.json({ 
        error: 'Failed to create admin user: ' + adminError.message 
      }, { status: 500 })
    }

    // TODO: Send welcome email with temporary password
    // await sendWelcomeEmail(email, full_name, temporaryPassword)

    return NextResponse.json({
      success: true,
      admin_user: adminUser,
      message: 'Admin user created successfully'
    })

  } catch (error) {
    console.error('Error creating admin user:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

function generateTemporaryPassword(): string {
  // Generate secure temporary password
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
  let password = ''
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return password
}

function getDefaultPermissions(role: string) {
  const permissions = {
    super_admin: {
      users: ['create', 'read', 'update', 'delete'],
      drivers: ['create', 'read', 'update', 'delete'],
      documents: ['create', 'read', 'update', 'delete'],
      analytics: ['read'],
      settings: ['read', 'update']
    },
    reviewer: {
      drivers: ['read', 'update'],
      documents: ['read', 'update'],
      analytics: ['read']
    },
    support: {
      drivers: ['read'],
      documents: ['read'],
      analytics: ['read']
    },
    analyst: {
      drivers: ['read'],
      documents: ['read'],
      analytics: ['read']
    },
    manager: {
      drivers: ['read', 'update'],
      documents: ['read', 'update'],
      analytics: ['read']
    }
  }

  return permissions[role as keyof typeof permissions] || permissions.reviewer
}
