import { createServerSupabase } from '@/lib/supabase'
import { cookies } from 'next/headers'

export default async function TestAdminPage() {
  const supabase = createServerSupabase()
  
  try {
    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    if (sessionError) {
      return <div>Session Error: {sessionError.message}</div>
    }
    
    if (!session) {
      return <div>No session found</div>
    }
    
    // Try to get admin user
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', session.user.id)
      .single()
    
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Admin Test Page</h1>
        
        <div className="space-y-4">
          <div>
            <h2 className="font-semibold">Session Info:</h2>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify({
                user_id: session.user.id,
                email: session.user.email,
                role: session.user.role
              }, null, 2)}
            </pre>
          </div>
          
          <div>
            <h2 className="font-semibold">Admin User Lookup:</h2>
            {adminError ? (
              <div className="text-red-600">
                Error: {adminError.message}
              </div>
            ) : adminUser ? (
              <pre className="bg-green-100 p-2 rounded text-sm">
                {JSON.stringify(adminUser, null, 2)}
              </pre>
            ) : (
              <div className="text-yellow-600">No admin user found</div>
            )}
          </div>
        </div>
      </div>
    )
  } catch (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
        <pre className="bg-red-100 p-2 rounded text-sm">
          {error instanceof Error ? error.message : 'Unknown error'}
        </pre>
      </div>
    )
  }
}
