import { formatDate, getStatusColor } from '@/lib/utils'
import { Shield, Mail, Calendar, User } from 'lucide-react'

interface AdminUser {
  id: string
  email: string
  full_name: string
  role: string
  department: string | null
  is_active: boolean
  last_login: string | null
  created_at: string
}

interface AdminUsersListProps {
  adminUsers: AdminUser[]
  currentUserId: string
  canManageUsers: boolean
}

export function AdminUsersList({ adminUsers, currentUserId, canManageUsers }: AdminUsersListProps) {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-100 text-red-800'
      case 'manager':
        return 'bg-purple-100 text-purple-800'
      case 'reviewer':
        return 'bg-blue-100 text-blue-800'
      case 'support':
        return 'bg-green-100 text-green-800'
      case 'analyst':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return '👑'
      case 'manager':
        return '👨‍💼'
      case 'reviewer':
        return '👀'
      case 'support':
        return '🎧'
      case 'analyst':
        return '📊'
      default:
        return '👤'
    }
  }

  return (
    <div className="bg-white shadow-sm rounded-lg border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Admin Users ({adminUsers.length})</h2>
      </div>

      <div className="divide-y divide-gray-200">
        {adminUsers.map((user) => (
          <div key={user.id} className="p-6 hover:bg-gray-50 transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-pink-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <h3 className="text-sm font-medium text-gray-900 truncate">
                      {user.full_name}
                    </h3>
                    {user.id === currentUserId && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        You
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center text-sm text-gray-500">
                      <Mail className="h-3 w-3 mr-1" />
                      {user.email}
                    </div>
                    
                    {user.department && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Shield className="h-3 w-3 mr-1" />
                        {user.department}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-2">
                    <div className="flex items-center text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      Created {formatDate(user.created_at)}
                    </div>
                    
                    {user.last_login && (
                      <div className="flex items-center text-xs text-gray-500">
                        Last login {formatDate(user.last_login)}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                  <span className="mr-1">{getRoleIcon(user.role)}</span>
                  {user.role.replace('_', ' ').toUpperCase()}
                </span>
                
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_active ? 'Active' : 'Inactive'}
                </span>

                {canManageUsers && user.id !== currentUserId && (
                  <div className="flex items-center space-x-2">
                    <button className="text-sm text-pink-600 hover:text-pink-700 font-medium">
                      Edit
                    </button>
                    <button className="text-sm text-red-600 hover:text-red-700 font-medium">
                      {user.is_active ? 'Deactivate' : 'Activate'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {adminUsers.length === 0 && (
        <div className="p-12 text-center">
          <Shield className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No admin users</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first admin user.
          </p>
        </div>
      )}
    </div>
  )
}
