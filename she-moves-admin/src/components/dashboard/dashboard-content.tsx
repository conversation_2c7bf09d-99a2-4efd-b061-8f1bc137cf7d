'use client'

import { useEffect, useState } from 'react'
import { createClientSupabase } from '@/lib/supabase'
import { StatsCards } from './stats-cards'
import { RecentActivity } from './recent-activity'
import { VerificationQueue } from './verification-queue'

interface DashboardStats {
  totalDrivers: number
  pendingDrivers: number
  approvedDrivers: number
  pendingDocuments: number
}

export function DashboardContent() {
  const [stats, setStats] = useState<DashboardStats>({
    totalDrivers: 0,
    pendingDrivers: 0,
    approvedDrivers: 0,
    pendingDocuments: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      const supabase = createClientSupabase()

      // Fetch dashboard statistics
      const [
        { count: totalDrivers },
        { count: pendingDrivers },
        { count: approvedDrivers },
        { count: pendingDocuments }
      ] = await Promise.all([
        supabase.from('drivers').select('*', { count: 'exact', head: true }),
        supabase.from('drivers').select('*', { count: 'exact', head: true }).eq('verification_status', 'pending'),
        supabase.from('drivers').select('*', { count: 'exact', head: true }).eq('verification_status', 'approved'),
        supabase.from('document_uploads').select('*', { count: 'exact', head: true }).eq('status', 'under_review')
      ])

      setStats({
        totalDrivers: totalDrivers || 0,
        pendingDrivers: pendingDrivers || 0,
        approvedDrivers: approvedDrivers || 0,
        pendingDocuments: pendingDocuments || 0
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      // Keep default values on error
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Loading dashboard data...</p>
        </div>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-gray-200 rounded-lg h-24"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to the SheMove admin dashboard</p>
      </div>

      {/* Stats cards */}
      <StatsCards stats={stats} />

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Verification queue */}
        <div className="lg:col-span-1">
          <VerificationQueue />
        </div>

        {/* Recent activity */}
        <div className="lg:col-span-1">
          <RecentActivity />
        </div>
      </div>
    </div>
  )
}
