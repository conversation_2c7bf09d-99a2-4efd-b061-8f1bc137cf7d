'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON>ircle, XCircle, Clock, User } from 'lucide-react'
import { createClientSupabase } from '@/lib/supabase'

interface Activity {
  id: string
  type: 'approval' | 'rejection' | 'review' | 'other'
  message: string
  timestamp: string
  user: string
}

export function RecentActivity() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchRecentActivity()
  }, [])

  const fetchRecentActivity = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('=== RECENT ACTIVITY DEBUG ===')
      console.log('Starting fetchRecentActivity...')

      const supabase = createClientSupabase()
      console.log('Supabase client created for activity logs')

      // Test basic connection first
      console.log('Testing admin_activity_logs table connection...')
      const { error: testError } = await supabase
        .from('admin_activity_logs')
        .select('id')
        .limit(1)

      if (testError) {
        console.error('Activity logs table connection test failed:', testError)
        setError(`Database connection failed: ${testError.message}`)
        return
      }

      console.log('Activity logs table connection successful')
      console.log('Fetching recent activity logs...')

      // Get activity logs
      const { data: logs, error: logsError } = await supabase
        .from('admin_activity_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10)

      if (logsError) {
        console.error('Error fetching activity logs:', logsError)
        setError(`Failed to fetch activity logs: ${logsError.message}`)
        return
      }

      console.log('Found activity logs:', logs?.length || 0)
      console.log('Activity logs data:', logs)

      if (!logs || logs.length === 0) {
        setActivities([])
        return
      }

      // Enrich logs with admin user info
      const enrichedActivities = []

      for (const log of logs) {
        try {
          let adminUser = null

          if (log.admin_id) {
            const { data: admin, error: adminError } = await supabase
              .from('admin_users')
              .select('id, full_name, email')
              .eq('id', log.admin_id)
              .single()

            if (!adminError && admin) {
              adminUser = admin
            }
          }

          // Determine activity type based on action
          let type: 'approval' | 'rejection' | 'review' | 'other' = 'other'
          const action = log.action.toLowerCase()

          if (action.includes('approve')) type = 'approval'
          else if (action.includes('reject')) type = 'rejection'
          else if (action.includes('review') || action.includes('start')) type = 'review'

          // Create a readable message from the action and details
          let message = log.action
          if (log.details && typeof log.details === 'object') {
            const details = log.details as any
            if (details.driver_license) {
              message = `${log.action} for driver ${details.driver_license}`
            } else if (details.document_type) {
              message = `${log.action} - ${details.document_type}`
            }
          }

          enrichedActivities.push({
            id: log.id,
            type,
            message,
            timestamp: log.created_at,
            user: adminUser?.full_name || 'System'
          })

        } catch (err) {
          console.error('Error processing activity log:', err)
        }
      }

      console.log('Enriched activities:', enrichedActivities.length)
      setActivities(enrichedActivities)
    } catch (err) {
      console.error('Exception fetching recent activity:', err)
      setError('Failed to load recent activity')
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'approval':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'rejection':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'review':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <User className="h-4 w-4 text-gray-500" />
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-start space-x-3">
                <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div className="p-6 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchRecentActivity}
            className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
      </div>

      {activities.length === 0 ? (
        <div className="p-6 text-center text-gray-500">
          <User className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p>No recent activity</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {activities.map((activity) => (
            <div key={activity.id} className="p-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">{activity.message}</p>
                  <div className="flex items-center text-xs text-gray-500 mt-1 space-x-2">
                    <span>{activity.user}</span>
                    <span>•</span>
                    <span>{formatTime(activity.timestamp)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="p-4 border-t border-gray-200">
        <button
          onClick={fetchRecentActivity}
          className="w-full text-sm text-pink-600 hover:text-pink-700 font-medium"
        >
          Refresh activity
        </button>
      </div>
    </div>
  )
}
