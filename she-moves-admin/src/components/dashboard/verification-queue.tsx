'use client'

import { useEffect, useState } from 'react'
import { <PERSON>, Eye } from 'lucide-react'
import { createClientSupabase } from '@/lib/supabase'
import { DocumentReviewModal } from './document-review-modal'

interface PendingItem {
  id: string
  driverName: string
  documentType: string
  submittedAt: string
  priority: 'low' | 'medium' | 'high'
}

export function VerificationQueue() {
  const [pendingItems, setPendingItems] = useState<PendingItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedReview, setSelectedReview] = useState<{
    id: string
    driverName: string
    documentType: string
    submittedAt: string
  } | null>(null)

  useEffect(() => {
    fetchPendingItems()
  }, [])

  const fetchPendingItems = async () => {
    try {
      setLoading(true)
      setError(null)

      const supabase = createClientSupabase()

      // Get pending document uploads (correct table)
      const { data: reviews, error: reviewsError } = await supabase
        .from('document_uploads')
        .select(`
          *,
          drivers!inner(
            id,
            user_id,
            profiles!inner(
              full_name,
              email
            )
          )
        `)
        .eq('status', 'under_review')
        .order('created_at', { ascending: true })
        .limit(10)

      if (reviewsError) {
        console.error('Error fetching document reviews:', reviewsError)
        setError(`Failed to fetch reviews: ${reviewsError.message}`)
        return
      }

      console.log('Found document uploads:', reviews?.length || 0)

      if (!reviews || reviews.length === 0) {
        setPendingItems([])
        return
      }

      // Process the document uploads data
      const enrichedItems = reviews.map((upload: any) => {
        const driverName = upload.drivers?.profiles?.full_name || 'Unknown Driver'
        const documentType = upload.document_type || 'Unknown Document'

        return {
          id: upload.id,
          driverName,
          documentType: formatDocumentType(documentType),
          submittedAt: upload.created_at,
          priority: getPriorityFromDate(upload.created_at)
        }
      })
      console.log('Processed items:', enrichedItems.length)
      setPendingItems(enrichedItems)
    } catch (err) {
      console.error('Exception fetching pending items:', err)
      setError('Failed to load verification queue')
    } finally {
      setLoading(false)
    }
  }

  // Helper function to format document type
  const formatDocumentType = (type: string) => {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  // Helper function to calculate priority based on submission date
  const getPriorityFromDate = (submittedAt: string): 'low' | 'medium' | 'high' => {
    const daysSinceSubmission = Math.floor(
      (new Date().getTime() - new Date(submittedAt).getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysSinceSubmission >= 3) return 'high'
    else if (daysSinceSubmission >= 1) return 'medium'
    else return 'low'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Verification Queue</h2>
        </div>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Verification Queue</h2>
        </div>
        <div className="p-6 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchPendingItems}
            className="px-4 py-2 bg-pink-600 text-white rounded-md hover:bg-pink-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Verification Queue</h2>
          <span className="text-sm text-gray-500">{pendingItems.length} pending</span>
        </div>
      </div>

      {pendingItems.length === 0 ? (
        <div className="p-6 text-center text-gray-500">
          <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p>No pending document reviews</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200">
          {pendingItems.map((item) => (
            <div key={item.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-sm font-medium text-gray-900">{item.driverName}</h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                      {item.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{item.documentType}</p>
                  <div className="flex items-center text-xs text-gray-500 mt-2">
                    <Clock className="h-3 w-3 mr-1" />
                    Submitted {new Date(item.submittedAt).toLocaleDateString()}
                  </div>
                </div>
                <button
                  onClick={() => setSelectedReview({
                    id: item.id,
                    driverName: item.driverName,
                    documentType: item.documentType,
                    submittedAt: item.submittedAt
                  })}
                  className="ml-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Review Document"
                >
                  <Eye className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="p-4 border-t border-gray-200">
        <button
          onClick={fetchPendingItems}
          className="w-full text-sm text-pink-600 hover:text-pink-700 font-medium"
        >
          Refresh queue
        </button>
      </div>

      {/* Document Review Modal */}
      {selectedReview && (
        <DocumentReviewModal
          isOpen={!!selectedReview}
          onClose={() => setSelectedReview(null)}
          reviewId={selectedReview.id}
          driverName={selectedReview.driverName}
          documentType={selectedReview.documentType}
          submittedAt={selectedReview.submittedAt}
          onReviewComplete={() => {
            setSelectedReview(null)
            fetchPendingItems() // Refresh the queue after review
          }}
        />
      )}
    </div>
  )
}
