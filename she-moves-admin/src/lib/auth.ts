import { createServerSupabase, isUserAdmin } from './supabase'
import { redirect } from 'next/navigation'

export async function requireAuth() {
  const supabase = createClientSupabase() // Use client supabase for auth

  const { data: { session }, error } = await supabase.auth.getSession()

  if (error || !session) {
    redirect('/login')
  }

  return session
}

export async function requireAdminAuth() {
  const session = await requireAuth()

  try {
    const isAdmin = await isUserAdmin(session.user.id)

    if (!isAdmin) {
      redirect('/unauthorized')
    }

    return session
  } catch (error) {
    console.error('Admin auth check failed:', error)
    redirect('/unauthorized')
  }
}

export async function getAuthUser() {
  const supabase = createServerSupabase()
  
  const { data: { session } } = await supabase.auth.getSession()
  
  return session?.user || null
}

export async function signOut() {
  const supabase = createServerSupabase()
  
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    throw new Error('Failed to sign out')
  }
  
  redirect('/login')
}
