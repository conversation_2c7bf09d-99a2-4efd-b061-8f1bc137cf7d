import { createClient } from '@supabase/supabase-js'

// Types for our database
export interface Database {
  public: {
    Tables: {
      drivers: {
        Row: {
          id: string
          user_id: string
          full_name: string
          email: string
          phone_number: string
          verification_status: 'pending' | 'approved' | 'rejected' | 'suspended'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          full_name: string
          email: string
          phone_number: string
          verification_status?: 'pending' | 'approved' | 'rejected' | 'suspended'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          full_name?: string
          email?: string
          phone_number?: string
          verification_status?: 'pending' | 'approved' | 'rejected' | 'suspended'
          created_at?: string
          updated_at?: string
        }
      }
      document_uploads: {
        Row: {
          id: string
          driver_id: string
          document_type: string
          file_path: string
          file_name: string
          file_size: number
          upload_status: 'pending' | 'uploaded' | 'failed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          driver_id: string
          document_type: string
          file_path: string
          file_name: string
          file_size: number
          upload_status?: 'pending' | 'uploaded' | 'failed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          driver_id?: string
          document_type?: string
          file_path?: string
          file_name?: string
          file_size?: number
          upload_status?: 'pending' | 'uploaded' | 'failed'
          created_at?: string
          updated_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          user_id: string
          email: string
          full_name: string
          role: 'super_admin' | 'reviewer' | 'support' | 'analyst' | 'manager'
          department: string | null
          permissions: Record<string, any>
          is_active: boolean
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          full_name: string
          role?: 'super_admin' | 'reviewer' | 'support' | 'analyst' | 'manager'
          department?: string | null
          permissions?: Record<string, any>
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          full_name?: string
          role?: 'super_admin' | 'reviewer' | 'support' | 'analyst' | 'manager'
          department?: string | null
          permissions?: Record<string, any>
          is_active?: boolean
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      document_reviews: {
        Row: {
          id: string
          document_id: string
          reviewer_id: string | null
          status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'requires_resubmission'
          review_notes: string | null
          rejection_reason: string | null
          quality_score: number | null
          review_started_at: string | null
          review_completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          document_id: string
          reviewer_id?: string | null
          status?: 'pending' | 'in_review' | 'approved' | 'rejected' | 'requires_resubmission'
          review_notes?: string | null
          rejection_reason?: string | null
          quality_score?: number | null
          review_started_at?: string | null
          review_completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          document_id?: string
          reviewer_id?: string | null
          status?: 'pending' | 'in_review' | 'approved' | 'rejected' | 'requires_resubmission'
          review_notes?: string | null
          rejection_reason?: string | null
          quality_score?: number | null
          review_started_at?: string | null
          review_completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

// Client-side Supabase client
export const createClientSupabase = () => {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Server-side Supabase client (simplified for now)
export const createServerSupabase = () => {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Admin client with service role (for server-side admin operations)
export const createAdminSupabase = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  
  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Helper function to check if user is admin
export async function isUserAdmin(userId: string) {
  const supabase = createAdminSupabase()

  try {
    const { data, error } = await supabase
      .from('admin_users')
      .select('id, role, is_active')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single()

    if (error) {
      console.error('Admin check error:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Admin check exception:', error)
    return false
  }
}

// Helper function to get admin user details
export async function getAdminUser(userId: string) {
  const supabase = createAdminSupabase()
  
  const { data, error } = await supabase
    .from('admin_users')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single()
  
  if (error) {
    throw new Error('Admin user not found')
  }
  
  return data
}
