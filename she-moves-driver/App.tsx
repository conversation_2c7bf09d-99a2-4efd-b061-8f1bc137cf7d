import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import our shared authentication service
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { DriverStatusProvider } from './src/contexts/DriverStatusContext';
import { LocationProvider } from './src/contexts/LocationContext';
import { AppNavigator } from './src/navigation/AppNavigator';

// Inner component that uses auth context to provide supabase to driver contexts
function AppWithDriverContexts() {
  const { authService } = useAuth();

  return (
    <DriverStatusProvider supabase={authService.supabase}>
      <LocationProvider>
        <AppNavigator />
      </LocationProvider>
    </DriverStatusProvider>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <AuthProvider>
          <AppWithDriverContexts />
          <StatusBar style="auto" />
        </AuthProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
