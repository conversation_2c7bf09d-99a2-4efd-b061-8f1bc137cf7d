-- FOCUSED FIX: RLS Policy Issue
-- This is the exact root cause of userType: undefined

-- =====================================================
-- 1. Check current RLS policies on profiles
-- =====================================================

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'profiles';

-- =====================================================
-- 2. Fix the RLS policy to allow proper profile access
-- =====================================================

-- Drop the restrictive policy
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;

-- Create a more permissive policy for profile reading
-- This allows the app to read profiles for authentication purposes
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (
        auth.uid() = id OR 
        auth.uid() IS NOT NULL  -- Allow authenticated users to read profiles for auth flow
    );

-- Alternative: Create a service role policy for auth operations
CREATE POLICY "Service can read profiles for auth" ON profiles
    FOR SELECT USING (true);

-- =====================================================
-- 3. Verify the user profile exists and is accessible
-- =====================================================

-- Check if the profile exists
SELECT 
    id,
    email,
    full_name,
    user_type,
    created_at,
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Check auth.users table
SELECT 
    id,
    email,
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- =====================================================
-- 4. Test the fix by simulating the auth flow
-- =====================================================

-- This simulates what the SharedAuthService does
-- Replace 'USER_ID_HERE' with the actual user ID from above
-- SELECT * FROM profiles WHERE id = 'USER_ID_HERE';
