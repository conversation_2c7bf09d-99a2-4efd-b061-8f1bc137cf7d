# Phase 1: Core Driver Functionality - Detailed Tasks

## IMMEDIATE FIX (Day 1)

### Task 1.0: Fix Driver Profile Creation Issue
**Priority**: CRITICAL | **Time**: 2 hours

#### Steps:
1. ✅ Run `fix_missing_driver_profile.sql` in Supabase
2. Update `DriverVerificationScreen.tsx` to ensure driver profile creation
3. Test authentication flow end-to-end
4. Verify document upload works after fix

#### Files to Update:
- `src/screens/onboarding/DriverVerificationScreen.tsx`
- Add driver profile creation logic in verification step

---

## WEEK 1: FOUNDATION SETUP

### Task 1.1: Database Schema for Core Features
**Priority**: HIGH | **Time**: 1 day

#### Create SQL Scripts:
```sql
-- File: phase1_core_schema.sql

-- Driver sessions tracking
CREATE TABLE driver_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE,
    status driver_status DEFAULT 'offline',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    current_location POINT,
    last_location_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Driver daily stats
CREATE TABLE driver_stats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE,
    date DATE DEFAULT CURRENT_DATE,
    trips_completed INTEGER DEFAULT 0,
    trips_cancelled INTEGER DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0,
    online_hours DECIMAL(5,2) DEFAULT 0,
    acceptance_rate DECIMAL(5,2) DEFAULT 100,
    rating_average DECIMAL(3,2) DEFAULT 5.0,
    total_distance_km DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, date)
);

-- Rides table (core structure)
CREATE TABLE rides (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    passenger_id UUID REFERENCES profiles(id),
    driver_id UUID REFERENCES drivers(id),
    pickup_location POINT NOT NULL,
    pickup_address TEXT NOT NULL,
    destination_location POINT NOT NULL,
    destination_address TEXT NOT NULL,
    ride_type ride_type DEFAULT 'SheRide',
    status ride_status DEFAULT 'requested',
    estimated_fare DECIMAL(8,2),
    actual_fare DECIMAL(8,2),
    distance_km DECIMAL(6,2),
    duration_minutes INTEGER,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    driver_arrived_at TIMESTAMP WITH TIME ZONE,
    pickup_completed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    cancellation_reason TEXT,
    cancellation_fee DECIMAL(6,2) DEFAULT 0,
    driver_notes TEXT,
    passenger_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ride status enum
CREATE TYPE ride_status AS ENUM (
    'requested',
    'accepted',
    'driver_arriving',
    'driver_arrived',
    'passenger_picked_up',
    'in_progress',
    'completed',
    'cancelled_by_driver',
    'cancelled_by_passenger',
    'cancelled_by_system'
);

-- Indexes for performance
CREATE INDEX idx_driver_sessions_driver_id ON driver_sessions(driver_id);
CREATE INDEX idx_driver_sessions_status ON driver_sessions(status);
CREATE INDEX idx_driver_stats_driver_date ON driver_stats(driver_id, date);
CREATE INDEX idx_rides_driver_id ON rides(driver_id);
CREATE INDEX idx_rides_status ON rides(status);
CREATE INDEX idx_rides_requested_at ON rides(requested_at);

-- RLS Policies
ALTER TABLE driver_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE rides ENABLE ROW LEVEL SECURITY;

-- Driver sessions policies
CREATE POLICY "Drivers can manage their own sessions" ON driver_sessions
    FOR ALL USING (driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()));

-- Driver stats policies  
CREATE POLICY "Drivers can view their own stats" ON driver_stats
    FOR ALL USING (driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()));

-- Rides policies
CREATE POLICY "Drivers can view their assigned rides" ON rides
    FOR SELECT USING (driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()));

CREATE POLICY "Drivers can update their assigned rides" ON rides
    FOR UPDATE USING (driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()));
```

### Task 1.2: Main Navigation Structure
**Priority**: HIGH | **Time**: 1 day

#### Create Files:
1. `src/navigation/MainTabNavigator.tsx` - Bottom tab navigation
2. `src/navigation/RideStackNavigator.tsx` - Ride-related screens
3. `src/navigation/ProfileStackNavigator.tsx` - Profile screens

#### Bottom Tab Structure:
```typescript
// MainTabNavigator.tsx
const tabs = [
  { name: 'Dashboard', icon: 'home', component: DashboardScreen },
  { name: 'Rides', icon: 'car', component: RideStackNavigator },
  { name: 'Earnings', icon: 'cash', component: EarningsScreen },
  { name: 'Profile', icon: 'person', component: ProfileStackNavigator },
];
```

### Task 1.3: Driver Status Management Service
**Priority**: HIGH | **Time**: 1 day

#### Create Files:
1. `src/services/DriverStatusService.ts` - Status management logic
2. `src/contexts/DriverStatusContext.tsx` - Global status state

#### Key Functions:
```typescript
// DriverStatusService.ts
class DriverStatusService {
  async goOnline(driverId: string, location: Location): Promise<void>
  async goOffline(driverId: string): Promise<void>
  async updateLocation(driverId: string, location: Location): Promise<void>
  async getCurrentSession(driverId: string): Promise<DriverSession | null>
  async getDriverStats(driverId: string, date: Date): Promise<DriverStats>
}
```

---

## WEEK 2: DASHBOARD IMPLEMENTATION

### Task 2.1: Main Dashboard Screen
**Priority**: HIGH | **Time**: 3 days

#### Create File: `src/screens/main/DashboardScreen.tsx`

#### Features to Implement:
1. **Status Toggle Component**
   - Online/Offline switch with visual feedback
   - Break mode option
   - Location permission handling

2. **Stats Cards**
   - Today's earnings
   - Trips completed
   - Hours online
   - Current rating

3. **Quick Actions**
   - Emergency button (prominent)
   - Navigation to earnings
   - Profile quick access

4. **Real-time Updates**
   - Live earnings counter
   - Trip notifications
   - Status change confirmations

#### UI Components Needed:
```typescript
// Components to create:
- StatusToggleCard
- EarningsCard  
- StatsGrid
- QuickActionButtons
- EmergencyButton
- NotificationBanner
```

### Task 2.2: Driver Status Screen
**Priority**: MEDIUM | **Time**: 2 days

#### Create File: `src/screens/main/DriverStatusScreen.tsx`

#### Features:
1. Detailed status management
2. Break timer functionality
3. Location accuracy indicator
4. Status history
5. Automatic offline after inactivity

---

## WEEK 3-4: RIDE REQUEST SYSTEM

### Task 3.1: Ride Request Notification System
**Priority**: HIGH | **Time**: 2 days

#### Create Files:
1. `src/services/RideRequestService.ts` - Handle incoming requests
2. `src/components/RideRequestModal.tsx` - Request popup modal

#### Features:
1. **Real-time Notifications**
   - Push notifications for new requests
   - Sound and vibration alerts
   - Background notification handling

2. **Request Modal**
   - Passenger information
   - Pickup/destination details
   - Estimated earnings
   - Accept/decline buttons with timer

### Task 3.2: Ride Request Screen
**Priority**: HIGH | **Time**: 2 days

#### Create File: `src/screens/rides/RideRequestScreen.tsx`

#### Features:
1. Full-screen ride request details
2. Map with pickup/destination markers
3. Route preview
4. Passenger rating and photo
5. Special instructions display
6. Accept/decline with reasons

### Task 3.3: Active Ride Management
**Priority**: HIGH | **Time**: 3 days

#### Create File: `src/screens/rides/ActiveRideScreen.tsx`

#### Features:
1. **Trip Progress Tracking**
   - Current trip status
   - Step-by-step progress
   - Time and distance tracking

2. **Passenger Communication**
   - Call passenger (masked number)
   - Send quick messages
   - Emergency contact options

3. **Navigation Integration**
   - Launch external navigation
   - In-app basic directions
   - Traffic updates

4. **Trip Actions**
   - Confirm pickup
   - Start trip
   - Complete trip
   - Report issues

---

## WEEK 4: INTEGRATION & TESTING

### Task 4.1: Location Services Integration
**Priority**: HIGH | **Time**: 2 days

#### Create File: `src/services/LocationService.ts`

#### Features:
1. Background location tracking
2. Location permission management
3. Battery optimization
4. Accuracy monitoring
5. Location history storage

### Task 4.2: Push Notifications Setup
**Priority**: HIGH | **Time**: 1 day

#### Implementation:
1. Expo Notifications configuration
2. Notification permission handling
3. Background notification processing
4. Custom notification sounds
5. Notification action handling

### Task 4.3: Real-time Data Sync
**Priority**: HIGH | **Time**: 2 days

#### Implementation:
1. Supabase Realtime subscriptions
2. Driver status synchronization
3. Ride request real-time updates
4. Conflict resolution
5. Offline data handling

### Task 4.4: End-to-End Testing
**Priority**: HIGH | **Time**: 2 days

#### Testing Scenarios:
1. Complete onboarding flow
2. Go online/offline flow
3. Receive and accept ride request
4. Complete trip flow
5. Handle network interruptions
6. Emergency scenarios

---

## DEPENDENCIES & PREREQUISITES

### External Services Setup:
1. **Google Maps API** - For navigation and mapping
2. **Expo Notifications** - Push notification service
3. **Supabase Realtime** - Real-time data synchronization
4. **Location Services** - Background location tracking

### Required Packages:
```bash
npm install @react-navigation/bottom-tabs
npm install expo-location
npm install expo-notifications
npm install @supabase/realtime-js
npm install react-native-maps
npm install expo-av # for notification sounds
```

### Environment Variables:
```env
GOOGLE_MAPS_API_KEY=your_key_here
EXPO_PUSH_TOKEN=your_token_here
```

---

## SUCCESS CRITERIA FOR PHASE 1

### Functional Requirements:
✅ Driver can complete onboarding successfully
✅ Driver can go online/offline
✅ Driver receives ride requests in real-time
✅ Driver can accept/decline ride requests
✅ Driver can track active trips
✅ Driver can communicate with passengers
✅ Driver can complete trips and see earnings

### Technical Requirements:
✅ Real-time location tracking works
✅ Push notifications function properly
✅ App works in background
✅ Data syncs correctly with backend
✅ Error handling for network issues
✅ Performance is smooth on target devices

### User Experience Requirements:
✅ Intuitive navigation and UI
✅ Quick response times (<2 seconds)
✅ Clear visual feedback for all actions
✅ Accessible emergency features
✅ Consistent SheMove branding

---

**Next Steps**: After completing Phase 1, proceed to Phase 2 (Passenger Communication & Earnings) as outlined in the comprehensive development plan.
