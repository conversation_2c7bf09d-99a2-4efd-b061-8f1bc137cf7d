-- =====================================================
-- ADD DRIVER ACTIVATION FIELDS
-- Run this to add activation tracking to the drivers table
-- =====================================================

-- Add activation tracking fields to drivers table
ALTER TABLE drivers 
ADD COLUMN IF NOT EXISTS activation_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS first_online_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS tutorial_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS preferred_areas TEXT[], -- Array of preferred pickup areas
ADD COLUMN IF NOT EXISTS emergency_contact_name TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_phone TEXT;

-- Add index for activation queries
CREATE INDEX IF NOT EXISTS idx_drivers_activation_completed ON drivers(activation_completed);

-- Update existing approved drivers to have activation_completed = false
-- so they go through the activation flow
UPDATE drivers 
SET activation_completed = FALSE 
WHERE verification_status = 'approved' 
AND activation_completed IS NULL;

-- Verification query
SELECT 
    'Activation Fields Added' as status,
    COUNT(*) as total_drivers,
    COUNT(*) FILTER (WHERE activation_completed = true) as activated_drivers,
    COUNT(*) FILTER (WHERE activation_completed = false) as pending_activation
FROM drivers;
