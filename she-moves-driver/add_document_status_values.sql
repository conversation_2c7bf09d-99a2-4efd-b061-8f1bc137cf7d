-- =====================================================
-- ADD VALUES TO EXISTING document_status ENUM
-- The enum exists but has no values
-- =====================================================

-- Add values to the existing document_status enum
ALTER TYPE document_status ADD VALUE 'uploaded';
ALTER TYPE document_status ADD VALUE 'under_review';
ALTER TYPE document_status ADD VALUE 'approved';
ALTER TYPE document_status ADD VALUE 'rejected';

-- Verify the values were added
SELECT 
    'document_status values added' as result,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;
