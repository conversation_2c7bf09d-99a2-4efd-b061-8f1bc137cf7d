-- =====================================================
-- ADMIN DASHBOARD DATABASE SCHEMA
-- Run this after the core driver app schema
-- =====================================================

-- =====================================================
-- 1. CREATE ADMIN-SPECIFIC ENUMS
-- =====================================================

-- Admin roles enum
CREATE TYPE admin_role AS ENUM (
    'super_admin',
    'reviewer', 
    'support',
    'analyst',
    'manager'
);

-- Review status enum
CREATE TYPE review_status AS ENUM (
    'pending',
    'in_review',
    'approved',
    'rejected',
    'requires_resubmission'
);

-- Ticket status enum
CREATE TYPE ticket_status AS ENUM (
    'open',
    'in_progress', 
    'waiting_response',
    'resolved',
    'closed'
);

-- Ticket priority enum
CREATE TYPE ticket_priority AS ENUM (
    'low',
    'medium',
    'high',
    'urgent'
);

-- =====================================================
-- 2. ADMIN USERS TABLE
-- =====================================================

CREATE TABLE admin_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    role admin_role NOT NULL DEFAULT 'reviewer',
    department TEXT,
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. DOCUMENT REVIEWS TABLE
-- =====================================================

CREATE TABLE document_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE,
    reviewer_id UUID REFERENCES admin_users(id),
    status review_status DEFAULT 'pending',
    review_notes TEXT,
    rejection_reason TEXT,
    quality_score INTEGER CHECK (quality_score >= 1 AND quality_score <= 5),
    review_started_at TIMESTAMP WITH TIME ZONE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. DRIVER VERIFICATION WORKFLOW
-- =====================================================

CREATE TABLE driver_verification_workflow (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE,
    current_step INTEGER DEFAULT 1,
    assigned_reviewer_id UUID REFERENCES admin_users(id),
    priority ticket_priority DEFAULT 'medium',
    verification_status verification_status DEFAULT 'pending',
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    target_completion_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. SUPPORT TICKETS TABLE
-- =====================================================

CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_number TEXT UNIQUE NOT NULL,
    driver_id UUID REFERENCES drivers(id),
    assigned_admin_id UUID REFERENCES admin_users(id),
    category TEXT NOT NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    status ticket_status DEFAULT 'open',
    priority ticket_priority DEFAULT 'medium',
    tags TEXT[],
    resolution_notes TEXT,
    first_response_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. TICKET MESSAGES TABLE
-- =====================================================

CREATE TABLE ticket_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_id UUID REFERENCES support_tickets(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES profiles(id),
    sender_type TEXT NOT NULL CHECK (sender_type IN ('driver', 'admin')),
    message_text TEXT NOT NULL,
    attachments JSONB DEFAULT '[]',
    is_internal BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 7. ADMIN ACTIVITY LOGS
-- =====================================================

CREATE TABLE admin_activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID REFERENCES admin_users(id),
    action TEXT NOT NULL,
    target_type TEXT, -- 'driver', 'document', 'ticket', 'user'
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 8. VERIFICATION METRICS TABLE
-- =====================================================

CREATE TABLE verification_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE DEFAULT CURRENT_DATE,
    applications_received INTEGER DEFAULT 0,
    applications_reviewed INTEGER DEFAULT 0,
    applications_approved INTEGER DEFAULT 0,
    applications_rejected INTEGER DEFAULT 0,
    avg_review_time_hours DECIMAL(6,2) DEFAULT 0,
    reviewer_id UUID REFERENCES admin_users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(date, reviewer_id)
);

-- =====================================================
-- 9. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Admin users indexes
CREATE INDEX idx_admin_users_role ON admin_users(role);
CREATE INDEX idx_admin_users_active ON admin_users(is_active);

-- Document reviews indexes
CREATE INDEX idx_document_reviews_document_id ON document_reviews(document_id);
CREATE INDEX idx_document_reviews_reviewer_id ON document_reviews(reviewer_id);
CREATE INDEX idx_document_reviews_status ON document_reviews(status);
CREATE INDEX idx_document_reviews_created_at ON document_reviews(created_at);

-- Verification workflow indexes
CREATE INDEX idx_verification_workflow_driver_id ON driver_verification_workflow(driver_id);
CREATE INDEX idx_verification_workflow_reviewer ON driver_verification_workflow(assigned_reviewer_id);
CREATE INDEX idx_verification_workflow_status ON driver_verification_workflow(verification_status);
CREATE INDEX idx_verification_workflow_priority ON driver_verification_workflow(priority);

-- Support tickets indexes
CREATE INDEX idx_support_tickets_driver_id ON support_tickets(driver_id);
CREATE INDEX idx_support_tickets_assigned_admin ON support_tickets(assigned_admin_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at);

-- Activity logs indexes
CREATE INDEX idx_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX idx_activity_logs_action ON admin_activity_logs(action);
CREATE INDEX idx_activity_logs_created_at ON admin_activity_logs(created_at);

-- =====================================================
-- 10. CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on all admin tables
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_verification_workflow ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_metrics ENABLE ROW LEVEL SECURITY;

-- Admin users policies
CREATE POLICY "Admins can view admin users" ON admin_users
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Document reviews policies
CREATE POLICY "Admins can manage document reviews" ON document_reviews
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Verification workflow policies
CREATE POLICY "Admins can manage verification workflow" ON driver_verification_workflow
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Support tickets policies
CREATE POLICY "Admins can manage support tickets" ON support_tickets
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Ticket messages policies
CREATE POLICY "Admins and drivers can view relevant ticket messages" ON ticket_messages
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
        OR 
        EXISTS (
            SELECT 1 FROM support_tickets st 
            JOIN drivers d ON st.driver_id = d.id 
            WHERE st.id = ticket_id AND d.user_id = auth.uid()
        )
    );

-- Activity logs policies (admin only)
CREATE POLICY "Admins can view activity logs" ON admin_activity_logs
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- Verification metrics policies
CREATE POLICY "Admins can view verification metrics" ON verification_metrics
    FOR ALL USING (
        EXISTS (SELECT 1 FROM admin_users au WHERE au.user_id = auth.uid() AND au.is_active = true)
    );

-- =====================================================
-- 11. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to generate ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
BEGIN
    RETURN 'SM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(NEXTVAL('ticket_number_seq')::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- Create sequence for ticket numbers
CREATE SEQUENCE IF NOT EXISTS ticket_number_seq START 1;

-- Function to update verification metrics
CREATE OR REPLACE FUNCTION update_verification_metrics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update daily metrics when document review status changes
    IF TG_OP = 'UPDATE' AND OLD.status != NEW.status THEN
        INSERT INTO verification_metrics (date, reviewer_id, applications_reviewed)
        VALUES (CURRENT_DATE, NEW.reviewer_id, 1)
        ON CONFLICT (date, reviewer_id) 
        DO UPDATE SET 
            applications_reviewed = verification_metrics.applications_reviewed + 1,
            applications_approved = verification_metrics.applications_approved + 
                CASE WHEN NEW.status = 'approved' THEN 1 ELSE 0 END,
            applications_rejected = verification_metrics.applications_rejected + 
                CASE WHEN NEW.status = 'rejected' THEN 1 ELSE 0 END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for metrics updates
CREATE TRIGGER update_verification_metrics_trigger
    AFTER UPDATE ON document_reviews
    FOR EACH ROW EXECUTE FUNCTION update_verification_metrics();

-- =====================================================
-- 12. INSERT DEFAULT ADMIN USER
-- =====================================================

-- Create default super admin (update email as needed)
INSERT INTO admin_users (user_id, email, full_name, role, permissions, is_active)
SELECT 
    id,
    '<EMAIL>',
    'System Administrator',
    'super_admin',
    '{"canReviewDocuments": true, "canApproveDrivers": true, "canManageUsers": true, "canViewAnalytics": true, "canHandleSupport": true}',
    true
FROM auth.users 
WHERE email = '<EMAIL>'
ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'ADMIN DASHBOARD SCHEMA CREATED SUCCESSFULLY';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created tables:';
    RAISE NOTICE '- admin_users (admin authentication)';
    RAISE NOTICE '- document_reviews (document verification)';
    RAISE NOTICE '- driver_verification_workflow (approval process)';
    RAISE NOTICE '- support_tickets (customer support)';
    RAISE NOTICE '- ticket_messages (support communication)';
    RAISE NOTICE '- admin_activity_logs (audit trail)';
    RAISE NOTICE '- verification_metrics (analytics)';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next: Build admin dashboard web application';
    RAISE NOTICE '=================================================';
END $$;
