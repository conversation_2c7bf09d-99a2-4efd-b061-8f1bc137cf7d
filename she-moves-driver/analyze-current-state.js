#!/usr/bin/env node

/**
 * Analyze current state of users and profiles
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function analyzeCurrentState() {
  console.log('🔍 Analyzing current database state...\n');

  try {
    // 1. Check all profiles
    console.log('1. Checking all profiles...');
    
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (profilesError) {
      console.error('❌ Error querying profiles:', profilesError);
      return;
    }

    console.log(`📊 Found ${profiles.length} profiles:`);
    profiles.forEach((profile, index) => {
      console.log(`   ${index + 1}. ${profile.email} (${profile.user_type})`);
      console.log(`      ID: ${profile.id}`);
      console.log(`      Name: ${profile.full_name}`);
      console.log(`      Created: ${profile.created_at}`);
      console.log('');
    });

    // 2. Check all drivers
    console.log('2. Checking all drivers...');
    
    const { data: drivers, error: driversError } = await supabase
      .from('drivers')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);

    if (driversError) {
      console.error('❌ Error querying drivers:', driversError);
      return;
    }

    console.log(`📊 Found ${drivers.length} drivers:`);
    drivers.forEach((driver, index) => {
      console.log(`   ${index + 1}. Driver ID: ${driver.id}`);
      console.log(`      User ID: ${driver.user_id}`);
      console.log(`      Status: ${driver.verification_status}`);
      console.log(`      Online: ${driver.is_online}`);
      console.log(`      Created: ${driver.created_at}`);
      console.log('');
    });

    // 3. Check storage buckets
    console.log('3. Checking storage buckets...');
    
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
    } else {
      console.log(`📊 Found ${buckets.length} storage buckets:`);
      buckets.forEach(bucket => {
        console.log(`   - ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
      });
    }

    // 4. Check document templates
    console.log('\n4. Checking document templates...');
    
    const { data: templates, error: templatesError } = await supabase
      .from('document_templates')
      .select('*')
      .order('sort_order');

    if (templatesError) {
      console.error('❌ Error querying templates:', templatesError);
    } else {
      console.log(`📊 Found ${templates.length} document templates:`);
      templates.forEach((template, index) => {
        console.log(`   ${index + 1}. ${template.display_name} (${template.document_type})`);
        console.log(`      Required: ${template.is_required}`);
      });
    }

    // 5. Identify the main issues
    console.log('\n📋 SYSTEMATIC ISSUE ANALYSIS:');
    console.log('');
    
    // Issue 1: Profile creation
    if (profiles.length === 0) {
      console.log('❌ ISSUE 1: No profiles exist');
      console.log('   → Profile creation is completely broken');
    } else {
      const driverProfiles = profiles.filter(p => p.user_type === 'driver');
      console.log(`✅ ISSUE 1: Profile creation works (${driverProfiles.length} driver profiles)`);
    }

    // Issue 2: Driver record creation
    if (drivers.length === 0) {
      console.log('❌ ISSUE 2: No driver records exist');
      console.log('   → Driver onboarding step 2 is broken');
    } else {
      console.log(`✅ ISSUE 2: Driver records exist (${drivers.length} drivers)`);
    }

    // Issue 3: Document templates
    if (templates.length === 0) {
      console.log('❌ ISSUE 3: No document templates');
      console.log('   → Document upload screen will be blank');
    } else {
      console.log(`✅ ISSUE 3: Document templates exist (${templates.length} templates)`);
    }

    // Issue 4: Storage buckets
    const driverDocsBucket = buckets?.find(b => b.name === 'driver-documents');
    if (!driverDocsBucket) {
      console.log('❌ ISSUE 4: No driver-documents bucket');
      console.log('   → Document uploads will fail');
    } else {
      console.log('✅ ISSUE 4: Storage bucket exists');
    }

    console.log('\n🔧 RECOMMENDED FIXES:');
    console.log('');
    
    if (profiles.length === 0) {
      console.log('1. Fix profile creation trigger or manual creation logic');
    }
    
    if (drivers.length === 0) {
      console.log('2. Fix driver record creation in onboarding step 2');
    }
    
    if (templates.length === 0) {
      console.log('3. Run SQL to create document templates (see previous message)');
    }
    
    if (!driverDocsBucket) {
      console.log('4. Create driver-documents storage bucket');
    }

    console.log('\n🧪 NEXT STEPS:');
    console.log('1. Create a new driver signup to test the flow');
    console.log('2. Monitor each step for specific errors');
    console.log('3. Fix issues one by one systematically');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the analysis
analyzeCurrentState().then(() => {
  console.log('\n🏁 Analysis complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Analysis failed:', error);
  process.exit(1);
});
