{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "shemove-driver", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#FFF0FF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": false, "bundleIdentifier": "com.shemove.driver", "infoPlist": {"NSLocationWhenInUseUsageDescription": "SheMove Driver needs location access to show your position to passengers and navigate to pickup locations.", "NSLocationAlwaysAndWhenInUseUsageDescription": "SheMove Driver needs background location access to track your position during trips.", "NSCameraUsageDescription": "SheMove Driver needs camera access to take photos for document verification.", "NSMicrophoneUsageDescription": "SheMove Driver needs microphone access for voice communication features.", "UIBackgroundModes": ["location", "background-fetch", "background-processing"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFF0FF"}, "edgeToEdgeEnabled": true, "package": "com.shemove.driver", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-notifications", "expo-camera", "expo-document-picker", "expo-image-picker", ["expo-router", {"origin": "https://driver.shemove.app"}]], "scheme": "shemove-driver", "extra": {"eas": {"projectId": "your-eas-project-id"}}}}