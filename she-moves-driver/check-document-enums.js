#!/usr/bin/env node

/**
 * Check what document_type enum values are allowed
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function checkDocumentEnums() {
  console.log('🔍 Checking document_type enum values...\n');

  try {
    // Try to get enum values from information_schema
    const { data, error } = await supabase
      .from('document_templates')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error querying document_templates:', error);
      return;
    }

    console.log('✅ document_templates table accessible');

    // Try inserting each document type individually to see which ones work
    const documentTypes = [
      'drivers_license',
      'vehicle_registration', 
      'insurance',
      'insurance_certificate',
      'profile_photo',
      'vehicle_photo'
    ];

    console.log('\n🧪 Testing each document type...');

    for (const docType of documentTypes) {
      try {
        const testTemplate = {
          document_type: docType,
          display_name: `Test ${docType}`,
          description: 'Test description',
          requirements: ['Test requirement'],
          sort_order: 1,
          help_text: 'Test help text',
          is_required: true,
          accepted_formats: ['image/*'],
          max_file_size: 5242880
        };

        const { data: insertData, error: insertError } = await supabase
          .from('document_templates')
          .insert(testTemplate)
          .select();

        if (insertError) {
          console.log(`❌ ${docType}: ${insertError.message}`);
        } else {
          console.log(`✅ ${docType}: SUCCESS`);
          
          // Clean up - delete the test record
          await supabase
            .from('document_templates')
            .delete()
            .eq('document_type', docType);
        }
      } catch (error) {
        console.log(`❌ ${docType}: ${error.message}`);
      }
    }

    // Check what's actually in the table
    console.log('\n📋 Current document_templates records:');
    const { data: allTemplates, error: allError } = await supabase
      .from('document_templates')
      .select('*')
      .order('sort_order');

    if (allError) {
      console.error('❌ Error getting all templates:', allError);
    } else {
      if (allTemplates.length === 0) {
        console.log('   No records found');
      } else {
        allTemplates.forEach((template, index) => {
          console.log(`   ${index + 1}. ${template.document_type} - ${template.display_name}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkDocumentEnums().then(() => {
  console.log('\n🏁 Check complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Check failed:', error);
  process.exit(1);
});
