-- =====================================================
-- CHECK WHAT VALUES ARE IN document_status ENUM
-- =====================================================

-- Check if document_status enum exists and what values it has
SELECT 
    'document_status enum values' as section,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;

-- Also check all enum types to see what we have
SELECT 
    'All enum types' as section,
    typname as enum_name
FROM pg_type 
WHERE typtype = 'e'
AND typname LIKE '%document%'
ORDER BY typname;
