-- =====================================================
-- CHECK WHAT ENUMS ACTUALLY EXIST
-- Run this to see what's in your database
-- =====================================================

-- Check all enum types
SELECT 
    'Enum Types' as section,
    typname as enum_name
FROM pg_type 
WHERE typtype = 'e'
ORDER BY typname;

-- Check document_status enum values specifically
SELECT 
    'document_status values' as section,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;

-- Check document_type enum values specifically  
SELECT 
    'document_type values' as section,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_type'
)
ORDER BY enumsortorder;
