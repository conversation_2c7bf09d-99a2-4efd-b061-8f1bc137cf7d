-- Check what's actually in the database for this user
-- Run this in your Supabase SQL Editor

-- Check the user in auth.users table
SELECT 
    id,
    email,
    created_at,
    raw_user_meta_data
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check the profile in profiles table
SELECT 
    id,
    email,
    full_name,
    user_type,
    created_at,
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Check if there are multiple profiles for this email
SELECT 
    COUNT(*) as profile_count,
    email
FROM profiles 
WHERE email = '<EMAIL>'
GROUP BY email;

-- Check the user_type enum values
SELECT enumlabel as allowed_user_types
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'user_type'
);

-- Try to manually update the user_type
UPDATE profiles 
SET user_type = 'driver'::user_type
WHERE email = '<EMAIL>';

-- Verify the update worked
SELECT 
    id,
    email,
    full_name,
    user_type,
    user_type::text as user_type_text,
    created_at,
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';
