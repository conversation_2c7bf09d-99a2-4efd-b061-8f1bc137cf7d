-- =====================================================
-- SIMPLIFIED AUTHENTICATION FIX FOR DRIVER APP
-- Run this AFTER running fix_database_schema.sql
-- =====================================================

-- =====================================================
-- 1. FIX EXISTING USER PROFILE
-- =====================================================

-- Update existing user to have correct user_type
UPDATE profiles
SET
    user_type = 'driver',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Update auth.users metadata to ensure consistency
UPDATE auth.users
SET raw_user_meta_data = jsonb_set(
    COALESCE(raw_user_meta_data, '{}'),
    '{user_type}',
    '"driver"'
)
WHERE email = '<EMAIL>'
AND (raw_user_meta_data->>'user_type' IS NULL OR raw_user_meta_data->>'user_type' != 'driver');

-- =====================================================
-- 2. VERIFICATION QUERIES
-- =====================================================

-- Check the existing user profile
SELECT
    'Existing User Check' as test_name,
    id,
    email,
    full_name,
    user_type,
    user_type::text as user_type_text,
    created_at,
    updated_at
FROM profiles
WHERE email = '<EMAIL>';

-- Check auth.users metadata
SELECT
    'Auth Users Metadata' as test_name,
    id,
    email,
    raw_user_meta_data,
    raw_user_meta_data->>'user_type' as metadata_user_type,
    raw_user_meta_data->>'full_name' as metadata_full_name
FROM auth.users
WHERE email = '<EMAIL>';

-- Check drivers table structure
SELECT
    'Drivers Table Columns' as test_name,
    string_agg(column_name, ', ') as available_columns
FROM information_schema.columns
WHERE table_name = 'drivers' AND table_schema = 'public';

-- =====================================================
-- 3. COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'AUTHENTICATION FIX COMPLETED';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Fixed existing user profile to driver type';
    RAISE NOTICE 'Updated auth.users metadata for consistency';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next: Test driver app login and signup';
    RAISE NOTICE '=================================================';
END $$;
