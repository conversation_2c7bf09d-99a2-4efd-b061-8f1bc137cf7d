-- =====================================================
-- Driver Sessions Table Migration
-- Creates the missing driver_sessions table for session tracking
-- =====================================================

-- Create driver_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS driver_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    session_end TIMESTAMP WITH TIME ZONE,
    initial_location POINT,
    final_location POINT,
    total_online_minutes INTEGER DEFAULT 0,
    location_updates_count INTEGER DEFAULT 0,
    trips_completed INTEGER DEFAULT 0,
    earnings_session DECIMAL(10,2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_driver_sessions_driver_id ON driver_sessions(driver_id);
CREATE INDEX IF NOT EXISTS idx_driver_sessions_start ON driver_sessions(session_start);
CREATE INDEX IF NOT EXISTS idx_driver_sessions_active ON driver_sessions(driver_id) WHERE session_end IS NULL;

-- Enable RLS
ALTER TABLE driver_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
DROP POLICY IF EXISTS "Drivers can manage their own sessions" ON driver_sessions;
CREATE POLICY "Drivers can manage their own sessions" ON driver_sessions
    FOR ALL USING (driver_id IN (SELECT id FROM drivers WHERE user_id = auth.uid()));

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_driver_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_driver_sessions_updated_at ON driver_sessions;
CREATE TRIGGER trigger_update_driver_sessions_updated_at
    BEFORE UPDATE ON driver_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_driver_sessions_updated_at();

-- Verification query
SELECT 'driver_sessions table created successfully' as status,
       COUNT(*) as existing_sessions
FROM driver_sessions;

-- Show table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'driver_sessions' 
AND table_schema = 'public'
ORDER BY ordinal_position;
