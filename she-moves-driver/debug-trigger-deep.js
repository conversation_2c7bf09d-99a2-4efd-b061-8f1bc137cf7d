#!/usr/bin/env node

/**
 * Deep debug of trigger issues
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function debugTriggerDeep() {
  console.log('🔍 Deep debugging trigger issues...\n');

  try {
    const newUserId = '597c151c-57c5-4d67-a61f-635e004d1743';
    
    // 1. Check if driver record was created by trigger
    console.log('1. Checking if trigger created driver record...');
    
    const { data: driver, error: driverError } = await supabase
      .from('drivers')
      .select('*')
      .eq('user_id', newUserId)
      .single();

    if (driverError && driverError.code !== 'PGRST116') {
      console.error('❌ Error checking driver:', driverError);
      return;
    }

    if (driver) {
      console.log('✅ Driver record exists (trigger worked):');
      console.log(`   Driver ID: ${driver.id}`);
      console.log(`   License: ${driver.license_number}`);
      console.log(`   Status: ${driver.verification_status}`);
      console.log(`   Created: ${driver.created_at}`);
    } else {
      console.log('❌ Driver record does NOT exist (trigger failed)');
    }

    // 2. Test manual driver creation with new RLS policy
    console.log('\n2. Testing manual driver creation with new RLS policy...');
    
    if (!driver) {
      const testDriverData = {
        user_id: newUserId,
        license_number: `PENDING-${newUserId.substring(0, 8)}`,
        vehicle_make: 'Not Specified',
        vehicle_model: 'Not Specified',
        vehicle_year: 2020,
        vehicle_color: 'Not Specified',
        vehicle_plate: `PENDING-${newUserId.substring(0, 6)}`,
        vehicle_type: 'SheRide',
        verification_status: 'pending',
        onboarding_completed: false
      };

      const { data: testDriver, error: testDriverError } = await supabase
        .from('drivers')
        .insert(testDriverData)
        .select()
        .single();

      if (testDriverError) {
        console.log('❌ Manual driver creation still fails:');
        console.log(`   Error: ${testDriverError.message}`);
        console.log(`   Code: ${testDriverError.code}`);
        console.log('   → RLS policy still blocking');
      } else {
        console.log('✅ Manual driver creation works:');
        console.log(`   Driver ID: ${testDriver.id}`);
        console.log('   → RLS policy fixed, but trigger still not working');
      }
    }

    // 3. The real issue: Manual profile creation bypasses trigger
    console.log('\n3. Analyzing the real issue...');
    console.log('');
    console.log('🎯 ROOT CAUSE IDENTIFIED:');
    console.log('   The app creates profiles MANUALLY after signup fails');
    console.log('   This bypasses the auth.users trigger completely');
    console.log('');
    console.log('📋 WHAT HAPPENS:');
    console.log('   1. User signs up → auth.users record created');
    console.log('   2. Trigger should fire → but profile creation fails');
    console.log('   3. App retries 5 times → profile not found');
    console.log('   4. App creates profile manually → bypasses trigger');
    console.log('   5. Driver record never created → services fail');
    console.log('');
    console.log('💡 SOLUTION:');
    console.log('   Fix the trigger OR create driver record during manual profile creation');

    console.log('\n📋 RECOMMENDED APPROACH:');
    console.log('   Since the trigger is problematic, enhance the manual profile creation');
    console.log('   to also create the driver record when user_type = "driver"');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the debug
debugTriggerDeep().then(() => {
  console.log('\n🏁 Debug complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Debug failed:', error);
  process.exit(1);
});
