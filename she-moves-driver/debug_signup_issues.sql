-- Debug signup issues
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. Check current user data
-- =====================================================

-- Check if the user exists in auth.users
SELECT 
    id, 
    email, 
    created_at,
    raw_user_meta_data,
    user_metadata
FROM auth.users 
WHERE email = '<EMAIL>';

-- Check if profile exists
SELECT 
    id, 
    email, 
    full_name, 
    user_type, 
    created_at, 
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- =====================================================
-- 2. Check trigger function
-- =====================================================

-- Check if the trigger function exists
SELECT 
    routine_name, 
    routine_type,
    routine_definition
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Check if the trigger exists
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- =====================================================
-- 3. Test the trigger function manually
-- =====================================================

-- Let's test what happens when we try to insert a profile manually
-- First, let's see what user_type values are allowed
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'user_type'
);

-- =====================================================
-- 4. Check for any constraints or issues
-- =====================================================

-- Check profiles table constraints
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'profiles'::regclass;

-- Check for any unique constraint violations
SELECT 
    COUNT(*) as duplicate_emails,
    email
FROM profiles 
GROUP BY email 
HAVING COUNT(*) > 1;

-- =====================================================
-- 5. Fix the existing user profile if needed
-- =====================================================

-- Update the existing user profile to have correct user_type
UPDATE profiles 
SET 
    user_type = 'driver',
    updated_at = NOW()
WHERE email = '<EMAIL>'
AND (user_type IS NULL OR user_type != 'driver');

-- Verify the update
SELECT 
    id, 
    email, 
    full_name, 
    user_type, 
    created_at, 
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';
