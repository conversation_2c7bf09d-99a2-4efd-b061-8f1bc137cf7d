-- =====================================================
-- STEP 1: CREATE ENUMS ONLY
-- Test this first - should have ZERO errors
-- =====================================================

-- Create document_type enum (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_type') THEN
        CREATE TYPE document_type AS ENUM (
            'drivers_license',
            'vehicle_registration',
            'insurance_certificate',
            'profile_photo'
        );
        RAISE NOTICE 'Created document_type enum';
    ELSE
        RAISE NOTICE 'document_type enum already exists';
    END IF;
END $$;

-- Create document_status enum (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_status') THEN
        CREATE TYPE document_status AS ENUM (
            'uploaded',
            'under_review',
            'approved',
            'rejected'
        );
        RAISE NOTICE 'Created document_status enum';
    ELSE
        RAISE NOTICE 'document_status enum already exists';
    END IF;
END $$;

-- Verify enums were created
SELECT 'SUCCESS: Enums created' as result,
       string_agg(typname, ', ') as enums_found
FROM pg_type
WHERE typname IN ('document_type', 'document_status');

-- =====================================================
-- 2. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL, -- Size in bytes
    file_url TEXT NOT NULL, -- Supabase Storage URL
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    
    -- Verification fields
    reviewed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    
    -- Expiry tracking
    expires_at TIMESTAMP WITH TIME ZONE,
    expiry_reminder_sent BOOLEAN DEFAULT FALSE,
    
    -- Upload metadata
    upload_ip_address INET,
    upload_user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(driver_id, document_type), -- One document per type per driver
    CHECK (file_size > 0),
    CHECK (file_size <= 10485760) -- Max 10MB file size
);

-- =====================================================
-- 3. CREATE DOCUMENT VERIFICATION HISTORY TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_verification_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE NOT NULL,
    previous_status document_status NOT NULL,
    new_status document_status NOT NULL,
    changed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    change_reason TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CREATE DRIVER ONBOARDING PROGRESS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS driver_onboarding_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE UNIQUE NOT NULL,
    current_step INTEGER DEFAULT 1, -- 1: Profile, 2: Documents, 3: Review, 4: Complete
    onboarding_status onboarding_status DEFAULT 'profile_created',
    
    -- Step completion tracking
    profile_completed BOOLEAN DEFAULT FALSE,
    documents_uploaded BOOLEAN DEFAULT FALSE,
    background_check_completed BOOLEAN DEFAULT FALSE,
    final_review_completed BOOLEAN DEFAULT FALSE,
    
    -- Document requirements checklist
    drivers_license_uploaded BOOLEAN DEFAULT FALSE,
    vehicle_registration_uploaded BOOLEAN DEFAULT FALSE,
    insurance_certificate_uploaded BOOLEAN DEFAULT FALSE,
    profile_photo_uploaded BOOLEAN DEFAULT FALSE,
    
    -- Progress metadata
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_completion_date TIMESTAMP WITH TIME ZONE,
    
    -- Admin fields
    assigned_reviewer UUID REFERENCES profiles(id) ON DELETE SET NULL,
    priority_level INTEGER DEFAULT 1, -- 1: Normal, 2: High, 3: Urgent
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. CREATE DOCUMENT TEMPLATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_type document_type NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[], -- Array of requirements
    accepted_formats TEXT[], -- e.g., ['image/jpeg', 'image/png', 'application/pdf']
    max_file_size BIGINT DEFAULT 5242880, -- 5MB default
    is_required BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    help_text TEXT,
    example_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 6. INSERT DEFAULT DOCUMENT TEMPLATES
-- =====================================================

INSERT INTO document_templates (document_type, display_name, description, requirements, accepted_formats, max_file_size, is_required, sort_order, help_text) VALUES
('drivers_license', 'Driver''s License', 'Valid driver''s license (front and back)', 
 ARRAY['Must be current and not expired', 'Clear photo showing all text', 'Full document visible'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 1,
 'Take a clear photo of both sides of your driver''s license. Ensure all text is readable and the document is not expired.'),

('vehicle_registration', 'Vehicle Registration', 'Current vehicle registration certificate', 
 ARRAY['Must match the vehicle you''ll be driving', 'Current registration (not expired)', 'Clear and readable'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 2,
 'Upload your current vehicle registration document. Make sure the vehicle details match what you entered in your profile.'),

('insurance_certificate', 'Insurance Certificate', 'Valid vehicle insurance certificate', 
 ARRAY['Must cover the registered vehicle', 'Current and not expired', 'Minimum coverage requirements met'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 3,
 'Provide proof of current vehicle insurance. The policy must cover the vehicle you''ll use for driving.'),

('profile_photo', 'Profile Photo', 'Professional headshot for your driver profile', 
 ARRAY['Clear face photo', 'Professional appearance', 'Good lighting'], 
 ARRAY['image/jpeg', 'image/png'], 2097152, TRUE, 4,
 'Upload a clear, professional photo of yourself. This will be shown to passengers when they book rides.'),

('vehicle_photo', 'Vehicle Photo', 'Photo of your vehicle (optional)', 
 ARRAY['Clear exterior photo', 'Clean vehicle', 'Good lighting'], 
 ARRAY['image/jpeg', 'image/png'], 3145728, FALSE, 5,
 'Optional: Upload a photo of your vehicle. This helps passengers identify your car.'),

('background_check', 'Background Check', 'Background verification document', 
 ARRAY['Official background check document', 'Recent (within 6 months)', 'Clear scan or photo'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, FALSE, 6,
 'If required, upload your background check document. This may be requested during the verification process.')

ON CONFLICT (document_type) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    requirements = EXCLUDED.requirements,
    accepted_formats = EXCLUDED.accepted_formats,
    max_file_size = EXCLUDED.max_file_size,
    help_text = EXCLUDED.help_text,
    updated_at = NOW();

-- =====================================================
-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for document_uploads table
CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);
CREATE INDEX IF NOT EXISTS idx_document_uploads_type ON document_uploads(document_type);
CREATE INDEX IF NOT EXISTS idx_document_uploads_created_at ON document_uploads(created_at);
CREATE INDEX IF NOT EXISTS idx_document_uploads_expires_at ON document_uploads(expires_at);

-- Indexes for verification history
CREATE INDEX IF NOT EXISTS idx_verification_history_document_id ON document_verification_history(document_id);
CREATE INDEX IF NOT EXISTS idx_verification_history_created_at ON document_verification_history(created_at);

-- Indexes for onboarding progress
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_driver_id ON driver_onboarding_progress(driver_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_status ON driver_onboarding_progress(onboarding_status);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_step ON driver_onboarding_progress(current_step);

-- =====================================================
-- 8. CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables
DROP TRIGGER IF EXISTS update_document_uploads_updated_at ON document_uploads;
CREATE TRIGGER update_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_onboarding_progress_updated_at ON driver_onboarding_progress;
CREATE TRIGGER update_onboarding_progress_updated_at
    BEFORE UPDATE ON driver_onboarding_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_document_templates_updated_at ON document_templates;
CREATE TRIGGER update_document_templates_updated_at
    BEFORE UPDATE ON document_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 9. CREATE RLS POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_verification_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_onboarding_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_uploads
CREATE POLICY "Drivers can view their own documents" ON document_uploads
    FOR SELECT USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can insert their own documents" ON document_uploads
    FOR INSERT WITH CHECK (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can update their own documents" ON document_uploads
    FOR UPDATE USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for verification history (read-only for drivers)
CREATE POLICY "Drivers can view their document history" ON document_verification_history
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM document_uploads du
            JOIN drivers d ON du.driver_id = d.id
            WHERE d.user_id = auth.uid()
        )
    );

-- RLS Policies for onboarding progress
CREATE POLICY "Drivers can view their onboarding progress" ON driver_onboarding_progress
    FOR SELECT USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can update their onboarding progress" ON driver_onboarding_progress
    FOR UPDATE USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can insert their onboarding progress" ON driver_onboarding_progress
    FOR INSERT WITH CHECK (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for document templates (public read)
CREATE POLICY "Anyone can view document templates" ON document_templates
    FOR SELECT USING (true);

-- =====================================================
-- 10. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to initialize driver onboarding progress
CREATE OR REPLACE FUNCTION initialize_driver_onboarding(driver_id_param UUID)
RETURNS UUID AS $$
DECLARE
    progress_id UUID;
BEGIN
    INSERT INTO driver_onboarding_progress (driver_id, current_step, onboarding_status, profile_completed)
    VALUES (driver_id_param, 2, 'documents_pending', TRUE)
    ON CONFLICT (driver_id) DO UPDATE SET
        profile_completed = TRUE,
        updated_at = NOW()
    RETURNING id INTO progress_id;

    RETURN progress_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update document upload status
CREATE OR REPLACE FUNCTION update_document_status(
    document_id_param UUID,
    new_status_param document_status,
    reviewer_id_param UUID DEFAULT NULL,
    reason_param TEXT DEFAULT NULL,
    notes_param TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_status document_status;
    driver_id_val UUID;
BEGIN
    -- Get current status and driver_id
    SELECT status, driver_id INTO old_status, driver_id_val
    FROM document_uploads
    WHERE id = document_id_param;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Update document status
    UPDATE document_uploads
    SET
        status = new_status_param,
        reviewed_by = reviewer_id_param,
        reviewed_at = CASE WHEN reviewer_id_param IS NOT NULL THEN NOW() ELSE reviewed_at END,
        rejection_reason = reason_param,
        admin_notes = notes_param,
        updated_at = NOW()
    WHERE id = document_id_param;

    -- Log the status change
    INSERT INTO document_verification_history (
        document_id, previous_status, new_status, changed_by, change_reason, admin_notes
    ) VALUES (
        document_id_param, old_status, new_status_param, reviewer_id_param, reason_param, notes_param
    );

    -- Update onboarding progress if needed
    PERFORM update_onboarding_progress(driver_id_val);

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update overall onboarding progress
CREATE OR REPLACE FUNCTION update_onboarding_progress(driver_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    doc_counts RECORD;
    progress_record RECORD;
    new_status onboarding_status;
    new_step INTEGER;
BEGIN
    -- Get document upload status counts
    SELECT
        COUNT(*) FILTER (WHERE document_type = 'drivers_license' AND status = 'approved') > 0 as license_approved,
        COUNT(*) FILTER (WHERE document_type = 'vehicle_registration' AND status = 'approved') > 0 as registration_approved,
        COUNT(*) FILTER (WHERE document_type = 'insurance_certificate' AND status = 'approved') > 0 as insurance_approved,
        COUNT(*) FILTER (WHERE document_type = 'profile_photo' AND status = 'approved') > 0 as photo_approved,
        COUNT(*) FILTER (WHERE status = 'uploaded') as uploaded_count,
        COUNT(*) FILTER (WHERE status = 'under_review') as review_count,
        COUNT(*) FILTER (WHERE status = 'rejected') as rejected_count,
        COUNT(*) FILTER (WHERE status = 'approved') as approved_count,
        COUNT(*) as total_count
    INTO doc_counts
    FROM document_uploads
    WHERE driver_id = driver_id_param;

    -- Determine new status and step
    IF doc_counts.approved_count >= 4 THEN -- All required docs approved
        new_status := 'approved';
        new_step := 4;
    ELSIF doc_counts.review_count > 0 THEN -- Some docs under review
        new_status := 'under_review';
        new_step := 3;
    ELSIF doc_counts.rejected_count > 0 THEN -- Some docs rejected
        new_status := 'rejected';
        new_step := 2;
    ELSIF doc_counts.uploaded_count > 0 THEN -- Some docs uploaded
        new_status := 'documents_uploaded';
        new_step := 3;
    ELSE
        new_status := 'documents_pending';
        new_step := 2;
    END IF;

    -- Update progress record
    UPDATE driver_onboarding_progress
    SET
        current_step = new_step,
        onboarding_status = new_status,
        documents_uploaded = (doc_counts.total_count > 0),
        drivers_license_uploaded = doc_counts.license_approved,
        vehicle_registration_uploaded = doc_counts.registration_approved,
        insurance_certificate_uploaded = doc_counts.insurance_approved,
        profile_photo_uploaded = doc_counts.photo_approved,
        completed_at = CASE WHEN new_status = 'approved' THEN NOW() ELSE NULL END,
        updated_at = NOW()
    WHERE driver_id = driver_id_param;

    -- Update driver verification status
    UPDATE drivers
    SET
        verification_status = CASE
            WHEN new_status = 'approved' THEN 'approved'::verification_status
            WHEN new_status = 'rejected' THEN 'rejected'::verification_status
            ELSE 'pending'::verification_status
        END,
        onboarding_completed = (new_status = 'approved'),
        updated_at = NOW()
    WHERE id = driver_id_param;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
