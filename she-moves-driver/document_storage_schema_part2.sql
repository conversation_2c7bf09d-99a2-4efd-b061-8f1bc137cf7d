-- =====================================================
-- DOCUMENT STORAGE AND VERIFICATION SCHEMA - PART 2
-- TABLES AND FUNCTIONS - Run this after part 1
-- =====================================================

-- =====================================================
-- 1. CREATE DOCUMENT UPLOADS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL, -- Size in bytes
    file_url TEXT NOT NULL, -- Supabase Storage URL
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'uploaded',
    
    -- Verification fields
    reviewed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    admin_notes TEXT,
    
    -- Expiry tracking
    expires_at TIMESTAMP WITH TIME ZONE,
    expiry_reminder_sent BOOLEAN DEFAULT FALSE,
    
    -- Upload metadata
    upload_ip_address INET,
    upload_user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(driver_id, document_type), -- One document per type per driver
    CHECK (file_size > 0),
    CHECK (file_size <= 10485760) -- Max 10MB file size
);

-- =====================================================
-- 2. CREATE DOCUMENT VERIFICATION HISTORY TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_verification_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_id UUID REFERENCES document_uploads(id) ON DELETE CASCADE NOT NULL,
    previous_status document_status NOT NULL,
    new_status document_status NOT NULL,
    changed_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    change_reason TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CREATE DRIVER ONBOARDING PROGRESS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS driver_onboarding_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE UNIQUE NOT NULL,
    current_step INTEGER DEFAULT 1, -- 1: Profile, 2: Documents, 3: Review, 4: Complete
    onboarding_status onboarding_status DEFAULT 'profile_created',
    
    -- Step completion tracking
    profile_completed BOOLEAN DEFAULT FALSE,
    documents_uploaded BOOLEAN DEFAULT FALSE,
    background_check_completed BOOLEAN DEFAULT FALSE,
    final_review_completed BOOLEAN DEFAULT FALSE,
    
    -- Document requirements checklist
    drivers_license_uploaded BOOLEAN DEFAULT FALSE,
    vehicle_registration_uploaded BOOLEAN DEFAULT FALSE,
    insurance_certificate_uploaded BOOLEAN DEFAULT FALSE,
    profile_photo_uploaded BOOLEAN DEFAULT FALSE,
    
    -- Progress metadata
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_completion_date TIMESTAMP WITH TIME ZONE,
    
    -- Admin fields
    assigned_reviewer UUID REFERENCES profiles(id) ON DELETE SET NULL,
    priority_level INTEGER DEFAULT 1, -- 1: Normal, 2: High, 3: Urgent
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 4. CREATE DOCUMENT TEMPLATES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS document_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_type document_type NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[], -- Array of requirements
    accepted_formats TEXT[], -- e.g., ['image/jpeg', 'image/png', 'application/pdf']
    max_file_size BIGINT DEFAULT 5242880, -- 5MB default
    is_required BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    help_text TEXT,
    example_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. INSERT DEFAULT DOCUMENT TEMPLATES
-- =====================================================

INSERT INTO document_templates (document_type, display_name, description, requirements, accepted_formats, max_file_size, is_required, sort_order, help_text) VALUES
('drivers_license', 'Driver''s License', 'Valid driver''s license (front and back)', 
 ARRAY['Must be current and not expired', 'Clear photo showing all text', 'Full document visible'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 1,
 'Take a clear photo of both sides of your driver''s license. Ensure all text is readable and the document is not expired.'),

('vehicle_registration', 'Vehicle Registration', 'Current vehicle registration certificate', 
 ARRAY['Must match the vehicle you''ll be driving', 'Current registration (not expired)', 'Clear and readable'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 2,
 'Upload your current vehicle registration document. Make sure the vehicle details match what you entered in your profile.'),

('insurance_certificate', 'Insurance Certificate', 'Valid vehicle insurance certificate', 
 ARRAY['Must cover the registered vehicle', 'Current and not expired', 'Minimum coverage requirements met'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 3,
 'Provide proof of current vehicle insurance. The policy must cover the vehicle you''ll use for driving.'),

('profile_photo', 'Profile Photo', 'Professional headshot for your driver profile', 
 ARRAY['Clear face photo', 'Professional appearance', 'Good lighting'], 
 ARRAY['image/jpeg', 'image/png'], 2097152, TRUE, 4,
 'Upload a clear, professional photo of yourself. This will be shown to passengers when they book rides.')

ON CONFLICT (document_type) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    requirements = EXCLUDED.requirements,
    accepted_formats = EXCLUDED.accepted_formats,
    max_file_size = EXCLUDED.max_file_size,
    help_text = EXCLUDED.help_text,
    updated_at = NOW();

-- =====================================================
-- 6. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for document_uploads table
CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);
CREATE INDEX IF NOT EXISTS idx_document_uploads_status ON document_uploads(status);
CREATE INDEX IF NOT EXISTS idx_document_uploads_type ON document_uploads(document_type);
CREATE INDEX IF NOT EXISTS idx_document_uploads_created_at ON document_uploads(created_at);

-- Indexes for verification history
CREATE INDEX IF NOT EXISTS idx_verification_history_document_id ON document_verification_history(document_id);
CREATE INDEX IF NOT EXISTS idx_verification_history_created_at ON document_verification_history(created_at);

-- Indexes for onboarding progress
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_driver_id ON driver_onboarding_progress(driver_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_status ON driver_onboarding_progress(onboarding_status);

-- =====================================================
-- 7. CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to tables
CREATE TRIGGER update_document_uploads_updated_at
    BEFORE UPDATE ON document_uploads
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_onboarding_progress_updated_at
    BEFORE UPDATE ON driver_onboarding_progress
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_templates_updated_at
    BEFORE UPDATE ON document_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 8. ENABLE RLS AND CREATE POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_verification_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_onboarding_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_uploads
CREATE POLICY "Drivers can view their own documents" ON document_uploads
    FOR SELECT USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can insert their own documents" ON document_uploads
    FOR INSERT WITH CHECK (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can update their own documents" ON document_uploads
    FOR UPDATE USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for onboarding progress
CREATE POLICY "Drivers can view their onboarding progress" ON driver_onboarding_progress
    FOR SELECT USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can update their onboarding progress" ON driver_onboarding_progress
    FOR UPDATE USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Drivers can insert their onboarding progress" ON driver_onboarding_progress
    FOR INSERT WITH CHECK (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for document templates (public read)
CREATE POLICY "Anyone can view document templates" ON document_templates
    FOR SELECT USING (true);

-- RLS Policies for verification history (read-only for drivers)
CREATE POLICY "Drivers can view their document history" ON document_verification_history
    FOR SELECT USING (
        document_id IN (
            SELECT id FROM document_uploads du 
            JOIN drivers d ON du.driver_id = d.id 
            WHERE d.user_id = auth.uid()
        )
    );

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'PART 2 COMPLETED - TABLES AND POLICIES CREATED';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Now run part 3 for helper functions';
    RAISE NOTICE '=================================================';
END $$;
