#!/usr/bin/env node

/**
 * Fix document upload screen by creating missing tables and data
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function fixDocumentUploadScreen() {
  console.log('🔧 Fixing document upload screen...\n');

  try {
    // 1. Check if document_templates table exists
    console.log('1. Checking document_templates table...');
    
    const { data: templates, error: templatesError } = await supabase
      .from('document_templates')
      .select('*')
      .limit(1);

    if (templatesError) {
      if (templatesError.code === '42P01') {
        console.log('❌ document_templates table does not exist');
        console.log('   Creating table and inserting sample data...');
        await createDocumentTemplatesTable();
      } else {
        console.error('❌ Error querying document_templates:', templatesError);
        return;
      }
    } else {
      console.log(`✅ document_templates table exists with ${templates.length} records`);
      
      if (templates.length === 0) {
        console.log('   Table is empty, inserting sample data...');
        await insertDocumentTemplates();
      }
    }

    // 2. Check if document_uploads table exists
    console.log('\n2. Checking document_uploads table...');
    
    const { data: uploads, error: uploadsError } = await supabase
      .from('document_uploads')
      .select('*')
      .limit(1);

    if (uploadsError) {
      if (uploadsError.code === '42P01') {
        console.log('❌ document_uploads table does not exist');
        console.log('   Creating table...');
        await createDocumentUploadsTable();
      } else {
        console.error('❌ Error querying document_uploads:', uploadsError);
        return;
      }
    } else {
      console.log(`✅ document_uploads table exists with ${uploads.length} records`);
    }

    // 3. Test the queries that the screen uses
    console.log('\n3. Testing document upload screen queries...');
    
    const { data: testTemplates, error: testError } = await supabase
      .from('document_templates')
      .select('*')
      .eq('is_required', true)
      .order('sort_order');

    if (testError) {
      console.error('❌ Error testing templates query:', testError);
    } else {
      console.log(`✅ Templates query successful: ${testTemplates.length} required documents`);
      testTemplates.forEach((template, index) => {
        console.log(`   ${index + 1}. ${template.display_name} (${template.document_type})`);
      });
    }

    console.log('\n🎉 Document upload screen should now work!');
    console.log('\n📋 Next steps:');
    console.log('1. Restart the driver app');
    console.log('2. Complete driver signup and onboarding');
    console.log('3. The document upload screen should now show the required documents');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function createDocumentTemplatesTable() {
  // This should be done via SQL in Supabase dashboard, but let's try to help
  console.log('⚠️  Cannot create tables via JavaScript with anon key');
  console.log('   Please run this SQL in your Supabase SQL Editor:');
  
  const sql = `
-- Create document_templates table
CREATE TABLE IF NOT EXISTS document_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_type TEXT UNIQUE NOT NULL,
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[] NOT NULL DEFAULT '{}',
    accepted_formats TEXT[] NOT NULL DEFAULT '{"image/*", "application/pdf"}',
    max_file_size INTEGER NOT NULL DEFAULT 5242880, -- 5MB
    is_required BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER NOT NULL DEFAULT 0,
    help_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert required document templates
INSERT INTO document_templates (document_type, display_name, description, requirements, sort_order, help_text) VALUES
('drivers_license', 'Driver''s License', 'Valid South African driver''s license', 
 ARRAY['Must be valid and not expired', 'Clear photo showing all details', 'Both front and back if applicable'], 
 1, 'Take a clear photo of your driver''s license. Make sure all text is readable.'),
 
('vehicle_registration', 'Vehicle Registration', 'Vehicle registration certificate', 
 ARRAY['Must match the vehicle you''ll be driving', 'Document must be current', 'All details must be clearly visible'], 
 2, 'Upload your vehicle registration certificate. This must match the vehicle details you provided.'),
 
('insurance_certificate', 'Insurance Certificate', 'Valid vehicle insurance certificate', 
 ARRAY['Must be current and valid', 'Must cover the registered vehicle', 'Policy must include third-party coverage'], 
 3, 'Upload your current vehicle insurance certificate. Make sure it covers the vehicle you''ll be driving.'),
 
('profile_photo', 'Profile Photo', 'Clear photo of yourself for identification', 
 ARRAY['Recent photo (taken within last 6 months)', 'Face must be clearly visible', 'Good lighting, no shadows'], 
 4, 'Take a clear, recent photo of yourself. This will be used for passenger identification.');

-- Enable RLS
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access
CREATE POLICY "Anyone can view document templates" ON document_templates
    FOR SELECT USING (true);
`;

  console.log(sql);
  
  // Try to insert the data anyway (might work if table exists)
  await insertDocumentTemplates();
}

async function insertDocumentTemplates() {
  try {
    const templates = [
      {
        document_type: 'drivers_license',
        display_name: 'Driver\'s License',
        description: 'Valid South African driver\'s license',
        requirements: ['Must be valid and not expired', 'Clear photo showing all details', 'Both front and back if applicable'],
        sort_order: 1,
        help_text: 'Take a clear photo of your driver\'s license. Make sure all text is readable.',
        is_required: true,
        accepted_formats: ['image/*', 'application/pdf'],
        max_file_size: 5242880
      },
      {
        document_type: 'vehicle_registration',
        display_name: 'Vehicle Registration',
        description: 'Vehicle registration certificate',
        requirements: ['Must match the vehicle you\'ll be driving', 'Document must be current', 'All details must be clearly visible'],
        sort_order: 2,
        help_text: 'Upload your vehicle registration certificate. This must match the vehicle details you provided.',
        is_required: true,
        accepted_formats: ['image/*', 'application/pdf'],
        max_file_size: 5242880
      },
      {
        document_type: 'insurance_certificate',
        display_name: 'Insurance Certificate',
        description: 'Valid vehicle insurance certificate',
        requirements: ['Must be current and valid', 'Must cover the registered vehicle', 'Policy must include third-party coverage'],
        sort_order: 3,
        help_text: 'Upload your current vehicle insurance certificate. Make sure it covers the vehicle you\'ll be driving.',
        is_required: true,
        accepted_formats: ['image/*', 'application/pdf'],
        max_file_size: 5242880
      },
      {
        document_type: 'profile_photo',
        display_name: 'Profile Photo',
        description: 'Clear photo of yourself for identification',
        requirements: ['Recent photo (taken within last 6 months)', 'Face must be clearly visible', 'Good lighting, no shadows'],
        sort_order: 4,
        help_text: 'Take a clear, recent photo of yourself. This will be used for passenger identification.',
        is_required: true,
        accepted_formats: ['image/*'],
        max_file_size: 5242880
      }
    ];

    const { data, error } = await supabase
      .from('document_templates')
      .upsert(templates, { onConflict: 'document_type' })
      .select();

    if (error) {
      console.error('❌ Error inserting templates:', error);
    } else {
      console.log(`✅ Inserted ${data.length} document templates`);
    }
  } catch (error) {
    console.error('❌ Error in insertDocumentTemplates:', error);
  }
}

async function createDocumentUploadsTable() {
  console.log('⚠️  Cannot create tables via JavaScript with anon key');
  console.log('   Please run this SQL in your Supabase SQL Editor:');
  
  const sql = `
-- Create document_uploads table
CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    document_type TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER NOT NULL DEFAULT 0,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL DEFAULT 'image/jpeg',
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES profiles(id),
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(driver_id, document_type)
);

-- Enable RLS
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Drivers can manage their own documents" ON document_uploads
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM drivers 
            WHERE drivers.id = document_uploads.driver_id 
            AND drivers.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all documents" ON document_uploads
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.user_type = 'admin'
        )
    );
`;

  console.log(sql);
}

// Run the fix
fixDocumentUploadScreen().then(() => {
  console.log('\n🏁 Fix complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Fix failed:', error);
  process.exit(1);
});
