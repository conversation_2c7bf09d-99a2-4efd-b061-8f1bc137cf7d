#!/usr/bin/env node

/**
 * Diagnose database trigger and RLS policy issues
 * Check why signup is failing with "Database error saving new user"
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function diagnoseSignupIssues() {
  console.log('� Diagnosing signup database errors...\n');

  try {
    // 1. Check if the trigger function exists
    console.log('1. Checking database trigger function...');

    const { data: functions, error: functionsError } = await supabase
      .from('information_schema.routines')
      .select('routine_name, routine_type')
      .eq('routine_name', 'handle_new_user')
      .eq('routine_schema', 'public');

    if (functionsError) {
      console.error('❌ Error checking functions:', functionsError);
    } else {
      console.log(`📊 Found ${functions.length} matching functions`);
      if (functions.length > 0) {
        console.log('✅ handle_new_user function exists');
      } else {
        console.log('❌ handle_new_user function NOT found');
        console.log('   → This could be why signup is failing');
      }
    }

    // 2. Check if the trigger exists
    console.log('\n2. Checking database trigger...');

    const { data: triggers, error: triggersError } = await supabase
      .from('information_schema.triggers')
      .select('trigger_name, event_manipulation, action_timing')
      .eq('trigger_name', 'on_auth_user_created');

    if (triggersError) {
      console.error('❌ Error checking triggers:', triggersError);
    } else {
      console.log(`📊 Found ${triggers.length} matching triggers`);
      if (triggers.length > 0) {
        console.log('✅ on_auth_user_created trigger exists');
        triggers.forEach(trigger => {
          console.log(`   - ${trigger.trigger_name}: ${trigger.action_timing} ${trigger.event_manipulation}`);
        });
      } else {
        console.log('❌ on_auth_user_created trigger NOT found');
        console.log('   → This could be why signup is failing');
      }
    }

    // 3. Check RLS policies on profiles table
    console.log('\n3. Checking RLS policies...');

    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('policyname, cmd, qual')
      .eq('tablename', 'profiles');

    if (policiesError) {
      console.error('❌ Error checking policies:', policiesError);
    } else {
      console.log(`📊 Found ${policies.length} RLS policies on profiles table`);
      policies.forEach(policy => {
        console.log(`   - ${policy.policyname} (${policy.cmd})`);
      });
    }

    // 4. Test manual profile creation
    console.log('\n4. Testing manual profile creation...');

    const testProfileData = {
      id: '00000000-0000-0000-0000-000000000001', // Test UUID
      email: '<EMAIL>',
      full_name: 'Test User',
      user_type: 'driver',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: testProfile, error: testError } = await supabase
      .from('profiles')
      .insert(testProfileData)
      .select()
      .single();

    if (testError) {
      console.log('❌ Manual profile creation failed:');
      console.log(`   Error: ${testError.message}`);
      console.log(`   Code: ${testError.code}`);
      console.log('   → This indicates RLS policy issues');
    } else {
      console.log('✅ Manual profile creation works');

      // Clean up test profile
      await supabase
        .from('profiles')
        .delete()
        .eq('id', testProfileData.id);
      console.log('   Test profile cleaned up');
    }

    // 5. Check storage bucket
    console.log('\n5. Checking storage bucket...');

    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
    } else {
      console.log(`📊 Found ${buckets.length} storage buckets`);
      const driverDocsBucket = buckets.find(b => b.name === 'driver-documents');
      if (driverDocsBucket) {
        console.log('✅ driver-documents bucket exists');
      } else {
        console.log('❌ driver-documents bucket missing');
      }
    }

    console.log('\n📋 DIAGNOSIS SUMMARY:');
    console.log('');
    console.log('The "Database error saving new user" error is likely caused by:');
    console.log('1. Missing or broken database trigger function');
    console.log('2. Incorrect RLS policies blocking profile creation');
    console.log('3. Trigger trying to insert into profiles but failing due to permissions');
    console.log('');
    console.log('🔧 RECOMMENDED FIXES:');
    console.log('1. Check if the trigger function was created properly');
    console.log('2. Verify RLS policies allow the trigger to insert profiles');
    console.log('3. Consider temporarily disabling RLS to test');
    console.log('4. Check Supabase logs for detailed error messages');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the diagnosis
diagnoseSignupIssues().then(() => {
  console.log('\n🏁 Diagnosis complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Diagnosis failed:', error);
  process.exit(1);
});
