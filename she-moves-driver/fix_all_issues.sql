-- Comprehensive fix for all signup and driver registration issues
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. Fix the signup trigger to properly handle user_type
-- =====================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. Check and add missing columns to drivers table
-- =====================================================

-- Add onboarding_completed column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'onboarding_completed'
    ) THEN
        ALTER TABLE drivers ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Add other missing columns that might be needed
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'total_earnings'
    ) THEN
        ALTER TABLE drivers ADD COLUMN total_earnings DECIMAL(12,2) DEFAULT 0.0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'acceptance_rate'
    ) THEN
        ALTER TABLE drivers ADD COLUMN acceptance_rate DECIMAL(5,2) DEFAULT 100.0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'cancellation_rate'
    ) THEN
        ALTER TABLE drivers ADD COLUMN cancellation_rate DECIMAL(5,2) DEFAULT 0.0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'average_rating'
    ) THEN
        ALTER TABLE drivers ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 5.0;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'background_check_status'
    ) THEN
        ALTER TABLE drivers ADD COLUMN background_check_status verification_status DEFAULT 'pending';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'emergency_contact_name'
    ) THEN
        ALTER TABLE drivers ADD COLUMN emergency_contact_name TEXT;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'emergency_contact_phone'
    ) THEN
        ALTER TABLE drivers ADD COLUMN emergency_contact_phone TEXT;
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'preferred_language'
    ) THEN
        ALTER TABLE drivers ADD COLUMN preferred_language TEXT DEFAULT 'en';
    END IF;
END $$;

-- =====================================================
-- 3. Verify current table structure
-- =====================================================

-- Check profiles table
SELECT 'profiles' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- Check drivers table
SELECT 'drivers' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'drivers' 
ORDER BY ordinal_position;

-- =====================================================
-- 4. Check existing user data
-- =====================================================

-- Check if the user profile exists
SELECT id, email, full_name, user_type, created_at 
FROM profiles 
WHERE email = '<EMAIL>';

-- Check if driver profile exists
SELECT d.id, d.user_id, d.license_number, d.verification_status, d.onboarding_completed
FROM drivers d
JOIN profiles p ON d.user_id = p.id
WHERE p.email = '<EMAIL>';
