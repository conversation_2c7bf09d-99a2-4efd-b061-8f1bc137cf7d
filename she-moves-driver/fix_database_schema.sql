-- =====================================================
-- COMPREHENSIVE DATABASE SCHEMA FIX
-- This fixes all missing tables, columns, and enums
-- Run this in your Supabase SQL Editor FIRST
-- =====================================================

-- =====================================================
-- 1. CREATE MISSING ENUMS
-- =====================================================

-- Create user_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_type') THEN
        CREATE TYPE user_type AS ENUM ('passenger', 'driver');
    END IF;
END $$;

-- Create ride_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'ride_type') THEN
        CREATE TYPE ride_type AS ENUM ('SheRide', 'ShePool', 'SheXL');
    END IF;
END $$;

-- Create verification_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'verification_status') THEN
        CREATE TYPE verification_status AS ENUM ('pending', 'approved', 'rejected');
    END IF;
END $$;

-- Create trip_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'trip_status') THEN
        CREATE TYPE trip_status AS ENUM ('requested', 'accepted', 'driver_arrived', 'in_progress', 'completed', 'cancelled');
    END IF;
END $$;

-- Create document_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_type') THEN
        CREATE TYPE document_type AS ENUM ('license', 'vehicle_registration', 'insurance', 'profile_photo');
    END IF;
END $$;

-- Create document_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_status') THEN
        CREATE TYPE document_status AS ENUM ('pending', 'approved', 'rejected');
    END IF;
END $$;

-- Create driver_status enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'driver_status') THEN
        CREATE TYPE driver_status AS ENUM ('offline', 'online', 'busy');
    END IF;
END $$;

-- =====================================================
-- 2. CREATE PROFILES TABLE IF NOT EXISTS
-- =====================================================

CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone_number TEXT,
    avatar_url TEXT,
    user_type user_type DEFAULT 'passenger',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. CREATE/FIX DRIVERS TABLE
-- =====================================================

-- Create drivers table if it doesn't exist
CREATE TABLE IF NOT EXISTS drivers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE UNIQUE NOT NULL,
    license_number TEXT UNIQUE NOT NULL,
    license_expiry DATE,
    vehicle_make TEXT NOT NULL,
    vehicle_model TEXT NOT NULL,
    vehicle_year INTEGER NOT NULL,
    vehicle_color TEXT NOT NULL,
    vehicle_plate TEXT UNIQUE NOT NULL,
    verification_status verification_status DEFAULT 'pending',
    is_online BOOLEAN DEFAULT FALSE,
    current_location POINT,
    rating DECIMAL(3,2) DEFAULT 5.0,
    total_trips INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add missing columns to drivers table
DO $$ 
BEGIN
    -- Add vehicle_type column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'vehicle_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN vehicle_type ride_type DEFAULT 'SheRide';
    END IF;
END $$;

DO $$ 
BEGIN
    -- Add onboarding_completed column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'onboarding_completed'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN onboarding_completed BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

DO $$ 
BEGIN
    -- Add insurance_policy column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'insurance_policy'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN insurance_policy TEXT;
    END IF;
END $$;

DO $$ 
BEGIN
    -- Add insurance_expiry column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'insurance_expiry'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN insurance_expiry DATE;
    END IF;
END $$;

DO $$ 
BEGIN
    -- Add total_earnings column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'total_earnings'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN total_earnings DECIMAL(12,2) DEFAULT 0.0;
    END IF;
END $$;

DO $$ 
BEGIN
    -- Add acceptance_rate column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'drivers' 
        AND column_name = 'acceptance_rate'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE drivers ADD COLUMN acceptance_rate DECIMAL(5,2) DEFAULT 100.0;
    END IF;
END $$;

-- =====================================================
-- 4. CREATE TRIGGER FUNCTION FOR USER SIGNUP
-- =====================================================

-- Drop existing trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create the handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the incoming data for debugging
    RAISE LOG 'handle_new_user triggered for user: %, email: %, metadata: %', 
        NEW.id, NEW.email, NEW.raw_user_meta_data;
    
    -- Insert profile with proper user_type handling
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger')::user_type
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW();
    
    RAISE LOG 'Profile created/updated for user: % with user_type: %', 
        NEW.id, COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger');
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =====================================================
-- 5. SET UP RLS POLICIES
-- =====================================================

-- Enable RLS on profiles table
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON profiles;

-- Create RLS policies for profiles
CREATE POLICY "Users can view their own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable insert for authenticated users" ON profiles
    FOR INSERT WITH CHECK (true);

-- Enable RLS on drivers table
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Drivers can view their own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can update their own data" ON drivers;
DROP POLICY IF EXISTS "Drivers can insert their own data" ON drivers;

-- Create RLS policies for drivers
CREATE POLICY "Drivers can view their own data" ON drivers
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Drivers can update their own data" ON drivers
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Drivers can insert their own data" ON drivers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================

-- Check that all enums exist
SELECT 'Enums Check' as test_name, string_agg(typname, ', ') as enums_found
FROM pg_type 
WHERE typname IN ('user_type', 'ride_type', 'verification_status', 'trip_status', 'document_type', 'document_status', 'driver_status');

-- Check profiles table structure
SELECT 'Profiles Table' as test_name, string_agg(column_name, ', ') as columns_found
FROM information_schema.columns 
WHERE table_name = 'profiles' AND table_schema = 'public';

-- Check drivers table structure
SELECT 'Drivers Table' as test_name, string_agg(column_name, ', ') as columns_found
FROM information_schema.columns 
WHERE table_name = 'drivers' AND table_schema = 'public';

-- Check trigger function exists
SELECT 'Trigger Function' as test_name, 
       CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'MISSING' END as status
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' AND routine_schema = 'public';

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DATABASE SCHEMA FIX COMPLETED';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Created/Fixed:';
    RAISE NOTICE '1. All required enums';
    RAISE NOTICE '2. Profiles table with user_type column';
    RAISE NOTICE '3. Drivers table with vehicle_type column';
    RAISE NOTICE '4. Trigger function for user signup';
    RAISE NOTICE '5. RLS policies for both tables';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next: Run the authentication fix script';
    RAISE NOTICE '=================================================';
END $$;
