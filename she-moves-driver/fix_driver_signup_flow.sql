-- =====================================================
-- FIX DRIVER SIGNUP FLOW FOR FUTURE REGISTRATIONS
-- Updates the trigger to automatically create driver profiles
-- =====================================================

-- Drop the existing trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create enhanced handle_new_user function that creates driver profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_type_value TEXT;
    new_driver_id UUID;
BEGIN
    -- Log the incoming data for debugging
    RAISE LOG 'handle_new_user triggered for user: %, email: %, metadata: %', 
        NEW.id, NEW.email, NEW.raw_user_meta_data;
    
    -- Get user type from metadata
    user_type_value := COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger');
    
    -- Insert profile with proper user_type handling
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        user_type_value::user_type
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW();
    
    RAISE LOG 'Profile created/updated for user: % with user_type: %', 
        NEW.id, user_type_value;
    
    -- If user is a driver, create driver profile automatically
    IF user_type_value = 'driver' THEN
        INSERT INTO public.drivers (
            user_id,
            license_number,
            vehicle_make,
            vehicle_model,
            vehicle_year,
            vehicle_color,
            vehicle_plate,
            vehicle_type,
            verification_status,
            onboarding_completed
        ) VALUES (
            NEW.id,
            'PENDING-' || SUBSTRING(NEW.id::text, 1, 8), -- Temporary license number
            'Not Specified', -- Will be updated during onboarding
            'Not Specified',
            2020,
            'Not Specified',
            'PENDING-' || SUBSTRING(NEW.id::text, 1, 6),
            'SheRide',
            'pending',
            false
        )
        ON CONFLICT (user_id) DO NOTHING; -- Avoid duplicates
        
        RAISE LOG 'Driver profile created for user: %', NEW.id;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user for user %: %', NEW.id, SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Test the updated function by checking if it exists
SELECT 
    'SUCCESS: Enhanced trigger function created' as result,
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- Verify trigger exists
SELECT 
    'SUCCESS: Trigger recreated' as result,
    trigger_name,
    event_manipulation,
    action_timing
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'DRIVER SIGNUP FLOW FIXED';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'New driver registrations will now automatically:';
    RAISE NOTICE '1. Create user profile with user_type = driver';
    RAISE NOTICE '2. Create driver record with default values';
    RAISE NOTICE '3. Set verification_status = pending';
    RAISE NOTICE '4. Set onboarding_completed = false';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Existing users: Run fix_missing_driver_profile.sql';
    RAISE NOTICE 'New users: Will work automatically';
    RAISE NOTICE '=================================================';
END $$;
