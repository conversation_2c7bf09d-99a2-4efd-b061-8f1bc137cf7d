-- =====================================================
-- FIX EXISTING USER PROFILE
-- Run this after running comprehensive_auth_fix.sql
-- =====================================================

-- Update the existing user to have correct user_type
UPDATE profiles 
SET 
    user_type = 'driver',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Verify the fix worked
SELECT 
    id, 
    email, 
    full_name, 
    user_type, 
    user_type::text as user_type_text,
    created_at, 
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Also check what's in the auth.users table for this user
SELECT 
    id,
    email,
    created_at,
    raw_user_meta_data,
    raw_user_meta_data->>'user_type' as metadata_user_type
FROM auth.users 
WHERE email = '<EMAIL>';

-- If the user doesn't have the correct metadata, update it
UPDATE auth.users 
SET raw_user_meta_data = jsonb_set(
    COALESCE(raw_user_meta_data, '{}'),
    '{user_type}',
    '"driver"'
)
WHERE email = '<EMAIL>'
AND (raw_user_meta_data->>'user_type' IS NULL OR raw_user_meta_data->>'user_type' != 'driver');

-- Final verification
SELECT 
    'auth.users' as table_name,
    email,
    raw_user_meta_data->>'user_type' as user_type
FROM auth.users 
WHERE email = '<EMAIL>'
UNION ALL
SELECT 
    'profiles' as table_name,
    email,
    user_type::text as user_type
FROM profiles 
WHERE email = '<EMAIL>';
