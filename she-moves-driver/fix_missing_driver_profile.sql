-- =====================================================
-- FIX MISSING DRIVER PROFILE - SIMPLIFIED
-- Creates driver profile for existing user
-- =====================================================

-- Check current user status
SELECT
    'Current User Status' as section,
    p.id,
    p.email,
    p.user_type,
    d.id as driver_profile_id
FROM profiles p
LEFT JOIN drivers d ON p.id = d.user_id
WHERE p.email = '<EMAIL>';

-- Create missing driver profile
INSERT INTO drivers (
    user_id,
    license_number,
    vehicle_make,
    vehicle_model,
    vehicle_year,
    vehicle_color,
    vehicle_plate,
    vehicle_type,
    verification_status,
    onboarding_completed
)
SELECT
    p.id,
    'TEMP-' || SUBSTRING(p.id::text, 1, 8), -- Temporary license number
    'Toyota', -- Default values - user can update later
    'Corolla',
    2020,
    'White',
    'TEMP-' || SUBSTRING(p.id::text, 1, 6),
    'SheRide',
    'pending',
    false
FROM profiles p
WHERE p.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM drivers d WHERE d.user_id = p.id
);

-- Verify the fix
SELECT
    'Fixed User Status' as section,
    p.id as profile_id,
    p.email,
    p.user_type,
    d.id as driver_id,
    d.license_number,
    d.verification_status,
    d.onboarding_completed
FROM profiles p
JOIN drivers d ON p.id = d.user_id
WHERE p.email = '<EMAIL>';
