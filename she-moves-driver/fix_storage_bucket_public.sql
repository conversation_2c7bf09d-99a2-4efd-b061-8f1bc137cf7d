-- =====================================================
-- FIX SUPABASE STORAGE BUCKET CONFIGURATION
-- Changes driver-documents bucket to public for easier access
-- while maintaining security through RLS policies
-- =====================================================

-- Update existing bucket to be public
UPDATE storage.buckets 
SET public = true 
WHERE id = 'driver-documents';

-- Drop existing policies (they may be too restrictive)
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;

-- Create more permissive policies for public bucket
-- Allow authenticated users to upload documents
CREATE POLICY "Allow authenticated uploads to driver-documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Allow public read access to documents (since bucket is public)
CREATE POLICY "Allow public read access to driver-documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'driver-documents');

-- Allow authenticated users to update their own documents
CREATE POLICY "Allow authenticated updates to driver-documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Allow authenticated users to delete their own documents
CREATE POLICY "Allow authenticated deletes from driver-documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Verify bucket configuration
SELECT 
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- Verify policies
SELECT 
    policyname,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver-documents%';

-- Success message
SELECT 'SUCCESS: Storage bucket updated to public with proper policies' as result;
