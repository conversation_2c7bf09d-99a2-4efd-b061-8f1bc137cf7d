-- =====================================================
-- FIX SUPABASE STORAGE UPLOAD ISSUES
-- Comprehensive fix for document upload failures
-- =====================================================

-- Step 1: Clean up existing bucket and policies
-- Remove any conflicting storage policies
DROP POLICY IF EXISTS "Drivers can upload their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can view their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can update their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Drivers can delete their own documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to driver-documents" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to driver-documents" ON storage.objects;

-- Step 2: Ensure the correct bucket exists and is properly configured
-- Delete existing bucket if it has wrong configuration
DELETE FROM storage.buckets WHERE id IN ('driver-document', 'documents');

-- Create or update the driver-documents bucket with correct settings
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'driver-documents',
    'driver-documents', 
    true, -- Public bucket for easier access
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
)
ON CONFLICT (id) DO UPDATE SET
    public = EXCLUDED.public,
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Step 3: Create simple, working storage policies
-- Allow authenticated users to upload to driver-documents bucket
CREATE POLICY "authenticated_upload_driver_documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Allow public read access to driver-documents (since bucket is public)
CREATE POLICY "public_read_driver_documents" ON storage.objects
    FOR SELECT USING (bucket_id = 'driver-documents');

-- Allow authenticated users to update their own documents
CREATE POLICY "authenticated_update_driver_documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Allow authenticated users to delete their own documents  
CREATE POLICY "authenticated_delete_driver_documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.role() = 'authenticated'
    );

-- Step 4: Verification queries
SELECT 
    'Storage Bucket Configuration' as check_type,
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

SELECT 
    'Storage Policies' as check_type,
    policyname,
    cmd as operation,
    CASE 
        WHEN qual IS NOT NULL THEN 'Has conditions'
        ELSE 'No conditions'
    END as conditions
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver_documents%';

-- Success message
SELECT 'SUCCESS: Storage bucket and policies configured correctly' as result;
