-- Fix trigger to handle existing profiles
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. Update trigger to use UPSERT instead of INSERT
-- =====================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Use INSERT ... ON CONFLICT to handle existing profiles
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger')
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. Also handle email conflicts
-- =====================================================

-- Drop and recreate the function to handle email conflicts too
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Use INSERT ... ON CONFLICT to handle existing profiles
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger')
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW()
    ON CONFLICT (email) DO UPDATE SET
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW();
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 3. Fix the existing user profile
-- =====================================================

-- Update the existing user to have correct user_type
UPDATE profiles 
SET 
    user_type = 'driver',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Verify the fix
SELECT 
    id, 
    email, 
    full_name, 
    user_type, 
    created_at, 
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';
