-- Corrected trigger fix
-- Run this in your Supabase SQL Editor

-- =====================================================
-- 1. Fix the trigger function with proper UPSERT
-- =====================================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Use INSERT ... ON CONFLICT to handle existing profiles
    INSERT INTO public.profiles (id, email, full_name, user_type)
    VALUES (
        NEW.id, 
        NEW.email, 
        NEW.raw_user_meta_data->>'full_name',
        COALESCE(NEW.raw_user_meta_data->>'user_type', 'passenger')
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        user_type = EXCLUDED.user_type,
        updated_at = NOW();
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the user creation
        RAISE WARNING 'Error in handle_new_user: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 2. Fix the existing user profile
-- =====================================================

-- Update the existing user to have correct user_type
UPDATE profiles 
SET 
    user_type = 'driver',
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- =====================================================
-- 3. Verify the fixes
-- =====================================================

-- Check the updated user profile
SELECT 
    id, 
    email, 
    full_name, 
    user_type, 
    created_at, 
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- Check if the trigger function was updated
SELECT 
    routine_name, 
    routine_type
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';
