{"name": "she-moves-driver", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.50.4", "dotenv": "^17.2.0", "expo": "~53.0.17", "expo-camera": "^16.1.10", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-document-picker": "^13.1.6", "expo-haptics": "^14.1.4", "expo-image-picker": "^16.1.4", "expo-linking": "^7.1.7", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-router": "^5.1.3", "expo-status-bar": "~2.2.3", "expo-task-manager": "^13.1.6", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-maps": "^1.24.3", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-webview": "^13.15.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "jest": "^29.7.0", "jest-expo": "^51.0.3", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "testMatch": ["**/__tests__/**/*.(ts|tsx|js)", "**/*.(test|spec).(ts|tsx|js)"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/**/__tests__/**", "!src/**/index.ts"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}, "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}}, "private": true}