-- =====================================================
-- SAFELY ADD MISSING VALUES TO document_status ENUM
-- Only adds values that don't already exist
-- =====================================================

-- First, let's see what values already exist
SELECT 
    'Existing document_status values' as section,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;

-- Add values only if they don't exist
DO $$
BEGIN
    -- Add 'uploaded' if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'uploaded' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'uploaded';
        RAISE NOTICE 'Added value: uploaded';
    ELSE
        RAISE NOTICE 'Value already exists: uploaded';
    END IF;

    -- Add 'under_review' if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'under_review' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'under_review';
        RAISE NOTICE 'Added value: under_review';
    ELSE
        RAISE NOTICE 'Value already exists: under_review';
    END IF;

    -- Add 'rejected' if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum 
        WHERE enumlabel = 'rejected' 
        AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
    ) THEN
        ALTER TYPE document_status ADD VALUE 'rejected';
        RAISE NOTICE 'Added value: rejected';
    ELSE
        RAISE NOTICE 'Value already exists: rejected';
    END IF;
END $$;

-- Show final values
SELECT 
    'Final document_status values' as section,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;
