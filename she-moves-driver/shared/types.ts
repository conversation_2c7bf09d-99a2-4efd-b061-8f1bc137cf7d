/**
 * Shared types for SheMove Driver App
 * These types are used across the application for consistency
 */

// Driver status types
export type DriverStatus = 'offline' | 'online' | 'busy' | 'break';

// Location and coordinates
export interface Coordinates {
  lat: number;
  lng: number;
}

// Driver profile interface
export interface DriverProfile {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  email: string;
  profile_photo_url?: string;
  date_of_birth: string;
  gender: 'male' | 'female' | 'other';
  emergency_contact_name: string;
  emergency_contact_phone: string;
  
  // Vehicle information
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number;
  vehicle_color: string;
  vehicle_license_plate: string;
  vehicle_photo_url?: string;
  
  // Verification and status
  verification_status: 'pending' | 'approved' | 'rejected';
  is_online: boolean;
  current_location?: string; // PostGIS POINT
  
  // Performance metrics
  rating: number;
  total_trips: number;
  total_earnings: number;
  
  // Documents
  drivers_license_url?: string;
  vehicle_registration_url?: string;
  insurance_certificate_url?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

// Driver availability interface
export interface DriverAvailability {
  id: string;
  driver_id: string;
  status: DriverStatus;
  available_from?: string;
  available_until?: string;
  preferred_areas?: string[];
  max_distance_km?: number;
  last_location_update?: string;
  created_at: string;
  updated_at: string;
}

// Trip related types
export type TripStatus = 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';

export interface Trip {
  id: string;
  passenger_id: string;
  driver_id?: string;
  pickup_location: string; // PostGIS POINT
  pickup_address: string;
  destination_location: string; // PostGIS POINT
  destination_address: string;
  status: TripStatus;
  fare_amount?: number;
  distance_km?: number;
  duration_minutes?: number;
  requested_at: string;
  accepted_at?: string;
  started_at?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  passenger_rating?: number;
  driver_rating?: number;
  created_at: string;
  updated_at: string;
}

// Trip request interface
export interface TripRequest {
  id: string;
  trip_id: string;
  driver_id: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  expires_at: string;
  responded_at?: string;
  created_at: string;
}

// User interface (from auth)
export interface User {
  id: string;
  email: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

// Location tracking
export interface LocationPoint {
  lat: number;
  lng: number;
  timestamp: string;
  accuracy?: number;
  heading?: number;
  speed?: number;
}

// Notification types
export interface NotificationData {
  type: 'trip_request' | 'trip_update' | 'system_message';
  title: string;
  body: string;
  data?: Record<string, any>;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form validation
export interface ValidationError {
  field: string;
  message: string;
}

// Document types for verification
export type DocumentType = 'drivers_license' | 'vehicle_registration' | 'insurance_certificate' | 'profile_photo' | 'vehicle_photo';

export interface Document {
  id: string;
  driver_id: string;
  document_type: DocumentType;
  file_url: string;
  verification_status: 'pending' | 'approved' | 'rejected';
  rejection_reason?: string;
  uploaded_at: string;
  verified_at?: string;
}

// Earnings and payments
export interface Earning {
  id: string;
  driver_id: string;
  trip_id: string;
  amount: number;
  commission: number;
  net_amount: number;
  payment_status: 'pending' | 'paid' | 'failed';
  paid_at?: string;
  created_at: string;
}

// Performance metrics
export interface PerformanceMetrics {
  driver_id: string;
  period_start: string;
  period_end: string;
  total_trips: number;
  completed_trips: number;
  cancelled_trips: number;
  acceptance_rate: number;
  cancellation_rate: number;
  average_rating: number;
  total_earnings: number;
  online_hours: number;
  earnings_per_hour: number;
}

// Real-time events
export interface RealtimeEvent {
  type: string;
  payload: any;
  timestamp: string;
}

// Map and routing
export interface RoutePoint {
  lat: number;
  lng: number;
  instruction?: string;
  distance?: number;
  duration?: number;
}

export interface Route {
  points: RoutePoint[];
  total_distance: number;
  total_duration: number;
  polyline?: string;
}

// App state
export interface AppState {
  isLoading: boolean;
  error?: string;
  user?: User;
  driver?: DriverProfile;
}

// Navigation types
export type RootStackParamList = {
  Home: undefined;
  Profile: undefined;
  Earnings: undefined;
  TripHistory: undefined;
  Settings: undefined;
  ActiveTrip: { tripId: string };
  TripRequest: { requestId: string };
};

// Theme and styling
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    error: string;
    success: string;
    warning: string;
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  typography: {
    fontSize: {
      xs: number;
      sm: number;
      md: number;
      lg: number;
      xl: number;
    };
  };
}
