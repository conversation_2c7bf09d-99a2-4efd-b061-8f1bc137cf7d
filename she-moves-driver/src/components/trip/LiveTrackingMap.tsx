/**
 * Live Tracking Map Component for SheMove Driver App
 * Displays real-time trip tracking with driver and passenger locations
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Text,
  TouchableOpacity,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import { TripTrackingService, TripLocationUpdate, RouteInfo } from '../../services/TripTrackingService';
import { LocationUpdate } from '../../services/DriverStatusService';
import { ActiveTrip } from '../../services/ActiveTripService';
import Colors from '../../constants/Colors';

interface LiveTrackingMapProps {
  trip: ActiveTrip;
  trackingService: TripTrackingService;
  currentLocation: LocationUpdate | null;
  onLocationUpdate?: (location: LocationUpdate) => void;
}

const { width, height } = Dimensions.get('window');

export default function LiveTrackingMap({
  trip,
  trackingService,
  currentLocation,
  onLocationUpdate
}: LiveTrackingMapProps) {
  const webViewRef = useRef<WebView>(null);
  const [isMapReady, setIsMapReady] = useState(false);
  const [passengerLocation, setPassengerLocation] = useState<LocationUpdate | null>(null);
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);
  const [eta, setEta] = useState<number | null>(null);

  // Initialize tracking when component mounts
  useEffect(() => {
    if (trackingService && trip) {
      startTracking();
    }

    return () => {
      if (trackingService) {
        trackingService.stopTracking();
      }
    };
  }, [trackingService, trip]);

  // Update driver location on map when it changes
  useEffect(() => {
    if (isMapReady && currentLocation) {
      updateDriverLocationOnMap(currentLocation);
      
      // Calculate ETA to destination
      if (trip.status === 'in_progress') {
        const estimatedTime = trackingService.calculateETA(
          { lat: currentLocation.lat, lng: currentLocation.lng },
          trip.destination_location,
          currentLocation.speed
        );
        setEta(estimatedTime);
      } else if (trip.status === 'en_route' || trip.status === 'arrived') {
        const estimatedTime = trackingService.calculateETA(
          { lat: currentLocation.lat, lng: currentLocation.lng },
          trip.pickup_location,
          currentLocation.speed
        );
        setEta(estimatedTime);
      }
    }
  }, [currentLocation, isMapReady, trip.status]);

  const startTracking = async () => {
    const success = await trackingService.startTracking(trip.id, {
      onLocationUpdate: (update: TripLocationUpdate) => {
        console.log('Location update received:', update);
        onLocationUpdate?.(update.driver_location);
      },
      onPassengerLocationUpdate: (location: LocationUpdate) => {
        console.log('Passenger location update:', location);
        setPassengerLocation(location);
        if (isMapReady) {
          updatePassengerLocationOnMap(location);
        }
      },
      onRouteUpdate: (route: RouteInfo) => {
        console.log('Route update:', route);
        setRouteInfo(route);
        if (isMapReady) {
          updateRouteOnMap(route);
        }
      },
      onError: (error: Error) => {
        console.error('Tracking error:', error);
      }
    });

    if (!success) {
      console.error('Failed to start tracking');
    }
  };

  const updateDriverLocationOnMap = (location: LocationUpdate) => {
    const script = `
      if (window.updateDriverLocation) {
        window.updateDriverLocation(${location.lat}, ${location.lng}, ${location.heading || 0});
      }
    `;
    webViewRef.current?.postMessage(script);
  };

  const updatePassengerLocationOnMap = (location: LocationUpdate) => {
    const script = `
      if (window.updatePassengerLocation) {
        window.updatePassengerLocation(${location.lat}, ${location.lng});
      }
    `;
    webViewRef.current?.postMessage(script);
  };

  const updateRouteOnMap = (route: RouteInfo) => {
    const script = `
      if (window.updateRoute) {
        window.updateRoute(${JSON.stringify(route)});
      }
    `;
    webViewRef.current?.postMessage(script);
  };

  const centerOnDriver = () => {
    if (currentLocation) {
      const script = `
        if (window.centerOnLocation) {
          window.centerOnLocation(${currentLocation.lat}, ${currentLocation.lng});
        }
      `;
      webViewRef.current?.postMessage(script);
    }
  };

  const showFullRoute = () => {
    const script = `
      if (window.showFullRoute) {
        window.showFullRoute();
      }
    `;
    webViewRef.current?.postMessage(script);
  };

  const getDestination = () => {
    return trip.status === 'in_progress' 
      ? trip.destination_location 
      : trip.pickup_location;
  };

  const getDestinationLabel = () => {
    return trip.status === 'in_progress' ? 'Destination' : 'Pickup';
  };

  const mapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Live Tracking Map</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            body { margin: 0; padding: 0; }
            #map { height: 100vh; width: 100vw; }
            .driver-marker { 
                background: ${Colors.primary}; 
                border: 3px solid white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
            }
            .passenger-marker { 
                background: ${Colors.info}; 
                border: 3px solid white;
                border-radius: 50%;
                width: 16px;
                height: 16px;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script>
            let map, driverMarker, passengerMarker, routeLine;
            
            // Initialize map
            map = L.map('map').setView([${currentLocation?.lat || trip.pickup_location.lat}, ${currentLocation?.lng || trip.pickup_location.lng}], 15);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);
            
            // Add pickup and destination markers
            const pickupIcon = L.divIcon({
                className: 'pickup-marker',
                html: '<div style="background: ${Colors.online}; border: 3px solid white; border-radius: 50%; width: 16px; height: 16px;"></div>',
                iconSize: [16, 16],
                iconAnchor: [8, 8]
            });
            
            const destinationIcon = L.divIcon({
                className: 'destination-marker',
                html: '<div style="background: ${Colors.error}; border: 3px solid white; border-radius: 50%; width: 16px; height: 16px;"></div>',
                iconSize: [16, 16],
                iconAnchor: [8, 8]
            });
            
            L.marker([${trip.pickup_location.lat}, ${trip.pickup_location.lng}], {icon: pickupIcon})
                .addTo(map)
                .bindPopup('Pickup Location');
                
            L.marker([${trip.destination_location.lat}, ${trip.destination_location.lng}], {icon: destinationIcon})
                .addTo(map)
                .bindPopup('Destination');
            
            // Driver marker
            const driverIcon = L.divIcon({
                className: 'driver-marker',
                html: '<div style="background: ${Colors.primary}; border: 3px solid white; border-radius: 50%; width: 20px; height: 20px;"></div>',
                iconSize: [20, 20],
                iconAnchor: [10, 10]
            });
            
            if (${currentLocation ? 'true' : 'false'}) {
                driverMarker = L.marker([${currentLocation?.lat || 0}, ${currentLocation?.lng || 0}], {icon: driverIcon})
                    .addTo(map)
                    .bindPopup('Your Location');
            }
            
            // Functions for React Native to call
            window.updateDriverLocation = function(lat, lng, heading) {
                if (driverMarker) {
                    driverMarker.setLatLng([lat, lng]);
                } else {
                    driverMarker = L.marker([lat, lng], {icon: driverIcon})
                        .addTo(map)
                        .bindPopup('Your Location');
                }
            };
            
            window.updatePassengerLocation = function(lat, lng) {
                const passengerIcon = L.divIcon({
                    className: 'passenger-marker',
                    html: '<div style="background: ${Colors.info}; border: 3px solid white; border-radius: 50%; width: 16px; height: 16px;"></div>',
                    iconSize: [16, 16],
                    iconAnchor: [8, 8]
                });
                
                if (passengerMarker) {
                    passengerMarker.setLatLng([lat, lng]);
                } else {
                    passengerMarker = L.marker([lat, lng], {icon: passengerIcon})
                        .addTo(map)
                        .bindPopup('Passenger Location');
                }
            };
            
            window.updateRoute = function(route) {
                if (routeLine) {
                    map.removeLayer(routeLine);
                }
                // In a real implementation, you would draw the actual route polyline
                // For now, just draw a straight line
                if (driverMarker && route.steps && route.steps.length > 0) {
                    const step = route.steps[0];
                    routeLine = L.polyline([
                        [step.start_location.lat, step.start_location.lng],
                        [step.end_location.lat, step.end_location.lng]
                    ], {color: '${Colors.primary}', weight: 4, opacity: 0.7}).addTo(map);
                }
            };
            
            window.centerOnLocation = function(lat, lng) {
                map.setView([lat, lng], 16);
            };
            
            window.showFullRoute = function() {
                const group = new L.featureGroup();
                if (driverMarker) group.addLayer(driverMarker);
                if (passengerMarker) group.addLayer(passengerMarker);
                
                // Add pickup and destination to bounds
                group.addLayer(L.marker([${trip.pickup_location.lat}, ${trip.pickup_location.lng}]));
                group.addLayer(L.marker([${trip.destination_location.lat}, ${trip.destination_location.lng}]));
                
                map.fitBounds(group.getBounds().pad(0.1));
            };
            
            // Notify React Native that map is ready
            window.ReactNativeWebView.postMessage('mapReady');
        </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    const message = event.nativeEvent.data;
    if (message === 'mapReady') {
      setIsMapReady(true);
    }
  };

  return (
    <View style={styles.container}>
      <WebView
        ref={webViewRef}
        source={{ html: mapHTML }}
        style={styles.map}
        onMessage={handleWebViewMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
      />
      
      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton} onPress={centerOnDriver}>
          <Ionicons name="locate" size={20} color={Colors.primary} />
        </TouchableOpacity>
        <TouchableOpacity style={styles.controlButton} onPress={showFullRoute}>
          <Ionicons name="resize" size={20} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      
      {/* ETA Display */}
      {eta && (
        <View style={styles.etaContainer}>
          <Text style={styles.etaLabel}>{getDestinationLabel()} ETA</Text>
          <Text style={styles.etaValue}>{eta} min</Text>
        </View>
      )}
      
      {/* Route Info */}
      {routeInfo && (
        <View style={styles.routeInfo}>
          <Text style={styles.routeDistance}>{routeInfo.distance_km.toFixed(1)} km</Text>
          <Text style={styles.routeDuration}>{routeInfo.duration_minutes} min</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'column',
  },
  controlButton: {
    width: 44,
    height: 44,
    backgroundColor: Colors.white,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  etaContainer: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  etaLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  etaValue: {
    fontSize: 16,
    color: Colors.text.primary,
    fontWeight: 'bold',
  },
  routeInfo: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    right: 16,
    backgroundColor: Colors.white,
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  routeDistance: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  routeDuration: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
});
