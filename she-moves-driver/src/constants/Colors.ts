/**
 * Color Constants for SheMove Driver App
 * Maintains the feminine pink branding while adapting for driver workflows
 */

export const Colors = {
  // Primary SheMove branding colors
  primary: '#E91E63',
  primaryLight: '#FFF0FF',
  primaryMedium: '#F9E6F7',
  primaryDark: '#C2185B',

  // Driver-specific colors
  online: '#4CAF50',
  offline: '#9E9E9E',
  busy: '#FF9800',
  break: '#2196F3',

  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',

  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },

  // Text colors
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD',
    inverse: '#FFFFFF',
  },

  // Background colors
  background: {
    primary: '#FFF0FF',
    secondary: '#FFFFFF',
    card: '#FFFFFF',
    disabled: '#F5F5F5',
  },

  // Border colors
  border: {
    light: '#E0E0E0',
    medium: '#BDBDBD',
    dark: '#757575',
  },

  // Trip status colors
  trip: {
    requested: '#2196F3',
    accepted: '#FF9800',
    inProgress: '#4CAF50',
    completed: '#9E9E9E',
    cancelled: '#F44336',
  },

  // Earnings colors
  earnings: {
    positive: '#4CAF50',
    neutral: '#9E9E9E',
    pending: '#FF9800',
  },
};

export default Colors;
