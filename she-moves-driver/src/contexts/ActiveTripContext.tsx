/**
 * Active Trip Context for SheMove Driver App
 * Provides global state management for active trip lifecycle
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import { ActiveTripService, ActiveTrip, TripStatus, TripCompletionData } from '../services/ActiveTripService';
import { LocationUpdate } from '../services/DriverStatusService';
import { useAuth } from './AuthContext';

interface ActiveTripContextType {
  // Trip state
  currentTrip: ActiveTrip | null;
  isLoading: boolean;
  
  // Actions
  startTrip: (tripId: string, location: LocationUpdate) => Promise<boolean>;
  arriveAtPickup: (location: LocationUpdate) => Promise<boolean>;
  pickupPassenger: (location: LocationUpdate) => Promise<boolean>;
  completeTrip: (completionData: TripCompletionData) => Promise<boolean>;
  cancelTrip: (reason: string, location: LocationUpdate) => Promise<boolean>;
  
  // Service instance (for advanced usage)
  tripService: ActiveTripService | null;
}

const ActiveTripContext = createContext<ActiveTripContextType | undefined>(undefined);

interface ActiveTripProviderProps {
  children: ReactNode;
  supabase: any; // SupabaseClient type
}

export function ActiveTripProvider({ children, supabase }: ActiveTripProviderProps) {
  const { user } = useAuth();
  
  // State
  const [currentTrip, setCurrentTrip] = useState<ActiveTrip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tripService, setTripService] = useState<ActiveTripService | null>(null);

  // Initialize service when user is available
  useEffect(() => {
    if (user && supabase) {
      initializeService();
    } else {
      cleanup();
    }
  }, [user, supabase]);

  const initializeService = async () => {
    try {
      setIsLoading(true);
      
      const service = new ActiveTripService(supabase);
      const initialized = await service.initialize(user!.id);
      
      if (!initialized) {
        console.error('Failed to initialize ActiveTripService');
        Alert.alert('Error', 'Failed to initialize trip service');
        return;
      }

      // Subscribe to trip updates
      const unsubscribe = service.onTripUpdate((trip) => {
        console.log('ActiveTripContext: Trip updated:', trip.status);
        setCurrentTrip(trip);
      });

      // Subscribe to trip completion
      const unsubscribeCompleted = service.onTripCompleted((trip) => {
        console.log('ActiveTripContext: Trip completed:', trip.id);
        setCurrentTrip(null);
      });

      setTripService(service);
      setCurrentTrip(service.getCurrentTrip());
      
      // Store unsubscribe functions for cleanup
      (service as any)._unsubscribe = unsubscribe;
      (service as any)._unsubscribeCompleted = unsubscribeCompleted;
      
    } catch (error) {
      console.error('Error initializing ActiveTripService:', error);
      Alert.alert('Error', 'Failed to initialize trip service');
    } finally {
      setIsLoading(false);
    }
  };

  const startTrip = async (tripId: string, location: LocationUpdate): Promise<boolean> => {
    if (!tripService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await tripService.startTrip(tripId, location);
      
      if (result.success) {
        // Trip update will be handled by callback
        return true;
      } else {
        Alert.alert('Cannot Start Trip', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error starting trip:', error);
      Alert.alert('Error', 'Failed to start trip');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const arriveAtPickup = async (location: LocationUpdate): Promise<boolean> => {
    if (!tripService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await tripService.arriveAtPickup(location);
      
      if (result.success) {
        return true;
      } else {
        Alert.alert('Cannot Mark Arrival', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error marking arrival:', error);
      Alert.alert('Error', 'Failed to mark arrival');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const pickupPassenger = async (location: LocationUpdate): Promise<boolean> => {
    if (!tripService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await tripService.pickupPassenger(location);
      
      if (result.success) {
        return true;
      } else {
        Alert.alert('Cannot Pickup Passenger', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error picking up passenger:', error);
      Alert.alert('Error', 'Failed to pickup passenger');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const completeTrip = async (completionData: TripCompletionData): Promise<boolean> => {
    if (!tripService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await tripService.completeTrip(completionData);
      
      if (result.success) {
        return true;
      } else {
        Alert.alert('Cannot Complete Trip', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error completing trip:', error);
      Alert.alert('Error', 'Failed to complete trip');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const cancelTrip = async (reason: string, location: LocationUpdate): Promise<boolean> => {
    if (!tripService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await tripService.cancelTrip(reason, location);
      
      if (result.success) {
        setCurrentTrip(null);
        return true;
      } else {
        Alert.alert('Cannot Cancel Trip', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error cancelling trip:', error);
      Alert.alert('Error', 'Failed to cancel trip');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const cleanup = () => {
    if (tripService) {
      // Call unsubscribe functions if they exist
      const unsubscribe = (tripService as any)._unsubscribe;
      const unsubscribeCompleted = (tripService as any)._unsubscribeCompleted;
      
      if (unsubscribe) {
        unsubscribe();
      }
      if (unsubscribeCompleted) {
        unsubscribeCompleted();
      }
      
      tripService.cleanup();
      setTripService(null);
    }
    
    setCurrentTrip(null);
    setIsLoading(false);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  const contextValue: ActiveTripContextType = {
    // Trip state
    currentTrip,
    isLoading,
    
    // Actions
    startTrip,
    arriveAtPickup,
    pickupPassenger,
    completeTrip,
    cancelTrip,
    
    // Service instance
    tripService,
  };

  return (
    <ActiveTripContext.Provider value={contextValue}>
      {children}
    </ActiveTripContext.Provider>
  );
}

export function useActiveTrip(): ActiveTripContextType {
  const context = useContext(ActiveTripContext);
  if (context === undefined) {
    throw new Error('useActiveTrip must be used within an ActiveTripProvider');
  }
  return context;
}
