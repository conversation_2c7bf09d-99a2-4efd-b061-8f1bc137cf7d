/**
 * Authentication Context for SheMove Driver App
 * Provides authentication state and methods using shared auth service
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import { SharedAuthService, AuthUser } from '../../shared/auth/SharedAuthService';

// Environment variables
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
const SUPABASE_ANON_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '';

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (accessToken: string, newPassword: string) => Promise<void>;
  isDriver: boolean;
  isVerified: boolean;
  authService: SharedAuthService;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Initialize shared auth service for driver app
  const authService = new SharedAuthService({
    supabaseUrl: SUPABASE_URL,
    supabaseAnonKey: SUPABASE_ANON_KEY,
    appType: 'driver',
  });

  // Function to refresh user profile data
  const refreshUserProfile = async () => {
    try {
      if (!user?.id) return;

      const profile = await authService.getUserProfile(user.id);
      let driverProfile = undefined;

      if (profile?.user_type === 'driver') {
        driverProfile = await authService.getDriverProfile(user.id);
      }

      const userData: AuthUser = {
        id: user.id,
        email: user.email,
        createdAt: user.createdAt,
        profile: profile || undefined,
        driverProfile: driverProfile || undefined,
      };

      setUser(userData);
    } catch (error) {
      console.error('Error refreshing user profile:', error);
    }
  };

  // Check if user is a verified driver
  const isDriver = user?.profile?.user_type === 'driver';
  const isVerified = user?.driverProfile?.verification_status === 'approved';

  // Debug logging
  if (user) {
    console.log('AuthContext - User data:', {
      id: user.id,
      email: user.email,
      userType: user.profile?.user_type,
      hasDriverProfile: !!user.driverProfile,
      verificationStatus: user.driverProfile?.verification_status,
      isDriver,
      isVerified
    });

    // Extra debugging for profile data
    console.log('AuthContext - Full profile data:', user.profile);
    console.log('AuthContext - Profile user_type raw:', user.profile?.user_type);
  }

  useEffect(() => {
    let mounted = true;

    // Check if auth service is properly configured
    if (!authService.isConfigured()) {
      console.warn('Auth service not properly configured. Please check environment variables.');
      setLoading(false);
      return;
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { user: sessionUser } = await authService.getSession();
        if (mounted && sessionUser) {
          // Get full user profile including driver profile
          const profile = await authService.getUserProfile(sessionUser.id);
          let driverProfile = undefined;

          if (profile?.user_type === 'driver') {
            driverProfile = await authService.getDriverProfile(sessionUser.id);
          }

          const userData: AuthUser = {
            id: sessionUser.id,
            email: sessionUser.email || '',
            createdAt: sessionUser.created_at,
            profile: profile || undefined,
            driverProfile: driverProfile || undefined,
          };

          setUser(userData);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth state changes
    const { unsubscribe } = authService.onAuthStateChange((userData) => {
      if (mounted) {
        setUser(userData);
        setLoading(false);
      }
    });

    return () => {
      mounted = false;
      unsubscribe();
    };
  }, []);

  // Set up real-time subscription for driver verification status changes
  useEffect(() => {
    let driverSubscription: any = null;

    const setupDriverSubscription = async () => {
      try {
        const supabase = authService.getSupabaseClient();
        if (!supabase || !user?.driverProfile?.id) return;

        console.log('Setting up driver verification subscription for driver:', user.driverProfile.id);

        // Subscribe to changes in the drivers table for this specific driver
        driverSubscription = supabase
          .channel(`driver-verification-${user.driverProfile.id}`)
          .on(
            'postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'drivers',
              filter: `id=eq.${user.driverProfile.id}`,
            },
            async (payload) => {
              console.log('Driver verification status changed:', payload);

              // Check if verification_status changed to 'approved'
              if (payload.new?.verification_status === 'approved' &&
                  payload.old?.verification_status !== 'approved') {

                console.log('Driver approved! Showing notification...');

                // Show approval notification
                Alert.alert(
                  '🎉 Congratulations!',
                  'Your driver application has been approved! You can now start driving with SheMove.',
                  [
                    {
                      text: 'Get Started',
                      onPress: async () => {
                        // Refresh user profile to trigger navigation update
                        await refreshUserProfile();
                      },
                    },
                  ]
                );
              } else {
                // For other status changes, just refresh the profile
                console.log('Driver status updated, refreshing profile...');
                await refreshUserProfile();
              }
            }
          )
          .subscribe();

        console.log('Driver subscription set up successfully');
      } catch (error) {
        console.error('Error setting up driver subscription:', error);
      }
    };

    // Set up subscription when user is loaded and is a driver
    if (user?.driverProfile?.id) {
      setupDriverSubscription();
    }

    return () => {
      // Clean up driver subscription
      if (driverSubscription) {
        console.log('Cleaning up driver subscription');
        driverSubscription.unsubscribe();
      }
    };
  }, [user?.driverProfile?.id]); // Re-run when driver profile changes

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const userData = await authService.signIn(email, password);

      // Note: We'll let the navigation logic handle driver validation
      // This allows new drivers who haven't completed their profile yet
      setUser(userData);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      setLoading(true);
      // Sign up as driver type
      const userData = await authService.signUp(email, password, fullName, 'driver');

      // Note: Driver profile will be created during onboarding process
      // For now, we just set the user data
      setUser(userData);
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await authService.signOut();
      setUser(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      await authService.forgotPassword(email);
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    }
  };

  const resetPassword = async (accessToken: string, newPassword: string) => {
    try {
      await authService.resetPassword(accessToken, newPassword);
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    forgotPassword,
    resetPassword,
    isDriver,
    isVerified,
    authService,
    refreshUserProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
