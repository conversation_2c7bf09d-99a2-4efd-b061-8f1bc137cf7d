/**
 * Driver Status Context for SheMove Driver App
 * Provides global state management for driver online/offline status and session data
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert } from 'react-native';
import { DriverStatusService, DriverSession, DriverStats, LocationUpdate, StatusChangeResult } from '../services/DriverStatusService';
import { DriverStatus } from '../../shared/types';
import { useAuth } from './AuthContext';

interface DriverStatusContextType {
  // Status state
  status: DriverStatus;
  isOnline: boolean;
  isLoading: boolean;
  
  // Session data
  currentSession: DriverSession | null;
  stats: DriverStats | null;
  
  // Actions
  goOnline: (location: LocationUpdate) => Promise<boolean>;
  goOffline: () => Promise<boolean>;
  setBusy: () => Promise<boolean>;
  setBreak: () => Promise<boolean>;
  updateLocation: (location: LocationUpdate) => Promise<boolean>;
  refreshStats: () => Promise<void>;
  
  // Service instance (for advanced usage)
  statusService: DriverStatusService | null;
}

const DriverStatusContext = createContext<DriverStatusContextType | undefined>(undefined);

interface DriverStatusProviderProps {
  children: ReactNode;
  supabase: any; // SupabaseClient type
}

export function DriverStatusProvider({ children, supabase }: DriverStatusProviderProps) {
  const { user } = useAuth();
  
  // State
  const [status, setStatus] = useState<DriverStatus>('offline');
  const [isLoading, setIsLoading] = useState(true);
  const [currentSession, setCurrentSession] = useState<DriverSession | null>(null);
  const [stats, setStats] = useState<DriverStats | null>(null);
  const [statusService, setStatusService] = useState<DriverStatusService | null>(null);

  // Computed values
  const isOnline = status === 'online';

  // Initialize service when user is available
  useEffect(() => {
    if (user && supabase) {
      initializeService();
    } else {
      cleanup();
    }
  }, [user, supabase]);

  // Load initial status and stats
  useEffect(() => {
    if (statusService) {
      loadInitialData();
    }
  }, [statusService]);

  const initializeService = async () => {
    try {
      setIsLoading(true);
      
      const service = new DriverStatusService(supabase);
      const initialized = await service.initialize(user!.id);
      
      if (!initialized) {
        // Don't show alert during onboarding - driver record doesn't exist yet
        console.log('DriverStatusService not initialized - driver record may not exist yet');
        return;
      }

      // Subscribe to status changes
      const unsubscribe = service.onStatusChange((newStatus) => {
        console.log('DriverStatusContext: Status changed to:', newStatus);
        setStatus(newStatus);
        setCurrentSession(service.getCurrentSession());
      });

      setStatusService(service);

      // Store unsubscribe function for cleanup
      (service as any)._unsubscribe = unsubscribe;

      // Load initial data after service is set up
      await loadInitialData();

    } catch (error) {
      console.error('Error initializing DriverStatusService:', error);
      // Don't show alert during onboarding - this is expected
    } finally {
      setIsLoading(false);
    }
  };

  const loadInitialData = async () => {
    if (!statusService) return;

    try {
      // Load current status
      const currentStatus = await statusService.getCurrentStatus();
      if (currentStatus) {
        setStatus(currentStatus);
      }

      // Load current session
      const session = statusService.getCurrentSession();
      setCurrentSession(session);

      // Load stats
      await refreshStats();
      
    } catch (error) {
      console.error('Error loading initial data:', error);
    }
  };

  const goOnline = async (location: LocationUpdate): Promise<boolean> => {
    if (!statusService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await statusService.goOnline(location);

      if (result.success) {
        // Don't manually set status here - let the callback handle it
        // The notifyStatusChange in the service will trigger the callback
        setCurrentSession(result.session || null);
        await refreshStats();
        return true;
      } else {
        Alert.alert('Cannot Go Online', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error going online:', error);
      Alert.alert('Error', 'Failed to go online');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const goOffline = async (): Promise<boolean> => {
    if (!statusService) {
      Alert.alert('Error', 'Service not initialized');
      return false;
    }

    try {
      setIsLoading(true);
      const result = await statusService.goOffline();

      if (result.success) {
        // Don't manually set status here - let the callback handle it
        // The notifyStatusChange in the service will trigger the callback
        setCurrentSession(result.session || null);
        await refreshStats();
        return true;
      } else {
        Alert.alert('Cannot Go Offline', result.error || 'Unknown error occurred');
        return false;
      }
    } catch (error) {
      console.error('Error going offline:', error);
      Alert.alert('Error', 'Failed to go offline');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const setBusy = async (): Promise<boolean> => {
    if (!statusService) return false;

    try {
      const result = await statusService.setBusy();
      if (result.success) {
        // Don't manually set status here - let the callback handle it
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error setting busy status:', error);
      return false;
    }
  };

  const setBreak = async (): Promise<boolean> => {
    if (!statusService) return false;

    try {
      const result = await statusService.setBreak();
      if (result.success) {
        // Don't manually set status here - let the callback handle it
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error setting break status:', error);
      return false;
    }
  };

  const updateLocation = async (location: LocationUpdate): Promise<boolean> => {
    if (!statusService) return false;

    try {
      return await statusService.updateLocation(location);
    } catch (error) {
      console.error('Error updating location:', error);
      return false;
    }
  };

  const refreshStats = async (): Promise<void> => {
    if (!statusService) return;

    try {
      const newStats = await statusService.getDriverStats();
      setStats(newStats);
    } catch (error) {
      console.error('Error refreshing stats:', error);
    }
  };

  const cleanup = () => {
    if (statusService) {
      // Call unsubscribe if it exists
      const unsubscribe = (statusService as any)._unsubscribe;
      if (unsubscribe) {
        unsubscribe();
      }
      
      statusService.cleanup();
      setStatusService(null);
    }
    
    setStatus('offline');
    setCurrentSession(null);
    setStats(null);
    setIsLoading(false);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  const contextValue: DriverStatusContextType = {
    // Status state
    status,
    isOnline,
    isLoading,
    
    // Session data
    currentSession,
    stats,
    
    // Actions
    goOnline,
    goOffline,
    setBusy,
    setBreak,
    updateLocation,
    refreshStats,
    
    // Service instance
    statusService,
  };

  return (
    <DriverStatusContext.Provider value={contextValue}>
      {children}
    </DriverStatusContext.Provider>
  );
}

export function useDriverStatus(): DriverStatusContextType {
  const context = useContext(DriverStatusContext);
  if (context === undefined) {
    throw new Error('useDriverStatus must be used within a DriverStatusProvider');
  }
  return context;
}
