/**
 * Location Context for SheMove Driver App
 * Provides global location state management and integration with DriverStatusService
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Alert, AppState, AppStateStatus } from 'react-native';
import { LocationService, LocationPermissionStatus, LocationServiceCallbacks } from '../services/LocationService';
import { LocationUpdate } from '../services/DriverStatusService';
import { useDriverStatus } from './DriverStatusContext';

interface LocationContextType {
  // Location state
  currentLocation: LocationUpdate | null;
  lastKnownLocation: LocationUpdate | null;
  locationHistory: LocationUpdate[];
  isTracking: boolean;
  isLoading: boolean;
  
  // Permission state
  permissionStatus: LocationPermissionStatus | null;
  
  // Actions
  requestPermissions: () => Promise<LocationPermissionStatus>;
  startTracking: () => Promise<boolean>;
  stopTracking: () => Promise<void>;
  startBackgroundTracking: () => Promise<boolean>;
  stopBackgroundTracking: () => Promise<void>;
  getCurrentLocation: () => Promise<LocationUpdate | null>;
  clearLocationHistory: () => void;
  
  // Service instance (for advanced usage)
  locationService: LocationService;
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

interface LocationProviderProps {
  children: ReactNode;
}

export function LocationProvider({ children }: LocationProviderProps) {
  const { updateLocation, isOnline, status } = useDriverStatus();
  
  // State
  const [currentLocation, setCurrentLocation] = useState<LocationUpdate | null>(null);
  const [lastKnownLocation, setLastKnownLocation] = useState<LocationUpdate | null>(null);
  const [locationHistory, setLocationHistory] = useState<LocationUpdate[]>([]);
  const [isTracking, setIsTracking] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<LocationPermissionStatus | null>(null);
  
  // Location service instance
  const [locationService] = useState(() => LocationService.getInstance({
    timeInterval: 30000, // 30 seconds
    distanceInterval: 10, // 10 meters
    accuracy: 4, // High accuracy
  }));

  // Initialize location service
  useEffect(() => {
    initializeLocationService();
    
    // Handle app state changes
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && isOnline) {
        // Resume location tracking when app becomes active
        startLocationTracking();
      } else if (nextAppState === 'background' && isOnline) {
        // Start background tracking when app goes to background
        startBackgroundLocationTracking();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
      locationService.cleanup();
    };
  }, []);

  // Start/stop tracking based on driver online status
  useEffect(() => {
    if (isOnline && status === 'online') {
      startLocationTracking();
    } else {
      stopLocationTracking();
    }
  }, [isOnline, status]);

  const initializeLocationService = async () => {
    try {
      setIsLoading(true);
      
      // Set up callbacks
      const callbacks: LocationServiceCallbacks = {
        onLocationUpdate: handleLocationUpdate,
        onLocationError: handleLocationError,
        onPermissionDenied: handlePermissionDenied,
      };
      
      locationService.setCallbacks(callbacks);
      
      // Initialize and request permissions
      const permissions = await locationService.initialize();
      setPermissionStatus(permissions);
      
      // Load last known location
      const lastLocation = locationService.getLastKnownLocation();
      if (lastLocation) {
        setLastKnownLocation(lastLocation);
        setCurrentLocation(lastLocation);
      }
      
      // Load location history
      const history = locationService.getLocationHistory();
      setLocationHistory(history);
      
    } catch (error) {
      console.error('Failed to initialize location service:', error);
      Alert.alert('Location Error', 'Failed to initialize location services');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationUpdate = async (location: LocationUpdate) => {
    try {
      // Update local state
      setCurrentLocation(location);
      setLastKnownLocation(location);
      setLocationHistory(locationService.getLocationHistory());
      
      // Update driver status service with new location
      if (isOnline) {
        await updateLocation(location);
      }
      
    } catch (error) {
      console.error('Error handling location update:', error);
    }
  };

  const handleLocationError = (error: Error) => {
    console.error('Location error:', error);
    
    // Show user-friendly error message
    if (error.message.includes('permission')) {
      Alert.alert(
        'Location Permission Required',
        'Please enable location permissions to use SheMove Driver',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Settings', onPress: requestPermissions }
        ]
      );
    } else {
      Alert.alert('Location Error', 'Unable to get your location. Please check your GPS settings.');
    }
  };

  const handlePermissionDenied = (status: LocationPermissionStatus) => {
    setPermissionStatus(status);
    
    if (!status.foreground) {
      Alert.alert(
        'Location Permission Required',
        'SheMove Driver needs location access to function properly. Please enable location permissions.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Request Again', onPress: requestPermissions }
        ]
      );
    } else if (!status.background) {
      Alert.alert(
        'Background Location Recommended',
        'For the best experience, please enable "Always" location access to track your position during trips.',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { text: 'Enable', onPress: requestPermissions }
        ]
      );
    }
  };

  const requestPermissions = async (): Promise<LocationPermissionStatus> => {
    try {
      setIsLoading(true);
      const status = await locationService.requestPermissions();
      setPermissionStatus(status);
      return status;
    } catch (error) {
      console.error('Error requesting permissions:', error);
      const errorStatus: LocationPermissionStatus = {
        foreground: false,
        background: false,
        canAskAgain: false,
        message: 'Failed to request permissions'
      };
      setPermissionStatus(errorStatus);
      return errorStatus;
    } finally {
      setIsLoading(false);
    }
  };

  const startLocationTracking = async (): Promise<boolean> => {
    try {
      const success = await locationService.startTracking();
      setIsTracking(success);
      return success;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  };

  const stopLocationTracking = async (): Promise<void> => {
    try {
      await locationService.stopTracking();
      setIsTracking(false);
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  };

  const startBackgroundLocationTracking = async (): Promise<boolean> => {
    try {
      return await locationService.startBackgroundTracking();
    } catch (error) {
      console.error('Error starting background location tracking:', error);
      return false;
    }
  };

  const stopBackgroundLocationTracking = async (): Promise<void> => {
    try {
      await locationService.stopBackgroundTracking();
    } catch (error) {
      console.error('Error stopping background location tracking:', error);
    }
  };

  const getCurrentLocationOnce = async (): Promise<LocationUpdate | null> => {
    try {
      setIsLoading(true);
      const location = await locationService.getCurrentLocation();
      if (location) {
        setCurrentLocation(location);
        setLastKnownLocation(location);
      }
      return location;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const clearHistory = (): void => {
    locationService.clearLocationHistory();
    setLocationHistory([]);
  };

  const contextValue: LocationContextType = {
    // Location state
    currentLocation,
    lastKnownLocation,
    locationHistory,
    isTracking: isTracking && locationService.isCurrentlyTracking(),
    isLoading,
    
    // Permission state
    permissionStatus,
    
    // Actions
    requestPermissions,
    startTracking: startLocationTracking,
    stopTracking: stopLocationTracking,
    startBackgroundTracking: startBackgroundLocationTracking,
    stopBackgroundTracking: stopBackgroundLocationTracking,
    getCurrentLocation: getCurrentLocationOnce,
    clearLocationHistory: clearHistory,
    
    // Service instance
    locationService,
  };

  return (
    <LocationContext.Provider value={contextValue}>
      {children}
    </LocationContext.Provider>
  );
}

export function useLocation(): LocationContextType {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
}
