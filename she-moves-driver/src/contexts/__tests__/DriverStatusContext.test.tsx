/**
 * Tests for DriverStatusContext
 * Testing the React context and provider functionality
 */

import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { DriverStatusProvider, useDriverStatus } from '../DriverStatusContext';
import { DriverStatusService } from '../../services/DriverStatusService';

// Mock Alert
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
}));

// Mock DriverStatusService
jest.mock('../../services/DriverStatusService');

// Mock AuthContext
const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  createdAt: '2023-01-01T00:00:00Z'
};

jest.mock('../AuthContext', () => ({
  useAuth: () => ({
    user: mockUser,
  }),
}));

// Mock Supabase
const mockSupabase = {
  from: jest.fn(),
  rpc: jest.fn(),
};

// Test component that uses the context
const TestComponent = () => {
  const {
    status,
    isOnline,
    isLoading,
    currentSession,
    stats,
    goOnline,
    goOffline,
    setBusy,
    setBreak,
    updateLocation,
    refreshStats,
    statusService
  } = useDriverStatus();

  return (
    <>
      <div testID="status">{status}</div>
      <div testID="isOnline">{isOnline.toString()}</div>
      <div testID="isLoading">{isLoading.toString()}</div>
      <div testID="hasSession">{(currentSession !== null).toString()}</div>
      <div testID="hasStats">{(stats !== null).toString()}</div>
      <div testID="hasService">{(statusService !== null).toString()}</div>
    </>
  );
};

describe('DriverStatusContext', () => {
  let mockServiceInstance: jest.Mocked<DriverStatusService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock service instance
    mockServiceInstance = {
      initialize: jest.fn(),
      goOnline: jest.fn(),
      goOffline: jest.fn(),
      setBusy: jest.fn(),
      setBreak: jest.fn(),
      updateLocation: jest.fn(),
      getCurrentSession: jest.fn(),
      getCurrentStatus: jest.fn(),
      getDriverStats: jest.fn(),
      onStatusChange: jest.fn(),
      cleanup: jest.fn(),
    } as any;

    // Mock the constructor
    (DriverStatusService as jest.Mock).mockImplementation(() => mockServiceInstance);
  });

  const renderWithProvider = () => {
    return render(
      <DriverStatusProvider supabase={mockSupabase}>
        <TestComponent />
      </DriverStatusProvider>
    );
  };

  describe('initialization', () => {
    it('should initialize service when user is available', async () => {
      mockServiceInstance.initialize.mockResolvedValue(true);
      mockServiceInstance.getCurrentStatus.mockResolvedValue('offline');
      mockServiceInstance.getCurrentSession.mockReturnValue(null);
      mockServiceInstance.getDriverStats.mockResolvedValue(null);
      mockServiceInstance.onStatusChange.mockReturnValue(() => {});

      const { getByTestId } = renderWithProvider();

      await waitFor(() => {
        expect(mockServiceInstance.initialize).toHaveBeenCalledWith(mockUser.id);
      });

      expect(getByTestId('hasService').children[0]).toBe('true');
    });

    it('should show error when initialization fails', async () => {
      mockServiceInstance.initialize.mockResolvedValue(false);

      renderWithProvider();

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to initialize driver status service');
      });
    });

    it('should handle initialization error', async () => {
      mockServiceInstance.initialize.mockRejectedValue(new Error('Init failed'));

      renderWithProvider();

      await waitFor(() => {
        expect(Alert.alert).toHaveBeenCalledWith('Error', 'Failed to initialize driver status service');
      });
    });
  });

  describe('status management', () => {
    beforeEach(async () => {
      mockServiceInstance.initialize.mockResolvedValue(true);
      mockServiceInstance.getCurrentStatus.mockResolvedValue('offline');
      mockServiceInstance.getCurrentSession.mockReturnValue(null);
      mockServiceInstance.getDriverStats.mockResolvedValue(null);
      mockServiceInstance.onStatusChange.mockReturnValue(() => {});
    });

    it('should update status when going online', async () => {
      const mockSession = {
        id: 'session-id',
        driver_id: 'driver-id',
        status: 'online' as const,
        session_start: new Date().toISOString(),
        total_online_minutes: 0,
        location_updates_count: 0,
        trips_completed: 0,
        earnings_session: 0
      };

      mockServiceInstance.goOnline.mockResolvedValue({
        success: true,
        session: mockSession
      });

      const { getByTestId } = renderWithProvider();

      // Wait for initialization
      await waitFor(() => {
        expect(getByTestId('hasService').children[0]).toBe('true');
      });

      // Test going online would be done through component interaction
      // For now, we test that the service method is available
      expect(mockServiceInstance.goOnline).toBeDefined();
    });

    it('should handle go online failure', async () => {
      mockServiceInstance.goOnline.mockResolvedValue({
        success: false,
        error: 'Driver not approved'
      });

      renderWithProvider();

      await waitFor(() => {
        expect(getByTestId('hasService').children[0]).toBe('true');
      });

      // The context would handle the error and show alert
      // This would be tested in integration tests
    });
  });

  describe('status change callbacks', () => {
    it('should register status change callback', async () => {
      const mockUnsubscribe = jest.fn();
      mockServiceInstance.initialize.mockResolvedValue(true);
      mockServiceInstance.getCurrentStatus.mockResolvedValue('offline');
      mockServiceInstance.getCurrentSession.mockReturnValue(null);
      mockServiceInstance.getDriverStats.mockResolvedValue(null);
      mockServiceInstance.onStatusChange.mockReturnValue(mockUnsubscribe);

      renderWithProvider();

      await waitFor(() => {
        expect(mockServiceInstance.onStatusChange).toHaveBeenCalled();
      });

      // Verify callback was registered
      const callback = mockServiceInstance.onStatusChange.mock.calls[0][0];
      expect(typeof callback).toBe('function');
    });
  });

  describe('cleanup', () => {
    it('should cleanup service on unmount', async () => {
      const mockUnsubscribe = jest.fn();
      mockServiceInstance.initialize.mockResolvedValue(true);
      mockServiceInstance.getCurrentStatus.mockResolvedValue('offline');
      mockServiceInstance.getCurrentSession.mockReturnValue(null);
      mockServiceInstance.getDriverStats.mockResolvedValue(null);
      mockServiceInstance.onStatusChange.mockReturnValue(mockUnsubscribe);

      const { unmount } = renderWithProvider();

      await waitFor(() => {
        expect(mockServiceInstance.initialize).toHaveBeenCalled();
      });

      unmount();

      expect(mockServiceInstance.cleanup).toHaveBeenCalled();
      expect(mockUnsubscribe).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should throw error when used outside provider', () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useDriverStatus must be used within a DriverStatusProvider');

      consoleSpy.mockRestore();
    });
  });

  describe('loading states', () => {
    it('should show loading state during initialization', () => {
      mockServiceInstance.initialize.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 100))
      );

      const { getByTestId } = renderWithProvider();

      expect(getByTestId('isLoading').children[0]).toBe('true');
    });

    it('should hide loading state after initialization', async () => {
      mockServiceInstance.initialize.mockResolvedValue(true);
      mockServiceInstance.getCurrentStatus.mockResolvedValue('offline');
      mockServiceInstance.getCurrentSession.mockReturnValue(null);
      mockServiceInstance.getDriverStats.mockResolvedValue(null);
      mockServiceInstance.onStatusChange.mockReturnValue(() => {});

      const { getByTestId } = renderWithProvider();

      await waitFor(() => {
        expect(getByTestId('isLoading').children[0]).toBe('false');
      });
    });
  });
});
