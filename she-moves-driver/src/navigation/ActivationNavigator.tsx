/**
 * Activation Navigator for SheMove Driver App
 * Handles the post-verification activation flow for newly approved drivers
 */

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

import ActivationWelcomeScreen from '../screens/activation/ActivationWelcomeScreen';
import ProfileCompletionScreen from '../screens/activation/ProfileCompletionScreen';
import AppTutorialScreen from '../screens/activation/AppTutorialScreen';
import FirstTimeSetupScreen from '../screens/activation/FirstTimeSetupScreen';
import ReadyToDriveScreen from '../screens/activation/ReadyToDriveScreen';

export type ActivationStackParamList = {
  ActivationWelcome: undefined;
  ProfileCompletion: undefined;
  AppTutorial: undefined;
  FirstTimeSetup: undefined;
  ReadyToDrive: undefined;
};

const Stack = createStackNavigator<ActivationStackParamList>();

export function ActivationNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFF0FF' },
        gestureEnabled: false, // Prevent going back during activation
      }}
    >
      <Stack.Screen 
        name="ActivationWelcome" 
        component={ActivationWelcomeScreen}
      />
      <Stack.Screen 
        name="ProfileCompletion" 
        component={ProfileCompletionScreen}
      />
      <Stack.Screen 
        name="AppTutorial" 
        component={AppTutorialScreen}
      />
      <Stack.Screen 
        name="FirstTimeSetup" 
        component={FirstTimeSetupScreen}
      />
      <Stack.Screen 
        name="ReadyToDrive" 
        component={ReadyToDriveScreen}
      />
    </Stack.Navigator>
  );
}

export default ActivationNavigator;
