/**
 * Main App Navigator for SheMove Driver App
 * Handles navigation between authenticated and unauthenticated states
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, ActivityIndicator, StyleSheet, Text, TouchableOpacity } from 'react-native';

import { useAuth } from '../contexts/AuthContext';
import { AuthNavigator } from './AuthNavigator';
import { DriverNavigator } from './DriverNavigator';
import { OnboardingNavigator } from './OnboardingNavigator';
import { ActivationNavigator } from './ActivationNavigator';

const Stack = createStackNavigator();

export function AppNavigator() {
  const { user, loading, isDriver, isVerified } = useAuth();

  // Check if driver has completed activation flow
  const isActivated = user?.driverProfile?.activation_completed === true;

  // Debug logging for navigation decisions
  console.log('AppNavigator - Navigation State:', {
    hasUser: !!user,
    isDriver,
    isVerified,
    isActivated,
    driverProfile: user?.driverProfile ? {
      id: user.driverProfile.id,
      verification_status: user.driverProfile.verification_status,
      activation_completed: user.driverProfile.activation_completed,
    } : null,
  });

  // Show loading screen while checking authentication
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#E91E63" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          // User not authenticated - show auth flow
          <Stack.Screen name="Auth" component={AuthNavigator} />
        ) : !isDriver ? (
          // User authenticated but not a driver - show error/redirect
          <Stack.Screen name="NotDriver" component={NotDriverScreen} />
        ) : !user.driverProfile ? (
          // Driver user but no driver profile yet - show onboarding to create profile
          <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
        ) : !isVerified ? (
          // Driver profile exists but not verified - show verification flow
          <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
        ) : !isActivated ? (
          // Verified driver but not activated - show activation flow
          <Stack.Screen name="Activation" component={ActivationNavigator} />
        ) : (
          // Verified and activated driver - show main app
          <Stack.Screen name="Driver" component={DriverNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// Screen for users who are not drivers
function NotDriverScreen() {
  const { signOut } = useAuth();

  return (
    <View style={styles.errorContainer}>
      <View style={styles.errorCard}>
        <Text style={styles.errorTitle}>Driver Account Required</Text>
        <Text style={styles.errorMessage}>
          This account is not registered as a driver. Please use the SheMove passenger app or contact support to register as a driver.
        </Text>
        <TouchableOpacity style={styles.signOutButton} onPress={signOut}>
          <Text style={styles.signOutButtonText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF0FF',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF0FF',
    padding: 20,
  },
  errorCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#E91E63',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  signOutButton: {
    backgroundColor: '#E91E63',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  signOutButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
