/**
 * Onboarding Navigator for SheMove Driver App
 * Handles driver verification and onboarding process
 */

import React, { useState, useEffect } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { View, ActivityIndicator, StyleSheet } from 'react-native';

import { useAuth } from '../contexts/AuthContext';
import DriverVerificationScreen from '../screens/onboarding/DriverVerificationScreen';
import DocumentUploadScreen from '../screens/onboarding/DocumentUploadScreen';
import DocumentReviewScreen from '../screens/onboarding/DocumentReviewScreen';
import VerificationPendingScreen from '../screens/onboarding/VerificationPendingScreen';
import VehicleInfoScreen from '../screens/onboarding/VehicleInfoScreen';
import BackgroundCheckScreen from '../screens/onboarding/BackgroundCheckScreen';
import OnboardingCompleteScreen from '../screens/onboarding/OnboardingCompleteScreen';

interface UploadedDocument {
  id?: string;
  document_type: string;
  file_name: string;
  file_size: number;
  file_url: string;
  mime_type: string;
  status: string;
  uri?: string;
}

export type OnboardingStackParamList = {
  DriverVerification: undefined;
  DocumentUpload: undefined;
  DocumentReview: { uploadedDocuments?: UploadedDocument[] } | undefined;
  VerificationPending: undefined;
  VehicleInfo: undefined;
  BackgroundCheck: undefined;
  OnboardingComplete: undefined;
};

const Stack = createStackNavigator<OnboardingStackParamList>();

export function OnboardingNavigator() {
  const { user, authService } = useAuth();
  const [initialRoute, setInitialRoute] = useState<keyof OnboardingStackParamList | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    determineInitialRoute();
  }, [user]);

  const determineInitialRoute = async () => {
    try {
      if (!user?.driverProfile?.id) {
        // No driver profile - start with driver verification (vehicle registration)
        setInitialRoute('DriverVerification');
        return;
      }

      // Check if vehicle information is complete
      if (user.driverProfile.vehicle_make === 'Not Specified' ||
          user.driverProfile.license_number?.startsWith('PENDING-')) {
        // Vehicle info needs to be completed
        setInitialRoute('DriverVerification');
        return;
      }

      const supabase = authService.getSupabaseClient();
      if (!supabase) {
        setInitialRoute('DriverVerification');
        return;
      }

      // Check if user has uploaded any documents
      const { data: documents, error } = await supabase
        .from('document_uploads')
        .select('status')
        .eq('driver_id', user.driverProfile.id);

      if (error) {
        console.error('Error checking documents:', error);
        setInitialRoute('DocumentUpload'); // Safe fallback
        return;
      }

      const docs = documents || [];

      if (docs.length === 0) {
        // No documents uploaded - go to document upload
        setInitialRoute('DocumentUpload');
      } else {
        const hasUnderReview = docs.some(d => d.status === 'under_review');
        const hasApproved = docs.some(d => d.status === 'approved');
        const hasRejected = docs.some(d => d.status === 'rejected');

        if (hasApproved && docs.every(d => d.status === 'approved')) {
          // All documents approved - go to completion
          setInitialRoute('OnboardingComplete');
        } else if (hasUnderReview || hasApproved) {
          // Some documents under review or approved - show pending
          setInitialRoute('VerificationPending');
        } else if (hasRejected) {
          // Some documents rejected - back to upload
          setInitialRoute('DocumentUpload');
        } else {
          // Documents uploaded but not submitted - go to review
          setInitialRoute('DocumentReview');
        }
      }
    } catch (error) {
      console.error('Error determining initial route:', error);
      setInitialRoute('DocumentUpload'); // Safe fallback
    } finally {
      setLoading(false);
    }
  };

  if (loading || !initialRoute) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#E91E63" />
      </View>
    );
  }

  return (
    <Stack.Navigator
      initialRouteName={initialRoute}
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: '#FFF0FF' },
      }}
    >
      <Stack.Screen name="DriverVerification" component={DriverVerificationScreen} />
      <Stack.Screen name="DocumentUpload" component={DocumentUploadScreen} />
      <Stack.Screen name="DocumentReview" component={DocumentReviewScreen} />
      <Stack.Screen name="VerificationPending" component={VerificationPendingScreen} />
      <Stack.Screen name="VehicleInfo" component={VehicleInfoScreen} />
      <Stack.Screen name="BackgroundCheck" component={BackgroundCheckScreen} />
      <Stack.Screen name="OnboardingComplete" component={OnboardingCompleteScreen} />
    </Stack.Navigator>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF0FF',
  },
});
