/**
 * Trip Request Screen for SheMove Driver App
 * Displays incoming trip requests with accept/decline options
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
  Animated,
  Vibration,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { TripRequestData } from '../services/TripRequestService';

interface TripRequestScreenProps {
  tripRequest: TripRequestData;
  onAccept: (requestId: string) => Promise<void>;
  onDecline: (requestId: string) => Promise<void>;
  onExpired: () => void;
}

const { width, height } = Dimensions.get('window');

export default function TripRequestScreen({
  tripRequest,
  onAccept,
  onDecline,
  onExpired,
}: TripRequestScreenProps) {
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    // Calculate initial time remaining
    const expiresAt = new Date(tripRequest.expires_at).getTime();
    const now = new Date().getTime();
    const remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));
    setTimeRemaining(remaining);

    // Start countdown timer
    const timer = setInterval(() => {
      const currentTime = new Date().getTime();
      const remainingTime = Math.max(0, Math.floor((expiresAt - currentTime) / 1000));
      
      setTimeRemaining(remainingTime);
      
      if (remainingTime <= 0) {
        clearInterval(timer);
        onExpired();
      }
    }, 1000);

    // Start pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    pulseAnimation.start();

    // Vibrate to alert driver
    Vibration.vibrate([0, 500, 200, 500]);

    return () => {
      clearInterval(timer);
      pulseAnimation.stop();
    };
  }, [tripRequest.expires_at]);

  const handleAccept = async () => {
    if (isProcessing) return;
    
    try {
      setIsProcessing(true);
      await onAccept(tripRequest.id);
    } catch (error) {
      console.error('Error accepting trip:', error);
      Alert.alert('Error', 'Failed to accept trip request');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDecline = async () => {
    if (isProcessing) return;
    
    Alert.alert(
      'Decline Trip',
      'Are you sure you want to decline this trip request?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsProcessing(true);
              await onDecline(tripRequest.id);
            } catch (error) {
              console.error('Error declining trip:', error);
              Alert.alert('Error', 'Failed to decline trip request');
            } finally {
              setIsProcessing(false);
            }
          },
        },
      ]
    );
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDistance = (km: number): string => {
    if (km < 1) {
      return `${Math.round(km * 1000)}m`;
    }
    return `${km.toFixed(1)}km`;
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}min`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}min`;
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Animated.View style={[styles.iconContainer, { transform: [{ scale: pulseAnim }] }]}>
          <Ionicons name="car" size={40} color="#FFF" />
        </Animated.View>
        <Text style={styles.headerTitle}>New Trip Request</Text>
        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>{formatTime(timeRemaining)}</Text>
        </View>
      </View>

      {/* Trip Details */}
      <View style={styles.detailsContainer}>
        {/* Passenger Info */}
        <View style={styles.passengerInfo}>
          <View style={styles.passengerIcon}>
            <Ionicons name="person" size={24} color="#666" />
          </View>
          <View style={styles.passengerDetails}>
            <Text style={styles.passengerName}>{tripRequest.passenger_name}</Text>
            {tripRequest.passenger_phone && (
              <Text style={styles.passengerPhone}>{tripRequest.passenger_phone}</Text>
            )}
          </View>
        </View>

        {/* Route Info */}
        <View style={styles.routeContainer}>
          <View style={styles.routeItem}>
            <View style={[styles.routeIcon, styles.pickupIcon]}>
              <Ionicons name="location" size={20} color="#4CAF50" />
            </View>
            <View style={styles.routeDetails}>
              <Text style={styles.routeLabel}>Pickup</Text>
              <Text style={styles.routeAddress}>{tripRequest.pickup_address}</Text>
            </View>
          </View>

          <View style={styles.routeLine} />

          <View style={styles.routeItem}>
            <View style={[styles.routeIcon, styles.destinationIcon]}>
              <Ionicons name="flag" size={20} color="#F44336" />
            </View>
            <View style={styles.routeDetails}>
              <Text style={styles.routeLabel}>Destination</Text>
              <Text style={styles.routeAddress}>{tripRequest.destination_address}</Text>
            </View>
          </View>
        </View>

        {/* Trip Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Ionicons name="cash" size={24} color="#4CAF50" />
            <Text style={styles.statValue}>R{tripRequest.estimated_fare.toFixed(2)}</Text>
            <Text style={styles.statLabel}>Estimated Fare</Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="map" size={24} color="#2196F3" />
            <Text style={styles.statValue}>{formatDistance(tripRequest.estimated_distance)}</Text>
            <Text style={styles.statLabel}>Distance</Text>
          </View>
          
          <View style={styles.statItem}>
            <Ionicons name="time" size={24} color="#FF9800" />
            <Text style={styles.statValue}>{formatDuration(tripRequest.estimated_duration)}</Text>
            <Text style={styles.statLabel}>Duration</Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={[styles.actionButton, styles.declineButton]}
          onPress={handleDecline}
          disabled={isProcessing}
        >
          <Ionicons name="close" size={32} color="#FFF" />
          <Text style={styles.actionButtonText}>Decline</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.acceptButton]}
          onPress={handleAccept}
          disabled={isProcessing}
        >
          <Ionicons name="checkmark" size={32} color="#FFF" />
          <Text style={styles.actionButtonText}>Accept</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  header: {
    backgroundColor: '#E91E63',
    paddingTop: 60,
    paddingBottom: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFF',
    marginBottom: 10,
  },
  timerContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  timerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
  },
  detailsContainer: {
    flex: 1,
    padding: 20,
  },
  passengerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF',
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  passengerIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  passengerDetails: {
    flex: 1,
  },
  passengerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  passengerPhone: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  routeContainer: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  routeItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  routeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  pickupIcon: {
    backgroundColor: '#E8F5E8',
  },
  destinationIcon: {
    backgroundColor: '#FFEBEE',
  },
  routeDetails: {
    flex: 1,
  },
  routeLabel: {
    fontSize: 12,
    color: '#666',
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  routeAddress: {
    fontSize: 16,
    color: '#333',
    marginTop: 2,
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: '#E0E0E0',
    marginLeft: 19,
    marginVertical: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    justifyContent: 'space-around',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  actionsContainer: {
    flexDirection: 'row',
    padding: 20,
    paddingBottom: 40,
    gap: 15,
  },
  actionButton: {
    flex: 1,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
  },
});
