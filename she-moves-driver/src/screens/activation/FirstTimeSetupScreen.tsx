/**
 * First Time Setup Screen for SheMove Driver App
 * Fourth screen in the activation flow - configure preferences
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { ActivationStackParamList } from '../../navigation/ActivationNavigator';

type Props = {
  navigation: StackNavigationProp<ActivationStackParamList, 'FirstTimeSetup'>;
};

export default function FirstTimeSetupScreen({ navigation }: Props) {
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    notifications: true,
    locationSharing: true,
    autoAccept: false,
    nightMode: false,
  });

  const handleContinue = async () => {
    setLoading(true);

    try {
      // Here you would save the settings to the database
      // For now, we'll just simulate the save
      await new Promise(resolve => setTimeout(resolve, 1000));

      navigation.navigate('ReadyToDrive');
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert(
        'Setup Error',
        'There was an issue saving your preferences. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Setup Preferences</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '75%' }]} />
          </View>
          <Text style={styles.progressText}>Step 3 of 4</Text>
        </View>

        {/* Content */}
        <View style={styles.content}>
          <View style={styles.introContainer}>
            <Ionicons name="settings" size={48} color="#E91E63" />
            <Text style={styles.introTitle}>Customize Your Experience</Text>
            <Text style={styles.introDescription}>
              Configure these settings to optimize your driving experience. 
              You can change these anytime in your profile settings.
            </Text>
          </View>

          {/* Settings */}
          <View style={styles.settingsContainer}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIcon}>
                  <Ionicons name="notifications" size={24} color="#E91E63" />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>Push Notifications</Text>
                  <Text style={styles.settingDescription}>
                    Receive ride requests and important updates
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.notifications}
                onValueChange={() => toggleSetting('notifications')}
                trackColor={{ false: '#E0E0E0', true: '#E91E63' }}
                thumbColor={settings.notifications ? '#FFF' : '#FFF'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIcon}>
                  <Ionicons name="location" size={24} color="#E91E63" />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>Location Sharing</Text>
                  <Text style={styles.settingDescription}>
                    Share your location with passengers during trips
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.locationSharing}
                onValueChange={() => toggleSetting('locationSharing')}
                trackColor={{ false: '#E0E0E0', true: '#E91E63' }}
                thumbColor={settings.locationSharing ? '#FFF' : '#FFF'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIcon}>
                  <Ionicons name="checkmark-circle" size={24} color="#E91E63" />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>Auto-Accept Rides</Text>
                  <Text style={styles.settingDescription}>
                    Automatically accept rides that match your preferences
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.autoAccept}
                onValueChange={() => toggleSetting('autoAccept')}
                trackColor={{ false: '#E0E0E0', true: '#E91E63' }}
                thumbColor={settings.autoAccept ? '#FFF' : '#FFF'}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <View style={styles.settingIcon}>
                  <Ionicons name="moon" size={24} color="#E91E63" />
                </View>
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>Night Mode</Text>
                  <Text style={styles.settingDescription}>
                    Darker interface for night driving
                  </Text>
                </View>
              </View>
              <Switch
                value={settings.nightMode}
                onValueChange={() => toggleSetting('nightMode')}
                trackColor={{ false: '#E0E0E0', true: '#E91E63' }}
                thumbColor={settings.nightMode ? '#FFF' : '#FFF'}
              />
            </View>
          </View>

          {/* Safety Notice */}
          <View style={styles.safetyContainer}>
            <Ionicons name="shield-checkmark" size={24} color="#4CAF50" />
            <View style={styles.safetyText}>
              <Text style={styles.safetyTitle}>Safety First</Text>
              <Text style={styles.safetyDescription}>
                SheMove prioritizes your safety. Emergency features are always 
                enabled and cannot be disabled.
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFF" />
          ) : (
            <>
              <Text style={styles.continueButtonText}>Continue</Text>
              <Ionicons name="arrow-forward" size={20} color="#FFF" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#E91E63',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  content: {
    paddingHorizontal: 20,
  },
  introContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  introDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
  },
  settingsContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  safetyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0FFF4',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#C8E6C9',
  },
  safetyText: {
    flex: 1,
    marginLeft: 12,
  },
  safetyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2E7D32',
    marginBottom: 4,
  },
  safetyDescription: {
    fontSize: 14,
    color: '#388E3C',
    lineHeight: 18,
  },
  footer: {
    padding: 20,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  continueButton: {
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  continueButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
});
