/**
 * Profile Completion Screen for SheMove Driver App
 * Second screen in the activation flow - complete driver profile
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { ActivationStackParamList } from '../../navigation/ActivationNavigator';

type Props = {
  navigation: StackNavigationProp<ActivationStackParamList, 'ProfileCompletion'>;
};

export default function ProfileCompletionScreen({ navigation }: Props) {
  const { user, authService } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    emergencyContactName: '',
    emergencyContactPhone: '',
    preferredAreas: '',
  });

  const handleContinue = async () => {
    // Validate required fields
    if (!formData.emergencyContactName.trim() || !formData.emergencyContactPhone.trim()) {
      Alert.alert('Required Fields', 'Please fill in your emergency contact information.');
      return;
    }

    setLoading(true);

    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) {
        throw new Error('Unable to connect to database');
      }

      // Update driver profile with additional information
      const { error } = await supabase
        .from('drivers')
        .update({
          emergency_contact_name: formData.emergencyContactName.trim(),
          emergency_contact_phone: formData.emergencyContactPhone.trim(),
          preferred_areas: formData.preferredAreas.trim() ? 
            formData.preferredAreas.split(',').map(area => area.trim()) : [],
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.driverProfile.id);

      if (error) throw error;

      // Navigate to next screen
      navigation.navigate('AppTutorial');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert(
        'Update Error',
        'There was an issue updating your profile. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Complete Your Profile</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '25%' }]} />
          </View>
          <Text style={styles.progressText}>Step 1 of 4</Text>
        </View>

        {/* Content */}
        <View style={styles.content}>
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Emergency Contact</Text>
            <Text style={styles.sectionDescription}>
              This information helps us keep you safe while driving
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Contact Name *</Text>
              <TextInput
                style={styles.textInput}
                value={formData.emergencyContactName}
                onChangeText={(text) => setFormData(prev => ({ ...prev, emergencyContactName: text }))}
                placeholder="Enter full name"
                placeholderTextColor="#999"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Phone Number *</Text>
              <TextInput
                style={styles.textInput}
                value={formData.emergencyContactPhone}
                onChangeText={(text) => setFormData(prev => ({ ...prev, emergencyContactPhone: text }))}
                placeholder="Enter phone number"
                placeholderTextColor="#999"
                keyboardType="phone-pad"
              />
            </View>
          </View>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Preferred Areas (Optional)</Text>
            <Text style={styles.sectionDescription}>
              Areas where you prefer to pick up passengers (comma-separated)
            </Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Areas</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                value={formData.preferredAreas}
                onChangeText={(text) => setFormData(prev => ({ ...prev, preferredAreas: text }))}
                placeholder="e.g. Sandton, Rosebank, Midrand"
                placeholderTextColor="#999"
                multiline
                numberOfLines={3}
              />
            </View>
          </View>

          <View style={styles.infoContainer}>
            <Ionicons name="information-circle" size={20} color="#E91E63" />
            <Text style={styles.infoText}>
              You can update this information anytime in your profile settings
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFF" />
          ) : (
            <>
              <Text style={styles.continueButtonText}>Continue</Text>
              <Ionicons name="arrow-forward" size={20} color="#FFF" />
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  progressContainer: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#E91E63',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  content: {
    paddingHorizontal: 20,
  },
  sectionContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#FFF',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF0FF',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F9E6F7',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    marginLeft: 12,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  continueButton: {
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  continueButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
});
