/**
 * Ready to Drive Screen for SheMove Driver App
 * Final screen in the activation flow - complete activation
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { ActivationStackParamList } from '../../navigation/ActivationNavigator';

type Props = {
  navigation: StackNavigationProp<ActivationStackParamList, 'ReadyToDrive'>;
};

export default function ReadyToDriveScreen({ navigation }: Props) {
  const { user, authService, refreshUserProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [animationValue] = useState(new Animated.Value(0));

  React.useEffect(() => {
    // Start celebration animation
    Animated.sequence([
      Animated.timing(animationValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(animationValue, {
        toValue: 0.95,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(animationValue, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleStartDriving = async () => {
    setLoading(true);

    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) {
        throw new Error('Unable to connect to database');
      }

      console.log('ReadyToDrive - Marking activation as complete for driver:', user.driverProfile.id);

      // Mark activation as complete
      const { error } = await supabase
        .from('drivers')
        .update({
          activation_completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.driverProfile.id);

      if (error) {
        console.error('ReadyToDrive - Error updating activation status:', error);
        throw error;
      }

      console.log('ReadyToDrive - Activation marked as complete, refreshing user profile...');

      // Refresh user profile to trigger navigation to main app
      await refreshUserProfile();

      console.log('ReadyToDrive - Profile refreshed, AppNavigator should navigate to main app');

    } catch (error) {
      console.error('Error completing activation:', error);
      Alert.alert(
        'Activation Error',
        'There was an issue completing your activation. Please try again.',
        [
          {
            text: 'Retry',
            onPress: () => handleStartDriving(),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const scaleAnimation = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  const opacityAnimation = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: '100%' }]} />
          </View>
          <Text style={styles.progressText}>Step 4 of 4 - Complete!</Text>
        </View>

        <Animated.View
          style={[
            styles.celebrationContainer,
            {
              transform: [{ scale: scaleAnimation }],
              opacity: opacityAnimation,
            },
          ]}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={100} color="#4CAF50" />
            <View style={styles.sparkle1}>
              <Ionicons name="sparkles" size={28} color="#FFD700" />
            </View>
            <View style={styles.sparkle2}>
              <Ionicons name="sparkles" size={24} color="#FFD700" />
            </View>
            <View style={styles.sparkle3}>
              <Ionicons name="sparkles" size={20} color="#FFD700" />
            </View>
          </View>

          <Text style={styles.readyTitle}>You're Ready to Drive!</Text>
          <Text style={styles.readySubtitle}>
            Welcome to the SheMove driver community
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.messageContainer,
            { opacity: opacityAnimation },
          ]}
        >
          <Text style={styles.messageText}>
            Congratulations, {user?.profile?.full_name || 'Driver'}! 
            You've completed your driver activation and you're now ready 
            to start earning with SheMove.
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.featuresContainer,
            { opacity: opacityAnimation },
          ]}
        >
          <Text style={styles.featuresTitle}>You can now:</Text>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="toggle" size={24} color="#E91E63" />
            </View>
            <Text style={styles.featureText}>Go online to receive ride requests</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="car" size={24} color="#E91E63" />
            </View>
            <Text style={styles.featureText}>Accept and complete trips</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="cash" size={24} color="#E91E63" />
            </View>
            <Text style={styles.featureText}>Track your earnings and payments</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="people" size={24} color="#E91E63" />
            </View>
            <Text style={styles.featureText}>Connect with the SheMove community</Text>
          </View>
        </Animated.View>
      </View>

      <Animated.View
        style={[
          styles.footer,
          { opacity: opacityAnimation },
        ]}
      >
        <TouchableOpacity
          style={styles.startDrivingButton}
          onPress={handleStartDriving}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFF" />
          ) : (
            <>
              <Ionicons name="car-sport" size={24} color="#FFF" />
              <Text style={styles.startDrivingButtonText}>Start Driving</Text>
            </>
          )}
        </TouchableOpacity>

        <Text style={styles.supportText}>
          Need help? Our support team is available 24/7
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
  },
  progressContainer: {
    marginBottom: 40,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F0F0F0',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#4CAF50',
    textAlign: 'center',
    fontWeight: '600',
  },
  celebrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  sparkle1: {
    position: 'absolute',
    top: -15,
    right: -15,
  },
  sparkle2: {
    position: 'absolute',
    bottom: -10,
    left: -20,
  },
  sparkle3: {
    position: 'absolute',
    top: 30,
    left: -25,
  },
  readyTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  readySubtitle: {
    fontSize: 18,
    color: '#E91E63',
    textAlign: 'center',
    fontWeight: '600',
  },
  messageContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  messageText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    textAlign: 'center',
  },
  featuresContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureText: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  footer: {
    padding: 24,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  startDrivingButton: {
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 12,
    marginBottom: 16,
  },
  startDrivingButtonText: {
    color: '#FFF',
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  supportText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});
