import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useDriverStatus } from '../../contexts/DriverStatusContext';
import Colors from '../../constants/Colors';

interface EarningsData {
  today: {
    earnings: number;
    trips: number;
    hours: number;
  };
  week: {
    earnings: number;
    trips: number;
    hours: number;
  };
  month: {
    earnings: number;
    trips: number;
    hours: number;
  };
}

export default function EarningsScreen() {
  const { user } = useAuth();
  const { statusService, stats } = useDriverStatus();
  const [earnings, setEarnings] = useState<EarningsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');

  useEffect(() => {
    loadEarnings();
  }, []);

  const loadEarnings = async () => {
    try {
      setIsLoading(true);

      // Use actual earnings data from DriverStatusService
      const actualEarnings: EarningsData = {
        today: {
          earnings: stats?.today_earnings || 0,
          trips: stats?.today_trips || 0,
          hours: (stats?.today_online_minutes || 0) / 60,
        },
        week: {
          earnings: stats?.week_earnings || 0,
          trips: stats?.week_trips || 0,
          hours: (stats?.week_online_minutes || 0) / 60,
        },
        month: {
          earnings: 0, // Will be calculated from actual data when available
          trips: 0,
          hours: 0,
        }
      };

      setEarnings(actualEarnings);
    } catch (error) {
      console.error('Error loading earnings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadEarnings();
    setIsRefreshing(false);
  };

  const getCurrentPeriodData = () => {
    if (!earnings) return null;
    return earnings[selectedPeriod];
  };

  const renderPeriodButton = (period: 'today' | 'week' | 'month', label: string) => (
    <TouchableOpacity
      style={[
        styles.periodButton,
        selectedPeriod === period && styles.periodButtonActive
      ]}
      onPress={() => setSelectedPeriod(period)}
    >
      <Text style={[
        styles.periodButtonText,
        selectedPeriod === period && styles.periodButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderMetricCard = (title: string, value: string, icon: string, color: string) => (
    <View style={styles.metricCard}>
      <View style={[styles.metricIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <View style={styles.metricContent}>
        <Text style={styles.metricTitle}>{title}</Text>
        <Text style={styles.metricValue}>{value}</Text>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading earnings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const currentData = getCurrentPeriodData();
  const hourlyRate = currentData ? (currentData.earnings / Math.max(currentData.hours, 1)) : 0;

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Earnings</Text>
          <Text style={styles.subtitle}>Track your driving income</Text>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {renderPeriodButton('today', 'Today')}
          {renderPeriodButton('week', 'This Week')}
          {renderPeriodButton('month', 'This Month')}
        </View>

        {/* Main Earnings Card */}
        {currentData && (
          <View style={styles.mainEarningsCard}>
            <Text style={styles.earningsLabel}>
              {selectedPeriod === 'today' ? "Today's Earnings" :
               selectedPeriod === 'week' ? "This Week's Earnings" :
               "This Month's Earnings"}
            </Text>
            <Text style={styles.earningsAmount}>R{currentData.earnings.toFixed(2)}</Text>
            <View style={styles.earningsDetails}>
              <View style={styles.earningsDetailItem}>
                <Text style={styles.earningsDetailValue}>{currentData.trips}</Text>
                <Text style={styles.earningsDetailLabel}>Trips</Text>
              </View>
              <View style={styles.earningsDetailItem}>
                <Text style={styles.earningsDetailValue}>{currentData.hours.toFixed(1)}h</Text>
                <Text style={styles.earningsDetailLabel}>Online</Text>
              </View>
              <View style={styles.earningsDetailItem}>
                <Text style={styles.earningsDetailValue}>R{hourlyRate.toFixed(0)}/h</Text>
                <Text style={styles.earningsDetailLabel}>Per Hour</Text>
              </View>
            </View>
          </View>
        )}

        {/* Metrics Grid */}
        <View style={styles.metricsGrid}>
          {renderMetricCard(
            'Total Earnings',
            `R${earnings?.month.earnings.toFixed(2) || '0.00'}`,
            'wallet',
            Colors.success
          )}
          {renderMetricCard(
            'Total Trips',
            `${earnings?.month.trips || 0}`,
            'car',
            Colors.primary
          )}
          {renderMetricCard(
            'Hours Online',
            `${earnings?.month.hours.toFixed(1) || '0.0'}h`,
            'time',
            Colors.info
          )}
          {renderMetricCard(
            'Avg Rating',
            `${stats?.average_rating.toFixed(1) || '5.0'}`,
            'star',
            Colors.warning
          )}
        </View>

        {/* Performance Summary */}
        <View style={styles.performanceCard}>
          <Text style={styles.performanceTitle}>Performance Summary</Text>

          <View style={styles.performanceItem}>
            <View style={styles.performanceLabel}>
              <Ionicons name="checkmark-circle" size={16} color={Colors.success} />
              <Text style={styles.performanceLabelText}>Acceptance Rate</Text>
            </View>
            <Text style={styles.performanceValue}>{stats?.acceptance_rate.toFixed(1) || '100.0'}%</Text>
          </View>

          <View style={styles.performanceItem}>
            <View style={styles.performanceLabel}>
              <Ionicons name="close-circle" size={16} color={Colors.error} />
              <Text style={styles.performanceLabelText}>Cancellation Rate</Text>
            </View>
            <Text style={styles.performanceValue}>{stats?.cancellation_rate.toFixed(1) || '0.0'}%</Text>
          </View>

          <View style={styles.performanceItem}>
            <View style={styles.performanceLabel}>
              <Ionicons name="star" size={16} color={Colors.warning} />
              <Text style={styles.performanceLabelText}>Average Rating</Text>
            </View>
            <Text style={styles.performanceValue}>{stats?.average_rating.toFixed(1) || '5.0'}</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  subtitle: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 4,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
  },
  periodButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 4,
    backgroundColor: Colors.gray[100],
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: Colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.secondary,
  },
  periodButtonTextActive: {
    color: Colors.white,
  },
  mainEarningsCard: {
    backgroundColor: Colors.primary,
    margin: 16,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
  },
  earningsLabel: {
    fontSize: 16,
    color: Colors.white + 'CC',
    marginBottom: 8,
  },
  earningsAmount: {
    fontSize: 36,
    fontWeight: 'bold',
    color: Colors.white,
    marginBottom: 16,
  },
  earningsDetails: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  earningsDetailItem: {
    alignItems: 'center',
  },
  earningsDetailValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.white,
  },
  earningsDetailLabel: {
    fontSize: 12,
    color: Colors.white + 'CC',
    marginTop: 4,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  metricCard: {
    width: '48%',
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    marginHorizontal: '1%',
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  metricContent: {
    flex: 1,
  },
  metricTitle: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  performanceCard: {
    backgroundColor: Colors.white,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  performanceTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  performanceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  performanceLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  performanceLabelText: {
    fontSize: 14,
    color: Colors.text.primary,
    marginLeft: 8,
  },
  performanceValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text.primary,
  },
});
