/**
 * Home Screen for SheMove Driver App
 * Main dashboard where drivers can go online/offline and receive trip requests
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useDriverStatus } from '../../contexts/DriverStatusContext';
import { useLocation } from '../../contexts/LocationContext';
import Colors from '../../constants/Colors';

export default function HomeScreen() {
  const { user, signOut } = useAuth();
  const {
    status,
    isOnline,
    isLoading: statusLoading,
    currentSession,
    stats,
    goOnline,
    goOffline,
    refreshStats
  } = useDriverStatus();
  const {
    currentLocation,
    getCurrentLocation,
    requestPermissions,
    permissionStatus,
    isLoading: locationLoading
  } = useLocation();

  const [isToggling, setIsToggling] = useState(false);

  // Refresh stats when component mounts or status changes
  useEffect(() => {
    if (isOnline) {
      refreshStats();
    }
  }, [isOnline, refreshStats]);

  const toggleOnlineStatus = async () => {
    if (isToggling) return;

    setIsToggling(true);

    try {
      if (!isOnline) {
        // Going online - need location
        let location = currentLocation;

        if (!location) {
          // Request location permission if needed
          if (!permissionStatus?.foreground) {
            const permissions = await requestPermissions();
            if (!permissions.foreground) {
              Alert.alert(
                'Location Required',
                'Location access is required to go online and receive trip requests.',
                [{ text: 'OK' }]
              );
              return;
            }
          }

          // Get current location
          location = await getCurrentLocation();
          if (!location) {
            Alert.alert(
              'Location Error',
              'Unable to get your current location. Please check your GPS settings.',
              [{ text: 'OK' }]
            );
            return;
          }
        }

        const success = await goOnline(location);
        if (!success) {
          // Error already handled by context
          return;
        }
      } else {
        // Going offline
        const success = await goOffline();
        if (!success) {
          // Error already handled by context
          return;
        }
      }
    } catch (error) {
      console.error('Error toggling online status:', error);
      Alert.alert('Error', 'Failed to update status. Please try again.');
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Hello,</Text>
            <Text style={styles.driverName}>{user?.profile?.full_name || 'Driver'}</Text>
            {currentSession && (
              <Text style={styles.sessionInfo}>
                Online for {Math.floor((currentSession.total_online_minutes || 0) / 60)}h {(currentSession.total_online_minutes || 0) % 60}m
              </Text>
            )}
          </View>
          <TouchableOpacity style={styles.profileButton} onPress={signOut}>
            <Ionicons name="person-circle-outline" size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Online/Offline Toggle */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>Driver Status</Text>
            <View style={styles.statusToggle}>
              <Text style={[styles.statusText, { color: isOnline ? Colors.online : Colors.offline }]}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Text>
              {(statusLoading || locationLoading || isToggling) ? (
                <ActivityIndicator size="small" color={Colors.primary} />
              ) : (
                <Switch
                  value={isOnline}
                  onValueChange={toggleOnlineStatus}
                  trackColor={{ false: Colors.gray[300], true: Colors.online }}
                  thumbColor={isOnline ? Colors.white : Colors.gray[400]}
                  disabled={statusLoading || locationLoading}
                />
              )}
            </View>
          </View>
          <Text style={styles.statusDescription}>
            {isOnline
              ? 'You are online and can receive trip requests'
              : 'Go online to start receiving trip requests'
            }
          </Text>
          {currentLocation && (
            <View style={styles.locationInfo}>
              <Ionicons name="location" size={16} color={Colors.text.secondary} />
              <Text style={styles.locationText}>
                Location updated {new Date(currentLocation.timestamp).toLocaleTimeString()}
              </Text>
            </View>
          )}
        </View>

        {/* Today's Summary */}
        <View style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Today's Summary</Text>
          <View style={styles.summaryGrid}>
            <View style={styles.summaryItem}>
              <Ionicons name="car" size={20} color={Colors.primary} />
              <Text style={styles.summaryValue}>{stats?.today_trips || 0}</Text>
              <Text style={styles.summaryLabel}>Trips</Text>
            </View>
            <View style={styles.summaryItem}>
              <Ionicons name="cash" size={20} color={Colors.primary} />
              <Text style={styles.summaryValue}>R{stats?.today_earnings?.toFixed(2) || '0.00'}</Text>
              <Text style={styles.summaryLabel}>Earnings</Text>
            </View>
            <View style={styles.summaryItem}>
              <Ionicons name="time" size={20} color={Colors.primary} />
              <Text style={styles.summaryValue}>
                {Math.floor((stats?.today_online_minutes || 0) / 60)}h {(stats?.today_online_minutes || 0) % 60}m
              </Text>
              <Text style={styles.summaryLabel}>Online</Text>
            </View>
          </View>
          {stats && (
            <View style={styles.performanceRow}>
              <View style={styles.performanceItem}>
                <Text style={styles.performanceValue}>{stats?.acceptance_rate?.toFixed(0) || '100'}%</Text>
                <Text style={styles.performanceLabel}>Acceptance Rate</Text>
              </View>
              <View style={styles.performanceItem}>
                <Text style={styles.performanceValue}>{stats?.average_rating?.toFixed(1) || '5.0'}</Text>
                <Text style={styles.performanceLabel}>Rating</Text>
              </View>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsCard}>
          <Text style={styles.cardTitle}>Quick Actions</Text>
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonIcon}>📍</Text>
              <Text style={styles.actionButtonText}>Set Location</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonIcon}>🚗</Text>
              <Text style={styles.actionButtonText}>Vehicle Info</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonIcon}>📞</Text>
              <Text style={styles.actionButtonText}>Support</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.activityCard}>
          <Text style={styles.cardTitle}>Recent Activity</Text>
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No recent trips</Text>
            <Text style={styles.emptyStateSubtext}>
              Go online to start receiving trip requests
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  greeting: {
    fontSize: 16,
    color: Colors.text.secondary,
  },
  driverName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  sessionInfo: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  profileButton: {
    backgroundColor: Colors.white,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  profileButtonText: {
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  statusCard: {
    backgroundColor: Colors.white,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  statusToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  statusDescription: {
    fontSize: 14,
    color: Colors.text.secondary,
    lineHeight: 20,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 4,
  },
  locationText: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  summaryCard: {
    backgroundColor: Colors.white,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.gray[200],
  },
  performanceItem: {
    alignItems: 'center',
  },
  performanceValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  performanceLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  actionsCard: {
    backgroundColor: Colors.white,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  actionButton: {
    alignItems: 'center',
    flex: 1,
  },
  actionButtonIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 12,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  activityCard: {
    backgroundColor: Colors.white,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
});
