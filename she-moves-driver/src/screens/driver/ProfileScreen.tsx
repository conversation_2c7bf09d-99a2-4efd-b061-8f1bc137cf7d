import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { useDriverStatus } from '../../contexts/DriverStatusContext';
import Colors from '../../constants/Colors';

interface DriverProfile {
  id: string;
  full_name: string;
  email: string;
  phone: string;
  license_number: string;
  vehicle_make: string;
  vehicle_model: string;
  vehicle_year: number;
  vehicle_color: string;
  license_plate: string;
  verification_status: 'pending' | 'approved' | 'rejected';
  rating: number;
  total_trips: number;
  member_since: string;
}

export default function ProfileScreen() {
  const { user, signOut } = useAuth();
  const { statusService, stats } = useDriverStatus();
  const [profile, setProfile] = useState<DriverProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setIsLoading(true);

      // Use actual profile data from authentication and database
      const actualProfile: DriverProfile = {
        id: user?.id || '',
        full_name: user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Driver',
        email: user?.email || '',
        phone: '', // Will be loaded from database when available
        license_number: '', // Will be loaded from database when available
        vehicle_make: '', // Will be loaded from database when available
        vehicle_model: '', // Will be loaded from database when available
        vehicle_year: new Date().getFullYear(), // Default to current year
        vehicle_color: '', // Will be loaded from database when available
        license_plate: '', // Will be loaded from database when available
        verification_status: 'pending', // Will be loaded from database when available
        rating: stats?.average_rating || 5.0,
        total_trips: (stats?.today_trips || 0) + (stats?.week_trips || 0),
        member_since: user?.created_at || new Date().toISOString()
      };

      setProfile(actualProfile);
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: () => signOut()
        }
      ]
    );
  };

  const renderProfileItem = (
    icon: string,
    label: string,
    value: string,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={styles.profileItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.profileItemLeft}>
        <View style={styles.profileItemIcon}>
          <Ionicons name={icon as any} size={20} color={Colors.primary} />
        </View>
        <View style={styles.profileItemContent}>
          <Text style={styles.profileItemLabel}>{label}</Text>
          <Text style={styles.profileItemValue}>{value}</Text>
        </View>
      </View>
      {onPress && (
        <Ionicons name="chevron-forward" size={20} color={Colors.gray[400]} />
      )}
    </TouchableOpacity>
  );

  const renderStatsCard = (title: string, value: string, icon: string, color: string) => (
    <View style={styles.statsCard}>
      <View style={[styles.statsIcon, { backgroundColor: color + '20' }]}>
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <Text style={styles.statsValue}>{value}</Text>
      <Text style={styles.statsTitle}>{title}</Text>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load profile</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {profile.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </Text>
              </View>
              <View style={[
                styles.verificationBadge,
                { backgroundColor: profile.verification_status === 'approved' ? Colors.success : Colors.warning }
              ]}>
                <Ionicons
                  name={profile.verification_status === 'approved' ? 'checkmark' : 'time'}
                  size={12}
                  color={Colors.white}
                />
              </View>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{profile.full_name}</Text>
              <Text style={styles.profileEmail}>{profile.email}</Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color={Colors.warning} />
                <Text style={styles.ratingText}>{profile.rating.toFixed(1)} rating</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          {renderStatsCard('Total Trips', profile.total_trips.toString(), 'car', Colors.primary)}
          {renderStatsCard('Rating', profile.rating.toFixed(1), 'star', Colors.warning)}
          {renderStatsCard('Acceptance', `${stats?.acceptance_rate.toFixed(0) || '100'}%`, 'checkmark-circle', Colors.success)}
        </View>

        {/* Personal Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>
          {renderProfileItem('person', 'Full Name', profile.full_name)}
          {renderProfileItem('mail', 'Email', profile.email)}
          {renderProfileItem('call', 'Phone', profile.phone || 'Not specified')}
          {renderProfileItem('card', 'License Number', profile.license_number || 'Not specified')}
        </View>

        {/* Vehicle Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Vehicle Information</Text>
          {renderProfileItem('car', 'Vehicle',
            profile.vehicle_make && profile.vehicle_model
              ? `${profile.vehicle_year} ${profile.vehicle_make} ${profile.vehicle_model}`
              : 'Not specified'
          )}
          {renderProfileItem('color-palette', 'Color', profile.vehicle_color || 'Not specified')}
          {renderProfileItem('document', 'License Plate', profile.license_plate || 'Not specified')}
        </View>

        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          {renderProfileItem('settings', 'Settings', 'App preferences', () => {})}
          {renderProfileItem('help-circle', 'Help & Support', 'Get help', () => {})}
          {renderProfileItem('document-text', 'Terms & Privacy', 'Legal information', () => {})}
          {renderProfileItem('log-out', 'Sign Out', 'Sign out of your account', handleSignOut)}
        </View>

        {/* Member Since */}
        <View style={styles.memberSince}>
          <Text style={styles.memberSinceText}>
            Member since {new Date(profile.member_since).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long'
            })}
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: Colors.white,
    paddingBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.white,
  },
  verificationBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.white,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  profileEmail: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
  },
  statsCard: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  statsIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  statsTitle: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  section: {
    backgroundColor: Colors.white,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.gray[50],
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[100],
  },
  profileItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profileItemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileItemContent: {
    flex: 1,
  },
  profileItemLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  profileItemValue: {
    fontSize: 16,
    color: Colors.text.primary,
    marginTop: 2,
  },
  memberSince: {
    padding: 16,
    alignItems: 'center',
  },
  memberSinceText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
});
