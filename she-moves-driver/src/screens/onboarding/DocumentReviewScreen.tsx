import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { OnboardingStackParamList } from '../../navigation/OnboardingNavigator';

type Props = {
  navigation: StackNavigationProp<OnboardingStackParamList, 'DocumentReview'>;
  route: RouteProp<OnboardingStackParamList, 'DocumentReview'>;
};

interface UploadedDocument {
  id: string;
  document_type: string;
  file_name: string;
  file_url: string;
  status: string;
  created_at: string;
  reviewed_at?: string;
  rejection_reason?: string;
}

interface DocumentTemplate {
  document_type: string;
  display_name: string;
  description: string;
}

export default function DocumentReviewScreen({ navigation, route }: Props) {
  const { user, authService } = useAuth();
  const [loading, setLoading] = useState(true);
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadDocuments();
    loadTemplates();
  }, []);

  const loadDocuments = async () => {
    try {
      // Check if documents were passed from upload screen (during onboarding)
      const uploadedDocuments = route.params?.uploadedDocuments;
      if (uploadedDocuments && uploadedDocuments.length > 0) {
        console.log('Using documents passed from upload screen:', uploadedDocuments);
        setDocuments(uploadedDocuments);
        setLoading(false);
        return;
      }

      // Otherwise, load from database (for existing drivers)
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) {
        setLoading(false);
        return;
      }

      const { data, error } = await supabase
        .from('document_uploads')
        .select('*')
        .eq('driver_id', user.driverProfile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Error loading documents:', error);
      Alert.alert('Error', 'Failed to load documents');
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase) return;

      const { data, error } = await supabase
        .from('document_templates')
        .select('document_type, display_name, description')
        .eq('is_required', true);

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error loading templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDocumentTemplate = (documentType: string) => {
    return templates.find(t => t.document_type === documentType);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'rejected':
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return 'checkmark-circle';
      case 'pending':
        return 'time-outline';
      case 'rejected':
        return 'close-circle';
      default:
        return 'document-outline';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Under Review';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Uploaded';
    }
  };

  const submitForReview = async () => {
    try {
      setSubmitting(true);

      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) return;

      // Check if documents are only stored locally (during onboarding)
      const uploadedDocuments = route.params?.uploadedDocuments;
      if (uploadedDocuments && uploadedDocuments.length > 0) {
        console.log('Saving locally stored documents to database...');

        // Save documents to database
        const documentsToSave = uploadedDocuments.map(doc => ({
          driver_id: user.driverProfile!.id,
          document_type: doc.document_type,
          file_name: doc.file_name,
          file_size: doc.file_size,
          file_url: doc.file_url,
          mime_type: doc.mime_type,
          status: 'under_review',
          created_at: doc.created_at || new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));

        const { error: insertError } = await supabase
          .from('document_uploads')
          .insert(documentsToSave);

        if (insertError) throw insertError;
      } else {
        // Update existing documents to under_review status
        const { error } = await supabase
          .from('document_uploads')
          .update({ status: 'under_review' })
          .eq('driver_id', user.driverProfile.id)
          .eq('status', 'pending');

        if (error) throw error;
      }

      // Update driver verification status
      const { error: driverError } = await supabase
        .from('drivers')
        .update({ verification_status: 'pending' })
        .eq('id', user.driverProfile.id);

      if (driverError) throw driverError;

      Alert.alert(
        'Submitted for Review',
        'Your documents have been submitted for review. You will be notified once the review is complete.',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('VerificationPending'),
          },
        ]
      );
    } catch (error) {
      console.error('Error submitting for review:', error);
      Alert.alert('Error', 'Failed to submit documents for review');
    } finally {
      setSubmitting(false);
    }
  };

  const canSubmit = () => {
    const requiredTemplates = templates.filter(t => t);
    const uploadedDocs = requiredTemplates.filter(template =>
      documents.some(doc => doc.document_type === template.document_type)
    );
    return uploadedDocs.length === requiredTemplates.length;
  };

  const hasRejectedDocuments = () => {
    return documents.some(doc => doc.status === 'rejected');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Loading documents...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.title}>Review Documents</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.summaryCard}>
          <Ionicons name="document-text-outline" size={32} color="#E91E63" />
          <Text style={styles.summaryTitle}>Document Review</Text>
          <Text style={styles.summaryText}>
            Please review your uploaded documents before submitting for verification.
          </Text>
        </View>

        {hasRejectedDocuments() && (
          <View style={styles.warningCard}>
            <Ionicons name="warning-outline" size={24} color="#F44336" />
            <Text style={styles.warningText}>
              Some documents were rejected. Please re-upload the rejected documents before submitting.
            </Text>
          </View>
        )}

        <Text style={styles.sectionTitle}>Uploaded Documents</Text>

        {documents.map((doc) => {
          const template = getDocumentTemplate(doc.document_type);
          const statusColor = getStatusColor(doc.status);

          return (
            <View key={doc.id} style={styles.documentCard}>
              <View style={styles.documentHeader}>
                <View style={styles.documentInfo}>
                  <Text style={styles.documentTitle}>
                    {template?.display_name || doc.document_type}
                  </Text>
                  <Text style={styles.documentDescription}>
                    {template?.description || 'Document uploaded'}
                  </Text>
                  <Text style={styles.uploadDate}>
                    Uploaded: {new Date(doc.created_at).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.statusContainer}>
                  <Ionicons
                    name={getStatusIcon(doc.status) as any}
                    size={24}
                    color={statusColor}
                  />
                  <Text style={[styles.statusText, { color: statusColor }]}>
                    {getStatusText(doc.status)}
                  </Text>
                </View>
              </View>

              {doc.rejection_reason && (
                <View style={styles.rejectionContainer}>
                  <Text style={styles.rejectionTitle}>Rejection Reason:</Text>
                  <Text style={styles.rejectionText}>{doc.rejection_reason}</Text>
                </View>
              )}

              <TouchableOpacity
                style={styles.replaceButton}
                onPress={() => navigation.navigate('DocumentUpload')}
              >
                <Ionicons name="refresh" size={16} color="#E91E63" />
                <Text style={styles.replaceButtonText}>Replace Document</Text>
              </TouchableOpacity>
            </View>
          );
        })}

        {documents.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="document-outline" size={64} color="#CCC" />
            <Text style={styles.emptyTitle}>No Documents Uploaded</Text>
            <Text style={styles.emptyText}>
              Please go back and upload your required documents.
            </Text>
            <TouchableOpacity
              style={styles.uploadButton}
              onPress={() => navigation.navigate('DocumentUpload')}
            >
              <Text style={styles.uploadButtonText}>Upload Documents</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {documents.length > 0 && (
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.submitButton,
              (!canSubmit() || hasRejectedDocuments()) && styles.submitButtonDisabled,
            ]}
            onPress={submitForReview}
            disabled={!canSubmit() || hasRejectedDocuments() || submitting}
          >
            {submitting ? (
              <ActivityIndicator size="small" color="#FFF" />
            ) : (
              <Text style={styles.submitButtonText}>Submit for Review</Text>
            )}
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  summaryCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 24,
    marginVertical: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 12,
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  warningCard: {
    backgroundColor: '#FFF5F5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: '#F44336',
    marginLeft: 12,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  documentCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  documentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  documentInfo: {
    flex: 1,
    marginRight: 12,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  uploadDate: {
    fontSize: 12,
    color: '#999',
  },
  statusContainer: {
    alignItems: 'center',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  rejectionContainer: {
    backgroundColor: '#FFF5F5',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#F44336',
  },
  rejectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#F44336',
    marginBottom: 4,
  },
  rejectionText: {
    fontSize: 14,
    color: '#F44336',
    lineHeight: 20,
  },
  replaceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E91E63',
  },
  replaceButtonText: {
    color: '#E91E63',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  uploadButton: {
    backgroundColor: '#E91E63',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: '#FFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    padding: 24,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  submitButton: {
    backgroundColor: '#E91E63',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#CCC',
  },
  submitButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
