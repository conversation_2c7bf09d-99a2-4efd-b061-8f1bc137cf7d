import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
  ScrollView
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { OnboardingStackParamList } from '../../navigation/OnboardingNavigator';
import { useAuth } from '../../contexts/AuthContext';
import Colors from '../../constants/Colors';

type DriverVerificationScreenNavigationProp = StackNavigationProp<OnboardingStackParamList, 'DriverVerification'>;

interface Props {
  navigation: DriverVerificationScreenNavigationProp;
}

export default function DriverVerificationScreen({ navigation }: Props) {
  const { user, authService } = useAuth();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    licenseNumber: '',
    vehicleMake: '',
    vehicleModel: '',
    vehicleYear: '',
    vehicleColor: '',
    vehiclePlate: '',
  });

  const handleUpdateDriverProfile = async () => {
    // Validate form
    if (!formData.licenseNumber.trim() || !formData.vehicleMake.trim() ||
        !formData.vehicleModel.trim() || !formData.vehicleYear.trim() ||
        !formData.vehicleColor.trim() || !formData.vehiclePlate.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      setLoading(true);

      // Update driver profile in database
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user) {
        throw new Error('Authentication service not available');
      }

      // Update existing driver profile with vehicle information
      const driverData: any = {
        license_number: formData.licenseNumber.trim(),
        vehicle_make: formData.vehicleMake.trim(),
        vehicle_model: formData.vehicleModel.trim(),
        vehicle_year: parseInt(formData.vehicleYear),
        vehicle_color: formData.vehicleColor.trim(),
        vehicle_plate: formData.vehiclePlate.trim().toUpperCase(),
        verification_status: 'pending',
      };

      // Add optional fields only if they exist in the table
      try {
        // Check if onboarding_completed column exists
        const { data: columns } = await supabase
          .from('drivers')
          .select('*')
          .limit(0);

        // If we can query without error, add optional fields
        driverData.vehicle_type = 'SheRide';
        driverData.onboarding_completed = false;
      } catch (columnError) {
        console.log('Some columns may not exist yet, using basic fields only');
      }

      // Update the existing driver record instead of inserting a new one
      const { data, error } = await supabase
        .from('drivers')
        .update(driverData)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      Alert.alert(
        'Success!',
        'Your driver profile has been updated. Please continue with document upload.',
        [{ text: 'Continue', onPress: () => navigation.navigate('DocumentUpload') }]
      );

    } catch (error: any) {
      console.error('Error updating driver profile:', error);
      Alert.alert('Error', error.message || 'Failed to update driver profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>Driver Registration</Text>
            <Text style={styles.subtitle}>Let's set up your driver profile</Text>
          </View>

          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Driver's License Number</Text>
              <TextInput
                style={styles.input}
                value={formData.licenseNumber}
                onChangeText={(text) => setFormData({...formData, licenseNumber: text})}
                placeholder="Enter your license number"
                autoCapitalize="characters"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Vehicle Make</Text>
              <TextInput
                style={styles.input}
                value={formData.vehicleMake}
                onChangeText={(text) => setFormData({...formData, vehicleMake: text})}
                placeholder="e.g., Toyota, BMW, Ford"
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Vehicle Model</Text>
              <TextInput
                style={styles.input}
                value={formData.vehicleModel}
                onChangeText={(text) => setFormData({...formData, vehicleModel: text})}
                placeholder="e.g., Corolla, X3, Focus"
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Vehicle Year</Text>
              <TextInput
                style={styles.input}
                value={formData.vehicleYear}
                onChangeText={(text) => setFormData({...formData, vehicleYear: text})}
                placeholder="e.g., 2020"
                keyboardType="numeric"
                maxLength={4}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>Vehicle Color</Text>
              <TextInput
                style={styles.input}
                value={formData.vehicleColor}
                onChangeText={(text) => setFormData({...formData, vehicleColor: text})}
                placeholder="e.g., White, Black, Silver"
                autoCapitalize="words"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.label}>License Plate</Text>
              <TextInput
                style={styles.input}
                value={formData.vehiclePlate}
                onChangeText={(text) => setFormData({...formData, vehiclePlate: text})}
                placeholder="e.g., ABC123GP"
                autoCapitalize="characters"
              />
            </View>

            <TouchableOpacity
              style={[styles.continueButton, loading && styles.continueButtonDisabled]}
              onPress={handleUpdateDriverProfile}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color={Colors.white} />
              ) : (
                <Text style={styles.continueButtonText}>Update Vehicle Information</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
  form: {
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  continueButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
  },
  continueButtonDisabled: {
    opacity: 0.6,
  },
  continueButtonText: {
    color: Colors.white,
    fontSize: 18,
    fontWeight: '600',
  },
});
