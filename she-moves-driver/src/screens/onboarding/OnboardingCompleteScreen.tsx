import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { OnboardingStackParamList } from '../../navigation/OnboardingNavigator';

const { width } = Dimensions.get('window');

type Props = {
  navigation: StackNavigationProp<OnboardingStackParamList, 'OnboardingComplete'>;
};

export default function OnboardingCompleteScreen({ navigation }: Props) {
  const { user, authService, refreshUserProfile, isVerified } = useAuth();
  const [loading, setLoading] = useState(false);
  const [animationValue] = useState(new Animated.Value(0));

  useEffect(() => {
    // Start celebration animation
    Animated.sequence([
      Animated.timing(animationValue, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(animationValue, {
        toValue: 0.9,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(animationValue, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();

    // Mark onboarding as complete
    markOnboardingComplete();
  }, []);

  // Auto-navigate when user becomes verified (e.g., through real-time subscription)
  useEffect(() => {
    if (isVerified) {
      console.log('User is now verified, AppNavigator will handle automatic navigation');
      // The AppNavigator will automatically switch to DriverNavigator
      // No manual navigation needed here
    }
  }, [isVerified]);

  const markOnboardingComplete = async () => {
    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) return;

      // Update driver profile to mark onboarding as complete
      const { error } = await supabase
        .from('drivers')
        .update({
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.driverProfile.id);

      if (error) {
        console.error('Error marking onboarding complete:', error);
      }
    } catch (error) {
      console.error('Error updating onboarding status:', error);
    }
  };

  const handleGetStarted = async () => {
    console.log('OnboardingComplete - Get Started clicked');
    setLoading(true);

    try {
      console.log('OnboardingComplete - Current user state before refresh:', {
        isVerified,
        driverProfile: user?.driverProfile ? {
          id: user.driverProfile.id,
          verification_status: user.driverProfile.verification_status,
          activation_completed: user.driverProfile.activation_completed,
        } : null,
      });

      // First, refresh user profile to ensure we have the latest verification status
      console.log('OnboardingComplete - Refreshing user profile...');
      await refreshUserProfile();

      // Small delay for smooth transition
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('OnboardingComplete - User profile refreshed. Current verification status:', isVerified);
      console.log('OnboardingComplete - AppNavigator should handle navigation automatically');

      // If for some reason the user is still not verified, show a message
      if (!isVerified) {
        console.log('OnboardingComplete - User still not verified after refresh');
        Alert.alert(
          'Verification Pending',
          'Your driver application is still being reviewed. You will be notified once approved.',
          [{ text: 'OK' }]
        );
      }

    } catch (error) {
      console.error('Error refreshing user profile:', error);

      // Show error message to user
      Alert.alert(
        'Navigation Error',
        'There was an issue loading your driver dashboard. Please try again.',
        [
          {
            text: 'Retry',
            onPress: () => handleGetStarted(),
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const scaleAnimation = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 1],
  });

  const opacityAnimation = animationValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.celebrationContainer,
            {
              transform: [{ scale: scaleAnimation }],
              opacity: opacityAnimation,
            },
          ]}
        >
          <View style={styles.iconContainer}>
            <Ionicons name="checkmark-circle" size={80} color="#4CAF50" />
            <View style={styles.sparkle1}>
              <Ionicons name="sparkles" size={24} color="#FFD700" />
            </View>
            <View style={styles.sparkle2}>
              <Ionicons name="sparkles" size={20} color="#FFD700" />
            </View>
            <View style={styles.sparkle3}>
              <Ionicons name="sparkles" size={16} color="#FFD700" />
            </View>
          </View>

          <Text style={styles.congratsTitle}>Congratulations!</Text>
          <Text style={styles.congratsSubtitle}>
            Welcome to the SheMove Driver Community
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.messageContainer,
            { opacity: opacityAnimation },
          ]}
        >
          <Text style={styles.messageText}>
            You've successfully completed the driver onboarding process!
            You're now ready to start earning with SheMove and providing
            safe, reliable rides for women in your community.
          </Text>
        </Animated.View>

        <Animated.View
          style={[
            styles.featuresContainer,
            { opacity: opacityAnimation },
          ]}
        >
          <Text style={styles.featuresTitle}>What's next?</Text>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="car" size={24} color="#E91E63" />
            </View>
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Start Driving</Text>
              <Text style={styles.featureDescription}>
                Go online and start accepting ride requests
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="location" size={24} color="#E91E63" />
            </View>
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Set Your Areas</Text>
              <Text style={styles.featureDescription}>
                Choose your preferred pickup locations
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="cash" size={24} color="#E91E63" />
            </View>
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Track Earnings</Text>
              <Text style={styles.featureDescription}>
                Monitor your daily and weekly earnings
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Ionicons name="shield-checkmark" size={24} color="#E91E63" />
            </View>
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Safety First</Text>
              <Text style={styles.featureDescription}>
                Access safety features and emergency support
              </Text>
            </View>
          </View>
        </Animated.View>
      </View>

      <Animated.View
        style={[
          styles.footer,
          { opacity: opacityAnimation },
        ]}
      >
        <TouchableOpacity
          style={styles.getStartedButton}
          onPress={handleGetStarted}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFF" />
          ) : (
            <>
              <Text style={styles.getStartedButtonText}>Get Started</Text>
              <Ionicons name="arrow-forward" size={20} color="#FFF" />
            </>
          )}
        </TouchableOpacity>

        <Text style={styles.supportText}>
          Need help? Contact our support team anytime
        </Text>
      </Animated.View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'center',
  },
  celebrationContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 24,
  },
  sparkle1: {
    position: 'absolute',
    top: -10,
    right: -10,
  },
  sparkle2: {
    position: 'absolute',
    bottom: -5,
    left: -15,
  },
  sparkle3: {
    position: 'absolute',
    top: 20,
    left: -20,
  },
  congratsTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  congratsSubtitle: {
    fontSize: 18,
    color: '#E91E63',
    textAlign: 'center',
    fontWeight: '600',
  },
  messageContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  messageText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    textAlign: 'center',
  },
  featuresContainer: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFF0FF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  footer: {
    padding: 24,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  getStartedButton: {
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  getStartedButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 8,
  },
  supportText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
  },
});
