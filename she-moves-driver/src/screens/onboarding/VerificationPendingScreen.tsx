import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { StackNavigationProp } from '@react-navigation/stack';
import { OnboardingStackParamList } from '../../navigation/OnboardingNavigator';

type Props = {
  navigation: StackNavigationProp<OnboardingStackParamList, 'VerificationPending'>;
};

interface VerificationStatus {
  overall_status: string;
  documents_count: number;
  approved_count: number;
  pending_count: number;
  rejected_count: number;
  estimated_completion: string;
}

export default function VerificationPendingScreen({ navigation }: Props) {
  const { user, authService } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [status, setStatus] = useState<VerificationStatus | null>(null);

  useEffect(() => {
    loadVerificationStatus();
  }, []);

  const loadVerificationStatus = async () => {
    try {
      const supabase = authService.getSupabaseClient();
      if (!supabase || !user?.driverProfile?.id) return;

      // Get document counts
      const { data: documents, error } = await supabase
        .from('document_uploads')
        .select('status')
        .eq('driver_id', user.driverProfile.id);

      if (error) throw error;

      const docs = documents || [];
      const approvedCount = docs.filter(d => d.status === 'approved').length;
      const pendingCount = docs.filter(d => d.status === 'pending' || d.status === 'under_review').length;
      const rejectedCount = docs.filter(d => d.status === 'rejected').length;

      let overallStatus = 'pending';
      if (rejectedCount > 0) {
        overallStatus = 'rejected';
      } else if (approvedCount === docs.length && docs.length > 0) {
        overallStatus = 'approved';
      }

      // Estimate completion time (1-3 business days)
      const estimatedDays = Math.ceil(Math.random() * 3) + 1;
      const estimatedDate = new Date();
      estimatedDate.setDate(estimatedDate.getDate() + estimatedDays);

      setStatus({
        overall_status: overallStatus,
        documents_count: docs.length,
        approved_count: approvedCount,
        pending_count: pendingCount,
        rejected_count: rejectedCount,
        estimated_completion: estimatedDate.toLocaleDateString(),
      });
    } catch (error) {
      console.error('Error loading verification status:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadVerificationStatus();
  };

  const getStatusIcon = () => {
    if (!status) return 'time-outline';
    
    switch (status.overall_status) {
      case 'approved':
        return 'checkmark-circle';
      case 'rejected':
        return 'close-circle';
      default:
        return 'time-outline';
    }
  };

  const getStatusColor = () => {
    if (!status) return '#FF9800';
    
    switch (status.overall_status) {
      case 'approved':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      default:
        return '#FF9800';
    }
  };

  const getStatusTitle = () => {
    if (!status) return 'Checking Status...';
    
    switch (status.overall_status) {
      case 'approved':
        return 'Verification Complete!';
      case 'rejected':
        return 'Action Required';
      default:
        return 'Verification in Progress';
    }
  };

  const getStatusMessage = () => {
    if (!status) return 'Please wait while we check your verification status.';
    
    switch (status.overall_status) {
      case 'approved':
        return 'Congratulations! Your documents have been approved and you can now start driving with SheMove.';
      case 'rejected':
        return 'Some of your documents need to be resubmitted. Please review the feedback and upload new documents.';
      default:
        return `Your documents are being reviewed by our team. This usually takes 1-3 business days. We'll notify you once the review is complete.`;
    }
  };

  const handleContinue = () => {
    if (status?.overall_status === 'approved') {
      navigation.navigate('OnboardingComplete');
    } else if (status?.overall_status === 'rejected') {
      navigation.navigate('DocumentUpload');
    } else {
      // For pending status, maybe go to a dashboard or home screen
      navigation.navigate('OnboardingComplete');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Checking verification status...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.statusCard}>
          <Ionicons
            name={getStatusIcon() as any}
            size={64}
            color={getStatusColor()}
          />
          <Text style={[styles.statusTitle, { color: getStatusColor() }]}>
            {getStatusTitle()}
          </Text>
          <Text style={styles.statusMessage}>
            {getStatusMessage()}
          </Text>
        </View>

        {status && (
          <View style={styles.progressCard}>
            <Text style={styles.progressTitle}>Verification Progress</Text>
            
            <View style={styles.progressItem}>
              <View style={styles.progressIcon}>
                <Ionicons
                  name={status.approved_count > 0 ? 'checkmark-circle' : 'ellipse-outline'}
                  size={24}
                  color={status.approved_count > 0 ? '#4CAF50' : '#CCC'}
                />
              </View>
              <View style={styles.progressText}>
                <Text style={styles.progressLabel}>Documents Approved</Text>
                <Text style={styles.progressValue}>
                  {status.approved_count} of {status.documents_count}
                </Text>
              </View>
            </View>

            <View style={styles.progressItem}>
              <View style={styles.progressIcon}>
                <Ionicons
                  name={status.pending_count > 0 ? 'time-outline' : 'ellipse-outline'}
                  size={24}
                  color={status.pending_count > 0 ? '#FF9800' : '#CCC'}
                />
              </View>
              <View style={styles.progressText}>
                <Text style={styles.progressLabel}>Under Review</Text>
                <Text style={styles.progressValue}>
                  {status.pending_count} documents
                </Text>
              </View>
            </View>

            {status.rejected_count > 0 && (
              <View style={styles.progressItem}>
                <View style={styles.progressIcon}>
                  <Ionicons
                    name="close-circle"
                    size={24}
                    color="#F44336"
                  />
                </View>
                <View style={styles.progressText}>
                  <Text style={styles.progressLabel}>Needs Attention</Text>
                  <Text style={styles.progressValue}>
                    {status.rejected_count} documents
                  </Text>
                </View>
              </View>
            )}

            {status.overall_status === 'pending' && (
              <View style={styles.estimateContainer}>
                <Ionicons name="calendar-outline" size={20} color="#666" />
                <Text style={styles.estimateText}>
                  Estimated completion: {status.estimated_completion}
                </Text>
              </View>
            )}
          </View>
        )}

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>What happens next?</Text>
          
          {status?.overall_status === 'approved' ? (
            <View style={styles.infoList}>
              <Text style={styles.infoItem}>✓ You can now start accepting ride requests</Text>
              <Text style={styles.infoItem}>✓ Complete your driver profile setup</Text>
              <Text style={styles.infoItem}>✓ Set your availability and preferred areas</Text>
            </View>
          ) : status?.overall_status === 'rejected' ? (
            <View style={styles.infoList}>
              <Text style={styles.infoItem}>• Review the feedback on rejected documents</Text>
              <Text style={styles.infoItem}>• Upload new, corrected documents</Text>
              <Text style={styles.infoItem}>• Resubmit for verification</Text>
            </View>
          ) : (
            <View style={styles.infoList}>
              <Text style={styles.infoItem}>• Our team will review your documents</Text>
              <Text style={styles.infoItem}>• You'll receive a notification when complete</Text>
              <Text style={styles.infoItem}>• Check back here for updates</Text>
            </View>
          )}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            { backgroundColor: getStatusColor() }
          ]}
          onPress={handleContinue}
        >
          <Text style={styles.continueButtonText}>
            {status?.overall_status === 'approved' ? 'Complete Setup' :
             status?.overall_status === 'rejected' ? 'Fix Documents' :
             'Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF0FF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  statusCard: {
    backgroundColor: '#FFF',
    borderRadius: 16,
    padding: 32,
    marginTop: 32,
    marginBottom: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  statusTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  statusMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  progressCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  progressItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressIcon: {
    marginRight: 16,
  },
  progressText: {
    flex: 1,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  progressValue: {
    fontSize: 14,
    color: '#666',
  },
  estimateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  estimateText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  infoCard: {
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  infoList: {
    gap: 12,
  },
  infoItem: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  footer: {
    padding: 24,
    backgroundColor: '#FFF',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  continueButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#FFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
});
