/**
 * Active Trip Screen for SheMove Driver App
 * Main interface for managing active trips from acceptance to completion
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { ActiveTripService, ActiveTrip, TripStatus } from '../../services/ActiveTripService';
import { useLocation } from '../../contexts/LocationContext';
import { useAuth } from '../../contexts/AuthContext';
import Colors from '../../constants/Colors';

interface ActiveTripScreenProps {
  supabase: any; // SupabaseClient type
}

export default function ActiveTripScreen({ supabase }: ActiveTripScreenProps) {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { currentLocation, getCurrentLocation } = useLocation();
  
  const [tripService, setTripService] = useState<ActiveTripService | null>(null);
  const [currentTrip, setCurrentTrip] = useState<ActiveTrip | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Initialize service
  useEffect(() => {
    if (user && supabase) {
      initializeService();
    }
  }, [user, supabase]);

  const initializeService = async () => {
    try {
      const service = new ActiveTripService(supabase);
      const initialized = await service.initialize(user!.id);
      
      if (!initialized) {
        Alert.alert('Error', 'Failed to initialize trip service');
        return;
      }

      // Subscribe to trip updates
      const unsubscribe = service.onTripUpdate((trip) => {
        setCurrentTrip(trip);
      });

      // Subscribe to trip completion
      const unsubscribeCompleted = service.onTripCompleted((trip) => {
        Alert.alert(
          'Trip Completed',
          `Trip completed successfully! Fare: R${trip.actual_fare?.toFixed(2)}`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      });

      setTripService(service);
      setCurrentTrip(service.getCurrentTrip());
      
      // Store unsubscribe functions for cleanup
      (service as any)._unsubscribe = unsubscribe;
      (service as any)._unsubscribeCompleted = unsubscribeCompleted;
      
    } catch (error) {
      console.error('Error initializing trip service:', error);
      Alert.alert('Error', 'Failed to initialize trip service');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (newStatus: TripStatus) => {
    if (!tripService || !currentTrip || isUpdating) return;

    setIsUpdating(true);

    try {
      let location = currentLocation;
      if (!location) {
        location = await getCurrentLocation();
        if (!location) {
          Alert.alert('Error', 'Unable to get current location');
          return;
        }
      }

      let result;
      switch (newStatus) {
        case 'en_route':
          result = await tripService.startTrip(currentTrip.id, location);
          break;
        case 'arrived':
          result = await tripService.arriveAtPickup(location);
          break;
        case 'in_progress':
          result = await tripService.pickupPassenger(location);
          break;
        case 'completed':
          // This should open a completion dialog
          handleTripCompletion();
          return;
        default:
          return;
      }

      if (!result.success) {
        Alert.alert('Error', result.error || 'Failed to update trip status');
      }
    } catch (error) {
      console.error('Error updating trip status:', error);
      Alert.alert('Error', 'Failed to update trip status');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTripCompletion = () => {
    if (!currentTrip) return;

    Alert.alert(
      'Complete Trip',
      'Are you sure you want to complete this trip?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          onPress: async () => {
            if (!tripService || !currentLocation) return;

            setIsUpdating(true);
            try {
              // Calculate trip duration
              const startTime = new Date(currentTrip.started_at || currentTrip.accepted_at!);
              const endTime = new Date();
              const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

              const result = await tripService.completeTrip({
                final_location: currentLocation,
                actual_distance_km: currentTrip.distance_km, // In real app, calculate actual distance
                actual_duration_minutes: durationMinutes,
                final_fare: currentTrip.fare_estimate, // In real app, calculate final fare
                driver_notes: 'Trip completed successfully'
              });

              if (!result.success) {
                Alert.alert('Error', result.error || 'Failed to complete trip');
              }
            } catch (error) {
              console.error('Error completing trip:', error);
              Alert.alert('Error', 'Failed to complete trip');
            } finally {
              setIsUpdating(false);
            }
          }
        }
      ]
    );
  };

  const handleCancelTrip = () => {
    Alert.alert(
      'Cancel Trip',
      'Are you sure you want to cancel this trip? This may affect your rating.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            if (!tripService || !currentLocation) return;

            setIsUpdating(true);
            try {
              const result = await tripService.cancelTrip('Driver cancelled', currentLocation);
              if (result.success) {
                Alert.alert('Trip Cancelled', 'The trip has been cancelled.', [
                  { text: 'OK', onPress: () => navigation.goBack() }
                ]);
              } else {
                Alert.alert('Error', result.error || 'Failed to cancel trip');
              }
            } catch (error) {
              console.error('Error cancelling trip:', error);
              Alert.alert('Error', 'Failed to cancel trip');
            } finally {
              setIsUpdating(false);
            }
          }
        }
      ]
    );
  };

  const getStatusText = (status: TripStatus): string => {
    switch (status) {
      case 'accepted': return 'Trip Accepted';
      case 'en_route': return 'En Route to Pickup';
      case 'arrived': return 'Arrived at Pickup';
      case 'in_progress': return 'Trip in Progress';
      case 'completed': return 'Trip Completed';
      case 'cancelled': return 'Trip Cancelled';
      default: return 'Unknown Status';
    }
  };

  const getNextAction = (status: TripStatus): { text: string; action: TripStatus } | null => {
    switch (status) {
      case 'accepted': return { text: 'Start Trip', action: 'en_route' };
      case 'en_route': return { text: 'Arrived at Pickup', action: 'arrived' };
      case 'arrived': return { text: 'Pickup Passenger', action: 'in_progress' };
      case 'in_progress': return { text: 'Complete Trip', action: 'completed' };
      default: return null;
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading trip...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentTrip) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <Ionicons name="car-outline" size={64} color={Colors.gray[400]} />
          <Text style={styles.emptyTitle}>No Active Trip</Text>
          <Text style={styles.emptyText}>You don't have any active trips at the moment.</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const nextAction = getNextAction(currentTrip.status);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Active Trip</Text>
          <TouchableOpacity onPress={handleCancelTrip}>
            <Ionicons name="close" size={24} color={Colors.error} />
          </TouchableOpacity>
        </View>

        {/* Trip Status */}
        <View style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <Text style={styles.statusTitle}>{getStatusText(currentTrip.status)}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(currentTrip.status) }]}>
              <Text style={styles.statusBadgeText}>{currentTrip.status.toUpperCase()}</Text>
            </View>
          </View>
        </View>

        {/* Passenger Info */}
        <View style={styles.passengerCard}>
          <View style={styles.passengerHeader}>
            <Ionicons name="person-circle" size={40} color={Colors.primary} />
            <View style={styles.passengerInfo}>
              <Text style={styles.passengerName}>{currentTrip.passenger_info.name}</Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color={Colors.warning} />
                <Text style={styles.ratingText}>{currentTrip.passenger_info.rating.toFixed(1)}</Text>
              </View>
            </View>
            {currentTrip.passenger_info.phone && (
              <TouchableOpacity style={styles.callButton}>
                <Ionicons name="call" size={20} color={Colors.primary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Trip Details */}
        <View style={styles.tripCard}>
          <View style={styles.locationRow}>
            <Ionicons name="radio-button-on" size={16} color={Colors.online} />
            <View style={styles.locationInfo}>
              <Text style={styles.locationLabel}>Pickup</Text>
              <Text style={styles.locationAddress}>{currentTrip.pickup_location.address}</Text>
            </View>
          </View>
          <View style={styles.locationDivider} />
          <View style={styles.locationRow}>
            <Ionicons name="location" size={16} color={Colors.error} />
            <View style={styles.locationInfo}>
              <Text style={styles.locationLabel}>Destination</Text>
              <Text style={styles.locationAddress}>{currentTrip.destination_location.address}</Text>
            </View>
          </View>
        </View>

        {/* Trip Metrics */}
        <View style={styles.metricsCard}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>R{currentTrip.fare_estimate.toFixed(2)}</Text>
            <Text style={styles.metricLabel}>Fare</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{currentTrip.distance_km.toFixed(1)} km</Text>
            <Text style={styles.metricLabel}>Distance</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>{currentTrip.estimated_duration_minutes} min</Text>
            <Text style={styles.metricLabel}>Duration</Text>
          </View>
        </View>

        {/* Action Button */}
        {nextAction && (
          <TouchableOpacity
            style={[styles.actionButton, isUpdating && styles.actionButtonDisabled]}
            onPress={() => handleStatusUpdate(nextAction.action)}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <ActivityIndicator size="small" color={Colors.white} />
            ) : (
              <Text style={styles.actionButtonText}>{nextAction.text}</Text>
            )}
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const getStatusColor = (status: TripStatus): string => {
  switch (status) {
    case 'accepted': return Colors.primary;
    case 'en_route': return Colors.warning;
    case 'arrived': return Colors.info;
    case 'in_progress': return Colors.online;
    case 'completed': return Colors.success;
    case 'cancelled': return Colors.error;
    default: return Colors.gray[400];
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginTop: 8,
  },
  backButton: {
    marginTop: 24,
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: Colors.primary,
    borderRadius: 8,
  },
  backButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  statusCard: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusBadgeText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  passengerCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  passengerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passengerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingText: {
    marginLeft: 4,
    fontSize: 14,
    color: Colors.text.secondary,
  },
  callButton: {
    padding: 8,
    backgroundColor: Colors.primary + '20',
    borderRadius: 20,
  },
  tripCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  locationInfo: {
    flex: 1,
    marginLeft: 12,
  },
  locationLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    textTransform: 'uppercase',
    fontWeight: '600',
  },
  locationAddress: {
    fontSize: 14,
    color: Colors.text.primary,
    marginTop: 2,
  },
  locationDivider: {
    height: 1,
    backgroundColor: Colors.gray[200],
    marginVertical: 12,
    marginLeft: 28,
  },
  metricsCard: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricItem: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  metricLabel: {
    fontSize: 12,
    color: Colors.text.secondary,
    marginTop: 4,
  },
  actionButton: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.primary,
    borderRadius: 12,
    alignItems: 'center',
  },
  actionButtonDisabled: {
    backgroundColor: Colors.gray[400],
  },
  actionButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
