/**
 * Trip Chat Screen for SheMove Driver App
 * Real-time messaging interface between driver and passenger
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { TripCommunicationService, TripMessage, QuickAction } from '../../services/TripCommunicationService';
import { useAuth } from '../../contexts/AuthContext';
import { useLocation } from '../../contexts/LocationContext';
import Colors from '../../constants/Colors';

interface TripChatScreenProps {
  supabase: any; // SupabaseClient type
}

export default function TripChatScreen({ supabase }: TripChatScreenProps) {
  const navigation = useNavigation();
  const route = useRoute();
  const { user } = useAuth();
  const { currentLocation } = useLocation();
  
  // Get trip ID from route params
  const { tripId } = route.params as { tripId: string };
  
  const [communicationService, setCommunicationService] = useState<TripCommunicationService | null>(null);
  const [messages, setMessages] = useState<TripMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  
  const flatListRef = useRef<FlatList>(null);

  // Initialize service
  useEffect(() => {
    if (user && supabase && tripId) {
      initializeService();
    }
  }, [user, supabase, tripId]);

  const initializeService = async () => {
    try {
      const service = new TripCommunicationService(supabase);
      const initialized = await service.initialize(user!.id);
      
      if (!initialized) {
        Alert.alert('Error', 'Failed to initialize communication service');
        navigation.goBack();
        return;
      }

      // Start communication for this trip
      const started = await service.startCommunication(tripId, {
        onMessageReceived: (message) => {
          setMessages(prev => [...prev, message]);
          scrollToBottom();
        },
        onMessageRead: (messageId) => {
          setMessages(prev => prev.map(msg => 
            msg.id === messageId ? { ...msg, is_read: true } : msg
          ));
        },
        onError: (error) => {
          console.error('Communication error:', error);
          Alert.alert('Error', 'Communication error occurred');
        }
      });

      if (!started) {
        Alert.alert('Error', 'Failed to start communication');
        navigation.goBack();
        return;
      }

      // Load message history
      const history = await service.getMessageHistory();
      setMessages(history);
      
      setCommunicationService(service);
      
    } catch (error) {
      console.error('Error initializing communication service:', error);
      Alert.alert('Error', 'Failed to initialize communication');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const sendMessage = async () => {
    if (!communicationService || !messageText.trim() || isSending) return;

    setIsSending(true);
    try {
      const success = await communicationService.sendMessage(messageText.trim());
      if (success) {
        setMessageText('');
        scrollToBottom();
      } else {
        Alert.alert('Error', 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setIsSending(false);
    }
  };

  const sendQuickAction = async (actionId: string) => {
    if (!communicationService) return;

    try {
      const success = await communicationService.sendQuickAction(actionId);
      if (success) {
        setShowQuickActions(false);
        scrollToBottom();
      } else {
        Alert.alert('Error', 'Failed to send quick action');
      }
    } catch (error) {
      console.error('Error sending quick action:', error);
      Alert.alert('Error', 'Failed to send quick action');
    }
  };

  const sendCurrentLocation = async () => {
    if (!communicationService || !currentLocation) {
      Alert.alert('Error', 'Current location not available');
      return;
    }

    try {
      const success = await communicationService.sendLocation(
        currentLocation.lat,
        currentLocation.lng,
        'My current location'
      );
      if (success) {
        scrollToBottom();
      } else {
        Alert.alert('Error', 'Failed to send location');
      }
    } catch (error) {
      console.error('Error sending location:', error);
      Alert.alert('Error', 'Failed to send location');
    }
  };

  const renderMessage = ({ item }: { item: TripMessage }) => {
    const isOwnMessage = item.sender === 'driver';
    const isSystemMessage = item.sender === 'system';

    return (
      <View style={[
        styles.messageContainer,
        isOwnMessage ? styles.ownMessage : styles.otherMessage,
        isSystemMessage && styles.systemMessage
      ]}>
        <View style={[
          styles.messageBubble,
          isOwnMessage ? styles.ownBubble : styles.otherBubble,
          isSystemMessage && styles.systemBubble
        ]}>
          {item.message_type === 'quick_action' && item.metadata?.icon && (
            <View style={styles.quickActionHeader}>
              <Ionicons 
                name={item.metadata.icon as any} 
                size={16} 
                color={isOwnMessage ? Colors.white : Colors.primary} 
              />
              <Text style={[
                styles.quickActionLabel,
                { color: isOwnMessage ? Colors.white : Colors.primary }
              ]}>
                Quick Action
              </Text>
            </View>
          )}
          
          <Text style={[
            styles.messageText,
            isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
            isSystemMessage && styles.systemMessageText
          ]}>
            {item.content}
          </Text>
          
          {item.message_type === 'location' && item.metadata && (
            <TouchableOpacity style={styles.locationButton}>
              <Ionicons name="location" size={16} color={Colors.primary} />
              <Text style={styles.locationText}>View Location</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <Text style={[
          styles.messageTime,
          isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime
        ]}>
          {new Date(item.sent_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          {isOwnMessage && item.is_read && (
            <Text style={styles.readIndicator}> ✓✓</Text>
          )}
        </Text>
      </View>
    );
  };

  const renderQuickActions = () => {
    if (!communicationService) return null;

    const quickActions = communicationService.getQuickActions();

    return (
      <View style={styles.quickActionsContainer}>
        <Text style={styles.quickActionsTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={styles.quickActionButton}
              onPress={() => sendQuickAction(action.id)}
            >
              <Ionicons name={action.icon as any} size={20} color={Colors.primary} />
              <Text style={styles.quickActionText}>{action.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading chat...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Trip Chat</Text>
          <TouchableOpacity onPress={sendCurrentLocation}>
            <Ionicons name="location" size={24} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Messages */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={scrollToBottom}
        />

        {/* Quick Actions */}
        {showQuickActions && renderQuickActions()}

        {/* Input */}
        <View style={styles.inputContainer}>
          <TouchableOpacity
            style={styles.quickActionToggle}
            onPress={() => setShowQuickActions(!showQuickActions)}
          >
            <Ionicons 
              name={showQuickActions ? "close" : "flash"} 
              size={20} 
              color={Colors.primary} 
            />
          </TouchableOpacity>
          
          <TextInput
            style={styles.textInput}
            value={messageText}
            onChangeText={setMessageText}
            placeholder="Type a message..."
            placeholderTextColor={Colors.text.secondary}
            multiline
            maxLength={500}
          />
          
          <TouchableOpacity
            style={[styles.sendButton, (!messageText.trim() || isSending) && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={!messageText.trim() || isSending}
          >
            <Ionicons 
              name="send" 
              size={20} 
              color={(!messageText.trim() || isSending) ? Colors.gray[400] : Colors.white} 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
    backgroundColor: Colors.white,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    marginBottom: 16,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  systemMessage: {
    alignItems: 'center',
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  ownBubble: {
    backgroundColor: Colors.primary,
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    backgroundColor: Colors.white,
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  systemBubble: {
    backgroundColor: Colors.gray[100],
    borderRadius: 16,
  },
  quickActionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  quickActionLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'uppercase',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: Colors.white,
  },
  otherMessageText: {
    color: Colors.text.primary,
  },
  systemMessageText: {
    color: Colors.text.secondary,
    fontStyle: 'italic',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    padding: 8,
    backgroundColor: Colors.white + '20',
    borderRadius: 8,
  },
  locationText: {
    color: Colors.primary,
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '600',
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
  },
  ownMessageTime: {
    color: Colors.text.secondary,
    textAlign: 'right',
  },
  otherMessageTime: {
    color: Colors.text.secondary,
    textAlign: 'left',
  },
  readIndicator: {
    color: Colors.success,
  },
  quickActionsContainer: {
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.gray[200],
    padding: 16,
  },
  quickActionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 12,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.gray[100],
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    color: Colors.text.primary,
    marginLeft: 6,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.gray[200],
  },
  quickActionToggle: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.gray[300],
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    backgroundColor: Colors.primary,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.gray[300],
  },
});
