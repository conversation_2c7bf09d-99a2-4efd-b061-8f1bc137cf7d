/**
 * Trip Completion Screen for SheMove Driver App
 * Displays trip summary, fare breakdown, and handles passenger rating
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { ActiveTrip } from '../../services/ActiveTripService';
import Colors from '../../constants/Colors';

interface TripCompletionScreenProps {
  supabase: any; // SupabaseClient type
}

interface TripSummary {
  trip: ActiveTrip;
  earnings: {
    base_fare: number;
    distance_fare: number;
    time_fare: number;
    surge_multiplier: number;
    total_fare: number;
    commission: number;
    driver_earnings: number;
    tips?: number;
  };
  route_summary: {
    total_distance_km: number;
    total_duration_minutes: number;
    pickup_time: string;
    dropoff_time: string;
  };
}

export default function TripCompletionScreen({ supabase }: TripCompletionScreenProps) {
  const navigation = useNavigation();
  const route = useRoute();
  
  // Get completed trip data from route params
  const { tripData } = route.params as { tripData: ActiveTrip };
  
  const [tripSummary, setTripSummary] = useState<TripSummary | null>(null);
  const [passengerRating, setPassengerRating] = useState<number>(5);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    loadTripSummary();
  }, []);

  const loadTripSummary = async () => {
    try {
      // In a real implementation, you would fetch detailed trip summary from the backend
      // For now, we'll calculate it based on the trip data
      
      const baseFare = 15.00; // Base fare in ZAR
      const perKmRate = 8.50; // Per km rate
      const perMinuteRate = 1.20; // Per minute rate
      const commissionRate = 0.20; // 20% commission
      
      const distanceFare = (tripData.actual_distance_km || tripData.distance_km) * perKmRate;
      const timeFare = (tripData.actual_duration_minutes || tripData.estimated_duration_minutes) * perMinuteRate;
      const totalFare = tripData.actual_fare || (baseFare + distanceFare + timeFare);
      const commission = totalFare * commissionRate;
      const driverEarnings = totalFare - commission;

      const summary: TripSummary = {
        trip: tripData,
        earnings: {
          base_fare: baseFare,
          distance_fare: distanceFare,
          time_fare: timeFare,
          surge_multiplier: 1.0,
          total_fare: totalFare,
          commission: commission,
          driver_earnings: driverEarnings,
          tips: 0
        },
        route_summary: {
          total_distance_km: tripData.actual_distance_km || tripData.distance_km,
          total_duration_minutes: tripData.actual_duration_minutes || tripData.estimated_duration_minutes,
          pickup_time: tripData.started_at || tripData.accepted_at!,
          dropoff_time: tripData.completed_at!
        }
      };

      setTripSummary(summary);
    } catch (error) {
      console.error('Error loading trip summary:', error);
      Alert.alert('Error', 'Failed to load trip summary');
    } finally {
      setIsLoading(false);
    }
  };

  const submitRating = async () => {
    if (!tripSummary || isSubmitting) return;

    setIsSubmitting(true);
    try {
      // Submit passenger rating
      const { error } = await supabase
        .from('trip_ratings')
        .upsert({
          trip_id: tripSummary.trip.id,
          driver_id: tripSummary.trip.driver_id,
          passenger_id: tripSummary.trip.passenger_id,
          passenger_rating: passengerRating,
          rated_by: 'driver',
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to submit rating:', error);
        Alert.alert('Error', 'Failed to submit rating');
        return;
      }

      Alert.alert(
        'Trip Complete!',
        `Thank you for completing this trip. You earned R${tripSummary.earnings.driver_earnings.toFixed(2)}!`,
        [
          {
            text: 'Continue',
            onPress: () => navigation.navigate('Home')
          }
        ]
      );
    } catch (error) {
      console.error('Error submitting rating:', error);
      Alert.alert('Error', 'Failed to submit rating');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStarRating = () => {
    return (
      <View style={styles.ratingContainer}>
        <Text style={styles.ratingLabel}>Rate your passenger:</Text>
        <View style={styles.starsContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => setPassengerRating(star)}
              style={styles.starButton}
            >
              <Ionicons
                name={star <= passengerRating ? "star" : "star-outline"}
                size={32}
                color={star <= passengerRating ? Colors.warning : Colors.gray[300]}
              />
            </TouchableOpacity>
          ))}
        </View>
        <Text style={styles.ratingText}>
          {passengerRating === 5 ? 'Excellent' :
           passengerRating === 4 ? 'Good' :
           passengerRating === 3 ? 'Average' :
           passengerRating === 2 ? 'Poor' : 'Very Poor'}
        </Text>
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading trip summary...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!tripSummary) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load trip summary</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={loadTripSummary}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.successIcon}>
            <Ionicons name="checkmark-circle" size={64} color={Colors.success} />
          </View>
          <Text style={styles.headerTitle}>Trip Completed!</Text>
          <Text style={styles.headerSubtitle}>
            You earned R{tripSummary.earnings.driver_earnings.toFixed(2)}
          </Text>
        </View>

        {/* Trip Summary */}
        <View style={styles.summaryCard}>
          <Text style={styles.cardTitle}>Trip Summary</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Passenger</Text>
            <Text style={styles.summaryValue}>{tripSummary.trip.passenger_info.name}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Distance</Text>
            <Text style={styles.summaryValue}>
              {tripSummary.route_summary.total_distance_km.toFixed(1)} km
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Duration</Text>
            <Text style={styles.summaryValue}>
              {Math.floor(tripSummary.route_summary.total_duration_minutes / 60)}h{' '}
              {tripSummary.route_summary.total_duration_minutes % 60}m
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Pickup Time</Text>
            <Text style={styles.summaryValue}>
              {new Date(tripSummary.route_summary.pickup_time).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Dropoff Time</Text>
            <Text style={styles.summaryValue}>
              {new Date(tripSummary.route_summary.dropoff_time).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Text>
          </View>
        </View>

        {/* Fare Breakdown */}
        <View style={styles.fareCard}>
          <Text style={styles.cardTitle}>Fare Breakdown</Text>
          
          <View style={styles.fareRow}>
            <Text style={styles.fareLabel}>Base Fare</Text>
            <Text style={styles.fareValue}>R{tripSummary.earnings.base_fare.toFixed(2)}</Text>
          </View>
          
          <View style={styles.fareRow}>
            <Text style={styles.fareLabel}>Distance ({tripSummary.route_summary.total_distance_km.toFixed(1)} km)</Text>
            <Text style={styles.fareValue}>R{tripSummary.earnings.distance_fare.toFixed(2)}</Text>
          </View>
          
          <View style={styles.fareRow}>
            <Text style={styles.fareLabel}>Time ({tripSummary.route_summary.total_duration_minutes} min)</Text>
            <Text style={styles.fareValue}>R{tripSummary.earnings.time_fare.toFixed(2)}</Text>
          </View>
          
          <View style={styles.fareDivider} />
          
          <View style={styles.fareRow}>
            <Text style={styles.fareTotalLabel}>Total Fare</Text>
            <Text style={styles.fareTotalValue}>R{tripSummary.earnings.total_fare.toFixed(2)}</Text>
          </View>
          
          <View style={styles.fareRow}>
            <Text style={styles.fareLabel}>SheMove Commission (20%)</Text>
            <Text style={styles.fareValue}>-R{tripSummary.earnings.commission.toFixed(2)}</Text>
          </View>
          
          <View style={styles.fareDivider} />
          
          <View style={styles.fareRow}>
            <Text style={styles.fareEarningsLabel}>Your Earnings</Text>
            <Text style={styles.fareEarningsValue}>R{tripSummary.earnings.driver_earnings.toFixed(2)}</Text>
          </View>
        </View>

        {/* Passenger Rating */}
        <View style={styles.ratingCard}>
          {renderStarRating()}
        </View>

        {/* Complete Button */}
        <TouchableOpacity
          style={[styles.completeButton, isSubmitting && styles.completeButtonDisabled]}
          onPress={submitRating}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <ActivityIndicator size="small" color={Colors.white} />
          ) : (
            <Text style={styles.completeButtonText}>Complete Trip</Text>
          )}
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    backgroundColor: Colors.primary,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    padding: 32,
  },
  successIcon: {
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 18,
    color: Colors.success,
    fontWeight: '600',
  },
  summaryCard: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fareCard: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ratingCard: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  summaryValue: {
    fontSize: 14,
    color: Colors.text.primary,
    fontWeight: '500',
  },
  fareRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  fareLabel: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  fareValue: {
    fontSize: 14,
    color: Colors.text.primary,
  },
  fareDivider: {
    height: 1,
    backgroundColor: Colors.gray[200],
    marginVertical: 8,
  },
  fareTotalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  fareTotalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  fareEarningsLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.success,
  },
  fareEarningsValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.success,
  },
  ratingContainer: {
    alignItems: 'center',
  },
  ratingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text.primary,
    marginBottom: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  starButton: {
    padding: 4,
  },
  ratingText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginTop: 8,
  },
  completeButton: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.primary,
    borderRadius: 12,
    alignItems: 'center',
  },
  completeButtonDisabled: {
    backgroundColor: Colors.gray[400],
  },
  completeButtonText: {
    color: Colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
