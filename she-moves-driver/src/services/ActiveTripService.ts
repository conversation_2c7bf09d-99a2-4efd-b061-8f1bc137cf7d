/**
 * Active Trip Service for SheMove Driver App
 * Manages trip lifecycle from acceptance to completion
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { LocationUpdate } from './DriverStatusService';

export type TripStatus = 'accepted' | 'en_route' | 'arrived' | 'in_progress' | 'completed' | 'cancelled';

export interface ActiveTrip {
  id: string;
  passenger_id: string;
  driver_id: string;
  status: TripStatus;
  pickup_location: {
    lat: number;
    lng: number;
    address: string;
  };
  destination_location: {
    lat: number;
    lng: number;
    address: string;
  };
  passenger_info: {
    name: string;
    phone?: string;
    rating: number;
  };
  fare_estimate: number;
  distance_km: number;
  estimated_duration_minutes: number;
  created_at: string;
  accepted_at?: string;
  started_at?: string;
  completed_at?: string;
  actual_fare?: number;
  actual_distance_km?: number;
  actual_duration_minutes?: number;
}

export interface TripStateUpdate {
  trip_id: string;
  status: TripStatus;
  location?: LocationUpdate;
  notes?: string;
  timestamp: string;
}

export interface TripUpdateResult {
  success: boolean;
  error?: string;
  trip?: ActiveTrip;
}

export interface TripCompletionData {
  final_location: LocationUpdate;
  actual_distance_km: number;
  actual_duration_minutes: number;
  final_fare: number;
  driver_notes?: string;
}

export class ActiveTripService {
  private supabase: SupabaseClient;
  private driverId: string | null = null;
  private currentTrip: ActiveTrip | null = null;
  private tripUpdateCallbacks: ((trip: ActiveTrip) => void)[] = [];
  private tripCompletedCallbacks: ((trip: ActiveTrip) => void)[] = [];

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Initialize the service with driver ID
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Get driver profile
      const { data: driverProfile, error } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        console.error('Failed to get driver profile:', error);
        return false;
      }

      this.driverId = driverProfile.id;
      
      // Load any active trip
      await this.loadActiveTrip();
      
      return true;
    } catch (error) {
      console.error('Failed to initialize ActiveTripService:', error);
      return false;
    }
  }

  /**
   * Start a trip (driver en route to pickup)
   */
  async startTrip(tripId: string, currentLocation: LocationUpdate): Promise<TripUpdateResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      // Update trip status to en_route
      const { data, error } = await this.supabase
        .from('trips')
        .update({
          status: 'en_route',
          driver_location: `(${currentLocation.lng},${currentLocation.lat})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', tripId);

      if (error) {
        console.error('Failed to start trip:', error);
        return { success: false, error: 'Failed to update trip status' };
      }

      // Load updated trip data
      const trip = await this.loadTripData(tripId);
      if (!trip) {
        return { success: false, error: 'Failed to load trip data' };
      }

      this.currentTrip = trip;
      this.notifyTripUpdate(trip);

      return { success: true, trip };
    } catch (error) {
      console.error('Error starting trip:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Mark arrival at pickup location
   */
  async arriveAtPickup(currentLocation: LocationUpdate): Promise<TripUpdateResult> {
    if (!this.currentTrip) {
      return { success: false, error: 'No active trip' };
    }

    try {
      const { data, error } = await this.supabase
        .from('trips')
        .update({
          status: 'arrived',
          driver_location: `(${currentLocation.lng},${currentLocation.lat})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentTrip.id);

      if (error) {
        console.error('Failed to mark arrival:', error);
        return { success: false, error: 'Failed to update trip status' };
      }

      // Update current trip
      this.currentTrip.status = 'arrived';
      this.notifyTripUpdate(this.currentTrip);

      return { success: true, trip: this.currentTrip };
    } catch (error) {
      console.error('Error marking arrival:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Start the trip (passenger picked up)
   */
  async pickupPassenger(currentLocation: LocationUpdate): Promise<TripUpdateResult> {
    if (!this.currentTrip) {
      return { success: false, error: 'No active trip' };
    }

    try {
      const { data, error } = await this.supabase
        .from('trips')
        .update({
          status: 'in_progress',
          started_at: new Date().toISOString(),
          driver_location: `(${currentLocation.lng},${currentLocation.lat})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentTrip.id);

      if (error) {
        console.error('Failed to start trip:', error);
        return { success: false, error: 'Failed to update trip status' };
      }

      // Update current trip
      this.currentTrip.status = 'in_progress';
      this.currentTrip.started_at = new Date().toISOString();
      this.notifyTripUpdate(this.currentTrip);

      return { success: true, trip: this.currentTrip };
    } catch (error) {
      console.error('Error starting trip:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Complete the trip
   */
  async completeTrip(completionData: TripCompletionData): Promise<TripUpdateResult> {
    if (!this.currentTrip) {
      return { success: false, error: 'No active trip' };
    }

    try {
      const { data, error } = await this.supabase
        .from('trips')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          end_location: `(${completionData.final_location.lng},${completionData.final_location.lat})`,
          actual_distance_km: completionData.actual_distance_km,
          actual_duration_minutes: completionData.actual_duration_minutes,
          actual_fare: completionData.final_fare,
          driver_notes: completionData.driver_notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentTrip.id);

      if (error) {
        console.error('Failed to complete trip:', error);
        return { success: false, error: 'Failed to complete trip' };
      }

      // Update current trip
      this.currentTrip.status = 'completed';
      this.currentTrip.completed_at = new Date().toISOString();
      this.currentTrip.actual_fare = completionData.final_fare;
      this.currentTrip.actual_distance_km = completionData.actual_distance_km;
      this.currentTrip.actual_duration_minutes = completionData.actual_duration_minutes;

      // Notify completion
      this.notifyTripCompleted(this.currentTrip);
      
      // Clear current trip
      const completedTrip = this.currentTrip;
      this.currentTrip = null;

      return { success: true, trip: completedTrip };
    } catch (error) {
      console.error('Error completing trip:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Cancel the trip
   */
  async cancelTrip(reason: string, currentLocation: LocationUpdate): Promise<TripUpdateResult> {
    if (!this.currentTrip) {
      return { success: false, error: 'No active trip' };
    }

    try {
      const { data, error } = await this.supabase
        .from('trips')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
          cancelled_by: 'driver',
          cancellation_reason: reason,
          driver_location: `(${currentLocation.lng},${currentLocation.lat})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.currentTrip.id);

      if (error) {
        console.error('Failed to cancel trip:', error);
        return { success: false, error: 'Failed to cancel trip' };
      }

      // Update current trip
      this.currentTrip.status = 'cancelled';
      
      // Clear current trip
      const cancelledTrip = this.currentTrip;
      this.currentTrip = null;

      return { success: true, trip: cancelledTrip };
    } catch (error) {
      console.error('Error cancelling trip:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Get current active trip
   */
  getCurrentTrip(): ActiveTrip | null {
    return this.currentTrip;
  }

  /**
   * Subscribe to trip updates
   */
  onTripUpdate(callback: (trip: ActiveTrip) => void): () => void {
    this.tripUpdateCallbacks.push(callback);
    return () => {
      const index = this.tripUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.tripUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Subscribe to trip completion
   */
  onTripCompleted(callback: (trip: ActiveTrip) => void): () => void {
    this.tripCompletedCallbacks.push(callback);
    return () => {
      const index = this.tripCompletedCallbacks.indexOf(callback);
      if (index > -1) {
        this.tripCompletedCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.tripUpdateCallbacks = [];
    this.tripCompletedCallbacks = [];
    this.currentTrip = null;
    this.driverId = null;
  }

  // Private helper methods
  private async loadActiveTrip(): Promise<void> {
    if (!this.driverId) return;

    try {
      const { data, error } = await this.supabase
        .from('trips')
        .select(`
          *,
          passenger:profiles!trips_passenger_id_fkey(full_name, phone),
          passenger_ratings:trip_ratings!trip_ratings_trip_id_fkey(passenger_rating)
        `)
        .eq('driver_id', this.driverId)
        .in('status', ['accepted', 'en_route', 'arrived', 'in_progress'])
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (data && !error) {
        this.currentTrip = this.mapTripData(data);
      }
    } catch (error) {
      console.error('Error loading active trip:', error);
    }
  }

  private async loadTripData(tripId: string): Promise<ActiveTrip | null> {
    try {
      const { data, error } = await this.supabase
        .from('trips')
        .select(`
          *,
          passenger:profiles!trips_passenger_id_fkey(full_name, phone),
          passenger_ratings:trip_ratings!trip_ratings_trip_id_fkey(passenger_rating)
        `)
        .eq('id', tripId)
        .single();

      if (error || !data) {
        console.error('Failed to load trip data:', error);
        return null;
      }

      return this.mapTripData(data);
    } catch (error) {
      console.error('Error loading trip data:', error);
      return null;
    }
  }

  private mapTripData(data: any): ActiveTrip {
    return {
      id: data.id,
      passenger_id: data.passenger_id,
      driver_id: data.driver_id,
      status: data.status,
      pickup_location: {
        lat: data.pickup_lat,
        lng: data.pickup_lng,
        address: data.pickup_address
      },
      destination_location: {
        lat: data.destination_lat,
        lng: data.destination_lng,
        address: data.destination_address
      },
      passenger_info: {
        name: data.passenger?.full_name || 'Passenger',
        phone: data.passenger?.phone,
        rating: data.passenger_ratings?.[0]?.passenger_rating || 5.0
      },
      fare_estimate: data.fare_estimate,
      distance_km: data.distance_km,
      estimated_duration_minutes: data.estimated_duration_minutes,
      created_at: data.created_at,
      accepted_at: data.accepted_at,
      started_at: data.started_at,
      completed_at: data.completed_at,
      actual_fare: data.actual_fare,
      actual_distance_km: data.actual_distance_km,
      actual_duration_minutes: data.actual_duration_minutes
    };
  }

  private notifyTripUpdate(trip: ActiveTrip): void {
    this.tripUpdateCallbacks.forEach(callback => {
      try {
        callback(trip);
      } catch (error) {
        console.error('Error in trip update callback:', error);
      }
    });
  }

  private notifyTripCompleted(trip: ActiveTrip): void {
    this.tripCompletedCallbacks.forEach(callback => {
      try {
        callback(trip);
      } catch (error) {
        console.error('Error in trip completed callback:', error);
      }
    });
  }
}
