/**
 * Driver Status Management Service for SheMove Driver App
 * Handles online/offline status, location tracking, and session management
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { DriverStatus, DriverProfile, Coordinates, DriverAvailability } from '../../shared/types';

export interface DriverSession {
  id: string;
  driver_id: string;
  status?: DriverStatus;
  session_start: string;
  session_end?: string;
  initial_location?: string;
  final_location?: string;
  total_online_minutes: number;
  location_updates_count: number;
  trips_completed: number;
  earnings_session: number;
}

export interface DriverStats {
  today_online_minutes: number;
  today_trips: number;
  today_earnings: number;
  week_online_minutes: number;
  week_trips: number;
  week_earnings: number;
  acceptance_rate: number;
  cancellation_rate: number;
  average_rating: number;
}

export interface LocationUpdate {
  lat: number;
  lng: number;
  heading?: number;
  speed?: number;
  accuracy?: number;
  timestamp: string;
}

export interface StatusChangeResult {
  success: boolean;
  error?: string;
  session?: DriverSession | null;
  availability?: DriverAvailability;
  message?: string;
}

export class DriverStatusService {
  private supabase: SupabaseClient;
  private driverId: string | null = null;
  private currentSession: DriverSession | null = null;
  private locationUpdateInterval: NodeJS.Timeout | null = null;
  private statusChangeCallbacks: ((status: DriverStatus) => void)[] = [];

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Initialize the service with driver ID
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Get driver profile
      const { data: driverProfile, error } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        // Only log unexpected errors, not missing driver profiles during onboarding
        if (error && error.code !== 'PGRST116') {
          console.error('Failed to get driver profile:', error);
        }
        return false;
      }

      this.driverId = driverProfile.id;
      
      // Load current session if exists
      await this.loadCurrentSession();
      
      return true;
    } catch (error) {
      console.error('Failed to initialize DriverStatusService:', error);
      return false;
    }
  }

  /**
   * Go online - start accepting trip requests
   */
  async goOnline(location: LocationUpdate): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      // Check if driver is verified
      const { data: driver, error: driverError } = await this.supabase
        .from('drivers')
        .select('verification_status, is_online')
        .eq('id', this.driverId)
        .single();

      if (driverError || !driver) {
        return { success: false, error: 'Failed to get driver information' };
      }

      if (driver.verification_status !== 'approved') {
        return { success: false, error: 'Driver not approved for driving' };
      }

      if (driver.is_online) {
        // Driver is already online - sync the UI state
        console.log('Driver is already online, syncing UI state');

        // Get current session if exists
        await this.loadCurrentSession();

        // Notify callbacks to update UI
        this.notifyStatusChange('online');

        return {
          success: true,
          session: this.currentSession,
          message: 'Already online - UI synchronized'
        };
      }

      // Start new session
      const sessionResult = await this.startSession(location);
      if (!sessionResult.success) {
        return sessionResult;
      }

      // Update driver status
      const { error: updateError } = await this.supabase
        .from('drivers')
        .update({
          is_online: true,
          current_location: `(${location.lng},${location.lat})`,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.driverId);

      if (updateError) {
        console.error('Failed to update driver online status:', updateError);
        return { success: false, error: 'Failed to update driver status' };
      }

      // Update availability
      const availabilityResult = await this.updateAvailability('online', location);
      if (!availabilityResult.success) {
        return availabilityResult;
      }

      // Start location tracking
      this.startLocationTracking();

      // Notify callbacks
      this.notifyStatusChange('online');

      return {
        success: true,
        session: this.currentSession!,
        availability: availabilityResult.availability
      };

    } catch (error) {
      console.error('Failed to go online:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Go offline - stop accepting trip requests
   */
  async goOffline(): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      // Check if driver has active trip
      const { data: activeTrip, error: tripError } = await this.supabase
        .from('trips')
        .select('id, status')
        .eq('driver_id', this.driverId)
        .in('status', ['accepted', 'in_progress'])
        .single();

      if (tripError && tripError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Failed to check active trips:', tripError);
        return { success: false, error: 'Failed to check active trips' };
      }

      if (activeTrip) {
        return { success: false, error: 'Cannot go offline with active trip' };
      }

      // End current session
      const sessionResult = await this.endSession();
      if (!sessionResult.success) {
        return sessionResult;
      }

      // Update driver status
      const { error: updateError } = await this.supabase
        .from('drivers')
        .update({
          is_online: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.driverId);

      if (updateError) {
        console.error('Failed to update driver offline status:', updateError);
        return { success: false, error: 'Failed to update driver status' };
      }

      // Update availability
      const availabilityResult = await this.updateAvailability('offline');
      if (!availabilityResult.success) {
        return availabilityResult;
      }

      // Stop location tracking
      this.stopLocationTracking();

      // Notify callbacks
      this.notifyStatusChange('offline');

      return {
        success: true,
        session: this.currentSession,
        availability: availabilityResult.availability
      };

    } catch (error) {
      console.error('Failed to go offline:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Set driver status to busy (during trip)
   */
  async setBusy(): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const availabilityResult = await this.updateAvailability('busy');
      if (!availabilityResult.success) {
        return availabilityResult;
      }

      this.notifyStatusChange('busy');

      return {
        success: true,
        availability: availabilityResult.availability
      };
    } catch (error) {
      console.error('Failed to set busy status:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Set driver status to break
   */
  async setBreak(): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const availabilityResult = await this.updateAvailability('break');
      if (!availabilityResult.success) {
        return availabilityResult;
      }

      this.notifyStatusChange('break');

      return {
        success: true,
        availability: availabilityResult.availability
      };
    } catch (error) {
      console.error('Failed to set break status:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Update driver location
   */
  async updateLocation(location: LocationUpdate): Promise<boolean> {
    if (!this.driverId) {
      return false;
    }

    try {
      // Use the database function for location updates
      const { data, error } = await this.supabase.rpc('update_driver_location', {
        driver_id_param: this.driverId,
        lat: location.lat,
        lng: location.lng,
        heading_param: location.heading || null,
        speed_param: location.speed || null,
        accuracy_param: location.accuracy || null
      });

      if (error) {
        console.error('Failed to update driver location:', error);
        return false;
      }

      // Update session location count
      if (this.currentSession) {
        this.currentSession.location_updates_count += 1;
      }

      return data === true;
    } catch (error) {
      console.error('Error updating driver location:', error);
      return false;
    }
  }

  /**
   * Get current driver session
   */
  getCurrentSession(): DriverSession | null {
    return this.currentSession;
  }

  /**
   * Get current driver status
   */
  async getCurrentStatus(): Promise<DriverStatus | null> {
    if (!this.driverId) {
      return null;
    }

    try {
      // Check both driver table and availability table for consistency
      const [driverResult, availabilityResult] = await Promise.all([
        this.supabase
          .from('drivers')
          .select('is_online')
          .eq('id', this.driverId)
          .single(),
        this.supabase
          .from('driver_availability')
          .select('status')
          .eq('driver_id', this.driverId)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle()
      ]);

      const driver = driverResult.data;
      const availability = availabilityResult.data;

      // If driver is marked as offline in drivers table, they're definitely offline
      if (!driver?.is_online) {
        return 'offline';
      }

      // If driver is online but no availability record, assume online
      if (!availability) {
        return 'online';
      }

      // Return the availability status
      return availability.status;
    } catch (error) {
      console.error('Failed to get current status:', error);
      return 'offline'; // Default to offline on error
    }
  }

  /**
   * Get driver statistics for a specific date
   */
  async getDriverStats(date: Date = new Date()): Promise<DriverStats | null> {
    if (!this.driverId) {
      return null;
    }

    try {
      // Calculate days back from the provided date to today
      const today = new Date();
      const daysDiff = Math.ceil((today.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
      const daysBack = Math.max(1, daysDiff); // At least 1 day

      // Use the correct database function to get driver performance metrics
      const { data, error } = await this.supabase.rpc('get_driver_performance_metrics', {
        driver_id_param: this.driverId,
        days_back: daysBack
      });

      if (error) {
        console.error('Failed to get driver stats:', error);
        return null;
      }

      if (!data || data.length === 0) {
        return {
          today_online_minutes: 0,
          today_trips: 0,
          today_earnings: 0,
          week_online_minutes: 0,
          week_trips: 0,
          week_earnings: 0,
          acceptance_rate: 100,
          cancellation_rate: 0,
          average_rating: 5.0
        };
      }

      const stats = data[0];

      // Get today's specific stats (1 day back)
      const { data: todayData } = await this.supabase.rpc('get_driver_performance_metrics', {
        driver_id_param: this.driverId,
        days_back: 1
      });

      // Get week's stats (7 days back)
      const { data: weekData } = await this.supabase.rpc('get_driver_performance_metrics', {
        driver_id_param: this.driverId,
        days_back: 7
      });

      const todayStats = todayData?.[0] || stats;
      const weekStats = weekData?.[0] || stats;

      return {
        today_online_minutes: Math.round((todayStats.online_hours || 0) * 60),
        today_trips: todayStats.total_trips || 0,
        today_earnings: Math.round((todayStats.earnings_per_hour || 0) * (todayStats.online_hours || 0) * 100) / 100,
        week_online_minutes: Math.round((weekStats.online_hours || 0) * 60),
        week_trips: weekStats.total_trips || 0,
        week_earnings: Math.round((weekStats.earnings_per_hour || 0) * (weekStats.online_hours || 0) * 100) / 100,
        acceptance_rate: Math.round((stats.acceptance_rate || 100) * 100) / 100,
        cancellation_rate: Math.round((stats.cancellation_rate || 0) * 100) / 100,
        average_rating: Math.round((stats.average_rating || 5.0) * 100) / 100
      };
    } catch (error) {
      console.error('Error getting driver stats:', error);
      return null;
    }
  }

  /**
   * Subscribe to status changes
   */
  onStatusChange(callback: (status: DriverStatus) => void): () => void {
    this.statusChangeCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      const index = this.statusChangeCallbacks.indexOf(callback);
      if (index > -1) {
        this.statusChangeCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Load current session from database
   */
  private async loadCurrentSession(): Promise<void> {
    if (!this.driverId) return;

    try {
      const { data, error } = await this.supabase
        .from('driver_sessions')
        .select('*')
        .eq('driver_id', this.driverId)
        .is('session_end', null)
        .order('session_start', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (!error && data) {
        this.currentSession = {
          id: data.id,
          driver_id: data.driver_id,
          session_start: data.session_start,
          session_end: data.session_end,
          initial_location: data.initial_location,
          final_location: data.final_location,
          total_online_minutes: data.total_online_minutes || 0,
          location_updates_count: data.location_updates_count || 0,
          trips_completed: data.trips_completed || 0,
          earnings_session: data.earnings_session || 0
        };
      }
    } catch (error) {
      console.error('Error loading current session:', error);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopLocationTracking();
    this.statusChangeCallbacks = [];
    this.currentSession = null;
    this.driverId = null;
  }

  // Private helper methods

  private async startSession(location: LocationUpdate): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const { data, error } = await this.supabase
        .from('driver_sessions')
        .insert({
          driver_id: this.driverId,
          session_start: new Date().toISOString(),
          initial_location: `(${location.lng},${location.lat})`,
          total_online_minutes: 0,
          location_updates_count: 1,
          trips_completed: 0,
          earnings_session: 0
        })
        .select()
        .single();

      if (error) {
        console.error('Failed to start session:', error);
        return { success: false, error: 'Failed to start session' };
      }

      this.currentSession = {
        id: data.id,
        driver_id: data.driver_id,
        status: 'online',
        session_start: data.session_start,
        total_online_minutes: 0,
        location_updates_count: 1,
        trips_completed: 0,
        earnings_session: 0
      };

      return { success: true, session: this.currentSession };
    } catch (error) {
      console.error('Error starting session:', error);
      return { success: false, error: 'Unexpected error starting session' };
    }
  }

  private async endSession(): Promise<StatusChangeResult> {
    if (!this.currentSession) {
      return { success: true }; // No active session to end
    }

    try {
      const sessionEnd = new Date().toISOString();
      const sessionStart = new Date(this.currentSession.session_start);
      const totalMinutes = Math.floor((new Date().getTime() - sessionStart.getTime()) / (1000 * 60));

      const { error } = await this.supabase
        .from('driver_sessions')
        .update({
          session_end: sessionEnd,
          total_online_minutes: totalMinutes,
          location_updates_count: this.currentSession.location_updates_count,
          trips_completed: this.currentSession.trips_completed,
          earnings_session: this.currentSession.earnings_session
        })
        .eq('id', this.currentSession.id);

      if (error) {
        console.error('Failed to end session:', error);
        return { success: false, error: 'Failed to end session' };
      }

      this.currentSession.session_end = sessionEnd;
      this.currentSession.total_online_minutes = totalMinutes;

      return { success: true, session: this.currentSession };
    } catch (error) {
      console.error('Error ending session:', error);
      return { success: false, error: 'Unexpected error ending session' };
    }
  }

  private async updateAvailability(status: DriverStatus, location?: LocationUpdate): Promise<StatusChangeResult> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const updateData: any = {
        driver_id: this.driverId,
        status,
        updated_at: new Date().toISOString()
      };

      if (location) {
        updateData.last_location_update = new Date().toISOString();
      }

      // Insert new availability record
      const { data, error } = await this.supabase
        .from('driver_availability')
        .insert(updateData)
        .select()
        .single();

      if (error) {
        console.error('Failed to update availability:', error);
        return { success: false, error: 'Failed to update availability' };
      }

      return {
        success: true,
        availability: {
          id: data.id,
          driver_id: data.driver_id,
          status: data.status,
          available_from: data.available_from,
          available_until: data.available_until,
          preferred_areas: data.preferred_areas,
          max_distance_km: data.max_distance_km,
          last_location_update: data.last_location_update,
          created_at: data.created_at,
          updated_at: data.updated_at
        }
      };
    } catch (error) {
      console.error('Error updating availability:', error);
      return { success: false, error: 'Unexpected error updating availability' };
    }
  }

  private startLocationTracking(): void {
    // Clear any existing interval
    this.stopLocationTracking();

    // Location tracking is now handled by LocationService
    // This method is kept for compatibility and session tracking
    console.log('Location tracking started - handled by LocationService');
  }

  private stopLocationTracking(): void {
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = null;
    }
  }

  private notifyStatusChange(status: DriverStatus): void {
    this.statusChangeCallbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Error in status change callback:', error);
      }
    });
  }
}
