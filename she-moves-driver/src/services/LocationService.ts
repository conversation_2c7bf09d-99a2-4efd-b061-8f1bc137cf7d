/**
 * Location Service for SheMove Driver App
 * Handles GPS tracking, permissions, and background location updates
 */

import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import { Platform } from 'react-native';
import { LocationUpdate } from './DriverStatusService';

export interface LocationPermissionStatus {
  foreground: boolean;
  background: boolean;
  canAskAgain: boolean;
  message?: string;
}

export interface LocationServiceConfig {
  accuracy: Location.LocationAccuracy;
  timeInterval: number; // milliseconds
  distanceInterval: number; // meters
  enableHighAccuracy: boolean;
  backgroundTaskName: string;
}

export interface LocationServiceCallbacks {
  onLocationUpdate?: (location: LocationUpdate) => void;
  onLocationError?: (error: Error) => void;
  onPermissionDenied?: (status: LocationPermissionStatus) => void;
}

const BACKGROUND_LOCATION_TASK = 'background-location-task';
const DEFAULT_CONFIG: LocationServiceConfig = {
  accuracy: Location.LocationAccuracy.High,
  timeInterval: 30000, // 30 seconds
  distanceInterval: 10, // 10 meters
  enableHighAccuracy: true,
  backgroundTaskName: BACKGROUND_LOCATION_TASK,
};

export class LocationService {
  private static instance: LocationService | null = null;
  private config: LocationServiceConfig;
  private callbacks: LocationServiceCallbacks = {};
  private isTracking: boolean = false;
  private foregroundSubscription: Location.LocationSubscription | null = null;
  private lastKnownLocation: LocationUpdate | null = null;
  private locationHistory: LocationUpdate[] = [];
  private maxHistorySize: number = 100;

  private constructor(config: Partial<LocationServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupBackgroundTask();
  }

  /**
   * Get singleton instance of LocationService
   */
  static getInstance(config?: Partial<LocationServiceConfig>): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService(config);
    }
    return LocationService.instance;
  }

  /**
   * Initialize location service and request permissions
   */
  async initialize(): Promise<LocationPermissionStatus> {
    try {
      const permissionStatus = await this.requestPermissions();
      
      if (!permissionStatus.foreground) {
        this.callbacks.onPermissionDenied?.(permissionStatus);
        return permissionStatus;
      }

      // Check if location services are enabled
      const providerStatus = await Location.getProviderStatusAsync();
      if (!providerStatus.locationServicesEnabled) {
        const error = new Error('Location services are disabled');
        this.callbacks.onLocationError?.(error);
        throw error;
      }

      return permissionStatus;
    } catch (error) {
      console.error('Failed to initialize LocationService:', error);
      this.callbacks.onLocationError?.(error as Error);
      throw error;
    }
  }

  /**
   * Request location permissions (foreground and background)
   */
  async requestPermissions(): Promise<LocationPermissionStatus> {
    try {
      // Request foreground permission first
      const foregroundStatus = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus.status !== 'granted') {
        return {
          foreground: false,
          background: false,
          canAskAgain: foregroundStatus.canAskAgain,
          message: 'Foreground location permission denied'
        };
      }

      // Request background permission
      const backgroundStatus = await Location.requestBackgroundPermissionsAsync();
      
      return {
        foreground: true,
        background: backgroundStatus.status === 'granted',
        canAskAgain: backgroundStatus.canAskAgain,
        message: backgroundStatus.status !== 'granted' 
          ? 'Background location permission denied - some features may be limited'
          : undefined
      };
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return {
        foreground: false,
        background: false,
        canAskAgain: false,
        message: 'Failed to request location permissions'
      };
    }
  }

  /**
   * Get current location permission status
   */
  async getPermissionStatus(): Promise<LocationPermissionStatus> {
    try {
      const foregroundStatus = await Location.getForegroundPermissionsAsync();
      const backgroundStatus = await Location.getBackgroundPermissionsAsync();
      
      return {
        foreground: foregroundStatus.status === 'granted',
        background: backgroundStatus.status === 'granted',
        canAskAgain: foregroundStatus.canAskAgain && backgroundStatus.canAskAgain,
      };
    } catch (error) {
      console.error('Error getting permission status:', error);
      return {
        foreground: false,
        background: false,
        canAskAgain: false,
        message: 'Failed to get permission status'
      };
    }
  }

  /**
   * Start location tracking (foreground)
   */
  async startTracking(): Promise<boolean> {
    if (this.isTracking) {
      console.log('Location tracking already started');
      return true;
    }

    try {
      const permissionStatus = await this.getPermissionStatus();
      if (!permissionStatus.foreground) {
        throw new Error('Foreground location permission not granted');
      }

      this.foregroundSubscription = await Location.watchPositionAsync(
        {
          accuracy: this.config.accuracy,
          timeInterval: this.config.timeInterval,
          distanceInterval: this.config.distanceInterval,
        },
        (location) => {
          this.handleLocationUpdate(location);
        }
      );

      this.isTracking = true;
      console.log('Location tracking started');
      return true;
    } catch (error) {
      console.error('Failed to start location tracking:', error);
      this.callbacks.onLocationError?.(error as Error);
      return false;
    }
  }

  /**
   * Stop location tracking (foreground)
   */
  async stopTracking(): Promise<void> {
    if (!this.isTracking) {
      return;
    }

    try {
      if (this.foregroundSubscription) {
        this.foregroundSubscription.remove();
        this.foregroundSubscription = null;
      }

      this.isTracking = false;
      console.log('Location tracking stopped');
    } catch (error) {
      console.error('Error stopping location tracking:', error);
    }
  }

  /**
   * Start background location tracking
   */
  async startBackgroundTracking(): Promise<boolean> {
    try {
      const permissionStatus = await this.getPermissionStatus();
      if (!permissionStatus.background) {
        console.warn('Background location permission not granted');
        return false;
      }

      const isRegistered = await TaskManager.isTaskRegisteredAsync(this.config.backgroundTaskName);
      if (isRegistered) {
        console.log('Background location task already registered');
        return true;
      }

      await Location.startLocationUpdatesAsync(this.config.backgroundTaskName, {
        accuracy: this.config.accuracy,
        timeInterval: this.config.timeInterval,
        distanceInterval: this.config.distanceInterval,
        foregroundService: {
          notificationTitle: 'SheMove Driver',
          notificationBody: 'Tracking your location for trip management',
          notificationColor: '#FFF0FF',
        },
        pausesUpdatesAutomatically: false,
        activityType: Location.LocationActivityType.AutomotiveNavigation,
      });

      console.log('Background location tracking started');
      return true;
    } catch (error) {
      console.error('Failed to start background location tracking:', error);
      this.callbacks.onLocationError?.(error as Error);
      return false;
    }
  }

  /**
   * Stop background location tracking
   */
  async stopBackgroundTracking(): Promise<void> {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(this.config.backgroundTaskName);
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(this.config.backgroundTaskName);
        console.log('Background location tracking stopped');
      }
    } catch (error) {
      console.error('Error stopping background location tracking:', error);
    }
  }

  /**
   * Get current location (one-time)
   */
  async getCurrentLocation(): Promise<LocationUpdate | null> {
    try {
      const permissionStatus = await this.getPermissionStatus();
      if (!permissionStatus.foreground) {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: this.config.accuracy,
        maximumAge: 10000, // 10 seconds
      });

      const locationUpdate = this.convertToLocationUpdate(location);
      this.lastKnownLocation = locationUpdate;
      return locationUpdate;
    } catch (error) {
      console.error('Failed to get current location:', error);
      this.callbacks.onLocationError?.(error as Error);
      return null;
    }
  }

  /**
   * Get last known location
   */
  getLastKnownLocation(): LocationUpdate | null {
    return this.lastKnownLocation;
  }

  /**
   * Get location history
   */
  getLocationHistory(): LocationUpdate[] {
    return [...this.locationHistory];
  }

  /**
   * Clear location history
   */
  clearLocationHistory(): void {
    this.locationHistory = [];
  }

  /**
   * Set callbacks for location events
   */
  setCallbacks(callbacks: LocationServiceCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Check if currently tracking location
   */
  isCurrentlyTracking(): boolean {
    return this.isTracking;
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<LocationServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Cleanup resources
   */
  async cleanup(): void {
    await this.stopTracking();
    await this.stopBackgroundTracking();
    this.callbacks = {};
    this.locationHistory = [];
    this.lastKnownLocation = null;
  }

  // Private helper methods

  /**
   * Handle location updates from GPS
   */
  private handleLocationUpdate(location: Location.LocationObject): void {
    try {
      const locationUpdate = this.convertToLocationUpdate(location);

      // Update last known location
      this.lastKnownLocation = locationUpdate;

      // Add to history
      this.addToLocationHistory(locationUpdate);

      // Notify callback
      this.callbacks.onLocationUpdate?.(locationUpdate);

    } catch (error) {
      console.error('Error handling location update:', error);
      this.callbacks.onLocationError?.(error as Error);
    }
  }

  /**
   * Convert Expo location to our LocationUpdate format
   */
  private convertToLocationUpdate(location: Location.LocationObject): LocationUpdate {
    return {
      lat: location.coords.latitude,
      lng: location.coords.longitude,
      heading: location.coords.heading || undefined,
      speed: location.coords.speed || undefined,
      accuracy: location.coords.accuracy || undefined,
      timestamp: new Date(location.timestamp).toISOString(),
    };
  }

  /**
   * Add location to history with size limit
   */
  private addToLocationHistory(location: LocationUpdate): void {
    this.locationHistory.push(location);

    // Keep only the most recent locations
    if (this.locationHistory.length > this.maxHistorySize) {
      this.locationHistory = this.locationHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Setup background location task
   */
  private setupBackgroundTask(): void {
    TaskManager.defineTask(this.config.backgroundTaskName, ({ data, error }) => {
      if (error) {
        console.error('Background location task error:', error);
        return;
      }

      if (data) {
        const { locations } = data as { locations: Location.LocationObject[] };

        // Process each location update
        locations.forEach((location) => {
          const locationUpdate = this.convertToLocationUpdate(location);

          // Store in history
          this.addToLocationHistory(locationUpdate);

          // Update last known location
          this.lastKnownLocation = locationUpdate;

          // Notify callback if available
          this.callbacks.onLocationUpdate?.(locationUpdate);
        });
      }
    });
  }

  /**
   * Calculate distance between two locations (in meters)
   */
  static calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  /**
   * Calculate bearing between two locations (in degrees)
   */
  static calculateBearing(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const y = Math.sin(Δλ) * Math.cos(φ2);
    const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

    const θ = Math.atan2(y, x);
    return ((θ * 180) / Math.PI + 360) % 360;
  }

  /**
   * Check if location is valid and recent
   */
  static isLocationValid(
    location: LocationUpdate,
    maxAgeMs: number = 300000 // 5 minutes
  ): boolean {
    const now = new Date().getTime();
    const locationTime = new Date(location.timestamp).getTime();
    const age = now - locationTime;

    return age <= maxAgeMs && location.accuracy !== undefined && location.accuracy <= 100;
  }
}
