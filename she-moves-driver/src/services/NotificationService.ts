/**
 * Notification Service for SheMove Driver App
 * Handles push notifications for trip requests, updates, and system messages
 */

import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { SupabaseClient } from '@supabase/supabase-js';

export interface NotificationData {
  type: 'trip_request' | 'trip_update' | 'system_message' | 'earnings_update';
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  priority?: 'default' | 'high' | 'max';
}

export interface NotificationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  message?: string;
}

export interface NotificationCallbacks {
  onNotificationReceived?: (notification: Notifications.Notification) => void;
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void;
  onError?: (error: Error) => void;
}

export class NotificationService {
  private static instance: NotificationService | null = null;
  private supabase: SupabaseClient | null = null;
  private driverId: string | null = null;
  private expoPushToken: string | null = null;
  private callbacks: NotificationCallbacks = {};
  private notificationListener: any = null;
  private responseListener: any = null;

  private constructor() {
    this.setupNotificationHandler();
  }

  /**
   * Get singleton instance of NotificationService
   */
  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Initialize notification service
   */
  async initialize(supabase: SupabaseClient, userId: string): Promise<boolean> {
    try {
      this.supabase = supabase;
      
      // Get driver profile
      const { data: driverProfile, error } = await supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        console.error('Failed to get driver profile:', error);
        return false;
      }

      this.driverId = driverProfile.id;

      // Request permissions and get push token
      const permissionStatus = await this.requestPermissions();
      if (!permissionStatus.granted) {
        console.warn('Notification permissions not granted');
        return false;
      }

      // Get push token
      const token = await this.registerForPushNotifications();
      if (token) {
        this.expoPushToken = token;
        await this.savePushTokenToDatabase(token);
      }

      // Set up listeners
      this.setupNotificationListeners();

      return true;
    } catch (error) {
      console.error('Failed to initialize NotificationService:', error);
      return false;
    }
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<NotificationPermissionStatus> {
    try {
      if (!Device.isDevice) {
        return {
          granted: false,
          canAskAgain: false,
          message: 'Push notifications only work on physical devices'
        };
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        return {
          granted: false,
          canAskAgain: true,
          message: 'Notification permissions are required for trip alerts'
        };
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('trip-requests', {
          name: 'Trip Requests',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#E91E63',
          sound: 'trip_request.wav',
        });

        await Notifications.setNotificationChannelAsync('trip-updates', {
          name: 'Trip Updates',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250],
          lightColor: '#2196F3',
        });

        await Notifications.setNotificationChannelAsync('system', {
          name: 'System Messages',
          importance: Notifications.AndroidImportance.DEFAULT,
        });
      }

      return { granted: true, canAskAgain: true };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return {
        granted: false,
        canAskAgain: false,
        message: 'Failed to request notification permissions'
      };
    }
  }

  /**
   * Register for push notifications and get token
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      });

      return token.data;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Save push token to database
   */
  private async savePushTokenToDatabase(token: string): Promise<void> {
    if (!this.supabase || !this.driverId) return;

    try {
      const { error } = await this.supabase
        .from('drivers')
        .update({
          push_token: token,
          updated_at: new Date().toISOString()
        })
        .eq('id', this.driverId);

      if (error) {
        console.error('Failed to save push token:', error);
      } else {
        console.log('Push token saved successfully');
      }
    } catch (error) {
      console.error('Error saving push token:', error);
    }
  }

  /**
   * Setup notification handler
   */
  private setupNotificationHandler(): void {
    Notifications.setNotificationHandler({
      handleNotification: async (notification) => {
        const notificationData = notification.request.content.data as any;
        
        // Handle different notification types
        switch (notificationData?.type) {
          case 'trip_request':
            return {
              shouldShowAlert: true,
              shouldPlaySound: true,
              shouldSetBadge: true,
              priority: Notifications.AndroidNotificationPriority.MAX,
            };
          case 'trip_update':
            return {
              shouldShowAlert: true,
              shouldPlaySound: true,
              shouldSetBadge: false,
              priority: Notifications.AndroidNotificationPriority.HIGH,
            };
          default:
            return {
              shouldShowAlert: true,
              shouldPlaySound: false,
              shouldSetBadge: false,
              priority: Notifications.AndroidNotificationPriority.DEFAULT,
            };
        }
      },
    });
  }

  /**
   * Setup notification listeners
   */
  private setupNotificationListeners(): void {
    // Listen for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received:', notification);
        this.callbacks.onNotificationReceived?.(notification);
      }
    );

    // Listen for user interactions with notifications
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        this.callbacks.onNotificationResponse?.(response);
      }
    );
  }

  /**
   * Send local notification
   */
  async sendLocalNotification(notificationData: NotificationData): Promise<string | null> {
    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title: notificationData.title,
          body: notificationData.body,
          data: notificationData.data || {},
          sound: notificationData.sound || 'default',
          priority: this.mapPriorityToExpo(notificationData.priority || 'default'),
        },
        trigger: null, // Send immediately
      });

      return identifier;
    } catch (error) {
      console.error('Error sending local notification:', error);
      this.callbacks.onError?.(error as Error);
      return null;
    }
  }

  /**
   * Cancel notification
   */
  async cancelNotification(identifier: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  /**
   * Cancel all notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  /**
   * Get notification badge count
   */
  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  /**
   * Set notification badge count
   */
  async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  /**
   * Clear badge count
   */
  async clearBadgeCount(): Promise<void> {
    await this.setBadgeCount(0);
  }

  /**
   * Set callbacks for notification events
   */
  setCallbacks(callbacks: NotificationCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Get current push token
   */
  getPushToken(): string | null {
    return this.expoPushToken;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
      this.notificationListener = null;
    }

    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
      this.responseListener = null;
    }

    this.callbacks = {};
    this.expoPushToken = null;
    this.driverId = null;
    this.supabase = null;
  }

  // Private helper methods

  private mapPriorityToExpo(priority: string): Notifications.AndroidNotificationPriority {
    switch (priority) {
      case 'max':
        return Notifications.AndroidNotificationPriority.MAX;
      case 'high':
        return Notifications.AndroidNotificationPriority.HIGH;
      case 'default':
      default:
        return Notifications.AndroidNotificationPriority.DEFAULT;
    }
  }
}
