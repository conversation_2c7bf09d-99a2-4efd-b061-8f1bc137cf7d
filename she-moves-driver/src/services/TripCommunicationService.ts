/**
 * Trip Communication Service for SheMove Driver App
 * Handles real-time messaging between driver and passenger during trips
 */

import { SupabaseClient } from '@supabase/supabase-js';

export type MessageType = 'text' | 'quick_action' | 'location' | 'system';
export type MessageSender = 'driver' | 'passenger' | 'system';

export interface TripMessage {
  id: string;
  trip_id: string;
  sender: MessageSender;
  sender_id: string;
  message_type: MessageType;
  content: string;
  metadata?: Record<string, any>;
  sent_at: string;
  read_at?: string;
  is_read: boolean;
}

export interface QuickAction {
  id: string;
  text: string;
  icon: string;
  action_type: 'arrival' | 'delay' | 'location' | 'contact' | 'emergency';
}

export interface CommunicationCallbacks {
  onMessageReceived?: (message: TripMessage) => void;
  onMessageRead?: (messageId: string) => void;
  onTypingIndicator?: (isTyping: boolean, userId: string) => void;
  onError?: (error: Error) => void;
}

export class TripCommunicationService {
  private supabase: SupabaseClient;
  private driverId: string | null = null;
  private activeTripId: string | null = null;
  private callbacks: CommunicationCallbacks = {};
  private realtimeSubscription: any = null;

  // Predefined quick actions for drivers
  private quickActions: QuickAction[] = [
    {
      id: 'arriving',
      text: "I'm arriving now",
      icon: 'car',
      action_type: 'arrival'
    },
    {
      id: 'here',
      text: "I'm here",
      icon: 'location',
      action_type: 'location'
    },
    {
      id: 'delayed',
      text: "Running 5 minutes late",
      icon: 'time',
      action_type: 'delay'
    },
    {
      id: 'traffic',
      text: "Stuck in traffic, will be there soon",
      icon: 'car',
      action_type: 'delay'
    },
    {
      id: 'call',
      text: "Please call me",
      icon: 'call',
      action_type: 'contact'
    },
    {
      id: 'safe',
      text: "Have a safe trip!",
      icon: 'heart',
      action_type: 'arrival'
    }
  ];

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Initialize the service with driver ID
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Get driver profile
      const { data: driverProfile, error } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        console.error('Failed to get driver profile:', error);
        return false;
      }

      this.driverId = driverProfile.id;
      return true;
    } catch (error) {
      console.error('Failed to initialize TripCommunicationService:', error);
      return false;
    }
  }

  /**
   * Start communication for a specific trip
   */
  async startCommunication(tripId: string, callbacks: CommunicationCallbacks): Promise<boolean> {
    if (!this.driverId) {
      console.error('Driver not initialized');
      return false;
    }

    try {
      this.activeTripId = tripId;
      this.callbacks = callbacks;

      // Start real-time subscription for messages
      await this.startRealtimeSubscription();

      console.log('Trip communication started for trip:', tripId);
      return true;
    } catch (error) {
      console.error('Failed to start trip communication:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Stop communication
   */
  stopCommunication(): void {
    // Stop real-time subscription
    if (this.realtimeSubscription) {
      this.realtimeSubscription.unsubscribe();
      this.realtimeSubscription = null;
    }

    this.activeTripId = null;
    this.callbacks = {};
    console.log('Trip communication stopped');
  }

  /**
   * Send a text message
   */
  async sendMessage(content: string): Promise<boolean> {
    if (!this.activeTripId || !this.driverId) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('trip_messages')
        .insert({
          trip_id: this.activeTripId,
          sender: 'driver',
          sender_id: this.driverId,
          message_type: 'text',
          content: content.trim(),
          sent_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to send message:', error);
        this.callbacks.onError?.(new Error('Failed to send message'));
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Send a quick action message
   */
  async sendQuickAction(actionId: string): Promise<boolean> {
    const action = this.quickActions.find(a => a.id === actionId);
    if (!action) {
      console.error('Quick action not found:', actionId);
      return false;
    }

    if (!this.activeTripId || !this.driverId) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('trip_messages')
        .insert({
          trip_id: this.activeTripId,
          sender: 'driver',
          sender_id: this.driverId,
          message_type: 'quick_action',
          content: action.text,
          metadata: {
            action_id: actionId,
            action_type: action.action_type,
            icon: action.icon
          },
          sent_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to send quick action:', error);
        this.callbacks.onError?.(new Error('Failed to send quick action'));
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending quick action:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Send location message
   */
  async sendLocation(lat: number, lng: number, address?: string): Promise<boolean> {
    if (!this.activeTripId || !this.driverId) {
      return false;
    }

    try {
      const { error } = await this.supabase
        .from('trip_messages')
        .insert({
          trip_id: this.activeTripId,
          sender: 'driver',
          sender_id: this.driverId,
          message_type: 'location',
          content: address || `Location: ${lat.toFixed(6)}, ${lng.toFixed(6)}`,
          metadata: {
            latitude: lat,
            longitude: lng,
            address: address
          },
          sent_at: new Date().toISOString()
        });

      if (error) {
        console.error('Failed to send location:', error);
        this.callbacks.onError?.(new Error('Failed to send location'));
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending location:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('trip_messages')
        .update({
          is_read: true,
          read_at: new Date().toISOString()
        })
        .eq('id', messageId);

      if (error) {
        console.error('Failed to mark message as read:', error);
        return false;
      }

      this.callbacks.onMessageRead?.(messageId);
      return true;
    } catch (error) {
      console.error('Error marking message as read:', error);
      return false;
    }
  }

  /**
   * Get trip message history
   */
  async getMessageHistory(limit: number = 50): Promise<TripMessage[]> {
    if (!this.activeTripId) {
      return [];
    }

    try {
      const { data, error } = await this.supabase
        .from('trip_messages')
        .select('*')
        .eq('trip_id', this.activeTripId)
        .order('sent_at', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Failed to get message history:', error);
        return [];
      }

      return (data || []).map(msg => ({
        ...msg,
        is_read: msg.is_read || false
      }));
    } catch (error) {
      console.error('Error getting message history:', error);
      return [];
    }
  }

  /**
   * Get available quick actions
   */
  getQuickActions(): QuickAction[] {
    return this.quickActions;
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(isTyping: boolean): Promise<void> {
    if (!this.activeTripId || !this.driverId) {
      return;
    }

    try {
      // In a real implementation, you might use a separate real-time channel
      // or a temporary table for typing indicators
      console.log('Typing indicator:', isTyping);
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopCommunication();
    this.driverId = null;
  }

  // Private helper methods
  private async startRealtimeSubscription(): Promise<void> {
    if (!this.activeTripId) return;

    try {
      // Subscribe to new messages
      this.realtimeSubscription = this.supabase
        .channel(`trip-messages-${this.activeTripId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'trip_messages',
            filter: `trip_id=eq.${this.activeTripId}`
          },
          (payload) => {
            this.handleNewMessage(payload.new as TripMessage);
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'trip_messages',
            filter: `trip_id=eq.${this.activeTripId}`
          },
          (payload) => {
            this.handleMessageUpdate(payload.new as TripMessage);
          }
        )
        .subscribe();

      console.log('Real-time message subscription started for trip:', this.activeTripId);
    } catch (error) {
      console.error('Failed to start real-time message subscription:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  private handleNewMessage(message: TripMessage): void {
    try {
      // Only notify for messages from passenger (not our own messages)
      if (message.sender === 'passenger' || message.sender === 'system') {
        this.callbacks.onMessageReceived?.(message);
      }
    } catch (error) {
      console.error('Error handling new message:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  private handleMessageUpdate(message: TripMessage): void {
    try {
      // Handle message read status updates
      if (message.is_read && message.read_at) {
        this.callbacks.onMessageRead?.(message.id);
      }
    } catch (error) {
      console.error('Error handling message update:', error);
      this.callbacks.onError?.(error as Error);
    }
  }
}
