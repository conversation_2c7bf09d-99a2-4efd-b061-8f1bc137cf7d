/**
 * Trip Request Service for SheMove Driver App
 * Handles incoming trip requests, notifications, and driver responses
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { Trip, TripRequest, TripStatus } from '../../shared/types';

export interface TripRequestData {
  id: string;
  trip_id: string;
  trip: Trip;
  expires_at: string;
  created_at: string;
  passenger_name: string;
  passenger_phone?: string;
  pickup_address: string;
  destination_address: string;
  estimated_fare: number;
  estimated_distance: number;
  estimated_duration: number;
}

export interface TripRequestResponse {
  success: boolean;
  error?: string;
  trip_request?: TripRequest;
}

export interface TripRequestCallbacks {
  onTripRequest?: (request: TripRequestData) => void;
  onTripRequestExpired?: (requestId: string) => void;
  onTripRequestCancelled?: (requestId: string) => void;
  onError?: (error: Error) => void;
}

export class TripRequestService {
  private supabase: SupabaseClient;
  private driverId: string | null = null;
  private callbacks: TripRequestCallbacks = {};
  private activeRequests: Map<string, TripRequestData> = new Map();
  private requestTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private realtimeSubscription: any = null;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Initialize the service with driver ID
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Get driver profile
      const { data: driverProfile, error } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        console.error('Failed to get driver profile:', error);
        return false;
      }

      this.driverId = driverProfile.id;
      
      // Start listening for trip requests
      await this.startListening();
      
      return true;
    } catch (error) {
      console.error('Failed to initialize TripRequestService:', error);
      return false;
    }
  }

  /**
   * Start listening for trip requests
   */
  private async startListening(): Promise<void> {
    if (!this.driverId) {
      throw new Error('Driver not initialized');
    }

    try {
      // Subscribe to trip requests for this driver
      this.realtimeSubscription = this.supabase
        .channel('trip_requests')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'trip_requests',
            filter: `driver_id=eq.${this.driverId}`,
          },
          (payload) => {
            this.handleNewTripRequest(payload.new as TripRequest);
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'trip_requests',
            filter: `driver_id=eq.${this.driverId}`,
          },
          (payload) => {
            this.handleTripRequestUpdate(payload.new as TripRequest);
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'trip_requests',
            filter: `driver_id=eq.${this.driverId}`,
          },
          (payload) => {
            this.handleTripRequestCancellation(payload.old as TripRequest);
          }
        )
        .subscribe();

      console.log('Started listening for trip requests');
    } catch (error) {
      console.error('Failed to start listening for trip requests:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Stop listening for trip requests
   */
  private async stopListening(): Promise<void> {
    if (this.realtimeSubscription) {
      await this.supabase.removeChannel(this.realtimeSubscription);
      this.realtimeSubscription = null;
    }

    // Clear all timeouts
    this.requestTimeouts.forEach((timeout) => {
      clearTimeout(timeout);
    });
    this.requestTimeouts.clear();
    this.activeRequests.clear();

    console.log('Stopped listening for trip requests');
  }

  /**
   * Handle new trip request
   */
  private async handleNewTripRequest(tripRequest: TripRequest): Promise<void> {
    try {
      // Get trip details
      const { data: trip, error: tripError } = await this.supabase
        .from('trips')
        .select(`
          *,
          passengers:passenger_id (
            first_name,
            last_name,
            phone_number
          )
        `)
        .eq('id', tripRequest.trip_id)
        .single();

      if (tripError || !trip) {
        console.error('Failed to get trip details:', tripError);
        return;
      }

      // Create trip request data
      const requestData: TripRequestData = {
        id: tripRequest.id,
        trip_id: tripRequest.trip_id,
        trip: trip,
        expires_at: tripRequest.expires_at,
        created_at: tripRequest.created_at,
        passenger_name: `${trip.passengers.first_name} ${trip.passengers.last_name}`,
        passenger_phone: trip.passengers.phone_number,
        pickup_address: trip.pickup_address,
        destination_address: trip.destination_address,
        estimated_fare: trip.fare_amount || 0,
        estimated_distance: trip.distance_km || 0,
        estimated_duration: trip.duration_minutes || 0,
      };

      // Store active request
      this.activeRequests.set(tripRequest.id, requestData);

      // Set expiration timeout
      const expiresAt = new Date(tripRequest.expires_at).getTime();
      const now = new Date().getTime();
      const timeoutMs = expiresAt - now;

      if (timeoutMs > 0) {
        const timeout = setTimeout(() => {
          this.handleTripRequestExpiration(tripRequest.id);
        }, timeoutMs);
        
        this.requestTimeouts.set(tripRequest.id, timeout);
      }

      // Notify callback
      this.callbacks.onTripRequest?.(requestData);

    } catch (error) {
      console.error('Error handling new trip request:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  /**
   * Handle trip request update
   */
  private handleTripRequestUpdate(tripRequest: TripRequest): void {
    if (tripRequest.status === 'expired') {
      this.handleTripRequestExpiration(tripRequest.id);
    }
  }

  /**
   * Handle trip request cancellation
   */
  private handleTripRequestCancellation(tripRequest: TripRequest): void {
    this.cleanupRequest(tripRequest.id);
    this.callbacks.onTripRequestCancelled?.(tripRequest.id);
  }

  /**
   * Handle trip request expiration
   */
  private handleTripRequestExpiration(requestId: string): void {
    this.cleanupRequest(requestId);
    this.callbacks.onTripRequestExpired?.(requestId);
  }

  /**
   * Accept a trip request
   */
  async acceptTripRequest(requestId: string): Promise<TripRequestResponse> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const requestData = this.activeRequests.get(requestId);
      if (!requestData) {
        return { success: false, error: 'Trip request not found' };
      }

      // Check if request is still valid
      const now = new Date().getTime();
      const expiresAt = new Date(requestData.expires_at).getTime();
      
      if (now >= expiresAt) {
        this.cleanupRequest(requestId);
        return { success: false, error: 'Trip request has expired' };
      }

      // Use database function to respond to trip request
      const { data, error } = await this.supabase.rpc('respond_to_trip_request', {
        request_id: requestId,
        driver_response: 'accepted'
      });

      if (error) {
        console.error('Failed to accept trip request:', error);
        return { success: false, error: 'Failed to accept trip request' };
      }

      // Clean up the request
      this.cleanupRequest(requestId);

      return { 
        success: true, 
        trip_request: data 
      };

    } catch (error) {
      console.error('Error accepting trip request:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Decline a trip request
   */
  async declineTripRequest(requestId: string): Promise<TripRequestResponse> {
    if (!this.driverId) {
      return { success: false, error: 'Driver not initialized' };
    }

    try {
      const requestData = this.activeRequests.get(requestId);
      if (!requestData) {
        return { success: false, error: 'Trip request not found' };
      }

      // Use database function to respond to trip request
      const { data, error } = await this.supabase.rpc('respond_to_trip_request', {
        request_id: requestId,
        driver_response: 'declined'
      });

      if (error) {
        console.error('Failed to decline trip request:', error);
        return { success: false, error: 'Failed to decline trip request' };
      }

      // Clean up the request
      this.cleanupRequest(requestId);

      return { 
        success: true, 
        trip_request: data 
      };

    } catch (error) {
      console.error('Error declining trip request:', error);
      return { success: false, error: 'Unexpected error occurred' };
    }
  }

  /**
   * Get active trip requests
   */
  getActiveRequests(): TripRequestData[] {
    return Array.from(this.activeRequests.values());
  }

  /**
   * Get specific trip request
   */
  getTripRequest(requestId: string): TripRequestData | null {
    return this.activeRequests.get(requestId) || null;
  }

  /**
   * Set callbacks for trip request events
   */
  setCallbacks(callbacks: TripRequestCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  /**
   * Cleanup resources
   */
  async cleanup(): void {
    await this.stopListening();
    this.callbacks = {};
    this.driverId = null;
  }

  /**
   * Clean up a specific request
   */
  private cleanupRequest(requestId: string): void {
    // Clear timeout
    const timeout = this.requestTimeouts.get(requestId);
    if (timeout) {
      clearTimeout(timeout);
      this.requestTimeouts.delete(requestId);
    }

    // Remove from active requests
    this.activeRequests.delete(requestId);
  }
}
