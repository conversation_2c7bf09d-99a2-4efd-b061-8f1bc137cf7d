/**
 * Trip Tracking Service for SheMove Driver App
 * Handles real-time location sharing during active trips
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { LocationUpdate } from './DriverStatusService';

export interface TripLocationUpdate {
  trip_id: string;
  driver_location: LocationUpdate;
  passenger_location?: LocationUpdate;
  timestamp: string;
  distance_to_pickup?: number;
  distance_to_destination?: number;
  estimated_arrival?: number;
}

export interface RouteInfo {
  distance_km: number;
  duration_minutes: number;
  polyline?: string;
  steps?: RouteStep[];
}

export interface RouteStep {
  instruction: string;
  distance_meters: number;
  duration_seconds: number;
  start_location: { lat: number; lng: number };
  end_location: { lat: number; lng: number };
}

export interface TripTrackingCallbacks {
  onLocationUpdate?: (update: TripLocationUpdate) => void;
  onPassengerLocationUpdate?: (location: LocationUpdate) => void;
  onRouteUpdate?: (route: RouteInfo) => void;
  onError?: (error: Error) => void;
}

export class TripTrackingService {
  private supabase: SupabaseClient;
  private driverId: string | null = null;
  private activeTripId: string | null = null;
  private callbacks: TripTrackingCallbacks = {};
  private locationUpdateInterval: NodeJS.Timeout | null = null;
  private realtimeSubscription: any = null;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Initialize the service with driver ID
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Get driver profile
      const { data: driverProfile, error } = await this.supabase
        .from('drivers')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (error || !driverProfile) {
        console.error('Failed to get driver profile:', error);
        return false;
      }

      this.driverId = driverProfile.id;
      return true;
    } catch (error) {
      console.error('Failed to initialize TripTrackingService:', error);
      return false;
    }
  }

  /**
   * Start tracking for a specific trip
   */
  async startTracking(tripId: string, callbacks: TripTrackingCallbacks): Promise<boolean> {
    if (!this.driverId) {
      console.error('Driver not initialized');
      return false;
    }

    try {
      this.activeTripId = tripId;
      this.callbacks = callbacks;

      // Start real-time subscription for passenger location updates
      await this.startRealtimeSubscription();

      // Start periodic location updates
      this.startLocationUpdates();

      console.log('Trip tracking started for trip:', tripId);
      return true;
    } catch (error) {
      console.error('Failed to start trip tracking:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Stop tracking
   */
  stopTracking(): void {
    // Stop location updates
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = null;
    }

    // Stop real-time subscription
    if (this.realtimeSubscription) {
      this.realtimeSubscription.unsubscribe();
      this.realtimeSubscription = null;
    }

    this.activeTripId = null;
    this.callbacks = {};
    console.log('Trip tracking stopped');
  }

  /**
   * Update driver location during trip
   */
  async updateDriverLocation(location: LocationUpdate): Promise<boolean> {
    if (!this.activeTripId || !this.driverId) {
      return false;
    }

    try {
      // Update driver location in database
      const { error } = await this.supabase
        .from('trip_locations')
        .insert({
          trip_id: this.activeTripId,
          driver_id: this.driverId,
          location: `POINT(${location.lng} ${location.lat})`,
          heading: location.heading,
          speed_kmh: location.speed,
          accuracy_meters: location.accuracy,
          recorded_at: location.timestamp
        });

      if (error) {
        console.error('Failed to update driver location:', error);
        return false;
      }

      // Calculate distances and ETA if we have trip data
      const tripUpdate: TripLocationUpdate = {
        trip_id: this.activeTripId,
        driver_location: location,
        timestamp: location.timestamp
      };

      // Notify callback
      this.callbacks.onLocationUpdate?.(tripUpdate);

      return true;
    } catch (error) {
      console.error('Error updating driver location:', error);
      this.callbacks.onError?.(error as Error);
      return false;
    }
  }

  /**
   * Get route information between two points
   */
  async getRoute(
    start: { lat: number; lng: number },
    end: { lat: number; lng: number }
  ): Promise<RouteInfo | null> {
    try {
      // In a real implementation, you would use a routing service like:
      // - Google Directions API
      // - Mapbox Directions API
      // - GraphHopper
      // - OpenRouteService

      // For now, return a mock route with straight-line distance
      const distance = this.calculateDistance(start, end);
      const duration = Math.round(distance / 30 * 60); // Assume 30 km/h average speed

      const route: RouteInfo = {
        distance_km: distance,
        duration_minutes: duration,
        steps: [
          {
            instruction: `Head towards ${end.lat.toFixed(4)}, ${end.lng.toFixed(4)}`,
            distance_meters: distance * 1000,
            duration_seconds: duration * 60,
            start_location: start,
            end_location: end
          }
        ]
      };

      this.callbacks.onRouteUpdate?.(route);
      return route;
    } catch (error) {
      console.error('Error getting route:', error);
      this.callbacks.onError?.(error as Error);
      return null;
    }
  }

  /**
   * Calculate estimated arrival time
   */
  calculateETA(
    currentLocation: { lat: number; lng: number },
    destination: { lat: number; lng: number },
    currentSpeed?: number
  ): number {
    const distance = this.calculateDistance(currentLocation, destination);
    const speed = currentSpeed && currentSpeed > 5 ? currentSpeed : 30; // Default to 30 km/h
    return Math.round(distance / speed * 60); // Return minutes
  }

  /**
   * Get trip tracking history
   */
  async getTripTrackingHistory(tripId: string): Promise<TripLocationUpdate[]> {
    try {
      const { data, error } = await this.supabase
        .from('trip_locations')
        .select('*')
        .eq('trip_id', tripId)
        .order('recorded_at', { ascending: true });

      if (error) {
        console.error('Failed to get trip tracking history:', error);
        return [];
      }

      return data.map(location => ({
        trip_id: location.trip_id,
        driver_location: {
          lat: location.location.coordinates[1],
          lng: location.location.coordinates[0],
          heading: location.heading,
          speed: location.speed_kmh,
          accuracy: location.accuracy_meters,
          timestamp: location.recorded_at
        },
        timestamp: location.recorded_at
      }));
    } catch (error) {
      console.error('Error getting trip tracking history:', error);
      return [];
    }
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.stopTracking();
    this.driverId = null;
  }

  // Private helper methods
  private async startRealtimeSubscription(): Promise<void> {
    if (!this.activeTripId) return;

    try {
      // Subscribe to passenger location updates
      this.realtimeSubscription = this.supabase
        .channel(`trip-${this.activeTripId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'trip_locations',
            filter: `trip_id=eq.${this.activeTripId}`
          },
          (payload) => {
            this.handleRealtimeLocationUpdate(payload.new);
          }
        )
        .subscribe();

      console.log('Real-time subscription started for trip:', this.activeTripId);
    } catch (error) {
      console.error('Failed to start real-time subscription:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  private startLocationUpdates(): void {
    // Update location every 10 seconds during active trip
    this.locationUpdateInterval = setInterval(() => {
      // This would typically get the current location from LocationService
      // and call updateDriverLocation()
      console.log('Location update interval triggered');
    }, 10000);
  }

  private handleRealtimeLocationUpdate(locationData: any): void {
    try {
      // Check if this is a passenger location update
      if (locationData.passenger_id && !locationData.driver_id) {
        const passengerLocation: LocationUpdate = {
          lat: locationData.location.coordinates[1],
          lng: locationData.location.coordinates[0],
          heading: locationData.heading,
          speed: locationData.speed_kmh,
          accuracy: locationData.accuracy_meters,
          timestamp: locationData.recorded_at
        };

        this.callbacks.onPassengerLocationUpdate?.(passengerLocation);
      }
    } catch (error) {
      console.error('Error handling real-time location update:', error);
      this.callbacks.onError?.(error as Error);
    }
  }

  private calculateDistance(
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(point2.lat - point1.lat);
    const dLng = this.toRadians(point2.lng - point1.lng);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
