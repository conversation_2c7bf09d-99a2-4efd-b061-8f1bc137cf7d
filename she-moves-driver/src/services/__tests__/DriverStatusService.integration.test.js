/**
 * Integration tests for DriverStatusService
 * Tests the service functionality without React Native dependencies
 */

// Simple mock for testing
const mockSupabase = {
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        single: jest.fn(() => Promise.resolve({
          data: { id: 'test-driver-id' },
          error: null
        }))
      }))
    })),
    insert: jest.fn(() => ({
      select: jest.fn(() => ({
        single: jest.fn(() => Promise.resolve({
          data: {
            id: 'session-id',
            driver_id: 'test-driver-id',
            session_start: new Date().toISOString()
          },
          error: null
        }))
      }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => Promise.resolve({ error: null }))
    }))
  })),
  rpc: jest.fn(() => Promise.resolve({ data: true, error: null }))
};

describe('DriverStatusService Integration', () => {
  let DriverStatusService;
  let service;

  beforeAll(async () => {
    // Dynamically import to avoid React Native setup issues
    const module = await import('../DriverStatusService');
    DriverStatusService = module.DriverStatusService;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    service = new DriverStatusService(mockSupabase);
  });

  describe('Service Creation', () => {
    test('should create service instance', () => {
      expect(service).toBeDefined();
      expect(service).toBeInstanceOf(DriverStatusService);
    });

    test('should have required methods', () => {
      expect(typeof service.initialize).toBe('function');
      expect(typeof service.goOnline).toBe('function');
      expect(typeof service.goOffline).toBe('function');
      expect(typeof service.updateLocation).toBe('function');
      expect(typeof service.getCurrentStatus).toBe('function');
      expect(typeof service.cleanup).toBe('function');
    });
  });

  describe('Initialization', () => {
    test('should initialize successfully with valid user ID', async () => {
      const result = await service.initialize('test-user-id');
      
      expect(result).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledWith('drivers');
    });

    test('should handle initialization failure', async () => {
      // Mock failure
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({
              data: null,
              error: { message: 'Driver not found' }
            }))
          }))
        }))
      });

      const result = await service.initialize('invalid-user-id');
      expect(result).toBe(false);
    });
  });

  describe('Location Updates', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id');
    });

    test('should update location successfully', async () => {
      const location = {
        lat: -26.2041,
        lng: 28.0473,
        heading: 45,
        speed: 30,
        accuracy: 5,
        timestamp: new Date().toISOString()
      };

      const result = await service.updateLocation(location);
      
      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_driver_location', {
        driver_id_param: 'test-driver-id',
        lat: location.lat,
        lng: location.lng,
        heading_param: location.heading,
        speed_param: location.speed,
        accuracy_param: location.accuracy
      });
    });

    test('should handle location update failure', async () => {
      mockSupabase.rpc.mockResolvedValueOnce({
        data: null,
        error: { message: 'Location update failed' }
      });

      const location = {
        lat: -26.2041,
        lng: 28.0473,
        timestamp: new Date().toISOString()
      };

      const result = await service.updateLocation(location);
      expect(result).toBe(false);
    });
  });

  describe('Status Management', () => {
    beforeEach(async () => {
      await service.initialize('test-user-id');
    });

    test('should handle status change callbacks', () => {
      const callback = jest.fn();
      
      const unsubscribe = service.onStatusChange(callback);
      expect(typeof unsubscribe).toBe('function');
      
      // Simulate status change
      service.notifyStatusChange('online');
      expect(callback).toHaveBeenCalledWith('online');
      
      // Test unsubscribe
      unsubscribe();
      service.notifyStatusChange('offline');
      expect(callback).toHaveBeenCalledTimes(1); // Should not be called again
    });
  });

  describe('Cleanup', () => {
    test('should cleanup resources properly', async () => {
      await service.initialize('test-user-id');
      
      const callback = jest.fn();
      service.onStatusChange(callback);
      
      service.cleanup();
      
      // After cleanup, callbacks should be cleared
      service.notifyStatusChange('online');
      expect(callback).not.toHaveBeenCalled();
      
      // Current session should be null
      expect(service.getCurrentSession()).toBeNull();
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors gracefully', async () => {
      mockSupabase.from.mockImplementationOnce(() => {
        throw new Error('Database connection failed');
      });

      const result = await service.initialize('test-user-id');
      expect(result).toBe(false);
    });

    test('should handle location update without initialization', async () => {
      const uninitializedService = new DriverStatusService(mockSupabase);
      
      const location = {
        lat: -26.2041,
        lng: 28.0473,
        timestamp: new Date().toISOString()
      };

      const result = await uninitializedService.updateLocation(location);
      expect(result).toBe(false);
    });
  });
});

// Test that our service exports are correct
describe('Service Exports', () => {
  test('should export DriverStatusService class', async () => {
    const module = await import('../DriverStatusService');
    expect(module.DriverStatusService).toBeDefined();
    expect(typeof module.DriverStatusService).toBe('function');
  });

  test('should have correct interface types', async () => {
    const module = await import('../DriverStatusService');
    
    // Test that we can create an instance
    const service = new module.DriverStatusService(mockSupabase);
    expect(service).toBeDefined();
  });
});

console.log('✅ DriverStatusService integration tests completed successfully');
