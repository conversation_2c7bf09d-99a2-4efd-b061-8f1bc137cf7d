/**
 * Tests for DriverStatusService
 * Comprehensive testing of driver status management functionality
 */

import { DriverStatusService, LocationUpdate } from '../DriverStatusService';
import { DriverStatus } from '../../../shared/types';

// Mock Supabase client
const mockSupabase = {
  from: jest.fn(),
  rpc: jest.fn(),
};

// Mock data
const mockDriverId = 'test-driver-id';
const mockUserId = 'test-user-id';
const mockLocation: LocationUpdate = {
  lat: -26.2041,
  lng: 28.0473,
  heading: 45,
  speed: 30,
  accuracy: 5,
  timestamp: new Date().toISOString()
};

describe('DriverStatusService', () => {
  let service: DriverStatusService;
  let mockFromChain: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup mock chain
    mockFromChain = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      is: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      in: jest.fn().mockReturnThis(),
    };

    mockSupabase.from.mockReturnValue(mockFromChain);
    
    service = new DriverStatusService(mockSupabase as any);
  });

  describe('initialize', () => {
    it('should initialize successfully with valid user ID', async () => {
      // Mock successful driver profile fetch
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });

      const result = await service.initialize(mockUserId);

      expect(result).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledWith('drivers');
      expect(mockFromChain.select).toHaveBeenCalledWith('id');
      expect(mockFromChain.eq).toHaveBeenCalledWith('user_id', mockUserId);
    });

    it('should fail to initialize with invalid user ID', async () => {
      // Mock failed driver profile fetch
      mockFromChain.single.mockResolvedValue({
        data: null,
        error: { message: 'Driver not found' }
      });

      const result = await service.initialize('invalid-user-id');

      expect(result).toBe(false);
    });
  });

  describe('goOnline', () => {
    beforeEach(async () => {
      // Initialize service first
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });
      await service.initialize(mockUserId);
    });

    it('should go online successfully when driver is approved', async () => {
      // Mock driver verification check
      mockFromChain.single
        .mockResolvedValueOnce({
          data: { verification_status: 'approved', is_online: false },
          error: null
        })
        // Mock session creation
        .mockResolvedValueOnce({
          data: {
            id: 'session-id',
            driver_id: mockDriverId,
            session_start: new Date().toISOString()
          },
          error: null
        })
        // Mock availability update
        .mockResolvedValueOnce({
          data: {
            id: 'availability-id',
            driver_id: mockDriverId,
            status: 'online',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          error: null
        });

      // Mock driver status update
      mockFromChain.update.mockResolvedValue({ error: null });
      
      // Mock RPC call for location update
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await service.goOnline(mockLocation);

      expect(result.success).toBe(true);
      expect(result.session).toBeDefined();
      expect(result.availability).toBeDefined();
    });

    it('should fail to go online when driver is not approved', async () => {
      // Mock driver verification check - not approved
      mockFromChain.single.mockResolvedValue({
        data: { verification_status: 'pending', is_online: false },
        error: null
      });

      const result = await service.goOnline(mockLocation);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Driver not approved for driving');
    });

    it('should fail to go online when driver is already online', async () => {
      // Mock driver verification check - already online
      mockFromChain.single.mockResolvedValue({
        data: { verification_status: 'approved', is_online: true },
        error: null
      });

      const result = await service.goOnline(mockLocation);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Driver is already online');
    });
  });

  describe('goOffline', () => {
    beforeEach(async () => {
      // Initialize service first
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });
      await service.initialize(mockUserId);
    });

    it('should go offline successfully when no active trip', async () => {
      // Mock active trip check - no active trips
      mockFromChain.single.mockResolvedValue({
        data: null,
        error: { code: 'PGRST116' } // No rows returned
      });

      // Mock session end
      mockFromChain.update.mockResolvedValue({ error: null });

      // Mock availability update
      mockFromChain.single.mockResolvedValue({
        data: {
          id: 'availability-id',
          driver_id: mockDriverId,
          status: 'offline',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        error: null
      });

      const result = await service.goOffline();

      expect(result.success).toBe(true);
    });

    it('should fail to go offline when driver has active trip', async () => {
      // Mock active trip check - has active trip
      mockFromChain.single.mockResolvedValue({
        data: { id: 'trip-id', status: 'accepted' },
        error: null
      });

      const result = await service.goOffline();

      expect(result.success).toBe(false);
      expect(result.error).toBe('Cannot go offline with active trip');
    });
  });

  describe('updateLocation', () => {
    beforeEach(async () => {
      // Initialize service first
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });
      await service.initialize(mockUserId);
    });

    it('should update location successfully', async () => {
      // Mock RPC call success
      mockSupabase.rpc.mockResolvedValue({ data: true, error: null });

      const result = await service.updateLocation(mockLocation);

      expect(result).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_driver_location', {
        driver_id_param: mockDriverId,
        lat: mockLocation.lat,
        lng: mockLocation.lng,
        heading_param: mockLocation.heading,
        speed_param: mockLocation.speed,
        accuracy_param: mockLocation.accuracy
      });
    });

    it('should handle location update failure', async () => {
      // Mock RPC call failure
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Location update failed' } 
      });

      const result = await service.updateLocation(mockLocation);

      expect(result).toBe(false);
    });
  });

  describe('getCurrentStatus', () => {
    beforeEach(async () => {
      // Initialize service first
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });
      await service.initialize(mockUserId);
    });

    it('should return current status from database', async () => {
      // Mock status fetch
      mockFromChain.single.mockResolvedValue({
        data: { status: 'online' },
        error: null
      });

      const status = await service.getCurrentStatus();

      expect(status).toBe('online');
      expect(mockSupabase.from).toHaveBeenCalledWith('driver_availability');
    });

    it('should return offline when no status found', async () => {
      // Mock status fetch - no data
      mockFromChain.single.mockResolvedValue({
        data: null,
        error: { message: 'No status found' }
      });

      const status = await service.getCurrentStatus();

      expect(status).toBe('offline');
    });
  });

  describe('getDriverStats', () => {
    beforeEach(async () => {
      // Initialize service first
      mockFromChain.single.mockResolvedValue({
        data: { id: mockDriverId },
        error: null
      });
      await service.initialize(mockUserId);
    });

    it('should call correct database function with proper parameters', async () => {
      // Mock the RPC call for get_driver_performance_metrics
      mockSupabase.rpc.mockResolvedValue({
        data: [{
          acceptance_rate: 95.5,
          cancellation_rate: 2.1,
          average_rating: 4.8,
          total_trips: 15,
          online_hours: 8.5,
          trips_per_hour: 1.76,
          earnings_per_hour: 125.50
        }],
        error: null
      });

      const stats = await service.getDriverStats();

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_driver_performance_metrics', {
        driver_id_param: mockDriverId,
        days_back: 1
      });
      expect(stats).toEqual({
        today_online_minutes: 510, // 8.5 * 60
        today_trips: 15,
        today_earnings: 1066.75, // 125.50 * 8.5
        week_online_minutes: 510,
        week_trips: 15,
        week_earnings: 1066.75,
        acceptance_rate: 95.5,
        cancellation_rate: 2.1,
        average_rating: 4.8
      });
    });

    it('should return default stats when no data available', async () => {
      // Mock empty response
      mockSupabase.rpc.mockResolvedValue({
        data: [],
        error: null
      });

      const stats = await service.getDriverStats();

      expect(stats).toEqual({
        today_online_minutes: 0,
        today_trips: 0,
        today_earnings: 0,
        week_online_minutes: 0,
        week_trips: 0,
        week_earnings: 0,
        acceptance_rate: 100,
        cancellation_rate: 0,
        average_rating: 5.0
      });
    });

    it('should handle database errors gracefully', async () => {
      // Mock database error
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database function not found' }
      });

      const stats = await service.getDriverStats();

      expect(stats).toBeNull();
    });

    it('should return null when driver not initialized', async () => {
      const uninitializedService = new DriverStatusService(mockSupabase);

      const stats = await uninitializedService.getDriverStats();

      expect(stats).toBeNull();
      expect(mockSupabase.rpc).not.toHaveBeenCalled();
    });
  });

  describe('status change callbacks', () => {
    it('should register and call status change callbacks', () => {
      const callback = jest.fn();
      
      const unsubscribe = service.onStatusChange(callback);
      
      // Simulate status change (this would normally be called internally)
      (service as any).notifyStatusChange('online');
      
      expect(callback).toHaveBeenCalledWith('online');
      
      // Test unsubscribe
      unsubscribe();
      (service as any).notifyStatusChange('offline');
      
      // Callback should not be called after unsubscribe
      expect(callback).toHaveBeenCalledTimes(1);
    });
  });

  describe('cleanup', () => {
    it('should cleanup resources properly', () => {
      const callback = jest.fn();
      service.onStatusChange(callback);
      
      service.cleanup();
      
      // After cleanup, callbacks should be cleared
      (service as any).notifyStatusChange('online');
      expect(callback).not.toHaveBeenCalled();
      
      // Current session should be null
      expect(service.getCurrentSession()).toBeNull();
    });
  });
});
