-- =====================================================
-- STEP 1: CREATE ENUMS ONLY
-- Test this first - should have ZERO errors
-- =====================================================

-- Create document_type enum (only if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_type') THEN
        CREATE TYPE document_type AS ENUM (
            'drivers_license', 
            'vehicle_registration', 
            'insurance_certificate', 
            'profile_photo'
        );
        RAISE NOTICE 'Created document_type enum';
    ELSE
        RAISE NOTICE 'document_type enum already exists';
    END IF;
END $$;

-- Create document_status enum (only if it doesn't exist)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'document_status') THEN
        CREATE TYPE document_status AS ENUM (
            'uploaded', 
            'under_review', 
            'approved', 
            'rejected'
        );
        RAISE NOTICE 'Created document_status enum';
    ELSE
        RAISE NOTICE 'document_status enum already exists';
    END IF;
END $$;

-- Verify enums were created
SELECT 'SUCCESS: Enums created' as result, 
       string_agg(typname, ', ') as enums_found
FROM pg_type 
WHERE typname IN ('document_type', 'document_status');
