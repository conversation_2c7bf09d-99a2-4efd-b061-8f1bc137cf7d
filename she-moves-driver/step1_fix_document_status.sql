-- =====================================================
-- FIX: CREATE MISSING document_status ENUM
-- The document_status enum wasn't created properly
-- =====================================================

-- Create document_status enum (it's missing)
CREATE TYPE document_status AS ENUM (
    'uploaded', 
    'under_review', 
    'approved', 
    'rejected'
);

-- Verify it was created
SELECT 
    'document_status enum created' as result,
    enumlabel as enum_value,
    enumsortorder as sort_order
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid FROM pg_type WHERE typname = 'document_status'
)
ORDER BY enumsortorder;
