-- =====================================================
-- STEP 2: CREATE DOCUMENT UPLOADS TABLE ONLY
-- Run this after Step 1 enums are successful
-- =====================================================

-- Create the main document uploads table
CREATE TABLE IF NOT EXISTS document_uploads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE NOT NULL,
    document_type document_type NOT NULL,
    file_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_url TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'pending',
    
    -- Basic metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(driver_id, document_type),
    CHECK (file_size > 0),
    CHECK (file_size <= 10485760) -- Max 10MB
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_document_uploads_driver_id ON document_uploads(driver_id);

-- Enable RLS
ALTER TABLE document_uploads ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for drivers to access their own documents
CREATE POLICY "Drivers can manage their own documents" ON document_uploads
    FOR ALL USING (
        driver_id IN (
            SELECT id FROM drivers WHERE user_id = auth.uid()
        )
    );

-- Verify table was created
SELECT 'SUCCESS: document_uploads table created' as result,
       COUNT(*) as column_count
FROM information_schema.columns 
WHERE table_name = 'document_uploads' AND table_schema = 'public';
