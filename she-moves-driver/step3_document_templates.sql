-- =====================================================
-- STEP 3: CREATE DOCUMENT TEMPLATES TABLE
-- This defines what documents drivers need to upload
-- =====================================================

-- Create document templates table
CREATE TABLE IF NOT EXISTS document_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_type document_type NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    requirements TEXT[], -- Array of requirements
    accepted_formats TEXT[], -- e.g., ['image/jpeg', 'image/png']
    max_file_size BIGINT DEFAULT 5242880, -- 5MB default
    is_required BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    help_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS (public read access)
ALTER TABLE document_templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for public read access
CREATE POLICY "Anyone can view document templates" ON document_templates
    FOR SELECT USING (true);

-- Insert the required document templates
INSERT INTO document_templates (document_type, display_name, description, requirements, accepted_formats, max_file_size, is_required, sort_order, help_text) VALUES
('drivers_license', 'Driver''s License', 'Valid driver''s license (front and back)', 
 ARRAY['Must be current and not expired', 'Clear photo showing all text', 'Full document visible'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 1,
 'Take a clear photo of both sides of your driver''s license. Ensure all text is readable and the document is not expired.'),

('vehicle_registration', 'Vehicle Registration', 'Current vehicle registration certificate', 
 ARRAY['Must match the vehicle you''ll be driving', 'Current registration (not expired)', 'Clear and readable'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 2,
 'Upload your current vehicle registration document. Make sure the vehicle details match what you entered in your profile.'),

('insurance', 'Insurance Certificate', 'Valid vehicle insurance certificate', 
 ARRAY['Must cover the registered vehicle', 'Current and not expired', 'Minimum coverage requirements met'], 
 ARRAY['image/jpeg', 'image/png', 'application/pdf'], 5242880, TRUE, 3,
 'Provide proof of current vehicle insurance. The policy must cover the vehicle you''ll use for driving.'),

('profile_photo', 'Profile Photo', 'Professional headshot for your driver profile', 
 ARRAY['Clear face photo', 'Professional appearance', 'Good lighting'], 
 ARRAY['image/jpeg', 'image/png'], 2097152, TRUE, 4,
 'Upload a clear, professional photo of yourself. This will be shown to passengers when they book rides.')

ON CONFLICT (document_type) DO UPDATE SET
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    requirements = EXCLUDED.requirements,
    accepted_formats = EXCLUDED.accepted_formats,
    max_file_size = EXCLUDED.max_file_size,
    help_text = EXCLUDED.help_text,
    updated_at = NOW();

-- Verify table was created and data inserted
SELECT 'SUCCESS: document_templates created' as result,
       COUNT(*) as template_count
FROM document_templates;
