-- =====================================================
-- STEP 4: SETUP SUPABASE STORAGE FOR DOCUMENTS
-- Run this to create storage bucket for driver documents
-- =====================================================

-- Create storage bucket for driver documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'driver-documents',
    'driver-documents',
    false, -- Private bucket for security
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
)
ON CONFLICT (id) DO UPDATE SET
    file_size_limit = EXCLUDED.file_size_limit,
    allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create RLS policies for storage
CREATE POLICY "Drivers can upload their own documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'driver-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Drivers can view their own documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'driver-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Drivers can update their own documents" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'driver-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Drivers can delete their own documents" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'driver-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Verify storage bucket was created
SELECT 'SUCCESS: Storage bucket created' as result,
       id, name, public, file_size_limit
FROM storage.buckets 
WHERE id = 'driver-documents';
