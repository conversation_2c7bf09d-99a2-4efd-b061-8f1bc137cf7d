-- =====================================================
-- TEST AUTHENTICATION FIX
-- Run this to verify the authentication fix is working
-- =====================================================

-- 1. Check if the trigger function exists and is properly configured
SELECT 
    'Trigger Function Check' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS: Function exists'
        ELSE 'FAIL: Function missing'
    END as result
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user' 
AND routine_schema = 'public';

-- 2. Check if the trigger exists
SELECT 
    'Trigger Check' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS: Trigger exists'
        ELSE 'FAIL: Trigger missing'
    END as result
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

-- 3. Check RLS policies on profiles table
SELECT 
    'RLS Policies Check' as test_name,
    CASE 
        WHEN COUNT(*) >= 3 THEN 'PASS: Required policies exist'
        ELSE 'FAIL: Missing policies'
    END as result
FROM pg_policies 
WHERE tablename = 'profiles';

-- 4. Check if user_type enum has correct values
SELECT 
    'User Type Enum Check' as test_name,
    'PASS: ' || string_agg(enumlabel, ', ') as result
FROM pg_enum 
WHERE enumtypid = (
    SELECT oid 
    FROM pg_type 
    WHERE typname = 'user_type'
);

-- 5. Check existing user profile
SELECT 
    'Existing User Check' as test_name,
    CASE 
        WHEN user_type = 'driver' THEN 'PASS: User type is driver'
        WHEN user_type IS NULL THEN 'FAIL: User type is NULL'
        ELSE 'FAIL: User type is ' || user_type::text
    END as result
FROM profiles 
WHERE email = '<EMAIL>';

-- 6. Detailed view of existing user
SELECT 
    'Detailed User Info' as section,
    id,
    email,
    full_name,
    user_type,
    user_type::text as user_type_text,
    created_at,
    updated_at
FROM profiles 
WHERE email = '<EMAIL>';

-- 7. Check auth.users metadata
SELECT 
    'Auth Users Metadata' as section,
    id,
    email,
    raw_user_meta_data,
    raw_user_meta_data->>'user_type' as metadata_user_type,
    raw_user_meta_data->>'full_name' as metadata_full_name
FROM auth.users 
WHERE email = '<EMAIL>';

-- 8. Test function to simulate trigger behavior
CREATE OR REPLACE FUNCTION test_trigger_simulation(
    test_user_id UUID,
    test_email TEXT,
    test_full_name TEXT,
    test_user_type TEXT
)
RETURNS TEXT AS $$
DECLARE
    result_text TEXT;
BEGIN
    -- Simulate what the trigger would do
    BEGIN
        INSERT INTO public.profiles (id, email, full_name, user_type)
        VALUES (
            test_user_id, 
            test_email, 
            test_full_name,
            test_user_type::user_type
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            full_name = EXCLUDED.full_name,
            user_type = EXCLUDED.user_type,
            updated_at = NOW();
        
        result_text := 'SUCCESS: Trigger simulation worked';
    EXCEPTION
        WHEN OTHERS THEN
            result_text := 'ERROR: ' || SQLERRM;
    END;
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Run the simulation test
SELECT test_trigger_simulation(
    gen_random_uuid(),
    '<EMAIL>',
    'Test User',
    'driver'
) as trigger_simulation_result;

-- Clean up test data
DELETE FROM profiles WHERE email = '<EMAIL>';

-- Drop the test function
DROP FUNCTION test_trigger_simulation;

-- =====================================================
-- SUMMARY
-- =====================================================
SELECT 
    '=== AUTHENTICATION FIX TEST SUMMARY ===' as summary;

SELECT 
    'If all tests show PASS, the authentication fix is working correctly.' as instruction;

SELECT 
    'Next step: Test driver app signup with a new email address.' as next_step;
