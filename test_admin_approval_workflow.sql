-- =====================================================
-- TEST ADMIN APPROVAL WORKFLOW
-- Simulate the exact operations the admin dashboard performs
-- =====================================================

-- Step 1: Find a document to test with
SELECT 
    '=== FINDING TEST DOCUMENT ===' as section,
    id,
    driver_id,
    document_type,
    status,
    created_at,
    updated_at
FROM document_uploads 
WHERE status IN ('uploaded', 'under_review')
LIMIT 1;

-- Step 2: Simulate the exact update that the admin dashboard performs
DO $$
DECLARE
    test_doc_id UUID;
    original_status TEXT;
    original_updated_at TIMESTAMP;
    new_updated_at TIMESTAMP;
    update_successful BOOLEAN := false;
BEGIN
    -- Find a document to test with
    SELECT id, status, updated_at 
    INTO test_doc_id, original_status, original_updated_at
    FROM document_uploads 
    WHERE status IN ('uploaded', 'under_review')
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        RAISE NOTICE 'Testing approval workflow on document: %', test_doc_id;
        RAISE NOTICE 'Original status: %, Original updated_at: %', original_status, original_updated_at;
        
        -- Simulate the exact update from admin dashboard
        UPDATE document_uploads 
        SET 
            status = 'approved',
            reviewed_at = NOW(),
            admin_notes = 'Test approval from trigger fix'
        WHERE id = test_doc_id;
        
        -- Check if update was successful
        SELECT updated_at INTO new_updated_at
        FROM document_uploads 
        WHERE id = test_doc_id;
        
        IF new_updated_at > original_updated_at THEN
            update_successful := true;
            RAISE NOTICE '✅ APPROVAL TEST SUCCESSFUL';
            RAISE NOTICE 'Document status updated to approved';
            RAISE NOTICE 'Updated_at changed from % to %', original_updated_at, new_updated_at;
        ELSE
            RAISE NOTICE '❌ APPROVAL TEST FAILED - updated_at did not change';
        END IF;
        
        -- Test rejection workflow too
        UPDATE document_uploads 
        SET 
            status = 'rejected',
            reviewed_at = NOW(),
            rejection_reason = 'Test rejection from trigger fix',
            admin_notes = 'Test rejection workflow'
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ REJECTION TEST SUCCESSFUL';
        
        -- Restore original status
        UPDATE document_uploads 
        SET 
            status = original_status::document_status,
            reviewed_at = NULL,
            rejection_reason = NULL,
            admin_notes = NULL
        WHERE id = test_doc_id;
        
        RAISE NOTICE '✅ Document restored to original state';
        
    ELSE
        RAISE NOTICE 'ℹ️ No documents found to test with';
        RAISE NOTICE 'Upload a document in the driver app first, then run this test';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ APPROVAL TEST FAILED WITH ERROR: %', SQLERRM;
        RAISE NOTICE 'Error code: %', SQLSTATE;
        
        -- Try to restore document if possible
        IF test_doc_id IS NOT NULL THEN
            BEGIN
                UPDATE document_uploads 
                SET status = original_status::document_status
                WHERE id = test_doc_id;
                RAISE NOTICE 'Document status restored after error';
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Could not restore document status';
            END;
        END IF;
END $$;

-- Step 3: Verify trigger is working correctly
SELECT 
    '=== TRIGGER STATUS CHECK ===' as section,
    trigger_name,
    event_manipulation,
    action_timing,
    CASE 
        WHEN trigger_name = 'handle_document_update_trigger' THEN '✅ Correct trigger active'
        ELSE '⚠️ Different trigger'
    END as status
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads'
ORDER BY trigger_name;

-- Step 4: Check for any remaining problematic triggers
SELECT 
    '=== POTENTIAL CONFLICTS ===' as section,
    COUNT(*) as trigger_count,
    CASE 
        WHEN COUNT(*) = 1 THEN '✅ Only one trigger (good)'
        WHEN COUNT(*) > 1 THEN '⚠️ Multiple triggers (potential conflict)'
        ELSE '❌ No triggers (bad)'
    END as assessment
FROM information_schema.triggers 
WHERE event_object_table = 'document_uploads';

-- Step 5: Show current document status for verification
SELECT 
    '=== CURRENT DOCUMENTS ===' as section,
    COUNT(*) as total_documents,
    COUNT(CASE WHEN status = 'uploaded' THEN 1 END) as uploaded,
    COUNT(CASE WHEN status = 'under_review' THEN 1 END) as under_review,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
FROM document_uploads;

SELECT 'TEST COMPLETE - Admin approval workflow should now work' as final_message;
