-- =====================================================
-- TEST DOCUMENT MANAGEMENT FIXES
-- Comprehensive testing of storage and trigger fixes
-- =====================================================

-- =====================================================
-- TEST 1: VERIFY STORAGE BUCKET CONFIGURATION
-- =====================================================

SELECT '=== STORAGE BUCKET TEST ===' as test_section;

-- Check if driver-documents bucket exists and is configured correctly
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM storage.buckets 
            WHERE id = 'driver-documents' 
            AND public = true 
            AND file_size_limit = 10485760
        ) THEN '✅ Storage bucket configured correctly'
        WHEN EXISTS (
            SELECT 1 FROM storage.buckets 
            WHERE id = 'driver-documents'
        ) THEN '⚠️ Storage bucket exists but may have wrong settings'
        ELSE '❌ Storage bucket missing'
    END as bucket_status;

-- Show current bucket configuration
SELECT 
    'Current bucket config:' as info,
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
WHERE id = 'driver-documents';

-- =====================================================
-- TEST 2: VERIFY STORAGE POLICIES
-- =====================================================

SELECT '=== STORAGE POLICIES TEST ===' as test_section;

-- Check if required policies exist
SELECT 
    CASE 
        WHEN COUNT(*) >= 4 THEN '✅ All required storage policies exist'
        WHEN COUNT(*) > 0 THEN '⚠️ Some storage policies missing'
        ELSE '❌ No storage policies found'
    END as policies_status
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver_docs%';

-- List current policies
SELECT 
    'Current policies:' as info,
    policyname,
    cmd as operation
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%driver_docs%'
ORDER BY policyname;

-- =====================================================
-- TEST 3: VERIFY DATABASE TRIGGERS
-- =====================================================

SELECT '=== DATABASE TRIGGERS TEST ===' as test_section;

-- Check if trigger function exists and has proper RETURN statements
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_proc 
            WHERE proname = 'update_updated_at_column'
            AND prosrc LIKE '%RETURN NEW%'
        ) THEN '✅ Trigger function exists and has RETURN statement'
        WHEN EXISTS (
            SELECT 1 FROM pg_proc 
            WHERE proname = 'update_updated_at_column'
        ) THEN '⚠️ Trigger function exists but may be missing RETURN'
        ELSE '❌ Trigger function missing'
    END as trigger_function_status;

-- Check if trigger exists on document_uploads table
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.triggers 
            WHERE trigger_name = 'update_document_uploads_updated_at'
            AND event_object_table = 'document_uploads'
        ) THEN '✅ Document uploads trigger exists'
        ELSE '❌ Document uploads trigger missing'
    END as trigger_status;

-- =====================================================
-- TEST 4: VERIFY DOCUMENT STATUS ENUM
-- =====================================================

SELECT '=== DOCUMENT STATUS ENUM TEST ===' as test_section;

-- Check if all required status values exist
WITH required_statuses AS (
    SELECT unnest(ARRAY['uploaded', 'under_review', 'approved', 'rejected']) as status
),
existing_statuses AS (
    SELECT enumlabel as status
    FROM pg_enum 
    WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
)
SELECT 
    CASE 
        WHEN COUNT(r.status) = COUNT(e.status) THEN '✅ All required document status values exist'
        ELSE '⚠️ Some document status values missing'
    END as status_enum_check
FROM required_statuses r
LEFT JOIN existing_statuses e ON r.status = e.status;

-- List current status values
SELECT 
    'Available status values:' as info,
    enumlabel as status_value
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'document_status')
ORDER BY enumlabel;

-- =====================================================
-- TEST 5: SIMULATE DOCUMENT UPDATE (SAFE TEST)
-- =====================================================

SELECT '=== DOCUMENT UPDATE SIMULATION ===' as test_section;

-- Test if we can safely update a document record (if any exist)
DO $$
DECLARE
    test_doc_id UUID;
    update_success BOOLEAN := false;
BEGIN
    -- Find a test document to update
    SELECT id INTO test_doc_id 
    FROM document_uploads 
    LIMIT 1;
    
    IF test_doc_id IS NOT NULL THEN
        -- Try a safe update that should trigger our fixed function
        UPDATE document_uploads 
        SET updated_at = NOW() 
        WHERE id = test_doc_id;
        
        update_success := true;
        RAISE NOTICE '✅ Document update test successful';
    ELSE
        RAISE NOTICE 'ℹ️ No documents found to test update';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Document update test failed: %', SQLERRM;
END $$;

-- =====================================================
-- TEST 6: FINAL SUMMARY
-- =====================================================

SELECT '=== FINAL TEST SUMMARY ===' as test_section;

-- Overall system health check
SELECT 
    CASE 
        WHEN (
            -- Storage bucket check
            EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'driver-documents' AND public = true) AND
            -- Storage policies check
            (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage' AND policyname LIKE '%driver_docs%') >= 4 AND
            -- Trigger function check
            EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column' AND prosrc LIKE '%RETURN NEW%') AND
            -- Trigger check
            EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'update_document_uploads_updated_at')
        ) THEN '🎉 ALL TESTS PASSED - Document management system is ready!'
        ELSE '⚠️ Some issues remain - check individual test results above'
    END as overall_status;

SELECT 'Test completed. Review results above.' as final_message;
