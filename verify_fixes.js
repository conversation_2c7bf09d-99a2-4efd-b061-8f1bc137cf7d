#!/usr/bin/env node

/**
 * SheMove Document Management Fixes Verification Script
 * 
 * This script helps verify that both storage and database fixes are working correctly.
 * Run this after applying the SQL fixes to ensure everything is configured properly.
 */

const { createClient } = require('@supabase/supabase-js');

// You'll need to update these with your actual Supabase credentials
const SUPABASE_URL = 'https://pcacyfyhxvzbjcouxzub.supabase.co';
const SUPABASE_ANON_KEY = 'your-anon-key-here'; // Replace with your actual anon key

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function verifyFixes() {
  console.log('🔍 SheMove Document Management Fixes Verification');
  console.log('================================================\n');

  let allTestsPassed = true;

  try {
    // Test 1: Check Storage Bucket
    console.log('1. Testing Storage Bucket Configuration...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.log('❌ Failed to list buckets:', bucketsError.message);
      allTestsPassed = false;
    } else {
      const driverDocsBucket = buckets.find(b => b.name === 'driver-documents');
      if (driverDocsBucket) {
        console.log('✅ driver-documents bucket exists');
        console.log(`   - Public: ${driverDocsBucket.public ? 'Yes' : 'No'}`);
        console.log(`   - File size limit: ${driverDocsBucket.file_size_limit || 'Not set'}`);
      } else {
        console.log('❌ driver-documents bucket not found');
        console.log('   Available buckets:', buckets.map(b => b.name).join(', '));
        allTestsPassed = false;
      }
    }

    // Test 2: Check Document Status Enum Values
    console.log('\n2. Testing Document Status Enum...');
    const { data: statusData, error: statusError } = await supabase
      .from('document_uploads')
      .select('status')
      .limit(1);

    if (statusError && statusError.code !== 'PGRST116') { // PGRST116 = no rows found, which is OK
      console.log('❌ Failed to query document_uploads table:', statusError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ document_uploads table accessible');
    }

    // Test 3: Check Database Connection and Basic Queries
    console.log('\n3. Testing Database Connection...');
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (testError && testError.code !== 'PGRST116') {
      console.log('❌ Database connection failed:', testError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Database connection working');
    }

    // Test 4: Storage Upload Test (if possible)
    console.log('\n4. Testing Storage Upload Capability...');
    try {
      // Create a small test file
      const testContent = 'SheMove document management test file';
      const testBlob = new Blob([testContent], { type: 'text/plain' });
      const testFileName = `test-${Date.now()}.txt`;

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('driver-documents')
        .upload(testFileName, testBlob);

      if (uploadError) {
        console.log('❌ Storage upload test failed:', uploadError.message);
        console.log('   This might be due to authentication - try uploading from the driver app');
        // Don't mark as failed since this might be due to auth context
      } else {
        console.log('✅ Storage upload test successful');
        
        // Clean up test file
        await supabase.storage
          .from('driver-documents')
          .remove([testFileName]);
        console.log('   Test file cleaned up');
      }
    } catch (error) {
      console.log('⚠️ Storage upload test skipped (requires authentication)');
    }

    // Summary
    console.log('\n📋 VERIFICATION SUMMARY');
    console.log('=======================');
    
    if (allTestsPassed) {
      console.log('🎉 All critical tests passed!');
      console.log('\nNext steps:');
      console.log('1. Test document upload in the driver app');
      console.log('2. Test document approval in the admin dashboard');
      console.log('3. Monitor logs for any remaining issues');
    } else {
      console.log('⚠️ Some tests failed. Please review the issues above.');
      console.log('\nTroubleshooting:');
      console.log('1. Ensure you ran the fix_document_management_issues.sql script');
      console.log('2. Check your Supabase project URL and keys');
      console.log('3. Verify your database schema is up to date');
    }

  } catch (error) {
    console.error('❌ Verification script failed:', error.message);
    console.log('\nPlease check:');
    console.log('1. Your Supabase URL and anon key are correct');
    console.log('2. Your internet connection is working');
    console.log('3. Your Supabase project is accessible');
  }
}

// Run verification if this script is executed directly
if (require.main === module) {
  console.log('⚠️ IMPORTANT: Update SUPABASE_URL and SUPABASE_ANON_KEY in this script first!\n');
  
  if (SUPABASE_ANON_KEY === 'your-anon-key-here') {
    console.log('❌ Please update the Supabase credentials in this script before running.');
    process.exit(1);
  }
  
  verifyFixes().then(() => {
    console.log('\nVerification complete.');
  }).catch(error => {
    console.error('Verification failed:', error);
    process.exit(1);
  });
}

module.exports = { verifyFixes };
