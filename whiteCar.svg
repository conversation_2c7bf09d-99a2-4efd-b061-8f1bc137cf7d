<svg width="151" height="151" viewBox="0 0 151 151" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_308_40)">
<path d="M111.594 104.253C116.988 102.542 126.786 97.9351 130.285 94.578C134.324 91.0588 125.793 87.303 124.779 86.7333C123.765 86.1635 73.9982 62.3337 65.3246 58.6592C56.6511 54.9848 29.4638 63.369 22.9048 67.1311C17.5584 70.1976 21.3954 72.2608 22.1219 72.8345C22.8484 73.4082 64.3397 95.7868 79.2361 102.586C90.5172 107.735 101.006 107.611 111.594 104.253Z" fill="black" fill-opacity="0.2"/>
</g>
<path d="M85.193 78.9925C85.1075 78.7872 79.2931 77.8871 76.3965 77.4626C75.3424 78.3723 73.1943 80.2848 73.0343 80.6588C72.8342 81.1263 73.0917 84.7883 75.3997 87.441C77.7079 90.0936 82.7095 85.997 84.3007 85.3554C85.892 84.7137 85.2998 79.2492 85.193 78.9925Z" fill="#2C2C2C"/>
<path d="M42.999 58.8208C42.9135 58.6154 37.099 57.7154 34.2025 57.291C33.1484 58.2005 31.0001 60.1132 30.8401 60.4872C30.6402 60.9545 30.8975 64.6167 33.2057 67.2693C35.5137 69.922 40.5154 65.8253 42.1066 65.1836C43.6979 64.5419 43.1057 59.0775 42.999 58.8208Z" fill="#131313"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77.2424 80.9609L82.6751 78.3487L82.6762 78.3511C82.7098 78.3342 82.7436 78.3179 82.7776 78.302C86.0144 76.7948 90.7068 80.0423 93.2587 85.5556C95.8104 91.0688 95.2552 96.76 92.0185 98.2673C91.9786 98.2859 91.9384 98.3037 91.8979 98.3209L86.6497 100.848L83.6418 94.4894C82.8535 93.4772 82.1365 92.3078 81.5375 91.0137C80.9849 89.8197 80.5779 88.6173 80.3125 87.451L77.2424 80.9609Z" fill="black"/>
<path d="M86.6339 100.855C83.3972 102.363 78.7063 99.1186 76.1565 93.6097C73.6066 88.1007 74.1635 82.413 77.4002 80.9058C80.6369 79.3986 85.3278 82.6426 87.8776 88.1516C90.4274 93.6605 89.8706 99.3482 86.6339 100.855Z" fill="black"/>
<g filter="url(#filter1_d_308_40)">
<path d="M85.2941 98.3706C82.8665 99.5011 79.3482 97.068 77.4357 92.9361C75.5232 88.8042 75.9409 84.5382 78.3685 83.4077C80.7962 82.2773 84.3145 84.7104 86.227 88.8423C88.1394 92.9742 87.7218 97.2402 85.2941 98.3706Z" fill="url(#paint0_linear_308_40)"/>
</g>
<g filter="url(#filter2_di_308_40)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.9664 85.0302C82.6628 85.4334 83.3521 86.0313 83.9779 86.7852L82.5984 89.2608C82.4145 89.0512 82.2179 88.8759 82.0179 88.7411L81.9664 85.0302ZM82.0819 93.3485C82.3711 93.4529 82.654 93.4638 82.9037 93.3623L85.0228 96.9285C84.9221 96.9954 84.8161 97.0549 84.7053 97.1066C83.9377 97.464 83.039 97.3927 82.1325 96.9783L82.0819 93.3485ZM81.8236 96.8239L81.7732 93.2028C81.5342 93.0641 81.2975 92.8661 81.0781 92.6196L79.7242 95.0495C80.378 95.8251 81.0988 96.4311 81.8236 96.8239ZM81.7088 88.5666L81.6573 84.8645C80.7053 84.3939 79.7554 84.2969 78.9499 84.672C78.7253 84.7766 78.5212 84.9135 78.3384 85.079L80.476 88.6761C80.547 88.6119 80.6261 88.5588 80.7133 88.5181C81.0103 88.3799 81.358 88.4069 81.7088 88.5666ZM78.1747 92.5904C78.5553 93.4129 79.0127 94.1544 79.5157 94.7936L80.8697 92.3635C80.6994 92.1347 80.5441 91.8753 80.4125 91.591C80.3997 91.5634 80.3874 91.536 80.3753 91.5084L77.9956 92.1837C78.0523 92.3193 78.1119 92.455 78.1747 92.5904ZM82.8057 89.5191L84.1852 87.0436C84.6701 87.6694 85.1113 88.3907 85.4804 89.1883C85.5563 89.352 85.6276 89.516 85.6947 89.68L83.2889 90.3626C83.2756 90.332 83.2617 90.3013 83.2476 90.2707C83.1199 89.9949 82.9701 89.7426 82.8057 89.5191ZM77.8803 91.8973C77.4387 90.7563 77.2085 89.6233 77.1801 88.5975L80.0347 90.1982C80.0597 90.5227 80.1344 90.8703 80.2608 91.2218L77.8803 91.8973ZM83.4026 90.6495C83.5533 91.0714 83.6295 91.4877 83.6345 91.8647L86.4777 93.4589C86.4866 92.3871 86.2671 91.1829 85.8076 89.9671L83.4026 90.6495ZM86.4667 93.8047L83.6181 92.2075C83.5699 92.6337 83.4169 92.9856 83.1654 93.2021L85.2687 96.7419C85.9893 96.1218 86.3956 95.0706 86.4667 93.8047ZM77.1783 88.2446L80.0277 89.8422C80.0421 89.4859 80.1238 89.1713 80.2697 88.9297L78.1163 85.3057C77.519 85.991 77.2006 87.03 77.1783 88.2446Z" fill="#4C4C4C"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M77.2439 80.8458L82.6765 78.2336L82.6771 78.2348C82.7109 78.2177 82.7451 78.2012 82.7797 78.1851C86.0163 76.6778 90.7087 79.9254 93.2605 85.4386C95.8123 90.9519 95.257 96.6432 92.0204 98.1504C91.9903 98.1644 91.9601 98.178 91.9299 98.1911L86.6511 100.733L83.6405 94.3685C82.8534 93.3572 82.1376 92.1892 81.5395 90.8969C80.9877 89.7048 80.5812 88.5044 80.3157 87.3397L77.2439 80.8458Z" fill="black"/>
<path d="M76.1472 93.718C78.6674 99.1628 83.2947 102.373 86.4825 100.889C89.6703 99.4044 90.2117 93.7871 87.6915 88.3422C85.1713 82.8974 80.4018 79.3938 77.2139 80.8783C74.026 82.3627 73.627 88.2731 76.1472 93.718Z" fill="#222222"/>
<g filter="url(#filter3_d_308_40)">
<path d="M85.1222 98.1457C82.8575 99.2306 79.5728 96.8607 77.7856 92.8523C75.9984 88.844 76.3854 84.7152 78.6501 83.6303C80.9148 82.5455 84.1995 84.9154 85.9868 88.9237C87.774 92.932 87.3869 97.0609 85.1222 98.1457Z" fill="url(#paint1_linear_308_40)"/>
</g>
<g filter="url(#filter4_di_308_40)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M79.0546 92.7693C80.2829 95.2146 82.2627 96.7137 83.8088 96.4116L82.5387 93.6168C81.8981 93.5648 81.1683 92.9842 80.6434 92.0873L79.0546 92.7693ZM78.6776 91.9277C77.6806 89.4083 77.834 86.9022 79.004 85.8385L80.2739 88.6329C79.9003 89.21 79.8805 90.2189 80.253 91.2516L78.6776 91.9277ZM83.2768 89.9536C82.7048 88.8155 81.8018 88.0946 81.0621 88.1472L79.8093 85.3901C81.4329 84.9372 83.5991 86.5832 84.852 89.2775L83.2768 89.9536ZM83.6146 90.8121L85.2036 90.1301C86.1075 92.6341 85.8771 95.0703 84.6443 96.0301L83.3912 93.2725C83.8218 92.7785 83.9087 91.8262 83.6146 90.8121Z" fill="#A9A8A8"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.9105 61.0873L40.883 59.7272L40.8836 59.7296C40.9157 59.7201 40.948 59.7112 40.9804 59.7028C43.9354 58.9338 47.3838 62.3898 48.6826 67.4219C49.9814 72.4539 48.6388 77.1567 45.6839 77.9256C45.647 77.9352 45.6102 77.9441 45.5734 77.9523L40.7659 79.27L39.2087 73.4386C38.705 72.4695 38.2848 71.3809 37.9817 70.2066C37.7022 69.1236 37.5451 68.0558 37.4999 67.0393L35.9105 61.0873Z" fill="black"/>
<path d="M40.7484 79.2733C37.7934 80.0422 34.3459 76.5895 33.0481 71.5613C31.7502 66.5332 33.0936 61.8337 36.0486 61.0647C39.0036 60.2957 42.4511 63.7485 43.749 68.7767C45.0468 73.8048 43.7034 78.5043 40.7484 79.2733Z" fill="black"/>
<g filter="url(#filter5_d_308_40)">
<path d="M40.0035 76.9758C37.7871 77.5526 35.2013 74.9629 34.2279 71.1916C33.2545 67.4203 34.2621 63.8955 36.4784 63.3187C38.6948 62.742 41.2806 65.3317 42.254 69.103C43.2274 72.8743 42.2198 76.3991 40.0035 76.9758Z" fill="url(#paint2_linear_308_40)"/>
</g>
<g filter="url(#filter6_di_308_40)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M39.2528 65.2373C39.7775 65.6839 40.266 66.2936 40.6771 67.0252L39.1345 68.8968C39.0117 68.6915 38.8731 68.5133 38.7252 68.3687L39.2528 65.2373ZM38.0703 72.2553C38.2983 72.3883 38.5355 72.4409 38.762 72.3932L39.9965 75.7209C39.9014 75.7617 39.803 75.7955 39.7017 75.8219C39.0002 76.0044 38.2542 75.8053 37.5547 75.3156L38.0703 72.2553ZM37.3186 75.1379L37.8329 72.0852C37.6535 71.9319 37.485 71.7293 37.3385 71.4886L35.827 73.3224C36.2573 74.0752 36.7698 74.6956 37.3186 75.1379ZM38.492 68.1742L39.0183 65.0503C38.2904 64.5089 37.507 64.2814 36.7722 64.4725C36.5673 64.5258 36.3747 64.6096 36.1955 64.7205L37.4398 68.0748C37.5089 68.0324 37.583 68.0003 37.6617 67.9798C37.9329 67.9093 38.2213 67.9857 38.492 68.1742ZM34.9021 71.015C35.0958 71.7655 35.3663 72.4596 35.691 73.0748L37.2025 71.241C37.0939 71.0219 37.0031 70.7792 36.9358 70.519C36.9294 70.494 36.9232 70.4689 36.9172 70.444L34.8138 70.6449C34.8407 70.7678 34.8701 70.8913 34.9021 71.015ZM39.269 69.1463L40.8117 67.2745C41.1237 67.876 41.3839 68.5511 41.5718 69.2794C41.6104 69.4287 41.6452 69.5774 41.6763 69.7255L39.5451 69.9291C39.5386 69.9012 39.5317 69.8733 39.5245 69.8453C39.4596 69.5943 39.3726 69.3593 39.269 69.1463ZM34.7609 70.3861C34.565 69.3586 34.5457 68.3704 34.6793 67.5035L36.8324 69.2884C36.8034 69.5654 36.8128 69.8697 36.8651 70.185L34.7609 70.3861ZM39.5966 70.188C39.6585 70.5661 39.6586 70.9279 39.6049 71.2458L41.7538 73.0273C41.9264 72.1265 41.927 71.079 41.7272 69.9846L39.5966 70.188ZM41.6913 73.3165L39.5384 71.5318C39.4327 71.8821 39.2506 72.1542 39.0065 72.298L40.2322 75.6017C40.9333 75.1913 41.4367 74.37 41.6913 73.3165ZM34.7321 67.2061L36.8812 68.9878C36.9484 68.6893 37.0662 68.4364 37.2271 68.2555L35.9738 64.8769C35.3656 65.3614 34.9376 66.1871 34.7321 67.2061Z" fill="#4C4C4C"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M39.304 73.9754C38.5289 72.7708 37.8884 71.3147 37.4704 69.6954C37.0762 68.1683 36.9253 66.6717 36.989 65.306L35.8973 61.2178L38.5324 60.4971C39.0605 59.8441 39.7119 59.3886 40.4692 59.1915C43.4241 58.4226 46.8725 61.8786 48.1714 66.9107C49.412 71.7177 48.2424 76.224 45.5593 77.288L45.6482 77.6183L40.7526 79.4006L39.304 73.9754Z" fill="black"/>
<path d="M40.68 79.4146C37.7345 80.1811 34.2979 76.7394 33.0043 71.7274C31.7106 66.7153 33.0497 62.0308 35.9952 61.2643C38.9407 60.4978 42.3772 63.9395 43.6709 68.9516C44.9646 73.9637 43.6255 78.6481 40.68 79.4146Z" fill="#222222"/>
<g filter="url(#filter7_d_308_40)">
<g filter="url(#filter8_d_308_40)">
<path d="M39.8963 76.9325C37.8063 77.43 35.3671 74.8769 34.4483 71.2302C33.5295 67.5834 34.479 64.2239 36.569 63.7264C38.6591 63.229 41.0982 65.782 42.017 69.4287C42.9358 73.0755 41.9863 76.435 39.8963 76.9325Z" fill="url(#paint3_linear_308_40)"/>
</g>
<g filter="url(#filter9_di_308_40)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.4425 71.4223C36.0866 73.5558 37.4343 75.0973 38.7336 75.2489L38.1622 72.6766C37.6744 72.4825 37.202 71.9209 36.9103 71.1666L35.4425 71.4223ZM35.2425 70.6314C34.7586 68.2673 35.3291 66.1374 36.5774 65.5426L37.1489 68.1148C36.7019 68.5149 36.5111 69.3994 36.6893 70.3793L35.2425 70.6314ZM39.4757 69.8936C39.188 68.8364 38.5494 68.0494 37.9165 67.9113L37.3541 65.3799C38.7851 65.4211 40.3125 67.2015 40.9199 69.6419L39.4757 69.8936ZM39.6148 70.6953L41.0803 70.4398C41.4281 72.7147 40.7934 74.6905 39.5262 75.1577L38.964 72.6267C39.4319 72.3397 39.6806 71.5862 39.6148 70.6953Z" fill="#A9A8A8"/>
</g>
</g>
<g filter="url(#filter10_ii_308_40)">
<path d="M129.286 86.1909C130.013 84.7273 129.812 83.4334 129.832 82.262L129.791 79.2742C129.758 76.9016 129.753 74.8685 129.719 74.0896C129.685 73.3107 130.512 71.0463 126.326 66.9296C122.565 63.2332 114.357 58.9992 112.319 58.4841C110.281 57.9688 101.514 45.7408 98.2843 43.3702C88.6373 36.2897 64.3357 29.6958 58.3491 29.7791C54.8795 29.6802 40.4798 37.0967 38.8767 38.1175C30.5879 43.3957 36.2093 41.2388 30.1415 49.1735C29.7564 49.5824 29.5133 51.613 29.5821 52.7737C29.5888 52.8889 29.5751 53.0041 29.5352 53.1123C29.3733 53.5499 29.1167 54.2214 29.0899 54.353C28.9 55.2834 29.5731 59.8824 29.5856 60.7788C29.6001 61.8259 29.4191 63.7283 29.6412 64.783C29.8632 65.8376 32.5604 68.3282 32.582 68.0081C32.5656 66.8333 32.6406 66.6216 32.9239 64.7373C33.6939 61.7952 35.0849 60.4779 36.2032 60.2262C38.5424 59.6998 40.7008 60.6362 42.4359 63.5765C45.6087 68.953 44.5791 72.4663 44.8772 74.8645C44.9068 75.1031 45.0629 75.3031 45.2778 75.4109L73.5338 89.585C74.1085 89.8733 74.7025 89.3912 74.6552 88.75C74.6476 88.6477 74.6385 88.5062 74.6233 88.288C74.5318 86.9796 74.463 80.8166 78.2587 79.447C81.6031 78.2402 85.9357 82.4149 87.9914 85.8203C89.6387 88.5495 90.9786 94.0428 91.3353 97.2499C91.3655 97.5208 91.5257 97.7585 91.7698 97.8797C93.7892 98.8815 99.3982 101.463 107.624 98.7531C125.031 93.0178 128.56 87.6512 129.286 86.1909Z" fill="url(#paint4_linear_308_40)"/>
</g>
<mask id="mask0_308_40" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="29" y="29" width="101" height="71">
<path d="M129.286 86.1909C130.013 84.7273 129.812 83.4334 129.832 82.262L129.791 79.2742C129.758 76.9016 129.753 74.8685 129.719 74.0896C129.685 73.3107 130.512 71.0463 126.326 66.9296C122.565 63.2332 114.357 58.9992 112.319 58.4841C110.281 57.9688 101.514 45.7408 98.2843 43.3702C88.6373 36.2897 64.3357 29.6958 58.3491 29.7791C54.8795 29.6802 40.4798 37.0967 38.8767 38.1175C30.5879 43.3957 36.2093 41.2388 30.1415 49.1735C29.7564 49.5824 29.5133 51.613 29.5821 52.7737C29.5888 52.8889 29.5751 53.0041 29.5352 53.1123C29.3733 53.5499 29.1167 54.2214 29.0899 54.353C28.9 55.2834 29.5731 59.8824 29.5856 60.7788C29.6001 61.8259 29.4191 63.7283 29.6412 64.783C29.8632 65.8376 32.5604 68.3282 32.582 68.0081C32.6035 67.6879 32.7506 65.8879 32.6621 64.6502C32.6321 62.4959 34.4054 60.6309 36.2032 60.2262C38.5424 59.6998 40.6978 60.4179 42.4329 63.3582C45.8943 69.2237 44.1217 72.3193 44.3395 74.6732C44.3617 74.9132 44.5201 75.1139 44.7357 75.2214L73.4673 89.5557C74.0542 89.8484 74.6522 89.3403 74.606 88.686C74.5984 88.5789 74.5897 88.4393 74.5758 88.242C74.4844 86.9334 74.463 80.8166 78.2587 79.447C81.6031 78.2402 85.9357 82.4149 87.9914 85.8203C89.6387 88.5495 90.9786 94.0428 91.3353 97.2499C91.3655 97.5208 91.5257 97.7585 91.7698 97.8797C93.7892 98.8815 99.3982 101.463 107.624 98.7531C125.031 93.0178 128.56 87.6512 129.286 86.1909Z" fill="url(#paint5_linear_308_40)"/>
</mask>
<g mask="url(#mask0_308_40)">
<g filter="url(#filter11_ddi_308_40)">
<path d="M82.736 68.4858C79.8732 66.3371 80.0661 63.587 75.62 55.8073C75.3545 55.3427 75.5449 54.7463 76.0346 54.5305L98.1903 44.7709C98.5099 44.6302 98.9204 44.5734 99.1864 44.7996C99.8766 45.3867 101.577 47.1387 105.764 52.2122C108.411 55.4191 108.917 56.2212 110.879 58.1559C111.113 58.3865 110.653 59.0122 109.664 59.8507C106.511 62.5237 87.5349 72.0873 82.736 68.4858Z" fill="url(#paint6_linear_308_40)"/>
</g>
<g filter="url(#filter12_di_308_40)">
<path d="M55.7315 45.938C49.496 42.9785 47.7905 42.6339 43.4784 44.3459C41.2986 45.2114 38.6 46.1691 38.2338 47.6715C37.8676 49.1738 44.2839 52.656 46.1606 53.6606C47.0777 54.1515 79.8003 70.8891 79.0986 68.8978C78.2386 66.4574 73.5671 56.6954 71.8487 54.2326C70.7425 52.6475 61.9671 48.8974 55.7315 45.938Z" fill="url(#paint7_linear_308_40)"/>
</g>
<g filter="url(#filter13_f_308_40)">
<path d="M91.0085 81.0077C83.0181 77.428 78.4926 72.858 73.6509 68.6904C87.9526 72.715 117.893 81.5409 117.434 82.1487C116.86 82.9086 114.732 83.8301 107.624 84.4086C100.626 84.9783 91.9234 81.4175 91.0085 81.0077Z" fill="url(#paint8_linear_308_40)" fill-opacity="0.21"/>
</g>
<g filter="url(#filter14_f_308_40)">
<path d="M121.42 62.9686C120.964 62.6039 115.214 56.8092 110.929 53.9441C114.532 60.7404 122.737 75.5075 123.48 75.4227C124.408 75.3167 128.652 74.461 129.865 71.5494C131.058 68.6829 121.99 63.4244 121.42 62.9686Z" fill="url(#paint9_linear_308_40)" fill-opacity="0.13"/>
</g>
<g filter="url(#filter15_di_308_40)">
<path d="M63.3136 66.5832C63.3119 66.4654 63.2433 66.3588 63.1367 66.3088L60.4578 65.0519C60.2529 64.9557 60.0184 65.1072 60.0215 65.3338L60.0269 65.7192C60.0285 65.8372 60.0974 65.9439 60.2042 65.9937L62.883 67.2455C63.0879 67.3412 63.322 67.1897 63.3189 66.9633L63.3136 66.5832Z" fill="#323232"/>
</g>
<g filter="url(#filter16_di_308_40)">
<path d="M48.0283 60.0909C48.0267 59.9731 47.9579 59.8665 47.8514 59.8164L45.1725 58.5594C44.9675 58.4633 44.7331 58.6148 44.7363 58.8414L44.7416 59.2268C44.7433 59.3447 44.8121 59.4514 44.919 59.5013L47.5977 60.7531C47.8027 60.8488 48.0368 60.6973 48.0336 60.471L48.0283 60.0909Z" fill="#323232"/>
</g>
<path d="M101.312 64.6133L84.1925 51.05C82.1278 51.6399 77.457 53.8694 76.563 54.2194C75.4048 54.6729 75.4597 55.3423 75.4597 55.3423C75.4646 55.6937 75.4194 55.4483 75.7985 56.1394C76.4277 57.2865 76.9325 58.3109 78.2959 61.2676C80.6377 66.3464 81.1394 67.6265 83.0973 68.7645C84.5721 69.6217 87.645 69.7498 91.3522 68.4691C94.6658 67.3244 98.4662 65.9436 101.312 64.6133Z" fill="#C4C4C4" fill-opacity="0.2"/>
<g opacity="0.17" filter="url(#filter17_f_308_40)">
<path d="M125.805 70.6218C120.93 74.591 110.04 80.3195 105.154 81.238" stroke="url(#paint10_linear_308_40)" stroke-width="40.5109" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter18_ddii_308_40)">
<path d="M127.574 72.8C128.513 71.4926 128.234 70.4612 128.07 68.8379C128.065 68.7936 128.054 68.7465 128.04 68.7043C127.607 67.4276 129.457 70.0667 129.673 71.9079C129.744 72.5174 129.709 72.8271 129.659 73.3913C129.637 73.6493 128.797 75.3661 128.88 75.7047C128.964 76.0433 128.909 78.6037 128.925 78.9574C128.943 79.3112 127.747 81.6127 123.9 84.2064C120.704 86.3609 116.093 87.8101 115.619 87.885C115.146 87.96 114.658 86.374 114.313 86.2908C113.408 86.0731 110.757 87.0888 109.767 87.3528C108.61 87.6606 105.912 88.3711 102.948 88.0321C99.2811 87.6131 98.7153 87.2047 95.3252 85.4193C93.1777 84.2884 91.6063 82.1041 90.9465 80.7717C90.7677 80.4106 91.0899 80.0825 91.4741 80.2038C93.8202 80.9442 98.6575 82.601 104.1 83.0825C112.189 83.798 118.515 79.8706 119.422 79.306C123.232 76.933 125.238 76.0472 127.574 72.8Z" fill="#272727"/>
</g>
<mask id="mask1_308_40" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="90" y="68" width="41" height="21">
<path d="M127.577 72.7842C128.326 71.741 128.349 70.5374 128.143 69.486C128.03 68.917 128.586 68.6041 128.891 69.1015C129.645 70.3301 129.972 71.379 130.07 71.9584C130.142 72.5679 130.132 72.9557 130.083 73.5199C130.061 73.7781 128.768 75.2589 128.852 75.5975C128.935 75.936 128.885 78.0319 128.895 78.7767C128.906 79.5216 127.757 81.5965 123.911 84.1925C120.717 86.3489 116.107 87.801 115.633 87.8761C115.16 87.9514 114.671 86.3658 114.325 86.2828C113.42 86.0656 110.771 87.083 109.78 87.3474C108.624 87.656 105.926 88.3681 102.961 88.031C99.2944 87.6141 98.7282 87.2061 95.337 85.4228C93.1826 84.2901 91.6071 82.098 90.9496 80.767C90.7719 80.4073 91.091 80.0865 91.4753 80.2075C93.8192 80.9454 98.662 82.602 104.11 83.0807C112.2 83.7913 118.524 79.8601 119.43 79.2951C123.238 76.9196 125.244 76.0326 127.577 72.7842Z" fill="#272727"/>
</mask>
<g mask="url(#mask1_308_40)">
<g filter="url(#filter19_d_308_40)">
<path d="M96.6948 81.2046L107.565 81.2172C107.565 81.2192 107.566 81.2213 107.567 81.2233C107.598 81.3102 107.638 81.4361 107.672 81.5898C107.74 81.8989 107.78 82.3105 107.68 82.7384C107.581 83.1616 107.343 83.6096 106.839 83.9982C106.331 84.3895 105.543 84.7285 104.332 84.9082C103.612 85.0149 102.615 85.0251 101.588 84.845C100.561 84.6648 99.5189 84.2968 98.6959 83.6591C98.2509 83.3143 97.701 82.629 97.2523 82.0099C97.0306 81.7041 96.8379 81.4206 96.7008 81.2136C96.6988 81.2106 96.6968 81.2076 96.6948 81.2046Z" stroke="white" stroke-opacity="0.9" stroke-width="1.70478"/>
</g>
<g filter="url(#filter20_d_308_40)">
<path d="M128.099 69.3921C128.1 69.3937 128.102 69.3953 128.102 69.397C128.205 69.5722 128.334 69.8251 128.436 70.1345C128.642 70.7526 128.744 71.5864 128.344 72.4802C128.23 72.7345 128.065 72.905 127.897 72.9761C127.74 73.0421 127.55 73.0355 127.333 72.8758C127.182 72.7656 126.977 72.7238 126.79 72.7078C126.595 72.691 126.382 72.6995 126.19 72.7168C126.109 72.7242 126.029 72.7333 125.954 72.743L128.099 69.3921Z" stroke="white" stroke-opacity="0.9" stroke-width="1.70478"/>
</g>
</g>
</g>
<g filter="url(#filter21_f_308_40)">
<path d="M76.0314 76.5225L46.2787 62.9656C46.0034 62.8402 45.7147 63.1445 45.8371 63.4211C47.2263 66.5574 47.0582 69.8807 47.001 71.4921C46.9967 71.6152 47.065 71.728 47.1755 71.7826L73.8317 84.9579C74.0425 85.0621 74.2894 84.9059 74.2913 84.6708C74.3206 80.9957 75.2153 78.3186 76.1285 77.258C76.3067 77.051 76.2801 76.6358 76.0314 76.5225Z" fill="url(#paint11_linear_308_40)" fill-opacity="0.12"/>
</g>
<defs>
<filter id="filter0_f_308_40" x="11.4464" y="49.0848" width="128.581" height="66.2149" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.33186" result="effect1_foregroundBlur_308_40"/>
</filter>
<filter id="filter1_d_308_40" x="75.6215" y="82.5171" width="12.4196" height="16.7442" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.3069"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter2_di_308_40" x="75.3369" y="84.3191" width="11.5093" height="14.2451" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.24552"/>
<feGaussianBlur stdDeviation="0.18414"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.8414" dy="1.2276"/>
<feGaussianBlur stdDeviation="0.920701"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter3_d_308_40" x="75.8819" y="82.5871" width="12.0085" height="16.602" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.390179"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter4_di_308_40" x="77.0202" y="85.2489" width="9.06487" height="11.8666" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.266134"/>
<feGaussianBlur stdDeviation="0.166334"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.998007" dy="0.665336"/>
<feGaussianBlur stdDeviation="0.499002"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter5_d_308_40" x="33.3318" y="62.7133" width="9.81825" height="14.8679" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.262499"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter6_di_308_40" x="33.0186" y="64.3008" width="9.17668" height="12.6379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.209999"/>
<feGaussianBlur stdDeviation="0.1575"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.41 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.57499" dy="1.05"/>
<feGaussianBlur stdDeviation="0.787497"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter7_d_308_40" x="33.631" y="63.1978" width="9.20338" height="14.2634" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.233386"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter8_d_308_40" x="33.2895" y="62.8563" width="9.88632" height="14.9463" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.404121"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter9_di_308_40" x="34.0374" y="65.311" width="7.4818" height="10.6272" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.275645"/>
<feGaussianBlur stdDeviation="0.172278"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.59 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.03367" dy="0.689111"/>
<feGaussianBlur stdDeviation="0.516834"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter10_ii_308_40" x="29.0562" y="23.5352" width="105.469" height="76.4194" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4.68215" dy="-6.24285"/>
<feGaussianBlur stdDeviation="18.7286"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_308_40"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.780359"/>
<feGaussianBlur stdDeviation="1.56071"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_308_40" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter11_ddi_308_40" x="73.0446" y="42.8061" width="39.1222" height="28.3217" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1.2276" dy="0.613802"/>
<feGaussianBlur stdDeviation="0.613802"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.613802"/>
<feGaussianBlur stdDeviation="0.613802"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.56 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_308_40" result="effect2_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.613802" dy="1.8414"/>
<feGaussianBlur stdDeviation="0.920701"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.75 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_308_40"/>
</filter>
<filter id="filter12_di_308_40" x="35.206" y="43.3142" width="44.3556" height="27.9289" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.451895"/>
<feGaussianBlur stdDeviation="0.225948"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.925374 0 0 0 0 0.925374 0 0 0 0 0.925374 0 0 0 0.37 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3.01279" dy="2.18081"/>
<feGaussianBlur stdDeviation="1.50639"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.63 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter13_f_308_40" x="65.0577" y="60.0972" width="60.9746" height="32.9662" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.29661" result="effect1_foregroundBlur_308_40"/>
</filter>
<filter id="filter14_f_308_40" x="105.405" y="48.4199" width="30.0925" height="32.5274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2.7621" result="effect1_foregroundBlur_308_40"/>
</filter>
<filter id="filter15_di_308_40" x="59.7171" y="64.4136" width="3.90632" height="4.38322" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.304432"/>
<feGaussianBlur stdDeviation="0.152216"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.52216"/>
<feGaussianBlur stdDeviation="0.761082"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.74 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter16_di_308_40" x="44.4318" y="57.9212" width="3.90629" height="4.38322" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.304432"/>
<feGaussianBlur stdDeviation="0.152216"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0 0.78755 0 0 0 0.19 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.52216"/>
<feGaussianBlur stdDeviation="0.761082"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.74 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_308_40"/>
</filter>
<filter id="filter17_f_308_40" x="81.8261" y="47.2967" width="67.3044" height="57.2694" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1.5345" result="effect1_foregroundBlur_308_40"/>
</filter>
<filter id="filter18_ddii_308_40" x="90.1828" y="67.2984" width="40.2432" height="25.7381" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.534764"/>
<feGaussianBlur stdDeviation="0.356508"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.57 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.356508"/>
<feGaussianBlur stdDeviation="0.356508"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_308_40" result="effect2_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_308_40" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.91488"/>
<feGaussianBlur stdDeviation="5.0088"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_308_40"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.535577"/>
<feGaussianBlur stdDeviation="0.535577"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_308_40" result="effect4_innerShadow_308_40"/>
</filter>
<filter id="filter19_d_308_40" x="94.9632" y="80.3503" width="14.9035" height="6.89883" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.568258" dy="0.705884"/>
<feGaussianBlur stdDeviation="0.352943"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter20_d_308_40" x="124.112" y="67.9109" width="6.63174" height="7.36905" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.568258" dy="0.705884"/>
<feGaussianBlur stdDeviation="0.352943"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_308_40"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_308_40" result="shape"/>
</filter>
<filter id="filter21_f_308_40" x="44.8641" y="61.9925" width="32.3233" height="23.9422" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.472011" result="effect1_foregroundBlur_308_40"/>
</filter>
<linearGradient id="paint0_linear_308_40" x1="76.7454" y1="107.13" x2="72.6501" y2="99.6864" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F3F3F"/>
<stop offset="1" stop-color="#5C5C5C"/>
</linearGradient>
<linearGradient id="paint1_linear_308_40" x1="77.1503" y1="106.593" x2="73.1212" y2="99.4841" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAAAAA"/>
<stop offset="1" stop-color="#C2C2C2"/>
</linearGradient>
<linearGradient id="paint2_linear_308_40" x1="31.4638" y1="83.0273" x2="29.1652" y2="76.1323" gradientUnits="userSpaceOnUse">
<stop stop-color="#3F3F3F"/>
<stop offset="1" stop-color="#5C5C5C"/>
</linearGradient>
<linearGradient id="paint3_linear_308_40" x1="31.8441" y1="82.5269" x2="29.7491" y2="75.8232" gradientUnits="userSpaceOnUse">
<stop stop-color="#AAAAAA"/>
<stop offset="1" stop-color="#C2C2C2"/>
</linearGradient>
<linearGradient id="paint4_linear_308_40" x1="46.1628" y1="71.4172" x2="125.879" y2="58.9946" gradientUnits="userSpaceOnUse">
<stop stop-color="#CECBCB"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_308_40" x1="91.0788" y1="84.6953" x2="65.6844" y2="28.7036" gradientUnits="userSpaceOnUse">
<stop stop-color="#CECBCB"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_308_40" x1="94.5898" y1="26.6825" x2="67.2742" y2="53.2585" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAE9"/>
<stop offset="0.498386" stop-color="#707072"/>
<stop offset="1" stop-color="#2F3032"/>
</linearGradient>
<linearGradient id="paint7_linear_308_40" x1="112.246" y1="125.003" x2="95.8246" y2="40.3327" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8EAE9"/>
<stop offset="0.498386" stop-color="#707072"/>
<stop offset="1" stop-color="#2F3032"/>
</linearGradient>
<linearGradient id="paint8_linear_308_40" x1="94.2455" y1="85.5769" x2="97.541" y2="75.4871" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEAEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_308_40" x1="133.037" y1="67.1441" x2="120.136" y2="71.8488" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEAEA"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_308_40" x1="115.869" y1="80.9793" x2="113.491" y2="76.5073" gradientUnits="userSpaceOnUse">
<stop stop-color="#4B4B4B"/>
<stop offset="0.899322" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_308_40" x1="56.8044" y1="82.8641" x2="63.4159" y2="70.6077" gradientUnits="userSpaceOnUse">
<stop stop-color="#040404"/>
<stop offset="1" stop-color="#3C3C3C" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
